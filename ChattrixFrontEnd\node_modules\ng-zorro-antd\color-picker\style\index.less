@import '../../style/themes/index';
@import '../../style/mixins/index';

@color-picker-prefix-cls: ~'@{ant-prefix}-color-picker';

.@{color-picker-prefix-cls} {
  position: absolute;
  z-index: 1;
  display: block;
  width: max-content;
  min-width: 250px;
  visibility: visible;

  &-hidden {
    display: none;
  }

  &-panel {
    display: flex;
    flex-direction: column;
    width: 250px;
    border-radius: @border-radius-base;

    &-disabled {
      cursor: not-allowed;
    }
  }

  &-gradient {
    position: absolute;
    inset: 0;
  }

  &-palette {
    position: relative;
  }

  &-select {
    .@{color-picker-prefix-cls}-palette {
      min-height: 160px;
      overflow: hidden;
      border-radius: @border-radius-base;

      & > .@{color-picker-prefix-cls}-gradient {
        border-top-left-radius: 5px;
      }
    }

    margin-bottom: @margin-sm;
  }

  &-saturation {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-image: linear-gradient(0deg, #000, transparent), linear-gradient(90deg, #fff, hsla(0deg, 0%, 100%, 0));
    border-radius: inherit;
  }

  &-transform {
    position: absolute;
    z-index: 1;
  }

  &-handler {
    box-sizing: border-box;
    width: 16px;
    height: 16px;
    border: @border-width-base solid #fff;
    border-radius: 50%;
  }

  &-handler-sm {
    width: 12px;
    height: 12px;
  }

  &-slider {
    width: 100%;
    border-radius: @border-radius-base;

    .@{color-picker-prefix-cls}-palette {
      height: 8px;
    }

    .@{color-picker-prefix-cls}-gradient {
      border-radius: @border-radius-base;
    }

    &-hue {
      margin-bottom: @margin-sm;
    }

    &-alpha {
      background-image: conic-gradient(
        rgba(0, 0, 0, 0.06) 0 25%,
        transparent 0 50%,
        rgba(0, 0, 0, 0.06) 0 75%,
        transparent 0
      );
      background-size: 8px 8px;
    }
  }

  &-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    padding: 3px;
    border: @border-width-base solid @border-color-split;
    border-radius: @border-radius-base;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      border-color: @primary-color;
    }

    &-text {
      margin: 0 @margin-xss 0 @margin-xs;
      font-size: @font-size-base;
    }
  }

  &-sm {
    min-width: 24px;
    height: 24px;
  }

  &-lg {
    min-width: 40px;
    height: 40px;

    .@{color-picker-prefix-cls}-trigger-text {
      font-size: @font-size-lg;
    }
  }

  &-color-block {
    position: relative;
    width: 28px;
    height: 28px;
    overflow: hidden;
    background-image: conic-gradient(
      rgba(0, 0, 0, 0.06) 0 25%,
      transparent 0 50%,
      rgba(0, 0, 0, 0.06) 0 75%,
      transparent 0
    );
    background-size: 50% 50%;
    border-radius: @border-radius-base;

    &-inner {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      border: @border-width-base solid @border-color-split;
    }
  }

  &-slider-container {
    display: flex;
    gap: @margin-sm;
    margin-bottom: @margin-sm;

    .@{color-picker-prefix-cls}-slider-group {
      flex: 1;

      &-disabled-alpha {
        display: flex;
        align-items: center;

        .@{color-picker-prefix-cls}-slider {
          margin-bottom: 0;
        }
      }
    }
  }

  &-title {
    display: flex;

    &-content {
      flex: 1;
      margin-bottom: @margin-xs;
    }

    .@{color-picker-prefix-cls}-clear {
      flex: 0 0 18px;
      margin-left: @margin-xs;
    }
  }

  &-clear {
    position: relative;
    width: 18px;
    height: 18px;
    margin-bottom: @margin-xs;
    overflow: hidden;
    border: @border-width-base solid @border-color-split;
    border-radius: @border-radius-base;
    cursor: pointer;

    &::after {
      position: absolute;
      top: 0;
      display: block;
      width: 40px;
      height: 2px;
      background-color: #f5222d;
      transform: rotate(-45deg);
      transform-origin: right;
      content: '';
      inset-inline-end: 1px;
    }
  }

  &-input-container {
    display: flex;

    .ant-select {
      font-size: 12px;
    }

    .ant-input {
      font-size: 12px;
    }

    .ant-input-number {
      width: auto;
      font-size: 12px;
    }

    .ant-input-number-sm input {
      padding: 0 0 0 4px;
    }

    .ant-select-selector {
      padding: 0;
    }

    .@{color-picker-prefix-cls}-format-select {
      width: auto;
    }

    .@{color-picker-prefix-cls}-input {
      flex: 1;

      .@{color-picker-prefix-cls}-hex-input {
        margin-right: @margin-xss;
        padding: 0;
      }

      .@{color-picker-prefix-cls}-hsb-input {
        display: flex;
        gap: 4px;
        align-items: center;
      }

      .@{color-picker-prefix-cls}-rgb-input {
        display: flex;
        gap: 4px;
        align-items: center;
      }
    }

    .@{color-picker-prefix-cls}-steppers {
      flex: 1;
    }

    .@{color-picker-prefix-cls}-alpha-input {
      flex: 0 0 44px;
      margin-left: @margin-xss;
    }
  }

  &-disabled {
    color: @disabled-color;
    background: @disabled-bg;
    cursor: not-allowed;

    .@{color-picker-prefix-cls}-trigger {
      cursor: not-allowed;

      &:hover {
        border-color: @border-color-split;
      }
    }
  }

  &-inline {
    display: inline-block;

    .@{color-picker-prefix-cls}-color-block {
      width: 24px;
      height: 24px;
      margin: 0;
    }

    &-lg {
      .@{color-picker-prefix-cls}-color-block {
        width: 32px;
        height: 32px;
      }
    }

    &-sm {
      .@{color-picker-prefix-cls}-color-block {
        width: 16px;
        height: 16px;
      }
    }
  }
}
