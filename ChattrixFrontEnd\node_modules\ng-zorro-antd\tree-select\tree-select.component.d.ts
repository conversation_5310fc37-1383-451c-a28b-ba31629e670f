/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */
import { FocusMonitor } from '@angular/cdk/a11y';
import { Direction, Directionality } from '@angular/cdk/bidi';
import { CdkConnectedOverlay, CdkOverlayOrigin, ConnectedOverlayPositionChange, ConnectionPositionPair } from '@angular/cdk/overlay';
import { ChangeDetectorRef, ElementRef, EventEmitter, OnChanges, OnDestroy, OnInit, Renderer2, SimpleChanges, TemplateRef } from '@angular/core';
import { ControlValueAccessor } from '@angular/forms';
import { NzConfigKey, NzConfigService } from 'ng-zorro-antd/core/config';
import { NzFormStatusService } from 'ng-zorro-antd/core/form';
import { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';
import { NzFormatEmitEvent, NzTreeBase, NzTreeNode, NzTreeNodeOptions } from 'ng-zorro-antd/core/tree';
import { NgClassInterface, NgStyleInterface, NzSizeLDSType, NzStatus, NzValidateStatus, OnChangeType, OnTouchedType } from 'ng-zorro-antd/core/types';
import { NzSelectSearchComponent } from 'ng-zorro-antd/select';
import { NzTreeComponent } from 'ng-zorro-antd/tree';
import { NzTreeSelectService } from './tree-select.service';
import * as i0 from "@angular/core";
import * as i1 from "ng-zorro-antd/space";
export type NzPlacementType = 'bottomLeft' | 'bottomRight' | 'topLeft' | 'topRight' | '';
export declare class NzTreeSelectComponent extends NzTreeBase implements ControlValueAccessor, OnInit, OnDestroy, OnChanges {
    nzConfigService: NzConfigService;
    private renderer;
    private cdr;
    private elementRef;
    private directionality;
    private focusMonitor;
    readonly _nzModuleName: NzConfigKey;
    nzId: string | null;
    nzAllowClear: boolean;
    nzShowExpand: boolean;
    nzShowLine: boolean;
    nzDropdownMatchSelectWidth: boolean;
    nzCheckable: boolean;
    nzHideUnMatched: boolean;
    nzShowIcon: boolean;
    nzShowSearch: boolean;
    nzDisabled: boolean;
    nzAsyncData: boolean;
    nzMultiple: boolean;
    nzDefaultExpandAll: boolean;
    nzCheckStrictly: boolean;
    nzVirtualItemSize: number;
    nzVirtualMaxBufferPx: number;
    nzVirtualMinBufferPx: number;
    nzVirtualHeight: string | null;
    nzExpandedIcon?: TemplateRef<{
        $implicit: NzTreeNode;
        origin: NzTreeNodeOptions;
    }>;
    nzNotFoundContent?: string | TemplateRef<void>;
    nzNodes: NzTreeNodeOptions[] | NzTreeNode[];
    nzOpen: boolean;
    nzSize: NzSizeLDSType;
    nzPlaceHolder: string;
    nzDropdownStyle: NgStyleInterface | null;
    nzDropdownClassName?: string;
    nzBackdrop: boolean;
    nzStatus: NzStatus;
    nzPlacement: NzPlacementType;
    set nzExpandedKeys(value: string[]);
    get nzExpandedKeys(): string[];
    nzDisplayWith: (node: NzTreeNode) => string | undefined;
    nzMaxTagCount: number;
    nzMaxTagPlaceholder: TemplateRef<{
        $implicit: NzTreeNode[];
    }> | null;
    readonly nzOpenChange: EventEmitter<boolean>;
    readonly nzCleared: EventEmitter<void>;
    readonly nzRemoved: EventEmitter<NzTreeNode>;
    readonly nzExpandChange: EventEmitter<NzFormatEmitEvent>;
    readonly nzTreeClick: EventEmitter<NzFormatEmitEvent>;
    readonly nzTreeCheckboxChange: EventEmitter<NzFormatEmitEvent>;
    nzSelectSearchComponent: NzSelectSearchComponent;
    treeRef: NzTreeComponent;
    cdkOverlayOrigin: CdkOverlayOrigin;
    cdkConnectedOverlay: CdkConnectedOverlay;
    nzTreeTemplate: TemplateRef<{
        $implicit: NzTreeNode;
        origin: NzTreeNodeOptions;
    }>;
    nzTreeTemplateChild: TemplateRef<{
        $implicit: NzTreeNode;
        origin: NzTreeNodeOptions;
    }>;
    get treeTemplate(): TemplateRef<{
        $implicit: NzTreeNode;
        origin: NzTreeNodeOptions;
    }>;
    prefixCls: string;
    statusCls: NgClassInterface;
    status: NzValidateStatus;
    hasFeedback: boolean;
    dropdownClassName: string;
    triggerWidth?: number;
    isComposing: boolean;
    isDestroy: boolean;
    isNotFound: boolean;
    focused: boolean;
    inputValue: string;
    dropdownPosition: 'top' | 'center' | 'bottom';
    selectedNodes: NzTreeNode[];
    expandedKeys: string[];
    value: string[];
    dir: Direction;
    positions: ConnectionPositionPair[];
    protected finalSize: import("@angular/core").Signal<NzSizeLDSType>;
    private size;
    private compactSize;
    private destroy$;
    private isNzDisableFirstChange;
    private isComposingChange$;
    private searchValueChange$;
    onChange: OnChangeType;
    onTouched: OnTouchedType;
    get placeHolderDisplay(): string;
    get isMultiple(): boolean;
    noAnimation: NzNoAnimationDirective | null;
    nzFormStatusService: NzFormStatusService | null;
    private nzFormNoStatusService;
    constructor(nzTreeService: NzTreeSelectService, nzConfigService: NzConfigService, renderer: Renderer2, cdr: ChangeDetectorRef, elementRef: ElementRef, directionality: Directionality, focusMonitor: FocusMonitor);
    ngOnInit(): void;
    ngOnDestroy(): void;
    isComposingChange(isComposing: boolean): void;
    setDisabledState(isDisabled: boolean): void;
    private setStatusStyles;
    ngOnChanges({ nzNodes, nzDropdownClassName, nzStatus, nzPlacement, nzSize }: SimpleChanges): void;
    writeValue(value: string[] | string): void;
    registerOnChange(fn: (_: string[] | string | null) => void): void;
    registerOnTouched(fn: () => void): void;
    onKeydown(event: KeyboardEvent): void;
    trigger(): void;
    openDropdown(): void;
    closeDropDown(): void;
    onKeyDownInput(e: KeyboardEvent): void;
    onExpandedKeysChange(value: NzFormatEmitEvent): void;
    setInputValue(value: string): void;
    removeSelected(node: NzTreeNode, emit?: boolean): void;
    focusOnInput(): void;
    subscribeSelectionChange(): void;
    updateSelectedNodes(init?: boolean): void;
    updatePosition(): void;
    onPositionChange(position: ConnectedOverlayPositionChange): void;
    onClearSelection(): void;
    onClickOutside(event: MouseEvent): void;
    setSearchValues($event: NzFormatEmitEvent): void;
    updateCdkConnectedOverlayStatus(): void;
    clearSelectedNodes(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTreeSelectComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzTreeSelectComponent, "nz-tree-select", ["nzTreeSelect"], { "nzId": { "alias": "nzId"; "required": false; }; "nzAllowClear": { "alias": "nzAllowClear"; "required": false; }; "nzShowExpand": { "alias": "nzShowExpand"; "required": false; }; "nzShowLine": { "alias": "nzShowLine"; "required": false; }; "nzDropdownMatchSelectWidth": { "alias": "nzDropdownMatchSelectWidth"; "required": false; }; "nzCheckable": { "alias": "nzCheckable"; "required": false; }; "nzHideUnMatched": { "alias": "nzHideUnMatched"; "required": false; }; "nzShowIcon": { "alias": "nzShowIcon"; "required": false; }; "nzShowSearch": { "alias": "nzShowSearch"; "required": false; }; "nzDisabled": { "alias": "nzDisabled"; "required": false; }; "nzAsyncData": { "alias": "nzAsyncData"; "required": false; }; "nzMultiple": { "alias": "nzMultiple"; "required": false; }; "nzDefaultExpandAll": { "alias": "nzDefaultExpandAll"; "required": false; }; "nzCheckStrictly": { "alias": "nzCheckStrictly"; "required": false; }; "nzVirtualItemSize": { "alias": "nzVirtualItemSize"; "required": false; }; "nzVirtualMaxBufferPx": { "alias": "nzVirtualMaxBufferPx"; "required": false; }; "nzVirtualMinBufferPx": { "alias": "nzVirtualMinBufferPx"; "required": false; }; "nzVirtualHeight": { "alias": "nzVirtualHeight"; "required": false; }; "nzExpandedIcon": { "alias": "nzExpandedIcon"; "required": false; }; "nzNotFoundContent": { "alias": "nzNotFoundContent"; "required": false; }; "nzNodes": { "alias": "nzNodes"; "required": false; }; "nzOpen": { "alias": "nzOpen"; "required": false; }; "nzSize": { "alias": "nzSize"; "required": false; }; "nzPlaceHolder": { "alias": "nzPlaceHolder"; "required": false; }; "nzDropdownStyle": { "alias": "nzDropdownStyle"; "required": false; }; "nzDropdownClassName": { "alias": "nzDropdownClassName"; "required": false; }; "nzBackdrop": { "alias": "nzBackdrop"; "required": false; }; "nzStatus": { "alias": "nzStatus"; "required": false; }; "nzPlacement": { "alias": "nzPlacement"; "required": false; }; "nzExpandedKeys": { "alias": "nzExpandedKeys"; "required": false; }; "nzDisplayWith": { "alias": "nzDisplayWith"; "required": false; }; "nzMaxTagCount": { "alias": "nzMaxTagCount"; "required": false; }; "nzMaxTagPlaceholder": { "alias": "nzMaxTagPlaceholder"; "required": false; }; "nzTreeTemplate": { "alias": "nzTreeTemplate"; "required": false; }; }, { "nzOpenChange": "nzOpenChange"; "nzCleared": "nzCleared"; "nzRemoved": "nzRemoved"; "nzExpandChange": "nzExpandChange"; "nzTreeClick": "nzTreeClick"; "nzTreeCheckboxChange": "nzTreeCheckboxChange"; }, ["nzTreeTemplateChild"], never, true, [{ directive: typeof i1.NzSpaceCompactItemDirective; inputs: {}; outputs: {}; }]>;
    static ngAcceptInputType_nzAllowClear: unknown;
    static ngAcceptInputType_nzShowExpand: unknown;
    static ngAcceptInputType_nzShowLine: unknown;
    static ngAcceptInputType_nzDropdownMatchSelectWidth: unknown;
    static ngAcceptInputType_nzCheckable: unknown;
    static ngAcceptInputType_nzHideUnMatched: unknown;
    static ngAcceptInputType_nzShowIcon: unknown;
    static ngAcceptInputType_nzShowSearch: unknown;
    static ngAcceptInputType_nzDisabled: unknown;
    static ngAcceptInputType_nzAsyncData: unknown;
    static ngAcceptInputType_nzMultiple: unknown;
    static ngAcceptInputType_nzDefaultExpandAll: unknown;
    static ngAcceptInputType_nzCheckStrictly: unknown;
    static ngAcceptInputType_nzMaxTagCount: unknown;
}
