{"version": 3, "file": "ng-zorro-antd-water-mark.mjs", "sources": ["../../components/water-mark/typings.ts", "../../components/water-mark/util.ts", "../../components/water-mark/water-mark.component.ts", "../../components/water-mark/water-mark.module.ts", "../../components/water-mark/public-api.ts", "../../components/water-mark/ng-zorro-antd-water-mark.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport interface FontType {\n  color?: string;\n  fontSize?: number;\n  fontWeight?: 'normal' | 'light' | 'weight' | number;\n  fontFamily?: string;\n  fontStyle?: 'none' | 'normal' | 'italic' | 'oblique';\n}\n\nexport interface MarkStyleType {\n  zIndex: number;\n  position: string;\n  left: string | number;\n  top: string | number;\n  width: string;\n  height: string;\n  pointerEvents: string;\n  backgroundRepeat: string;\n  backgroundPosition?: string;\n  visibility: string;\n}\n\nexport interface MarkStyleCanvasType extends MarkStyleType {\n  backgroundImage: string;\n  backgroundSize: string;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { MarkStyleCanvasType } from './typings';\n\n/** Returns the ratio of the device's physical pixel resolution to the css pixel resolution */\nexport function getPixelRatio(): number {\n  return window.devicePixelRatio || 1;\n}\n\nexport function toLowercaseSeparator(key: keyof MarkStyleCanvasType): string {\n  return key.replace(/([A-Z])/g, '-$1').toLowerCase();\n}\n\nexport function getStyleStr(style: MarkStyleCanvasType): string {\n  const keys = Object.keys(style) as Array<keyof MarkStyleCanvasType>;\n  const styleCss: string[] = keys.map(\n    (key: keyof MarkStyleCanvasType) => `${toLowercaseSeparator(key)}: ${style[key]};`\n  );\n  return styleCss.join(' ');\n}\n\n/** Whether to re-render the watermark */\nexport function reRendering(mutation: MutationRecord, watermarkElement?: HTMLElement): boolean {\n  let flag = false;\n  // Whether to delete the watermark node\n  if (mutation.removedNodes.length) {\n    flag = Array.from(mutation.removedNodes).some(node => node === watermarkElement);\n  }\n  // Whether the watermark dom property value has been modified\n  if (mutation.type === 'attributes' && mutation.target === watermarkElement) {\n    flag = true;\n  }\n  return flag;\n}\n\n/** Rotate with the watermark as the center point */\nexport function rotateWatermark(ctx: CanvasRenderingContext2D, rotateX: number, rotateY: number, rotate: number): void {\n  ctx.translate(rotateX, rotateY);\n  ctx.rotate((Math.PI / 180) * Number(rotate));\n  ctx.translate(-rotateX, -rotateY);\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { DOCUMENT } from '@angular/common';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  SimpleChanges,\n  inject,\n  numberAttribute\n} from '@angular/core';\n\nimport { FontType, MarkStyleType } from './typings';\nimport { getPixelRatio, getStyleStr, reRendering, rotateWatermark } from './util';\n\n/**\n * Base size of the canvas, 1 for parallel layout and 2 for alternate layout\n * Only alternate layout is currently supported\n */\nconst BaseSize = 2;\nconst FontGap = 3;\n\n@Component({\n  selector: 'nz-water-mark',\n  exportAs: 'NzWaterMark',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  template: ` <ng-content></ng-content> `,\n  host: {\n    class: 'ant-water-mark'\n  }\n})\nexport class NzWaterMarkComponent implements AfterViewInit, OnInit, OnChanges, OnDestroy {\n  @Input({ transform: numberAttribute }) nzWidth: number = 120;\n  @Input({ transform: numberAttribute }) nzHeight: number = 64;\n  @Input({ transform: numberAttribute }) nzRotate: number = -22;\n  @Input({ transform: numberAttribute }) nzZIndex: number = 9;\n  @Input() nzImage: string = '';\n  @Input() nzContent: string | string[] = '';\n  @Input() nzFont: FontType = {};\n  @Input() nzGap: [number, number] = [100, 100];\n  @Input() nzOffset: [number, number] = [this.nzGap[0] / 2, this.nzGap[1] / 2];\n\n  private document: Document = inject(DOCUMENT);\n\n  waterMarkElement: HTMLDivElement = this.document.createElement('div');\n  stopObservation: boolean = false;\n\n  observer = new MutationObserver(mutations => {\n    if (this.stopObservation) {\n      return;\n    }\n    mutations.forEach(mutation => {\n      if (reRendering(mutation, this.waterMarkElement)) {\n        this.destroyWatermark();\n        this.renderWatermark();\n      }\n    });\n  });\n\n  constructor(\n    private el: ElementRef,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  ngOnInit(): void {\n    this.observer.observe(this.el.nativeElement, {\n      subtree: true,\n      childList: true,\n      attributeFilter: ['style', 'class']\n    });\n  }\n\n  ngAfterViewInit(): void {\n    this.renderWatermark();\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzRotate, nzZIndex, nzWidth, nzHeight, nzImage, nzContent, nzFont, gapX, gapY, offsetLeft, offsetTop } =\n      changes;\n\n    if (\n      nzRotate ||\n      nzZIndex ||\n      nzWidth ||\n      nzHeight ||\n      nzImage ||\n      nzContent ||\n      nzFont ||\n      gapX ||\n      gapY ||\n      offsetLeft ||\n      offsetTop\n    ) {\n      this.renderWatermark();\n    }\n  }\n\n  getFont(): void {\n    const font: FontType = {\n      color: 'rgba(0,0,0,.15)',\n      fontSize: 16,\n      fontWeight: 'normal',\n      fontFamily: 'sans-serif',\n      fontStyle: 'normal'\n    };\n\n    this.nzFont = { ...font, ...this.nzFont };\n    this.cdr.markForCheck();\n  }\n\n  getMarkStyle(): MarkStyleType {\n    const markStyle: MarkStyleType = {\n      zIndex: this.nzZIndex,\n      position: 'absolute',\n      left: 0,\n      top: 0,\n      width: '100%',\n      height: '100%',\n      pointerEvents: 'none',\n      backgroundRepeat: 'repeat',\n      visibility: 'visible'\n    };\n\n    /** Calculate the style of the nzOffset */\n    let positionLeft = (this.nzOffset?.[0] ?? this.nzGap[0] / 2) - this.nzGap[0] / 2;\n    let positionTop = (this.nzOffset?.[1] ?? this.nzGap[1] / 2) - this.nzGap[1] / 2;\n    if (positionLeft > 0) {\n      markStyle.left = `${positionLeft}px`;\n      markStyle.width = `calc(100% - ${positionLeft}px)`;\n      positionLeft = 0;\n    }\n    if (positionTop > 0) {\n      markStyle.top = `${positionTop}px`;\n      markStyle.height = `calc(100% - ${positionTop}px)`;\n      positionTop = 0;\n    }\n    markStyle.backgroundPosition = `${positionLeft}px ${positionTop}px`;\n\n    return markStyle;\n  }\n\n  destroyWatermark(): void {\n    if (this.waterMarkElement) {\n      this.waterMarkElement.remove();\n    }\n  }\n\n  appendWatermark(base64Url: string, markWidth: number): void {\n    this.stopObservation = true;\n    this.waterMarkElement.setAttribute(\n      'style',\n      getStyleStr({\n        ...this.getMarkStyle(),\n        backgroundImage: `url('${base64Url}')`,\n        backgroundSize: `${(this.nzGap[0] + markWidth) * BaseSize}px`\n      })\n    );\n    this.el.nativeElement.append(this.waterMarkElement);\n    this.cdr.markForCheck();\n\n    // Delayed execution\n    setTimeout(() => {\n      this.stopObservation = false;\n      this.cdr.markForCheck();\n    });\n  }\n\n  getMarkSize(ctx: CanvasRenderingContext2D): [number, number] {\n    let defaultWidth = 120;\n    let defaultHeight = 64;\n    if (!this.nzImage && ctx.measureText) {\n      ctx.font = `${Number(this.nzFont.fontSize)}px ${this.nzFont.fontFamily}`;\n      const contents = Array.isArray(this.nzContent) ? this.nzContent : [this.nzContent];\n      const widths = contents.map(item => ctx.measureText(item!).width);\n      defaultWidth = Math.ceil(Math.max(...widths));\n      defaultHeight = Number(this.nzFont.fontSize) * contents.length + (contents.length - 1) * FontGap;\n    }\n    return [this.nzWidth ?? defaultWidth, this.nzHeight ?? defaultHeight];\n  }\n\n  fillTexts(ctx: CanvasRenderingContext2D, drawX: number, drawY: number, drawWidth: number, drawHeight: number): void {\n    const ratio = getPixelRatio();\n    const mergedFontSize = Number(this.nzFont.fontSize) * ratio;\n    ctx.font = `${this.nzFont.fontStyle} normal ${this.nzFont.fontWeight} ${mergedFontSize}px/${drawHeight}px ${this.nzFont.fontFamily}`;\n    if (this.nzFont.color) ctx.fillStyle = this.nzFont.color;\n    ctx.textAlign = 'center';\n    ctx.textBaseline = 'top';\n    ctx.translate(drawWidth / 2, 0);\n    const contents = Array.isArray(this.nzContent) ? this.nzContent : [this.nzContent];\n    contents?.forEach((item, index) => {\n      ctx.fillText(item ?? '', drawX, drawY + index * (mergedFontSize + FontGap * ratio));\n    });\n  }\n\n  drawText(\n    canvas: HTMLCanvasElement,\n    ctx: CanvasRenderingContext2D,\n    drawX: number,\n    drawY: number,\n    drawWidth: number,\n    drawHeight: number,\n    alternateRotateX: number,\n    alternateRotateY: number,\n    alternateDrawX: number,\n    alternateDrawY: number,\n    markWidth: number\n  ): void {\n    this.fillTexts(ctx, drawX, drawY, drawWidth, drawHeight);\n\n    /** Fill the interleaved text after rotation */\n    ctx.restore();\n    rotateWatermark(ctx, alternateRotateX, alternateRotateY, this.nzRotate);\n    this.fillTexts(ctx, alternateDrawX, alternateDrawY, drawWidth, drawHeight);\n    this.appendWatermark(canvas.toDataURL(), markWidth);\n  }\n\n  renderWatermark(): void {\n    if (!this.nzContent && !this.nzImage) {\n      return;\n    }\n    const canvas: HTMLCanvasElement = this.document.createElement('canvas');\n    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;\n\n    if (ctx) {\n      if (!this.waterMarkElement) {\n        this.waterMarkElement = this.document.createElement('div');\n      }\n      this.getFont();\n      const ratio = getPixelRatio();\n      const [markWidth, markHeight] = this.getMarkSize(ctx);\n      const canvasWidth = (this.nzGap[0] + markWidth) * ratio;\n      const canvasHeight = (this.nzGap[1] + markHeight) * ratio;\n      canvas.setAttribute('width', `${canvasWidth * BaseSize}px`);\n      canvas.setAttribute('height', `${canvasHeight * BaseSize}px`);\n\n      const drawX = (this.nzGap[0] * ratio) / 2;\n      const drawY = (this.nzGap[1] * ratio) / 2;\n      const drawWidth = markWidth * ratio;\n      const drawHeight = markHeight * ratio;\n      const rotateX = (drawWidth + this.nzGap[0] * ratio) / 2;\n      const rotateY = (drawHeight + this.nzGap[1] * ratio) / 2;\n\n      /** Alternate drawing parameters */\n      const alternateDrawX = drawX + canvasWidth;\n      const alternateDrawY = drawY + canvasHeight;\n      const alternateRotateX = rotateX + canvasWidth;\n      const alternateRotateY = rotateY + canvasHeight;\n\n      ctx.save();\n      rotateWatermark(ctx, rotateX, rotateY, this.nzRotate);\n\n      if (this.nzImage) {\n        const img = new Image();\n\n        const onLoad = (): void => {\n          cleanup();\n\n          ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);\n\n          /** Draw interleaved pictures after rotation */\n          ctx.restore();\n          rotateWatermark(ctx, alternateRotateX, alternateRotateY, this.nzRotate);\n          ctx.drawImage(img, alternateDrawX, alternateDrawY, drawWidth, drawHeight);\n          this.appendWatermark(canvas.toDataURL(), markWidth);\n        };\n\n        const onError = (): void => {\n          cleanup();\n\n          this.drawText(\n            canvas,\n            ctx,\n            drawX,\n            drawY,\n            drawWidth,\n            drawHeight,\n            alternateRotateX,\n            alternateRotateY,\n            alternateDrawX,\n            alternateDrawY,\n            markWidth\n          );\n        };\n\n        const cleanup = (): void => {\n          img.removeEventListener('load', onLoad);\n          img.removeEventListener('error', onError);\n        };\n\n        img.addEventListener('load', onLoad);\n        img.addEventListener('error', onError);\n\n        img.crossOrigin = 'anonymous';\n        img.referrerPolicy = 'no-referrer';\n        img.src = this.nzImage;\n      } else {\n        this.drawText(\n          canvas,\n          ctx,\n          drawX,\n          drawY,\n          drawWidth,\n          drawHeight,\n          alternateRotateX,\n          alternateRotateY,\n          alternateDrawX,\n          alternateDrawY,\n          markWidth\n        );\n      }\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.observer.disconnect();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzWaterMarkComponent } from './water-mark.component';\n\n@NgModule({\n  exports: [NzWaterMarkComponent],\n  imports: [NzWaterMarkComponent]\n})\nexport class NzWaterMarkModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './typings';\nexport * from './water-mark.component';\nexport * from './water-mark.module';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": [], "mappings": ";;;;AAAA;;;AAGG;;ACHH;;;AAGG;AAIH;SACgB,aAAa,GAAA;AAC3B,IAAA,OAAO,MAAM,CAAC,gBAAgB,IAAI,CAAC;AACrC;AAEM,SAAU,oBAAoB,CAAC,GAA8B,EAAA;IACjE,OAAO,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,WAAW,EAAE;AACrD;AAEM,SAAU,WAAW,CAAC,KAA0B,EAAA;IACpD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAqC;IACnE,MAAM,QAAQ,GAAa,IAAI,CAAC,GAAG,CACjC,CAAC,GAA8B,KAAK,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAK,EAAA,EAAA,KAAK,CAAC,GAAG,CAAC,CAAG,CAAA,CAAA,CACnF;AACD,IAAA,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;AAC3B;AAEA;AACgB,SAAA,WAAW,CAAC,QAAwB,EAAE,gBAA8B,EAAA;IAClF,IAAI,IAAI,GAAG,KAAK;;AAEhB,IAAA,IAAI,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE;QAChC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,gBAAgB,CAAC;;;AAGlF,IAAA,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,IAAI,QAAQ,CAAC,MAAM,KAAK,gBAAgB,EAAE;QAC1E,IAAI,GAAG,IAAI;;AAEb,IAAA,OAAO,IAAI;AACb;AAEA;AACM,SAAU,eAAe,CAAC,GAA6B,EAAE,OAAe,EAAE,OAAe,EAAE,MAAc,EAAA;AAC7G,IAAA,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC;AAC/B,IAAA,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC;IAC5C,GAAG,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;AACnC;;AC3CA;;;AAGG;AAqBH;;;AAGG;AACH,MAAM,QAAQ,GAAG,CAAC;AAClB,MAAM,OAAO,GAAG,CAAC;MAWJ,oBAAoB,CAAA;AA6BrB,IAAA,EAAA;AACA,IAAA,GAAA;IA7B6B,OAAO,GAAW,GAAG;IACrB,QAAQ,GAAW,EAAE;IACrB,QAAQ,GAAW,CAAC,EAAE;IACtB,QAAQ,GAAW,CAAC;IAClD,OAAO,GAAW,EAAE;IACpB,SAAS,GAAsB,EAAE;IACjC,MAAM,GAAa,EAAE;AACrB,IAAA,KAAK,GAAqB,CAAC,GAAG,EAAE,GAAG,CAAC;IACpC,QAAQ,GAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAEpE,IAAA,QAAQ,GAAa,MAAM,CAAC,QAAQ,CAAC;IAE7C,gBAAgB,GAAmB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;IACrE,eAAe,GAAY,KAAK;AAEhC,IAAA,QAAQ,GAAG,IAAI,gBAAgB,CAAC,SAAS,IAAG;AAC1C,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB;;AAEF,QAAA,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAG;YAC3B,IAAI,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAE;gBAChD,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,eAAe,EAAE;;AAE1B,SAAC,CAAC;AACJ,KAAC,CAAC;IAEF,WACU,CAAA,EAAc,EACd,GAAsB,EAAA;QADtB,IAAE,CAAA,EAAA,GAAF,EAAE;QACF,IAAG,CAAA,GAAA,GAAH,GAAG;;IAGb,QAAQ,GAAA;QACN,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;AAC3C,YAAA,OAAO,EAAE,IAAI;AACb,YAAA,SAAS,EAAE,IAAI;AACf,YAAA,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;AACnC,SAAA,CAAC;;IAGJ,eAAe,GAAA;QACb,IAAI,CAAC,eAAe,EAAE;;AAGxB,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,GAC5G,OAAO;AAET,QAAA,IACE,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,OAAO;YACP,SAAS;YACT,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,UAAU;AACV,YAAA,SAAS,EACT;YACA,IAAI,CAAC,eAAe,EAAE;;;IAI1B,OAAO,GAAA;AACL,QAAA,MAAM,IAAI,GAAa;AACrB,YAAA,KAAK,EAAE,iBAAiB;AACxB,YAAA,QAAQ,EAAE,EAAE;AACZ,YAAA,UAAU,EAAE,QAAQ;AACpB,YAAA,UAAU,EAAE,YAAY;AACxB,YAAA,SAAS,EAAE;SACZ;AAED,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE;AACzC,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;IAGzB,YAAY,GAAA;AACV,QAAA,MAAM,SAAS,GAAkB;YAC/B,MAAM,EAAE,IAAI,CAAC,QAAQ;AACrB,YAAA,QAAQ,EAAE,UAAU;AACpB,YAAA,IAAI,EAAE,CAAC;AACP,YAAA,GAAG,EAAE,CAAC;AACN,YAAA,KAAK,EAAE,MAAM;AACb,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,aAAa,EAAE,MAAM;AACrB,YAAA,gBAAgB,EAAE,QAAQ;AAC1B,YAAA,UAAU,EAAE;SACb;;AAGD,QAAA,IAAI,YAAY,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAChF,QAAA,IAAI,WAAW,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAC/E,QAAA,IAAI,YAAY,GAAG,CAAC,EAAE;AACpB,YAAA,SAAS,CAAC,IAAI,GAAG,CAAG,EAAA,YAAY,IAAI;AACpC,YAAA,SAAS,CAAC,KAAK,GAAG,CAAe,YAAA,EAAA,YAAY,KAAK;YAClD,YAAY,GAAG,CAAC;;AAElB,QAAA,IAAI,WAAW,GAAG,CAAC,EAAE;AACnB,YAAA,SAAS,CAAC,GAAG,GAAG,CAAG,EAAA,WAAW,IAAI;AAClC,YAAA,SAAS,CAAC,MAAM,GAAG,CAAe,YAAA,EAAA,WAAW,KAAK;YAClD,WAAW,GAAG,CAAC;;QAEjB,SAAS,CAAC,kBAAkB,GAAG,CAAA,EAAG,YAAY,CAAM,GAAA,EAAA,WAAW,IAAI;AAEnE,QAAA,OAAO,SAAS;;IAGlB,gBAAgB,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;;;IAIlC,eAAe,CAAC,SAAiB,EAAE,SAAiB,EAAA;AAClD,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI;QAC3B,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAChC,OAAO,EACP,WAAW,CAAC;YACV,GAAG,IAAI,CAAC,YAAY,EAAE;YACtB,eAAe,EAAE,CAAQ,KAAA,EAAA,SAAS,CAAI,EAAA,CAAA;AACtC,YAAA,cAAc,EAAE,CAAA,EAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,QAAQ,CAAI,EAAA;AAC9D,SAAA,CAAC,CACH;QACD,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACnD,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;QAGvB,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,eAAe,GAAG,KAAK;AAC5B,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,SAAC,CAAC;;AAGJ,IAAA,WAAW,CAAC,GAA6B,EAAA;QACvC,IAAI,YAAY,GAAG,GAAG;QACtB,IAAI,aAAa,GAAG,EAAE;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,GAAG,CAAC,WAAW,EAAE;AACpC,YAAA,GAAG,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACxE,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;AAClF,YAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,WAAW,CAAC,IAAK,CAAC,CAAC,KAAK,CAAC;AACjE,YAAA,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;YAC7C,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO;;AAElG,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO,IAAI,YAAY,EAAE,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC;;IAGvE,SAAS,CAAC,GAA6B,EAAE,KAAa,EAAE,KAAa,EAAE,SAAiB,EAAE,UAAkB,EAAA;AAC1G,QAAA,MAAM,KAAK,GAAG,aAAa,EAAE;AAC7B,QAAA,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK;QAC3D,GAAG,CAAC,IAAI,GAAG,CAAG,EAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAW,QAAA,EAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA,CAAA,EAAI,cAAc,CAAA,GAAA,EAAM,UAAU,CAAA,GAAA,EAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA,CAAE;AACpI,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK;YAAE,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;AACxD,QAAA,GAAG,CAAC,SAAS,GAAG,QAAQ;AACxB,QAAA,GAAG,CAAC,YAAY,GAAG,KAAK;QACxB,GAAG,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC;QAC/B,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;QAClF,QAAQ,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;YAChC,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,GAAG,KAAK,IAAI,cAAc,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC;AACrF,SAAC,CAAC;;IAGJ,QAAQ,CACN,MAAyB,EACzB,GAA6B,EAC7B,KAAa,EACb,KAAa,EACb,SAAiB,EACjB,UAAkB,EAClB,gBAAwB,EACxB,gBAAwB,EACxB,cAAsB,EACtB,cAAsB,EACtB,SAAiB,EAAA;AAEjB,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC;;QAGxD,GAAG,CAAC,OAAO,EAAE;QACb,eAAe,CAAC,GAAG,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC;AACvE,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,CAAC;QAC1E,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,SAAS,CAAC;;IAGrD,eAAe,GAAA;QACb,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACpC;;QAEF,MAAM,MAAM,GAAsB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC;QACvE,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAA6B;QAE/D,IAAI,GAAG,EAAE;AACP,YAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC1B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;;YAE5D,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,MAAM,KAAK,GAAG,aAAa,EAAE;AAC7B,YAAA,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;AACrD,YAAA,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,KAAK;AACvD,YAAA,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU,IAAI,KAAK;YACzD,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,CAAG,EAAA,WAAW,GAAG,QAAQ,CAAI,EAAA,CAAA,CAAC;YAC3D,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAG,EAAA,YAAY,GAAG,QAAQ,CAAI,EAAA,CAAA,CAAC;AAE7D,YAAA,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC;AACzC,YAAA,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC;AACzC,YAAA,MAAM,SAAS,GAAG,SAAS,GAAG,KAAK;AACnC,YAAA,MAAM,UAAU,GAAG,UAAU,GAAG,KAAK;AACrC,YAAA,MAAM,OAAO,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC;AACvD,YAAA,MAAM,OAAO,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC;;AAGxD,YAAA,MAAM,cAAc,GAAG,KAAK,GAAG,WAAW;AAC1C,YAAA,MAAM,cAAc,GAAG,KAAK,GAAG,YAAY;AAC3C,YAAA,MAAM,gBAAgB,GAAG,OAAO,GAAG,WAAW;AAC9C,YAAA,MAAM,gBAAgB,GAAG,OAAO,GAAG,YAAY;YAE/C,GAAG,CAAC,IAAI,EAAE;YACV,eAAe,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC;AAErD,YAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,gBAAA,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE;gBAEvB,MAAM,MAAM,GAAG,MAAW;AACxB,oBAAA,OAAO,EAAE;AAET,oBAAA,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC;;oBAGvD,GAAG,CAAC,OAAO,EAAE;oBACb,eAAe,CAAC,GAAG,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC;AACvE,oBAAA,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,CAAC;oBACzE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,SAAS,CAAC;AACrD,iBAAC;gBAED,MAAM,OAAO,GAAG,MAAW;AACzB,oBAAA,OAAO,EAAE;oBAET,IAAI,CAAC,QAAQ,CACX,MAAM,EACN,GAAG,EACH,KAAK,EACL,KAAK,EACL,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,SAAS,CACV;AACH,iBAAC;gBAED,MAAM,OAAO,GAAG,MAAW;AACzB,oBAAA,GAAG,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC;AACvC,oBAAA,GAAG,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC;AAC3C,iBAAC;AAED,gBAAA,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC;AACpC,gBAAA,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC;AAEtC,gBAAA,GAAG,CAAC,WAAW,GAAG,WAAW;AAC7B,gBAAA,GAAG,CAAC,cAAc,GAAG,aAAa;AAClC,gBAAA,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;;iBACjB;gBACL,IAAI,CAAC,QAAQ,CACX,MAAM,EACN,GAAG,EACH,KAAK,EACL,KAAK,EACL,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,SAAS,CACV;;;;IAKP,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;;uGA3RjB,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAApB,oBAAoB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EACX,eAAe,CACf,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,sCACf,eAAe,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EACf,eAAe,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,WAAA,EAAA,MAAA,EAAA,QAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EATzB,CAA6B,2BAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAK5B,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAThC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,aAAa;oBACvB,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE,CAA6B,2BAAA,CAAA;AACvC,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE;AACR;AACF,iBAAA;+GAEwC,OAAO,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACE,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACE,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACE,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBAC5B,OAAO,EAAA,CAAA;sBAAf;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;;;ACjDH;;;AAGG;MAUU,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAjB,iBAAiB,EAAA,OAAA,EAAA,CAFlB,oBAAoB,CAAA,EAAA,OAAA,EAAA,CADpB,oBAAoB,CAAA,EAAA,CAAA;wGAGnB,iBAAiB,EAAA,CAAA;;2FAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAJ7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,oBAAoB,CAAC;oBAC/B,OAAO,EAAE,CAAC,oBAAoB;AAC/B,iBAAA;;;ACZD;;;AAGG;;ACHH;;AAEG;;;;"}