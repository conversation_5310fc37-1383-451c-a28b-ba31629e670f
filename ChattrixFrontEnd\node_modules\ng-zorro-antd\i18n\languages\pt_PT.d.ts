/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */
declare const _default: {
    locale: string;
    Pagination: {
        items_per_page: string;
        jump_to: string;
        jump_to_confirm: string;
        page: string;
        prev_page: string;
        next_page: string;
        prev_5: string;
        next_5: string;
        prev_3: string;
        next_3: string;
        page_size: string;
    };
    DatePicker: {
        lang: {
            yearPlaceholder: string;
            quarterPlaceholder: string;
            monthPlaceholder: string;
            weekPlaceholder: string;
            rangePlaceholder: string[];
            rangeYearPlaceholder: string[];
            rangeMonthPlaceholder: string[];
            rangeWeekPlaceholder: string[];
            locale: string;
            today: string;
            now: string;
            backToToday: string;
            ok: string;
            clear: string;
            month: string;
            year: string;
            timeSelect: string;
            dateSelect: string;
            monthSelect: string;
            yearSelect: string;
            decadeSelect: string;
            yearFormat: string;
            dateFormat: string;
            dayFormat: string;
            dateTimeFormat: string;
            monthBeforeYear: boolean;
            previousMonth: string;
            nextMonth: string;
            previousYear: string;
            nextYear: string;
            previousDecade: string;
            nextDecade: string;
            previousCentury: string;
            nextCentury: string;
            placeholder: string;
            monthFormat: string;
        };
        timePickerLocale: {
            placeholder: string;
        };
    };
    TimePicker: {
        placeholder: string;
    };
    Calendar: {
        lang: {
            locale: string;
            today: string;
            now: string;
            backToToday: string;
            ok: string;
            clear: string;
            month: string;
            year: string;
            timeSelect: string;
            dateSelect: string;
            monthSelect: string;
            yearSelect: string;
            decadeSelect: string;
            yearFormat: string;
            dateFormat: string;
            dayFormat: string;
            dateTimeFormat: string;
            monthBeforeYear: boolean;
            previousMonth: string;
            nextMonth: string;
            previousYear: string;
            nextYear: string;
            previousDecade: string;
            nextDecade: string;
            previousCentury: string;
            nextCentury: string;
            placeholder: string;
            rangePlaceholder: string[];
            monthFormat: string;
        };
        timePickerLocale: {
            placeholder: string;
        };
    };
    Table: {
        filterTitle: string;
        filterConfirm: string;
        filterReset: string;
        selectAll: string;
        selectInvert: string;
        sortTitle: string;
    };
    Modal: {
        okText: string;
        cancelText: string;
        justOkText: string;
    };
    Popconfirm: {
        okText: string;
        cancelText: string;
    };
    Transfer: {
        searchPlaceholder: string;
        itemUnit: string;
        itemsUnit: string;
    };
    Upload: {
        uploading: string;
        removeFile: string;
        uploadError: string;
        previewFile: string;
        downloadFile: string;
    };
    Empty: {
        description: string;
    };
};
export default _default;
