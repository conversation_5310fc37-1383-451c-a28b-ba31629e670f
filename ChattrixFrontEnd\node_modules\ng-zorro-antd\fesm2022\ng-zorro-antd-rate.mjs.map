{"version": 3, "file": "ng-zorro-antd-rate.mjs", "sources": ["../../components/rate/rate-item.component.ts", "../../components/rate/rate.component.ts", "../../components/rate/rate.module.ts", "../../components/rate/public-api.ts", "../../components/rate/ng-zorro-antd-rate.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  EventEmitter,\n  Input,\n  Output,\n  TemplateRef,\n  ViewEncapsulation,\n  booleanAttribute\n} from '@angular/core';\n\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  selector: '[nz-rate-item]',\n  exportAs: 'nzRateItem',\n  template: `\n    <div\n      class=\"ant-rate-star-second\"\n      (mouseover)=\"hoverRate(false); $event.stopPropagation()\"\n      (click)=\"clickRate(false)\"\n    >\n      <ng-template\n        [ngTemplateOutlet]=\"character || defaultCharacter\"\n        [ngTemplateOutletContext]=\"{ $implicit: index }\"\n      ></ng-template>\n    </div>\n    <div class=\"ant-rate-star-first\" (mouseover)=\"hoverRate(true); $event.stopPropagation()\" (click)=\"clickRate(true)\">\n      <ng-template\n        [ngTemplateOutlet]=\"character || defaultCharacter\"\n        [ngTemplateOutletContext]=\"{ $implicit: index }\"\n      ></ng-template>\n    </div>\n\n    <ng-template #defaultCharacter>\n      <nz-icon nzType=\"star\" nzTheme=\"fill\" />\n    </ng-template>\n  `,\n  imports: [NgTemplateOutlet, NzIconModule]\n})\nexport class NzRateItemComponent {\n  @Input() character!: TemplateRef<{ $implicit: number }>;\n  @Input() index = 0;\n  @Input({ transform: booleanAttribute }) allowHalf: boolean = false;\n  @Output() readonly itemHover = new EventEmitter<boolean>();\n  @Output() readonly itemClick = new EventEmitter<boolean>();\n\n  hoverRate(isHalf: boolean): void {\n    this.itemHover.next(isHalf && this.allowHalf);\n  }\n\n  clickRate(isHalf: boolean): void {\n    this.itemClick.next(isHalf && this.allowHalf);\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { LEFT_ARROW, RIGHT_ARROW } from '@angular/cdk/keycodes';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnChanges,\n  OnInit,\n  Output,\n  Renderer2,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  forwardRef,\n  numberAttribute\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzConfigKey, NzConfigService, WithConfig } from 'ng-zorro-antd/core/config';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { NgClassType } from 'ng-zorro-antd/core/types';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\nimport { NzToolTipModule } from 'ng-zorro-antd/tooltip';\n\nimport { NzRateItemComponent } from './rate-item.component';\n\nconst NZ_CONFIG_MODULE_NAME: NzConfigKey = 'rate';\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  selector: 'nz-rate',\n  exportAs: 'nzRate',\n  preserveWhitespaces: false,\n  template: `\n    <ul\n      #ulElement\n      class=\"ant-rate\"\n      [class.ant-rate-disabled]=\"nzDisabled\"\n      [class.ant-rate-rtl]=\"dir === 'rtl'\"\n      [class]=\"classMap\"\n      (keydown)=\"onKeyDown($event); $event.preventDefault()\"\n      (mouseleave)=\"onRateLeave(); $event.stopPropagation()\"\n      [tabindex]=\"nzDisabled ? -1 : 1\"\n    >\n      @for (star of starArray; track star) {\n        <li\n          class=\"ant-rate-star\"\n          [class]=\"starStyleArray[$index] || ''\"\n          nz-tooltip\n          [nzTooltipTitle]=\"nzTooltips[$index]\"\n        >\n          <div\n            nz-rate-item\n            [allowHalf]=\"nzAllowHalf\"\n            [character]=\"nzCharacter\"\n            [index]=\"$index\"\n            (itemHover)=\"onItemHover($index, $event)\"\n            (itemClick)=\"onItemClick($index, $event)\"\n          ></div>\n        </li>\n      }\n    </ul>\n  `,\n  providers: [\n    NzDestroyService,\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => NzRateComponent),\n      multi: true\n    }\n  ],\n  imports: [NzToolTipModule, NzRateItemComponent, NzToolTipModule]\n})\nexport class NzRateComponent implements OnInit, ControlValueAccessor, OnChanges {\n  readonly _nzModuleName: NzConfigKey = NZ_CONFIG_MODULE_NAME;\n\n  @ViewChild('ulElement', { static: true }) ulElement!: ElementRef<HTMLUListElement>;\n\n  @Input({ transform: booleanAttribute }) @WithConfig() nzAllowClear: boolean = true;\n  @Input({ transform: booleanAttribute }) @WithConfig() nzAllowHalf: boolean = false;\n  @Input({ transform: booleanAttribute }) nzDisabled: boolean = false;\n  @Input({ transform: booleanAttribute }) nzAutoFocus: boolean = false;\n  @Input() nzCharacter!: TemplateRef<{ $implicit: number }>;\n  @Input({ transform: numberAttribute }) nzCount: number = 5;\n  @Input() nzTooltips: string[] = [];\n  @Output() readonly nzOnBlur = new EventEmitter<FocusEvent>();\n  @Output() readonly nzOnFocus = new EventEmitter<FocusEvent>();\n  @Output() readonly nzOnHoverChange = new EventEmitter<number>();\n  @Output() readonly nzOnKeyDown = new EventEmitter<KeyboardEvent>();\n\n  classMap: NgClassType = {};\n  starArray: number[] = [];\n  starStyleArray: NgClassType[] = [];\n  dir: Direction = 'ltr';\n\n  private hasHalf = false;\n  private hoverValue = 0;\n  private isFocused = false;\n  private _value = 0;\n  private isNzDisableFirstChange: boolean = true;\n\n  get nzValue(): number {\n    return this._value;\n  }\n\n  set nzValue(input: number) {\n    if (this._value === input) {\n      return;\n    }\n\n    this._value = input;\n    this.hasHalf = !Number.isInteger(input) && this.nzAllowHalf;\n    this.hoverValue = Math.ceil(input);\n  }\n\n  constructor(\n    public nzConfigService: NzConfigService,\n    private ngZone: NgZone,\n    private renderer: Renderer2,\n    private cdr: ChangeDetectorRef,\n    private directionality: Directionality,\n    private destroy$: NzDestroyService\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzAutoFocus, nzCount, nzValue } = changes;\n\n    if (nzAutoFocus && !nzAutoFocus.isFirstChange()) {\n      const el = this.ulElement.nativeElement;\n      if (this.nzAutoFocus && !this.nzDisabled) {\n        this.renderer.setAttribute(el, 'autofocus', 'autofocus');\n      } else {\n        this.renderer.removeAttribute(el, 'autofocus');\n      }\n    }\n\n    if (nzCount) {\n      this.updateStarArray();\n    }\n\n    if (nzValue) {\n      this.updateStarStyle();\n    }\n  }\n\n  ngOnInit(): void {\n    this.nzConfigService\n      .getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => this.cdr.markForCheck());\n\n    this.directionality.change.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n\n    this.dir = this.directionality.value;\n\n    fromEventOutsideAngular<FocusEvent>(this.ulElement.nativeElement, 'focus')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(event => {\n        this.isFocused = true;\n        if (this.nzOnFocus.observers.length) {\n          this.ngZone.run(() => this.nzOnFocus.emit(event));\n        }\n      });\n\n    fromEventOutsideAngular<FocusEvent>(this.ulElement.nativeElement, 'blur')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(event => {\n        this.isFocused = false;\n        if (this.nzOnBlur.observers.length) {\n          this.ngZone.run(() => this.nzOnBlur.emit(event));\n        }\n      });\n  }\n\n  onItemClick(index: number, isHalf: boolean): void {\n    if (this.nzDisabled) {\n      return;\n    }\n\n    this.hoverValue = index + 1;\n\n    const actualValue = isHalf ? index + 0.5 : index + 1;\n\n    if (this.nzValue === actualValue) {\n      if (this.nzAllowClear) {\n        this.nzValue = 0;\n        this.onChange(this.nzValue);\n      }\n    } else {\n      this.nzValue = actualValue;\n      this.onChange(this.nzValue);\n    }\n\n    this.updateStarStyle();\n  }\n\n  onItemHover(index: number, isHalf: boolean): void {\n    if (this.nzDisabled) {\n      return;\n    }\n    if (this.hoverValue !== index + 1 || isHalf !== this.hasHalf) {\n      this.hoverValue = index + 1;\n      this.hasHalf = isHalf;\n      this.updateStarStyle();\n    }\n    this.nzOnHoverChange.emit(this.hoverValue);\n  }\n\n  onRateLeave(): void {\n    this.hasHalf = !Number.isInteger(this.nzValue);\n    this.hoverValue = Math.ceil(this.nzValue);\n    this.nzOnHoverChange.emit(this.hoverValue);\n    this.updateStarStyle();\n  }\n\n  focus(): void {\n    this.ulElement.nativeElement.focus();\n  }\n\n  blur(): void {\n    this.ulElement.nativeElement.blur();\n  }\n\n  onKeyDown(e: KeyboardEvent): void {\n    const oldVal = this.nzValue;\n\n    if (e.keyCode === RIGHT_ARROW && this.nzValue < this.nzCount) {\n      this.nzValue += this.nzAllowHalf ? 0.5 : 1;\n    } else if (e.keyCode === LEFT_ARROW && this.nzValue > 0) {\n      this.nzValue -= this.nzAllowHalf ? 0.5 : 1;\n    }\n\n    if (oldVal !== this.nzValue) {\n      this.onChange(this.nzValue);\n      this.nzOnKeyDown.emit(e);\n      this.updateStarStyle();\n      this.cdr.markForCheck();\n    }\n  }\n\n  private updateStarArray(): void {\n    this.starArray = Array(this.nzCount)\n      .fill(0)\n      .map((_, i) => i);\n\n    this.updateStarStyle();\n  }\n\n  private updateStarStyle(): void {\n    this.starStyleArray = this.starArray.map(i => {\n      const prefix = 'ant-rate-star';\n      const value = i + 1;\n      return {\n        [`${prefix}-full`]: value < this.hoverValue || (!this.hasHalf && value === this.hoverValue),\n        [`${prefix}-half`]: this.hasHalf && value === this.hoverValue,\n        [`${prefix}-active`]: this.hasHalf && value === this.hoverValue,\n        [`${prefix}-zero`]: value > this.hoverValue,\n        [`${prefix}-focused`]: this.hasHalf && value === this.hoverValue && this.isFocused\n      };\n    });\n  }\n\n  writeValue(value: number | null): void {\n    this.nzValue = value || 0;\n    this.updateStarArray();\n    this.cdr.markForCheck();\n  }\n\n  setDisabledState(isDisabled: boolean): void {\n    this.nzDisabled = (this.isNzDisableFirstChange && this.nzDisabled) || isDisabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n\n  registerOnChange(fn: (_: number) => void): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this.onTouched = fn;\n  }\n\n  onChange: (value: number) => void = () => null;\n  onTouched: () => void = () => null;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzRateItemComponent } from './rate-item.component';\nimport { NzRateComponent } from './rate.component';\n\n@NgModule({\n  imports: [NzRateComponent, NzRateItemComponent],\n  exports: [NzRateComponent]\n})\nexport class NzRateModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './rate.component';\nexport * from './rate.module';\nexport * from './rate-item.component';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;AAGG;MA6CU,mBAAmB,CAAA;AACrB,IAAA,SAAS;IACT,KAAK,GAAG,CAAC;IACsB,SAAS,GAAY,KAAK;AAC/C,IAAA,SAAS,GAAG,IAAI,YAAY,EAAW;AACvC,IAAA,SAAS,GAAG,IAAI,YAAY,EAAW;AAE1D,IAAA,SAAS,CAAC,MAAe,EAAA;QACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC;;AAG/C,IAAA,SAAS,CAAC,MAAe,EAAA;QACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC;;uGAZpC,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAnB,mBAAmB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAGV,gBAAgB,CA3B1B,EAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;GAqBT,EACS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,mJAAE,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE7B,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBA7B/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AAqBT,EAAA,CAAA;AACD,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,YAAY;AACzC,iBAAA;8BAEU,SAAS,EAAA,CAAA;sBAAjB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACuC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACnB,SAAS,EAAA,CAAA;sBAA3B;gBACkB,SAAS,EAAA,CAAA;sBAA3B;;;ACfH,MAAM,qBAAqB,GAAgB,MAAM;IAgDpC,eAAe,GAAA,CAAA,MAAA;;;;;;;iBAAf,eAAe,CAAA;;;AAKe,YAAA,wBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,uBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;YADC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,wBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,cAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,cAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,YAAY,EAAZ,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,YAAY,GAAiB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,0BAAA,EAAA,+BAAA,CAAA;YAC7B,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,uBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,aAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,aAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,WAAW,EAAX,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,WAAW,GAAkB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,yBAAA,EAAA,8BAAA,CAAA;;;QAqC1E,eAAe;QACd,MAAM;QACN,QAAQ;QACR,GAAG;QACH,cAAc;QACd,QAAQ;QA/CT,aAAa,GAAgB,qBAAqB;AAEjB,QAAA,SAAS;QAEG,YAAY,GAAA,iBAAA,CAAA,IAAA,EAAA,0BAAA,EAAY,IAAI,CAAC;QAC7B,WAAW,IAAA,iBAAA,CAAA,IAAA,EAAA,+BAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,yBAAA,EAAY,KAAK,CAAC;QAC3C,UAAU,IAAA,iBAAA,CAAA,IAAA,EAAA,8BAAA,CAAA,EAAY,KAAK;QAC3B,WAAW,GAAY,KAAK;AAC3D,QAAA,WAAW;QACmB,OAAO,GAAW,CAAC;QACjD,UAAU,GAAa,EAAE;AACf,QAAA,QAAQ,GAAG,IAAI,YAAY,EAAc;AACzC,QAAA,SAAS,GAAG,IAAI,YAAY,EAAc;AAC1C,QAAA,eAAe,GAAG,IAAI,YAAY,EAAU;AAC5C,QAAA,WAAW,GAAG,IAAI,YAAY,EAAiB;QAElE,QAAQ,GAAgB,EAAE;QAC1B,SAAS,GAAa,EAAE;QACxB,cAAc,GAAkB,EAAE;QAClC,GAAG,GAAc,KAAK;QAEd,OAAO,GAAG,KAAK;QACf,UAAU,GAAG,CAAC;QACd,SAAS,GAAG,KAAK;QACjB,MAAM,GAAG,CAAC;QACV,sBAAsB,GAAY,IAAI;AAE9C,QAAA,IAAI,OAAO,GAAA;YACT,OAAO,IAAI,CAAC,MAAM;;QAGpB,IAAI,OAAO,CAAC,KAAa,EAAA;AACvB,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;gBACzB;;AAGF,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW;YAC3D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;;QAGpC,WACS,CAAA,eAAgC,EAC/B,MAAc,EACd,QAAmB,EACnB,GAAsB,EACtB,cAA8B,EAC9B,QAA0B,EAAA;YAL3B,IAAe,CAAA,eAAA,GAAf,eAAe;YACd,IAAM,CAAA,MAAA,GAAN,MAAM;YACN,IAAQ,CAAA,QAAA,GAAR,QAAQ;YACR,IAAG,CAAA,GAAA,GAAH,GAAG;YACH,IAAc,CAAA,cAAA,GAAd,cAAc;YACd,IAAQ,CAAA,QAAA,GAAR,QAAQ;;AAGlB,QAAA,WAAW,CAAC,OAAsB,EAAA;YAChC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,OAAO;YAEjD,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE;AAC/C,gBAAA,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa;gBACvC,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBACxC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,EAAE,WAAW,EAAE,WAAW,CAAC;;qBACnD;oBACL,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,EAAE,WAAW,CAAC;;;YAIlD,IAAI,OAAO,EAAE;gBACX,IAAI,CAAC,eAAe,EAAE;;YAGxB,IAAI,OAAO,EAAE;gBACX,IAAI,CAAC,eAAe,EAAE;;;QAI1B,QAAQ,GAAA;AACN,YAAA,IAAI,CAAC;iBACF,gCAAgC,CAAC,qBAAqB;AACtD,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;YAE3C,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC3F,gBAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,gBAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,aAAC,CAAC;YAEF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;YAEpC,uBAAuB,CAAa,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,OAAO;AACtE,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,KAAK,IAAG;AACjB,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI;gBACrB,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE;AACnC,oBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;AAErD,aAAC,CAAC;YAEJ,uBAAuB,CAAa,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,MAAM;AACrE,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,KAAK,IAAG;AACjB,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK;gBACtB,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE;AAClC,oBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;AAEpD,aAAC,CAAC;;QAGN,WAAW,CAAC,KAAa,EAAE,MAAe,EAAA;AACxC,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB;;AAGF,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC;AAE3B,YAAA,MAAM,WAAW,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,KAAK,GAAG,CAAC;AAEpD,YAAA,IAAI,IAAI,CAAC,OAAO,KAAK,WAAW,EAAE;AAChC,gBAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,oBAAA,IAAI,CAAC,OAAO,GAAG,CAAC;AAChB,oBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;;;iBAExB;AACL,gBAAA,IAAI,CAAC,OAAO,GAAG,WAAW;AAC1B,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;;YAG7B,IAAI,CAAC,eAAe,EAAE;;QAGxB,WAAW,CAAC,KAAa,EAAE,MAAe,EAAA;AACxC,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB;;AAEF,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,GAAG,CAAC,IAAI,MAAM,KAAK,IAAI,CAAC,OAAO,EAAE;AAC5D,gBAAA,IAAI,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC;AAC3B,gBAAA,IAAI,CAAC,OAAO,GAAG,MAAM;gBACrB,IAAI,CAAC,eAAe,EAAE;;YAExB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;;QAG5C,WAAW,GAAA;AACT,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;YAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;YACzC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YAC1C,IAAI,CAAC,eAAe,EAAE;;QAGxB,KAAK,GAAA;AACH,YAAA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE;;QAGtC,IAAI,GAAA;AACF,YAAA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE;;AAGrC,QAAA,SAAS,CAAC,CAAgB,EAAA;AACxB,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO;AAE3B,YAAA,IAAI,CAAC,CAAC,OAAO,KAAK,WAAW,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE;AAC5D,gBAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,GAAG,CAAC;;AACrC,iBAAA,IAAI,CAAC,CAAC,OAAO,KAAK,UAAU,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE;AACvD,gBAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,GAAG,CAAC;;AAG5C,YAAA,IAAI,MAAM,KAAK,IAAI,CAAC,OAAO,EAAE;AAC3B,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;AAC3B,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;gBACxB,IAAI,CAAC,eAAe,EAAE;AACtB,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;;QAInB,eAAe,GAAA;YACrB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO;iBAChC,IAAI,CAAC,CAAC;iBACN,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;YAEnB,IAAI,CAAC,eAAe,EAAE;;QAGhB,eAAe,GAAA;YACrB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAG;gBAC3C,MAAM,MAAM,GAAG,eAAe;AAC9B,gBAAA,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC;gBACnB,OAAO;oBACL,CAAC,CAAA,EAAG,MAAM,CAAO,KAAA,CAAA,GAAG,KAAK,GAAG,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC;AAC3F,oBAAA,CAAC,CAAG,EAAA,MAAM,CAAO,KAAA,CAAA,GAAG,IAAI,CAAC,OAAO,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU;AAC7D,oBAAA,CAAC,CAAG,EAAA,MAAM,CAAS,OAAA,CAAA,GAAG,IAAI,CAAC,OAAO,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU;oBAC/D,CAAC,CAAA,EAAG,MAAM,CAAO,KAAA,CAAA,GAAG,KAAK,GAAG,IAAI,CAAC,UAAU;AAC3C,oBAAA,CAAC,GAAG,MAAM,CAAA,QAAA,CAAU,GAAG,IAAI,CAAC,OAAO,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC;iBAC1E;AACH,aAAC,CAAC;;AAGJ,QAAA,UAAU,CAAC,KAAoB,EAAA;AAC7B,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,IAAI,CAAC;YACzB,IAAI,CAAC,eAAe,EAAE;AACtB,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,QAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,YAAA,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU;AAChF,YAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;AACnC,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,QAAA,gBAAgB,CAAC,EAAuB,EAAA;AACtC,YAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAGpB,QAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB,QAAA,QAAQ,GAA4B,MAAM,IAAI;AAC9C,QAAA,SAAS,GAAe,MAAM,IAAI;2GArNvB,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;+FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAKN,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAChB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAChB,gBAAgB,CAChB,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,CAEhB,EAAA,WAAA,EAAA,aAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,eAAe,CApBxB,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,SAAA,EAAA;gBACT,gBAAgB;AAChB,gBAAA;AACE,oBAAA,OAAO,EAAE,iBAAiB;AAC1B,oBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,eAAe,CAAC;AAC9C,oBAAA,KAAK,EAAE;AACR;aACF,EArCS,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BT,EASS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,eAAe,yfAAE,mBAAmB,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,WAAA,EAAA,OAAA,EAAA,WAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,EAAA,WAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;;2FAEnC,eAAe,EAAA,UAAA,EAAA,CAAA;kBA9C3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,QAAQ,EAAE,QAAQ;AAClB,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BT,EAAA,CAAA;AACD,oBAAA,SAAS,EAAE;wBACT,gBAAgB;AAChB,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,qBAAqB,CAAC;AAC9C,4BAAA,KAAK,EAAE;AACR;AACF,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,mBAAmB,EAAE,eAAe;AAChE,iBAAA;+NAI2C,SAAS,EAAA,CAAA;sBAAlD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBAEc,YAAY,EAAA,CAAA;sBAAjE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACgB,WAAW,EAAA,CAAA;sBAAhE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,WAAW,EAAA,CAAA;sBAAnB;gBACsC,OAAO,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBAC5B,UAAU,EAAA,CAAA;sBAAlB;gBACkB,QAAQ,EAAA,CAAA;sBAA1B;gBACkB,SAAS,EAAA,CAAA;sBAA3B;gBACkB,eAAe,EAAA,CAAA;sBAAjC;gBACkB,WAAW,EAAA,CAAA;sBAA7B;;;ACrGH;;;AAGG;MAWU,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,EAHb,OAAA,EAAA,CAAA,eAAe,EAAE,mBAAmB,aACpC,eAAe,CAAA,EAAA,CAAA;wGAEd,YAAY,EAAA,OAAA,EAAA,CAHb,eAAe,EAAE,mBAAmB,CAAA,EAAA,CAAA;;2FAGnC,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,mBAAmB,CAAC;oBAC/C,OAAO,EAAE,CAAC,eAAe;AAC1B,iBAAA;;;ACbD;;;AAGG;;ACHH;;AAEG;;;;"}