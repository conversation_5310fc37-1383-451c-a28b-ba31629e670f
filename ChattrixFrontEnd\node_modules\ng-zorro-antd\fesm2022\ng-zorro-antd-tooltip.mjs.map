{"version": 3, "file": "ng-zorro-antd-tooltip.mjs", "sources": ["../../components/tooltip/base.ts", "../../components/tooltip/tooltip.ts", "../../components/tooltip/tooltip.module.ts", "../../components/tooltip/public-api.ts", "../../components/tooltip/ng-zorro-antd-tooltip.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { CdkConnectedOverlay, ConnectedOverlayPositionChange, ConnectionPositionPair } from '@angular/cdk/overlay';\nimport { _getEventTarget } from '@angular/cdk/platform';\nimport { isPlatformBrowser } from '@angular/common';\nimport {\n  AfterViewInit,\n  ChangeDetectorRef,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  PLATFORM_ID,\n  Renderer2,\n  SimpleChanges,\n  TemplateRef,\n  Type,\n  ViewChild,\n  ViewContainerRef,\n  inject\n} from '@angular/core';\nimport { Subject, asapScheduler } from 'rxjs';\nimport { delay, distinctUntilChanged, filter, takeUntil } from 'rxjs/operators';\n\nimport { NzConfigService, PopConfirmConfig, PopoverConfig } from 'ng-zorro-antd/core/config';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport { DEFAULT_TOOLTIP_POSITIONS, POSITION_MAP, POSITION_TYPE, getPlacementName } from 'ng-zorro-antd/core/overlay';\nimport { NgClassInterface, NgStyleInterface, NzSafeAny, NzTSType } from 'ng-zorro-antd/core/types';\nimport { isNotNil, toBoolean } from 'ng-zorro-antd/core/util';\n\nexport interface PropertyMapping {\n  [key: string]: [string, () => unknown];\n}\n\nexport type NzTooltipTrigger = 'click' | 'focus' | 'hover' | null;\n\n@Directive()\nexport abstract class NzTooltipBaseDirective implements AfterViewInit, OnChanges, OnDestroy {\n  config?: Required<PopoverConfig | PopConfirmConfig>;\n  abstract arrowPointAtCenter?: boolean;\n  abstract directiveTitle?: NzTSType | null;\n  abstract directiveContent?: NzTSType | null;\n  abstract title?: NzTSType | null;\n  abstract content?: NzTSType | null;\n  abstract trigger?: NzTooltipTrigger;\n  abstract placement?: string | string[];\n  abstract origin?: ElementRef<HTMLElement>;\n  abstract visible?: boolean;\n  abstract mouseEnterDelay?: number;\n  abstract mouseLeaveDelay?: number;\n  abstract overlayClassName?: string;\n  abstract overlayStyle?: NgStyleInterface;\n  abstract overlayClickable?: boolean;\n  cdkConnectedOverlayPush?: boolean;\n  visibleChange = new EventEmitter<boolean>();\n\n  /**\n   * This true title that would be used in other parts on this component.\n   */\n  protected get _title(): NzTSType | null {\n    return this.title || this.directiveTitle || null;\n  }\n\n  protected get _content(): NzTSType | null {\n    return this.content || this.directiveContent || null;\n  }\n\n  protected get _trigger(): NzTooltipTrigger {\n    return typeof this.trigger !== 'undefined' ? this.trigger : 'hover';\n  }\n\n  protected get _placement(): string[] {\n    const p = this.placement;\n    return Array.isArray(p) && p.length > 0 ? p : typeof p === 'string' && p ? [p] : ['top'];\n  }\n\n  protected get _visible(): boolean {\n    return (typeof this.visible !== 'undefined' ? this.visible : this.internalVisible) || false;\n  }\n\n  protected get _mouseEnterDelay(): number {\n    return this.mouseEnterDelay || 0.15;\n  }\n\n  protected get _mouseLeaveDelay(): number {\n    return this.mouseLeaveDelay || 0.1;\n  }\n\n  protected get _overlayClassName(): string | null {\n    return this.overlayClassName || null;\n  }\n\n  protected get _overlayStyle(): NgStyleInterface | null {\n    return this.overlayStyle || null;\n  }\n\n  protected get _overlayClickable(): boolean {\n    return this.overlayClickable ?? true;\n  }\n\n  private internalVisible = false;\n\n  protected getProxyPropertyMap(): PropertyMapping {\n    return {\n      noAnimation: ['noAnimation', () => !!this.noAnimation]\n    };\n  }\n\n  component?: NzTooltipBaseComponent;\n\n  protected readonly destroy$ = new Subject<void>();\n  protected readonly triggerDisposables: Array<() => void> = [];\n\n  private delayTimer?: ReturnType<typeof setTimeout>;\n\n  elementRef = inject(ElementRef);\n  protected hostView = inject(ViewContainerRef);\n  protected renderer = inject(Renderer2);\n  protected noAnimation = inject(NzNoAnimationDirective, { host: true, optional: true });\n  protected nzConfigService = inject(NzConfigService);\n  protected platformId = inject(PLATFORM_ID);\n\n  constructor(protected componentType: Type<NzTooltipBaseComponent>) {}\n\n  ngAfterViewInit(): void {\n    if (isPlatformBrowser(this.platformId)) {\n      this.createComponent();\n      this.registerTriggers();\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { trigger } = changes;\n\n    if (trigger && !trigger.isFirstChange()) {\n      this.registerTriggers();\n    }\n\n    if (this.component) {\n      this.updatePropertiesByChanges(changes);\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n\n    // Clear toggling timer. Issue #3875 #4317 #4386\n    this.clearTogglingTimer();\n    this.removeTriggerListeners();\n  }\n\n  show(): void {\n    this.component?.show();\n  }\n\n  hide(): void {\n    this.component?.hide();\n  }\n\n  /**\n   * Force the component to update its position.\n   */\n  updatePosition(): void {\n    if (this.component) {\n      this.component.updatePosition();\n    }\n  }\n\n  /**\n   * Create a dynamic tooltip component. This method can be override.\n   */\n  protected createComponent(): void {\n    const componentRef = this.hostView.createComponent(this.componentType);\n\n    this.component = componentRef.instance as NzTooltipBaseComponent;\n\n    // Remove the component's DOM because it should be in the overlay container.\n    this.renderer.removeChild(\n      this.renderer.parentNode(this.elementRef.nativeElement),\n      componentRef.location.nativeElement\n    );\n    this.component.setOverlayOrigin(this.origin || this.elementRef);\n\n    this.initProperties();\n\n    const ngVisibleChange$ = this.component.nzVisibleChange.pipe(distinctUntilChanged());\n\n    ngVisibleChange$.pipe(takeUntil(this.destroy$)).subscribe((visible: boolean) => {\n      this.internalVisible = visible;\n      this.visibleChange.emit(visible);\n    });\n\n    // In some cases, the rendering takes into account the height at which the `arrow` is in wrong place,\n    // so `cdk` sets the container position incorrectly.\n    // To avoid this, after placing the `arrow` in the correct position, we should `re-calculate` the position of the `overlay`.\n    ngVisibleChange$\n      .pipe(\n        filter((visible: boolean) => visible),\n        delay(0, asapScheduler),\n        filter(() => Boolean(this.component?.overlay?.overlayRef)),\n        takeUntil(this.destroy$)\n      )\n      .subscribe(() => {\n        this.component?.updatePosition();\n      });\n  }\n\n  protected registerTriggers(): void {\n    // When the method gets invoked, all properties has been synced to the dynamic component.\n    // After removing the old API, we can just check the directive's own `nzTrigger`.\n    const el = this.elementRef.nativeElement;\n    const trigger = this.trigger;\n\n    this.removeTriggerListeners();\n\n    if (trigger === 'hover') {\n      let overlayElement: HTMLElement;\n      this.triggerDisposables.push(\n        this.renderer.listen(el, 'mouseenter', () => {\n          this.delayEnterLeave(true, true, this._mouseEnterDelay);\n        })\n      );\n      this.triggerDisposables.push(\n        this.renderer.listen(el, 'mouseleave', () => {\n          this.delayEnterLeave(true, false, this._mouseLeaveDelay);\n          if (this.component?.overlay.overlayRef && !overlayElement) {\n            overlayElement = this.component.overlay.overlayRef.overlayElement;\n            this.triggerDisposables.push(\n              this.renderer.listen(overlayElement, 'mouseenter', () => {\n                this.delayEnterLeave(false, true, this._mouseEnterDelay);\n              })\n            );\n            this.triggerDisposables.push(\n              this.renderer.listen(overlayElement, 'mouseleave', () => {\n                this.delayEnterLeave(false, false, this._mouseLeaveDelay);\n              })\n            );\n          }\n        })\n      );\n    } else if (trigger === 'focus') {\n      this.triggerDisposables.push(this.renderer.listen(el, 'focusin', () => this.show()));\n      this.triggerDisposables.push(this.renderer.listen(el, 'focusout', () => this.hide()));\n    } else if (trigger === 'click') {\n      this.triggerDisposables.push(\n        this.renderer.listen(el, 'click', (e: MouseEvent) => {\n          e.preventDefault();\n          this.show();\n        })\n      );\n    }\n    // Else do nothing because user wants to control the visibility programmatically.\n  }\n\n  private updatePropertiesByChanges(changes: SimpleChanges): void {\n    this.updatePropertiesByKeys(Object.keys(changes));\n  }\n\n  private updatePropertiesByKeys(keys?: string[]): void {\n    const mappingProperties: PropertyMapping = {\n      // common mappings\n      title: ['nzTitle', () => this._title],\n      directiveTitle: ['nzTitle', () => this._title],\n      content: ['nzContent', () => this._content],\n      directiveContent: ['nzContent', () => this._content],\n      trigger: ['nzTrigger', () => this._trigger],\n      placement: ['nzPlacement', () => this._placement],\n      visible: ['nzVisible', () => this._visible],\n      mouseEnterDelay: ['nzMouseEnterDelay', () => this._mouseEnterDelay],\n      mouseLeaveDelay: ['nzMouseLeaveDelay', () => this._mouseLeaveDelay],\n      overlayClassName: ['nzOverlayClassName', () => this._overlayClassName],\n      overlayStyle: ['nzOverlayStyle', () => this._overlayStyle],\n      overlayClickable: ['nzOverlayClickable', () => this._overlayClickable],\n      arrowPointAtCenter: ['nzArrowPointAtCenter', () => this.arrowPointAtCenter],\n      cdkConnectedOverlayPush: ['cdkConnectedOverlayPush', () => this.cdkConnectedOverlayPush],\n      ...this.getProxyPropertyMap()\n    };\n\n    (keys || Object.keys(mappingProperties).filter(key => !key.startsWith('directive'))).forEach(\n      (property: NzSafeAny) => {\n        if (mappingProperties[property]) {\n          const [name, valueFn] = mappingProperties[property];\n          this.updateComponentValue(name, valueFn());\n        }\n      }\n    );\n\n    this.component?.updateByDirective();\n  }\n\n  private initProperties(): void {\n    this.updatePropertiesByKeys();\n  }\n\n  private updateComponentValue(key: string, value: NzSafeAny): void {\n    if (typeof value !== 'undefined') {\n      // @ts-ignore\n      this.component[key] = value;\n    }\n  }\n\n  private delayEnterLeave(isOrigin: boolean, isEnter: boolean, delay: number = -1): void {\n    if (this.delayTimer) {\n      this.clearTogglingTimer();\n    } else if (delay > 0) {\n      this.delayTimer = setTimeout(() => {\n        this.delayTimer = undefined;\n        isEnter ? this.show() : this.hide();\n      }, delay * 1000);\n    } else {\n      // `isOrigin` is used due to the tooltip will not hide immediately\n      // (may caused by the fade-out animation).\n      isEnter && isOrigin ? this.show() : this.hide();\n    }\n  }\n\n  private removeTriggerListeners(): void {\n    this.triggerDisposables.forEach(dispose => dispose());\n    this.triggerDisposables.length = 0;\n  }\n\n  private clearTogglingTimer(): void {\n    if (this.delayTimer) {\n      clearTimeout(this.delayTimer);\n      this.delayTimer = undefined;\n    }\n  }\n}\n\n@Directive()\nexport abstract class NzTooltipBaseComponent implements OnDestroy, OnInit {\n  @ViewChild('overlay', { static: false }) overlay!: CdkConnectedOverlay;\n\n  noAnimation = inject(NzNoAnimationDirective, { host: true, optional: true });\n  cdr = inject(ChangeDetectorRef);\n  private directionality = inject(Directionality);\n\n  nzTitle: NzTSType | null = null;\n  nzContent: NzTSType | null = null;\n  nzArrowPointAtCenter: boolean = false;\n  nzOverlayClassName!: string;\n  nzOverlayStyle: NgStyleInterface = {};\n  nzOverlayClickable: boolean = true;\n  nzBackdrop = false;\n  nzMouseEnterDelay?: number;\n  nzMouseLeaveDelay?: number;\n  cdkConnectedOverlayPush?: boolean = true;\n\n  nzVisibleChange = new Subject<boolean>();\n\n  set nzVisible(value: boolean) {\n    const visible = toBoolean(value);\n    if (this._visible !== visible) {\n      this._visible = visible;\n      this.nzVisibleChange.next(visible);\n    }\n  }\n\n  get nzVisible(): boolean {\n    return this._visible;\n  }\n\n  _visible = false;\n\n  set nzTrigger(value: NzTooltipTrigger) {\n    this._trigger = value;\n  }\n\n  get nzTrigger(): NzTooltipTrigger {\n    return this._trigger;\n  }\n\n  protected _trigger: NzTooltipTrigger = 'hover';\n\n  set nzPlacement(value: POSITION_TYPE[]) {\n    const preferredPosition = value.map(placement => POSITION_MAP[placement]);\n    this._positions = [...preferredPosition, ...DEFAULT_TOOLTIP_POSITIONS];\n  }\n\n  preferredPlacement: string = 'top';\n\n  origin!: ElementRef<NzSafeAny>;\n\n  public dir: Direction = 'ltr';\n\n  _classMap: NgClassInterface = {};\n\n  _prefix = 'ant-tooltip';\n\n  _positions: ConnectionPositionPair[] = [...DEFAULT_TOOLTIP_POSITIONS];\n\n  protected destroy$ = new Subject<void>();\n\n  ngOnInit(): void {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n\n    this.dir = this.directionality.value;\n  }\n\n  ngOnDestroy(): void {\n    this.nzVisibleChange.complete();\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  show(): void {\n    if (this.nzVisible) {\n      return;\n    }\n\n    if (!this.isEmpty()) {\n      this.nzVisible = true;\n      this.nzVisibleChange.next(true);\n      this.cdr.detectChanges();\n    }\n\n    // for ltr for overlay to display tooltip in correct placement in rtl direction.\n    if (this.origin && this.overlay && this.overlay.overlayRef && this.overlay.overlayRef.getDirection() === 'rtl') {\n      this.overlay.overlayRef.setDirection('ltr');\n    }\n  }\n\n  hide(): void {\n    if (!this.nzVisible) {\n      return;\n    }\n\n    this.nzVisible = false;\n    this.nzVisibleChange.next(false);\n    this.cdr.detectChanges();\n  }\n\n  updateByDirective(): void {\n    this.updateStyles();\n    this.cdr.detectChanges();\n\n    Promise.resolve().then(() => {\n      this.updatePosition();\n      this.updateVisibilityByTitle();\n    });\n  }\n\n  /**\n   * Force the component to update its position.\n   */\n  updatePosition(): void {\n    if (this.origin && this.overlay && this.overlay.overlayRef) {\n      this.overlay.overlayRef.updatePosition();\n    }\n  }\n\n  onPositionChange(position: ConnectedOverlayPositionChange): void {\n    this.preferredPlacement = getPlacementName(position)!;\n    this.updateStyles();\n\n    // We have to trigger immediate change detection or the element would blink.\n    this.cdr.detectChanges();\n  }\n\n  setOverlayOrigin(origin: ElementRef<HTMLElement>): void {\n    this.origin = origin;\n    this.cdr.markForCheck();\n  }\n\n  onClickOutside(event: MouseEvent): void {\n    if (!this.nzOverlayClickable) {\n      return;\n    }\n    const target = _getEventTarget(event);\n    if (!this.origin.nativeElement.contains(target) && this.nzTrigger !== null) {\n      this.hide();\n    }\n  }\n\n  /**\n   * Hide the component while the content is empty.\n   */\n  private updateVisibilityByTitle(): void {\n    if (this.isEmpty()) {\n      this.hide();\n    }\n  }\n\n  protected updateStyles(): void {\n    this._classMap = {\n      ...this.transformClassListToMap(this.nzOverlayClassName),\n      [`${this._prefix}-placement-${this.preferredPlacement}`]: true\n    };\n  }\n\n  protected transformClassListToMap(klass: string): Record<string, boolean> {\n    const result: Record<string, boolean> = {};\n    /**\n     * @see https://github.com/angular/angular/blob/f6e97763cfab9fa2bea6e6b1303b64f1b499c3ef/packages/common/src/directives/ng_class.ts#L92\n     */\n    const classes = klass !== null ? klass.split(/\\s+/) : [];\n    classes.forEach(className => (result[className] = true));\n    return result;\n  }\n\n  /**\n   * Empty component cannot be opened.\n   */\n  protected abstract isEmpty(): boolean;\n}\n\nexport function isTooltipEmpty(value: string | TemplateRef<void> | null): boolean {\n  return value instanceof TemplateRef ? false : value === '' || !isNotNil(value);\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { OverlayModule } from '@angular/cdk/overlay';\nimport {\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  Component,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Input,\n  Output,\n  ViewEncapsulation\n} from '@angular/core';\n\nimport { zoomBigMotion } from 'ng-zorro-antd/core/animation';\nimport { isPresetColor, NzPresetColor } from 'ng-zorro-antd/core/color';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzOverlayModule } from 'ng-zorro-antd/core/overlay';\nimport { NgStyleInterface, NzTSType } from 'ng-zorro-antd/core/types';\n\nimport {\n  isTooltipEmpty,\n  NzTooltipBaseComponent,\n  NzTooltipBaseDirective,\n  NzTooltipTrigger,\n  PropertyMapping\n} from './base';\n\n@Directive({\n  selector: '[nz-tooltip]',\n  exportAs: 'nzTooltip',\n  host: {\n    '[class.ant-tooltip-open]': 'visible'\n  }\n})\nexport class NzTooltipDirective extends NzTooltipBaseDirective {\n  /* eslint-disable @angular-eslint/no-input-rename, @angular-eslint/no-output-rename */\n  @Input('nzTooltipTitle') override title?: NzTSType | null;\n  @Input('nzTooltipTitleContext') titleContext?: object | null = null;\n  @Input('nz-tooltip') override directiveTitle?: NzTSType | null;\n  @Input('nzTooltipTrigger') override trigger?: NzTooltipTrigger = 'hover';\n  @Input('nzTooltipPlacement') override placement?: string | string[] = 'top';\n  @Input('nzTooltipOrigin') override origin?: ElementRef<HTMLElement>;\n  @Input('nzTooltipVisible') override visible?: boolean;\n  @Input('nzTooltipMouseEnterDelay') override mouseEnterDelay?: number;\n  @Input('nzTooltipMouseLeaveDelay') override mouseLeaveDelay?: number;\n  @Input('nzTooltipOverlayClassName') override overlayClassName?: string;\n  @Input('nzTooltipOverlayStyle') override overlayStyle?: NgStyleInterface;\n  @Input({ alias: 'nzTooltipArrowPointAtCenter', transform: booleanAttribute }) override arrowPointAtCenter?: boolean;\n  @Input({ transform: booleanAttribute }) override cdkConnectedOverlayPush?: boolean = true;\n  @Input() nzTooltipColor?: string;\n\n  override directiveContent?: NzTSType | null = null;\n  override content?: NzTSType | null = null;\n  override overlayClickable?: boolean;\n\n  @Output('nzTooltipVisibleChange') override readonly visibleChange = new EventEmitter<boolean>();\n\n  constructor() {\n    super(NzToolTipComponent);\n  }\n\n  protected override getProxyPropertyMap(): PropertyMapping {\n    return {\n      ...super.getProxyPropertyMap(),\n      nzTooltipColor: ['nzColor', () => this.nzTooltipColor],\n      titleContext: ['nzTitleContext', () => this.titleContext]\n    };\n  }\n}\n\n@Component({\n  selector: 'nz-tooltip',\n  exportAs: 'nzTooltipComponent',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  animations: [zoomBigMotion],\n  template: `\n    <ng-template\n      #overlay=\"cdkConnectedOverlay\"\n      cdkConnectedOverlay\n      nzConnectedOverlay\n      [cdkConnectedOverlayOrigin]=\"origin\"\n      [cdkConnectedOverlayOpen]=\"_visible\"\n      [cdkConnectedOverlayPositions]=\"_positions\"\n      [cdkConnectedOverlayPush]=\"cdkConnectedOverlayPush\"\n      [nzArrowPointAtCenter]=\"nzArrowPointAtCenter\"\n      (overlayOutsideClick)=\"onClickOutside($event)\"\n      (detach)=\"hide()\"\n      (positionChange)=\"onPositionChange($event)\"\n    >\n      <div\n        class=\"ant-tooltip\"\n        [class.ant-tooltip-rtl]=\"dir === 'rtl'\"\n        [class]=\"_classMap\"\n        [style]=\"nzOverlayStyle\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        [@zoomBigMotion]=\"'active'\"\n      >\n        <div class=\"ant-tooltip-content\">\n          <div class=\"ant-tooltip-arrow\">\n            <span class=\"ant-tooltip-arrow-content\" [style]=\"_contentStyleMap\"></span>\n          </div>\n          <div class=\"ant-tooltip-inner\" [style]=\"_contentStyleMap\">\n            <ng-container *nzStringTemplateOutlet=\"nzTitle; context: nzTitleContext\">{{ nzTitle }}</ng-container>\n          </div>\n        </div>\n      </div>\n    </ng-template>\n  `,\n  preserveWhitespaces: false,\n  imports: [OverlayModule, NzNoAnimationDirective, NzOutletModule, NzOverlayModule]\n})\nexport class NzToolTipComponent extends NzTooltipBaseComponent {\n  override nzTitle: NzTSType | null = null;\n  nzTitleContext: object | null = null;\n\n  nzColor?: string | NzPresetColor;\n\n  _contentStyleMap: NgStyleInterface = {};\n\n  protected isEmpty(): boolean {\n    return isTooltipEmpty(this.nzTitle);\n  }\n\n  protected override updateStyles(): void {\n    const isColorPreset = this.nzColor && isPresetColor(this.nzColor);\n\n    this._classMap = {\n      ...this.transformClassListToMap(this.nzOverlayClassName),\n      [`${this._prefix}-placement-${this.preferredPlacement}`]: true,\n      [`${this._prefix}-${this.nzColor}`]: isColorPreset\n    };\n\n    this._contentStyleMap = {\n      backgroundColor: !!this.nzColor && !isColorPreset ? this.nzColor : null,\n      '--antd-arrow-background-color': this.nzColor\n    };\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\n// NOTE: the `t` is not uppercase in directives. Change this would however introduce breaking change.\nimport { NzToolTipComponent, NzTooltipDirective } from './tooltip';\n\n@NgModule({\n  imports: [NzToolTipComponent, NzTooltipDirective],\n  exports: [NzToolTipComponent, NzTooltipDirective]\n})\nexport class NzToolTipModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './tooltip';\nexport * from './tooltip.module';\nexport * from './base';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;MAwCmB,sBAAsB,CAAA;AAqFpB,IAAA,aAAA;AApFtB,IAAA,MAAM;AAeN,IAAA,uBAAuB;AACvB,IAAA,aAAa,GAAG,IAAI,YAAY,EAAW;AAE3C;;AAEG;AACH,IAAA,IAAc,MAAM,GAAA;QAClB,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI;;AAGlD,IAAA,IAAc,QAAQ,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI;;AAGtD,IAAA,IAAc,QAAQ,GAAA;AACpB,QAAA,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK,WAAW,GAAG,IAAI,CAAC,OAAO,GAAG,OAAO;;AAGrE,IAAA,IAAc,UAAU,GAAA;AACtB,QAAA,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS;AACxB,QAAA,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;;AAG1F,IAAA,IAAc,QAAQ,GAAA;QACpB,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,KAAK,WAAW,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,KAAK,KAAK;;AAG7F,IAAA,IAAc,gBAAgB,GAAA;AAC5B,QAAA,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI;;AAGrC,IAAA,IAAc,gBAAgB,GAAA;AAC5B,QAAA,OAAO,IAAI,CAAC,eAAe,IAAI,GAAG;;AAGpC,IAAA,IAAc,iBAAiB,GAAA;AAC7B,QAAA,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI;;AAGtC,IAAA,IAAc,aAAa,GAAA;AACzB,QAAA,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI;;AAGlC,IAAA,IAAc,iBAAiB,GAAA;AAC7B,QAAA,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI;;IAG9B,eAAe,GAAG,KAAK;IAErB,mBAAmB,GAAA;QAC3B,OAAO;AACL,YAAA,WAAW,EAAE,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW;SACtD;;AAGH,IAAA,SAAS;AAEU,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;IAC9B,kBAAkB,GAAsB,EAAE;AAErD,IAAA,UAAU;AAElB,IAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AACrB,IAAA,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC;AACnC,IAAA,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;AAC5B,IAAA,WAAW,GAAG,MAAM,CAAC,sBAAsB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAC5E,IAAA,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;AACzC,IAAA,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;AAE1C,IAAA,WAAA,CAAsB,aAA2C,EAAA;QAA3C,IAAa,CAAA,aAAA,GAAb,aAAa;;IAEnC,eAAe,GAAA;AACb,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACtC,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,gBAAgB,EAAE;;;AAI3B,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO;QAE3B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE;YACvC,IAAI,CAAC,gBAAgB,EAAE;;AAGzB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC;;;IAI3C,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;QAGxB,IAAI,CAAC,kBAAkB,EAAE;QACzB,IAAI,CAAC,sBAAsB,EAAE;;IAG/B,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE;;IAGxB,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE;;AAGxB;;AAEG;IACH,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE;;;AAInC;;AAEG;IACO,eAAe,GAAA;AACvB,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC;AAEtE,QAAA,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,QAAkC;;QAGhE,IAAI,CAAC,QAAQ,CAAC,WAAW,CACvB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,EACvD,YAAY,CAAC,QAAQ,CAAC,aAAa,CACpC;AACD,QAAA,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC;QAE/D,IAAI,CAAC,cAAc,EAAE;AAErB,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAEpF,QAAA,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAgB,KAAI;AAC7E,YAAA,IAAI,CAAC,eAAe,GAAG,OAAO;AAC9B,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;AAClC,SAAC,CAAC;;;;QAKF;AACG,aAAA,IAAI,CACH,MAAM,CAAC,CAAC,OAAgB,KAAK,OAAO,CAAC,EACrC,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC,EACvB,MAAM,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,EAC1D,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAEzB,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,SAAS,EAAE,cAAc,EAAE;AAClC,SAAC,CAAC;;IAGI,gBAAgB,GAAA;;;AAGxB,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;AACxC,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO;QAE5B,IAAI,CAAC,sBAAsB,EAAE;AAE7B,QAAA,IAAI,OAAO,KAAK,OAAO,EAAE;AACvB,YAAA,IAAI,cAA2B;AAC/B,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,EAAE,MAAK;gBAC1C,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC;aACxD,CAAC,CACH;AACD,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,EAAE,MAAK;gBAC1C,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC;gBACxD,IAAI,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,UAAU,IAAI,CAAC,cAAc,EAAE;oBACzD,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc;AACjE,oBAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,YAAY,EAAE,MAAK;wBACtD,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC;qBACzD,CAAC,CACH;AACD,oBAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,YAAY,EAAE,MAAK;wBACtD,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC;qBAC1D,CAAC,CACH;;aAEJ,CAAC,CACH;;AACI,aAAA,IAAI,OAAO,KAAK,OAAO,EAAE;YAC9B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,EAAE,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACpF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;;AAChF,aAAA,IAAI,OAAO,KAAK,OAAO,EAAE;AAC9B,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,CAAa,KAAI;gBAClD,CAAC,CAAC,cAAc,EAAE;gBAClB,IAAI,CAAC,IAAI,EAAE;aACZ,CAAC,CACH;;;;AAKG,IAAA,yBAAyB,CAAC,OAAsB,EAAA;QACtD,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;AAG3C,IAAA,sBAAsB,CAAC,IAAe,EAAA;AAC5C,QAAA,MAAM,iBAAiB,GAAoB;;YAEzC,KAAK,EAAE,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC;YACrC,cAAc,EAAE,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC;YAC9C,OAAO,EAAE,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC;YAC3C,gBAAgB,EAAE,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC;YACpD,OAAO,EAAE,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC;YAC3C,SAAS,EAAE,CAAC,aAAa,EAAE,MAAM,IAAI,CAAC,UAAU,CAAC;YACjD,OAAO,EAAE,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC;YAC3C,eAAe,EAAE,CAAC,mBAAmB,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC;YACnE,eAAe,EAAE,CAAC,mBAAmB,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC;YACnE,gBAAgB,EAAE,CAAC,oBAAoB,EAAE,MAAM,IAAI,CAAC,iBAAiB,CAAC;YACtE,YAAY,EAAE,CAAC,gBAAgB,EAAE,MAAM,IAAI,CAAC,aAAa,CAAC;YAC1D,gBAAgB,EAAE,CAAC,oBAAoB,EAAE,MAAM,IAAI,CAAC,iBAAiB,CAAC;YACtE,kBAAkB,EAAE,CAAC,sBAAsB,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC;YAC3E,uBAAuB,EAAE,CAAC,yBAAyB,EAAE,MAAM,IAAI,CAAC,uBAAuB,CAAC;YACxF,GAAG,IAAI,CAAC,mBAAmB;SAC5B;AAED,QAAA,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE,OAAO,CAC1F,CAAC,QAAmB,KAAI;AACtB,YAAA,IAAI,iBAAiB,CAAC,QAAQ,CAAC,EAAE;gBAC/B,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC;gBACnD,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC;;AAE9C,SAAC,CACF;AAED,QAAA,IAAI,CAAC,SAAS,EAAE,iBAAiB,EAAE;;IAG7B,cAAc,GAAA;QACpB,IAAI,CAAC,sBAAsB,EAAE;;IAGvB,oBAAoB,CAAC,GAAW,EAAE,KAAgB,EAAA;AACxD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;;AAEhC,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK;;;AAIvB,IAAA,eAAe,CAAC,QAAiB,EAAE,OAAgB,EAAE,KAAA,GAAgB,CAAC,CAAC,EAAA;AAC7E,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,kBAAkB,EAAE;;AACpB,aAAA,IAAI,KAAK,GAAG,CAAC,EAAE;AACpB,YAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,MAAK;AAChC,gBAAA,IAAI,CAAC,UAAU,GAAG,SAAS;AAC3B,gBAAA,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE;AACrC,aAAC,EAAE,KAAK,GAAG,IAAI,CAAC;;aACX;;;AAGL,YAAA,OAAO,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE;;;IAI3C,sBAAsB,GAAA;AAC5B,QAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AACrD,QAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC;;IAG5B,kBAAkB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;AAC7B,YAAA,IAAI,CAAC,UAAU,GAAG,SAAS;;;uGAhSX,sBAAsB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAtB,sBAAsB,EAAA,YAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAtB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAD3C;;MAuSqB,sBAAsB,CAAA;AACD,IAAA,OAAO;AAEhD,IAAA,WAAW,GAAG,MAAM,CAAC,sBAAsB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAC5E,IAAA,GAAG,GAAG,MAAM,CAAC,iBAAiB,CAAC;AACvB,IAAA,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;IAE/C,OAAO,GAAoB,IAAI;IAC/B,SAAS,GAAoB,IAAI;IACjC,oBAAoB,GAAY,KAAK;AACrC,IAAA,kBAAkB;IAClB,cAAc,GAAqB,EAAE;IACrC,kBAAkB,GAAY,IAAI;IAClC,UAAU,GAAG,KAAK;AAClB,IAAA,iBAAiB;AACjB,IAAA,iBAAiB;IACjB,uBAAuB,GAAa,IAAI;AAExC,IAAA,eAAe,GAAG,IAAI,OAAO,EAAW;IAExC,IAAI,SAAS,CAAC,KAAc,EAAA;AAC1B,QAAA,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC;AAChC,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;AAC7B,YAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;AACvB,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC;;;AAItC,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,QAAQ;;IAGtB,QAAQ,GAAG,KAAK;IAEhB,IAAI,SAAS,CAAC,KAAuB,EAAA;AACnC,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;;AAGvB,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,QAAQ;;IAGZ,QAAQ,GAAqB,OAAO;IAE9C,IAAI,WAAW,CAAC,KAAsB,EAAA;AACpC,QAAA,MAAM,iBAAiB,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,IAAI,YAAY,CAAC,SAAS,CAAC,CAAC;QACzE,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,iBAAiB,EAAE,GAAG,yBAAyB,CAAC;;IAGxE,kBAAkB,GAAW,KAAK;AAElC,IAAA,MAAM;IAEC,GAAG,GAAc,KAAK;IAE7B,SAAS,GAAqB,EAAE;IAEhC,OAAO,GAAG,aAAa;AAEvB,IAAA,UAAU,GAA6B,CAAC,GAAG,yBAAyB,CAAC;AAE3D,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;IAExC,QAAQ,GAAA;QACN,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,YAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,SAAC,CAAC;QAEF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;;IAGtC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;AAC/B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;IAG1B,IAAI,GAAA;AACF,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB;;AAGF,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;AAC/B,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;;;QAI1B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,EAAE,KAAK,KAAK,EAAE;YAC9G,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC;;;IAI/C,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB;;AAGF,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC;AAChC,QAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;;IAG1B,iBAAiB,GAAA;QACf,IAAI,CAAC,YAAY,EAAE;AACnB,QAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAExB,QAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;YAC1B,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,uBAAuB,EAAE;AAChC,SAAC,CAAC;;AAGJ;;AAEG;IACH,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;AAC1D,YAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,EAAE;;;AAI5C,IAAA,gBAAgB,CAAC,QAAwC,EAAA;AACvD,QAAA,IAAI,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,QAAQ,CAAE;QACrD,IAAI,CAAC,YAAY,EAAE;;AAGnB,QAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;;AAG1B,IAAA,gBAAgB,CAAC,MAA+B,EAAA;AAC9C,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;AACpB,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,IAAA,cAAc,CAAC,KAAiB,EAAA;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B;;AAEF,QAAA,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;YAC1E,IAAI,CAAC,IAAI,EAAE;;;AAIf;;AAEG;IACK,uBAAuB,GAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YAClB,IAAI,CAAC,IAAI,EAAE;;;IAIL,YAAY,GAAA;QACpB,IAAI,CAAC,SAAS,GAAG;AACf,YAAA,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACxD,CAAC,CAAA,EAAG,IAAI,CAAC,OAAO,CAAA,WAAA,EAAc,IAAI,CAAC,kBAAkB,CAAA,CAAE,GAAG;SAC3D;;AAGO,IAAA,uBAAuB,CAAC,KAAa,EAAA;QAC7C,MAAM,MAAM,GAA4B,EAAE;AAC1C;;AAEG;AACH,QAAA,MAAM,OAAO,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE;AACxD,QAAA,OAAO,CAAC,OAAO,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;AACxD,QAAA,OAAO,MAAM;;uGA1KK,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAtB,sBAAsB,EAAA,YAAA,EAAA,IAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAtB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAD3C;8BAE0C,OAAO,EAAA,CAAA;sBAA/C,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;;AAkLnC,SAAU,cAAc,CAAC,KAAwC,EAAA;AACrE,IAAA,OAAO,KAAK,YAAY,WAAW,GAAG,KAAK,GAAG,KAAK,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;AAChF;;ACtgBA;;;AAGG;AAqCG,MAAO,kBAAmB,SAAQ,sBAAsB,CAAA;;AAE1B,IAAA,KAAK;IACP,YAAY,GAAmB,IAAI;AACrC,IAAA,cAAc;IACR,OAAO,GAAsB,OAAO;IAClC,SAAS,GAAuB,KAAK;AACxC,IAAA,MAAM;AACL,IAAA,OAAO;AACC,IAAA,eAAe;AACf,IAAA,eAAe;AACd,IAAA,gBAAgB;AACpB,IAAA,YAAY;AACkC,IAAA,kBAAkB;IACxD,uBAAuB,GAAa,IAAI;AAChF,IAAA,cAAc;IAEd,gBAAgB,GAAqB,IAAI;IACzC,OAAO,GAAqB,IAAI;AAChC,IAAA,gBAAgB;AAE2B,IAAA,aAAa,GAAG,IAAI,YAAY,EAAW;AAE/F,IAAA,WAAA,GAAA;QACE,KAAK,CAAC,kBAAkB,CAAC;;IAGR,mBAAmB,GAAA;QACpC,OAAO;YACL,GAAG,KAAK,CAAC,mBAAmB,EAAE;YAC9B,cAAc,EAAE,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC;YACtD,YAAY,EAAE,CAAC,gBAAgB,EAAE,MAAM,IAAI,CAAC,YAAY;SACzD;;uGAhCQ,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,EAAA,YAAA,EAAA,CAAA,uBAAA,EAAA,cAAA,CAAA,EAAA,cAAA,EAAA,CAAA,YAAA,EAAA,gBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,kBAAA,EAAA,SAAA,CAAA,EAAA,SAAA,EAAA,CAAA,oBAAA,EAAA,WAAA,CAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,kBAAA,EAAA,SAAA,CAAA,EAAA,eAAA,EAAA,CAAA,0BAAA,EAAA,iBAAA,CAAA,EAAA,eAAA,EAAA,CAAA,0BAAA,EAAA,iBAAA,CAAA,EAAA,gBAAA,EAAA,CAAA,2BAAA,EAAA,kBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,uBAAA,EAAA,cAAA,CAAA,EAAA,kBAAA,EAAA,CAAA,6BAAA,EAAA,oBAAA,EAa6B,gBAAgB,CAAA,EAAA,uBAAA,EAAA,CAAA,yBAAA,EAAA,yBAAA,EACtD,gBAAgB,CAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,aAAA,EAAA,wBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,wBAAA,EAAA,SAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAdzB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAP9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,IAAI,EAAE;AACJ,wBAAA,0BAA0B,EAAE;AAC7B;AACF,iBAAA;wDAGmC,KAAK,EAAA,CAAA;sBAAtC,KAAK;uBAAC,gBAAgB;gBACS,YAAY,EAAA,CAAA;sBAA3C,KAAK;uBAAC,uBAAuB;gBACA,cAAc,EAAA,CAAA;sBAA3C,KAAK;uBAAC,YAAY;gBACiB,OAAO,EAAA,CAAA;sBAA1C,KAAK;uBAAC,kBAAkB;gBACa,SAAS,EAAA,CAAA;sBAA9C,KAAK;uBAAC,oBAAoB;gBACQ,MAAM,EAAA,CAAA;sBAAxC,KAAK;uBAAC,iBAAiB;gBACY,OAAO,EAAA,CAAA;sBAA1C,KAAK;uBAAC,kBAAkB;gBACmB,eAAe,EAAA,CAAA;sBAA1D,KAAK;uBAAC,0BAA0B;gBACW,eAAe,EAAA,CAAA;sBAA1D,KAAK;uBAAC,0BAA0B;gBACY,gBAAgB,EAAA,CAAA;sBAA5D,KAAK;uBAAC,2BAA2B;gBACO,YAAY,EAAA,CAAA;sBAApD,KAAK;uBAAC,uBAAuB;gBACyD,kBAAkB,EAAA,CAAA;sBAAxG,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAE,KAAK,EAAE,6BAA6B,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC3B,uBAAuB,EAAA,CAAA;sBAAvE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,cAAc,EAAA,CAAA;sBAAtB;gBAMmD,aAAa,EAAA,CAAA;sBAAhE,MAAM;uBAAC,wBAAwB;;AA0D5B,MAAO,kBAAmB,SAAQ,sBAAsB,CAAA;IACnD,OAAO,GAAoB,IAAI;IACxC,cAAc,GAAkB,IAAI;AAEpC,IAAA,OAAO;IAEP,gBAAgB,GAAqB,EAAE;IAE7B,OAAO,GAAA;AACf,QAAA,OAAO,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC;;IAGlB,YAAY,GAAA;AAC7B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;QAEjE,IAAI,CAAC,SAAS,GAAG;AACf,YAAA,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACxD,CAAC,CAAA,EAAG,IAAI,CAAC,OAAO,CAAA,WAAA,EAAc,IAAI,CAAC,kBAAkB,CAAA,CAAE,GAAG,IAAI;YAC9D,CAAC,CAAA,EAAG,IAAI,CAAC,OAAO,CAAA,CAAA,EAAI,IAAI,CAAC,OAAO,CAAA,CAAE,GAAG;SACtC;QAED,IAAI,CAAC,gBAAgB,GAAG;AACtB,YAAA,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI;YACvE,+BAA+B,EAAE,IAAI,CAAC;SACvC;;uGAxBQ,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,EArCnB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCT,EAES,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,aAAa,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,mBAAA,EAAA,QAAA,EAAA,qEAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,8BAAA,EAAA,qCAAA,EAAA,4BAAA,EAAA,4BAAA,EAAA,0BAAA,EAAA,2BAAA,EAAA,6BAAA,EAAA,8BAAA,EAAA,kCAAA,EAAA,+BAAA,EAAA,mCAAA,EAAA,mCAAA,EAAA,yBAAA,EAAA,iCAAA,EAAA,sCAAA,EAAA,gCAAA,EAAA,iCAAA,EAAA,uCAAA,EAAA,kCAAA,EAAA,yBAAA,EAAA,wCAAA,CAAA,EAAA,OAAA,EAAA,CAAA,eAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,qBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,sBAAsB,EAAE,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,cAAc,gPAAE,eAAe,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,2BAAA,EAAA,QAAA,EAAA,2CAAA,EAAA,MAAA,EAAA,CAAA,sBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EApCpE,CAAC,aAAa,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAsChB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBA3C9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,QAAQ,EAAE,oBAAoB;oBAC9B,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,UAAU,EAAE,CAAC,aAAa,CAAC;AAC3B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCT,EAAA,CAAA;AACD,oBAAA,mBAAmB,EAAE,KAAK;oBAC1B,OAAO,EAAE,CAAC,aAAa,EAAE,sBAAsB,EAAE,cAAc,EAAE,eAAe;AACjF,iBAAA;;;ACtHD;;;AAGG;MAWU,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAHhB,kBAAkB,EAAE,kBAAkB,CACtC,EAAA,OAAA,EAAA,CAAA,kBAAkB,EAAE,kBAAkB,CAAA,EAAA,CAAA;AAErC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAHhB,kBAAkB,CAAA,EAAA,CAAA;;2FAGjB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;AACjD,oBAAA,OAAO,EAAE,CAAC,kBAAkB,EAAE,kBAAkB;AACjD,iBAAA;;;ACbD;;;AAGG;;ACHH;;AAEG;;;;"}