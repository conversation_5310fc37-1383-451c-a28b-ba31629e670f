{"version": 3, "file": "build-component.js", "sourceRoot": "", "sources": ["../../../schematics/utils/build-component.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;AAiMH,wCAgGC;AA/RD,wDAAqH;AAErH,+CAAgF;AAEhF,2DAaoC;AAEpC,iEAAyF;AACzF,qGAAqG;AACrG,qEAAwH;AACxH,+DAAkE;AAClE,yEAAmG;AACnG,uEAAmE;AACnE,uEAA8E;AAC9E,qEAAqE;AACrE,mFAA2E;AAE3E,2BAA4C;AAC5C,+BAA8C;AAE9C,SAAS,0BAA0B,CAAC,IAAa;IAC/C,IAAI,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,IAAI,CAAC,MAAM,IAAI,0BAA0B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChE,CAAC;AAED,SAAS,oBAAoB,CAAC,MAAqB;IACjD,wCAAwC;IACxC,MAAM,iBAAiB,GAAG,IAAA,gCAAoB,EAAC,MAAM,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC;IACpF,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACnC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,gGAAgG;IAChG,YAAY;IACZ,MAAM,WAAW,GAAG,0BAA0B,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QACtC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,qDAAqD;IACrD,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;AAC/B,CAAC;AAMD;;;;GAIG;AACH,SAAS,gBAAgB,CAAC,OAA0B;IAClD,MAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC;IAEtF,MAAM,cAAc,GAAG,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,8BAAW,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;IAErG,OAAO,GAAG,IAAI,GAAG,cAAc,EAAE,CAAC;AACpC,CAAC;AAED;;;GAGG;AACH,MAAM,sBAAsB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAEvE,8DAA8D;AAC9D,SAAS,kBAAkB,CAAC,IAAU,EAAE,UAAkB;IACxD,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACnC,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;QAClB,MAAM,IAAI,gCAAmB,CAAC,QAAQ,UAAU,kBAAkB,CAAC,CAAC;IACtE,CAAC;IAED,OAAO,EAAE,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC/F,CAAC;AAED,8DAA8D;AAC9D,SAAS,wBAAwB,CAAC,MAAW;;IAC3C,MAAM,SAAS,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC;IAC/C,IAAI,SAAS,EAAE,CAAC;QACd,MAAM,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAClD,OAAO,MAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAG,CAAC,CAAC,mCAAI,IAAI,CAAC;IAChC,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED,SAAS,wBAAwB,CAAC,OAA8B;IAC9D,OAAO,CAAC,IAAU,EAAE,EAAE;QACpB,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAChE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;QAClC,IAAI,MAAM,GAAG,kBAAkB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAElD,MAAM,aAAa,GAAG,IAAI,OAAO,CAAC,IAAI,IACpC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,cAAO,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,GACxD,GAAG,cAAO,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;QAC/C,MAAM,YAAY,GAAG,IAAA,+BAAiB,EAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAClE,IAAI,cAAc,GAAG,cAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,IAAI,WAAW,CAAC,CAAC;QAElE,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAChC,MAAM,YAAY,GAAG,wBAAwB,CAAC,MAAM,CAAC,CAAC;YACtD,IAAI,YAAY,EAAE,CAAC;gBACjB,cAAc,GAAG,GAAG,YAAY,GAAG,cAAc,EAAE,CAAC;YACtD,CAAC;QACH,CAAC;QAED,MAAM,kBAAkB,GAAG,IAAA,kCAAsB,EAAC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;QAEpG,MAAM,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACzD,KAAK,MAAM,MAAM,IAAI,kBAAkB,EAAE,CAAC;YACxC,IAAI,MAAM,YAAY,qBAAY,EAAE,CAAC;gBACnC,mBAAmB,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAEvC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,qEAAqE;YACrE,MAAM,GAAG,kBAAkB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAE9C,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACpD,MAAM,aAAa,GAAG,IAAA,6BAAiB,EACrC,MAAM,EACN,UAAU,EACV,cAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,IAAI,WAAW,CAAC,EAC5C,YAAY,CACb,CAAC;YAEF,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;gBACnC,IAAI,MAAM,YAAY,qBAAY,EAAE,CAAC;oBACnC,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CAAC,OAA8B,EAAE,aAAqB,EAAE,gBAAwB;IACpG,IAAI,QAAQ,GAAG,cAAO,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/C,IAAI,YAAY,GAAG,EAAE,CAAC;IACtB,IAAI,gBAAgB,EAAE,CAAC;QACrB,YAAY,GAAG,GAAG,cAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC;IAC3D,CAAC;IACD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,QAAQ,GAAG,GAAG,OAAO,CAAC,MAAM,IAAI,YAAY,GAAG,QAAQ,EAAE,CAAC;IAC5D,CAAC;SAAM,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,aAAa,EAAE,CAAC;QACzD,QAAQ,GAAG,GAAG,aAAa,IAAI,YAAY,GAAG,QAAQ,EAAE,CAAC;IAC3D,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;GAIG;AACH,SAAS,iBAAiB,CAAC,IAAY,EAAE,SAAiB;IACxD,wFAAwF;IACxF,qFAAqF;IACrF,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,KAAK,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AACrE,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,cAAc,CAAC,OAA8B,EAAE,kBAA0C,EAAE;IACzG,OAAO,CAAO,IAAU,EAAE,OAAmC,EAAE,EAAE;QAC/D,MAAM,SAAS,GAAG,MAAM,IAAA,wBAAY,EAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,IAAA,oCAAuB,EAAC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACpE,MAAM,4BAA4B,GAAG,IAAA,uCAA0B,EAAC,OAAO,CAAC,CAAC;QACzE,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,sEAAsE;QACtE,6FAA6F;QAC7F,0FAA0F;QAC1F,4BAA4B;QAC5B,MAAM,aAAa,GAAG,IAAA,aAAQ,EAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE;YAC9E,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI;YACpC,CAAC,CAAC,IAAA,cAAO,EAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAEhD,MAAM,iBAAiB,GAAG,SAAS,CAAC;QACpC,MAAM,kBAAkB,GAAG,IAAA,cAAO,EAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;QAErE,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,cAAK,CAAC,GAAG,CAAC;QAC3C,wFAAwF;QACxF,yDAAyD;QACzD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,IAAI,4BAA4B,CAAC,UAAU,CAAC,CAAC;aAC7F,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,4BAA4B,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAE3F,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,CAAC,UAAU,GAAG,MAAM,IAAA,kCAAqB,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEhE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO,CAAC,MAAM,GAAG,IAAA,mCAAqB,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,UAAU,GAAG,IAAA,sBAAS,EAAC,OAAO,CAAC,IAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAI,OAAO,CAAC,mBAAmB,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACzE,MAAM,MAAM,GAAG,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YACxD,YAAY,GAAG,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAC/B,OAAO,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAC/B,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAE5F,IAAA,iCAAoB,EAAC,OAAO,CAAC,QAAS,CAAC,CAAC;QAExC,MAAM,aAAa,GAAG,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,KAAK,KAAK,cAAK,CAAC,IAAI,CAAC;QAC1E,oFAAoF;QACpF,mFAAmF;QACnF,kFAAkF;QAClF,yFAAyF;QACzF,IAAI,CAAC,aAAa,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAM,CAAC,EAAE,CAAC;YACvE,OAAO,CAAC,KAAK,GAAG,cAAK,CAAC,GAAG,CAAC;QAC5B,CAAC;QAED,MAAM,eAAe,GAAG,CAAC,IAAY,EAAU,EAAE;YAC/C,OAAO,GAAG,YAAY,GAAG,cAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACpD,CAAC,CAAC;QACF,6DAA6D;QAC7D,MAAM,mBAAmB,iDACpB,cAAO,KACV,SAAS,EAAE,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EACjD,QAAQ,EAAE,eAAe,KACtB,OAAO,CACX,CAAC;QAEF,2FAA2F;QAC3F,0DAA0D;QAC1D,MAAM,aAAa,GAA2B,EAAE,CAAC;QAEjD,KAAK,MAAM,GAAG,IAAI,eAAe,EAAE,CAAC;YAClC,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;gBACzB,MAAM,WAAW,GAAG,IAAA,iBAAY,EAAC,IAAA,WAAI,EAAC,kBAAkB,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBAE1F,uEAAuE;gBACvE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAA,eAAmB,EAAC,WAAW,CAAC,CAAC,mBAAmB,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;QAED,MAAM,cAAc,GAAG,IAAA,kBAAK,EAAC,IAAA,gBAAG,EAAC,iBAAiB,CAAC,EAAE;YACnD,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAA,mBAAM,EAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAI,GAAE;YAChF,aAAa,CAAC,CAAC,CAAC,IAAA,mBAAM,EAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAI,GAAE;YAC9E,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAA,mBAAM,EAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAI,GAAE;YAClF,0FAA0F;YAC1F,wFAAwF;YACxF,8DAA8D;YAC9D,IAAA,2BAAc,EAAC,gBAAE,iBAAiB,EAAE,aAAa,IAAK,mBAAmB,CAAS,CAAC;YACnF,6EAA6E;YAC7E,0EAA0E;YAC1E,8DAA8D;YAC9D,IAAA,iBAAI,EAAC,IAAW,EAAE,UAAU,CAAC,IAAI,CAAC;SACnC,CAAC,CAAC;QAEH,OAAO,GAAG,EAAE,CACV,IAAA,kBAAK,EAAC,CAAC,IAAA,2BAAc,EAAC,IAAA,kBAAK,EAAC,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAE,IAAA,sBAAS,EAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAClH,CAAC,CAAA,CAAC;AACJ,CAAC"}