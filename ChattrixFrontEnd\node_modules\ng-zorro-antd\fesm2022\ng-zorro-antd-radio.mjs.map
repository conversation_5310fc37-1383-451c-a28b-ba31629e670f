{"version": 3, "file": "ng-zorro-antd-radio.mjs", "sources": ["../../components/radio/radio.service.ts", "../../components/radio/radio-group.component.ts", "../../components/radio/radio.component.ts", "../../components/radio/radio.module.ts", "../../components/radio/public-api.ts", "../../components/radio/ng-zorro-antd-radio.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Injectable } from '@angular/core';\nimport { ReplaySubject, Subject } from 'rxjs';\n\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\n@Injectable()\nexport class NzRadioService {\n  selected$ = new ReplaySubject<NzSafeAny>(1);\n  touched$ = new Subject<void>();\n  disabled$ = new ReplaySubject<boolean>(1);\n  name$ = new ReplaySubject<string>(1);\n  touch(): void {\n    this.touched$.next();\n  }\n  select(value: NzSafeAny): void {\n    this.selected$.next(value);\n  }\n  setDisabled(value: boolean): void {\n    this.disabled$.next(value);\n  }\n  setName(value: string): void {\n    this.name$.next(value);\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  SimpleChanges,\n  ViewEncapsulation,\n  booleanAttribute,\n  forwardRef\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzSafeAny, NzSizeLDSType, OnChangeType, OnTouchedType } from 'ng-zorro-antd/core/types';\n\nimport { NzRadioService } from './radio.service';\n\nexport type NzRadioButtonStyle = 'outline' | 'solid';\n\n@Component({\n  selector: 'nz-radio-group',\n  exportAs: 'nzRadioGroup',\n  preserveWhitespaces: false,\n  template: ` <ng-content></ng-content> `,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    NzRadioService,\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => NzRadioGroupComponent),\n      multi: true\n    }\n  ],\n  host: {\n    class: 'ant-radio-group',\n    '[class.ant-radio-group-large]': `nzSize === 'large'`,\n    '[class.ant-radio-group-small]': `nzSize === 'small'`,\n    '[class.ant-radio-group-solid]': `nzButtonStyle === 'solid'`,\n    '[class.ant-radio-group-rtl]': `dir === 'rtl'`\n  }\n})\nexport class NzRadioGroupComponent implements OnInit, ControlValueAccessor, OnDestroy, OnChanges {\n  private value: NzSafeAny | null = null;\n  private destroy$ = new Subject<boolean>();\n  private isNzDisableFirstChange: boolean = true;\n  onChange: OnChangeType = () => {};\n  onTouched: OnTouchedType = () => {};\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Input() nzButtonStyle: NzRadioButtonStyle = 'outline';\n  @Input() nzSize: NzSizeLDSType = 'default';\n  @Input() nzName: string | null = null;\n\n  dir: Direction = 'ltr';\n\n  constructor(\n    private cdr: ChangeDetectorRef,\n    private nzRadioService: NzRadioService,\n    private directionality: Directionality\n  ) {}\n\n  ngOnInit(): void {\n    this.nzRadioService.selected$.pipe(takeUntil(this.destroy$)).subscribe(value => {\n      if (this.value !== value) {\n        this.value = value;\n        this.onChange(this.value);\n      }\n    });\n    this.nzRadioService.touched$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      Promise.resolve().then(() => this.onTouched());\n    });\n\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n\n    this.dir = this.directionality.value;\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzDisabled, nzName } = changes;\n    if (nzDisabled) {\n      this.nzRadioService.setDisabled(this.nzDisabled);\n    }\n    if (nzName) {\n      this.nzRadioService.setName(this.nzName!);\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n\n  writeValue(value: NzSafeAny): void {\n    this.value = value;\n    this.nzRadioService.select(value);\n    this.cdr.markForCheck();\n  }\n\n  registerOnChange(fn: OnChangeType): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: OnTouchedType): void {\n    this.onTouched = fn;\n  }\n\n  setDisabledState(isDisabled: boolean): void {\n    this.nzDisabled = (this.isNzDisableFirstChange && this.nzDisabled) || isDisabled;\n    this.isNzDisableFirstChange = false;\n    this.nzRadioService.setDisabled(this.nzDisabled);\n    this.cdr.markForCheck();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  Input,\n  NgZone,\n  OnDestroy,\n  OnInit,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  forwardRef,\n  inject\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzFormStatusService } from 'ng-zorro-antd/core/form';\nimport { NzSafeAny, OnChangeType, OnTouchedType } from 'ng-zorro-antd/core/types';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\n\nimport { NzRadioService } from './radio.service';\n\n@Component({\n  selector: '[nz-radio],[nz-radio-button]',\n  exportAs: 'nzRadio',\n  preserveWhitespaces: false,\n  template: `\n    <span\n      [class.ant-radio]=\"!isRadioButton\"\n      [class.ant-radio-checked]=\"isChecked && !isRadioButton\"\n      [class.ant-radio-disabled]=\"nzDisabled && !isRadioButton\"\n      [class.ant-radio-button]=\"isRadioButton\"\n      [class.ant-radio-button-checked]=\"isChecked && isRadioButton\"\n      [class.ant-radio-button-disabled]=\"nzDisabled && isRadioButton\"\n    >\n      <input\n        #inputElement\n        type=\"radio\"\n        [attr.autofocus]=\"nzAutoFocus ? 'autofocus' : null\"\n        [class.ant-radio-input]=\"!isRadioButton\"\n        [class.ant-radio-button-input]=\"isRadioButton\"\n        [disabled]=\"nzDisabled\"\n        [checked]=\"isChecked\"\n        [attr.name]=\"name\"\n      />\n      <span [class.ant-radio-inner]=\"!isRadioButton\" [class.ant-radio-button-inner]=\"isRadioButton\"></span>\n    </span>\n    <span><ng-content></ng-content></span>\n  `,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => NzRadioComponent),\n      multi: true\n    }\n  ],\n  host: {\n    '[class.ant-radio-wrapper-in-form-item]': '!!nzFormStatusService',\n    '[class.ant-radio-wrapper]': '!isRadioButton',\n    '[class.ant-radio-button-wrapper]': 'isRadioButton',\n    '[class.ant-radio-wrapper-checked]': 'isChecked && !isRadioButton',\n    '[class.ant-radio-button-wrapper-checked]': 'isChecked && isRadioButton',\n    '[class.ant-radio-wrapper-disabled]': 'nzDisabled && !isRadioButton',\n    '[class.ant-radio-button-wrapper-disabled]': 'nzDisabled && isRadioButton',\n    '[class.ant-radio-wrapper-rtl]': `!isRadioButton && dir === 'rtl'`,\n    '[class.ant-radio-button-wrapper-rtl]': `isRadioButton && dir === 'rtl'`\n  }\n})\nexport class NzRadioComponent implements ControlValueAccessor, AfterViewInit, OnDestroy, OnInit {\n  private isNgModel = false;\n  private destroy$ = new Subject<void>();\n  private isNzDisableFirstChange: boolean = true;\n  private directionality = inject(Directionality);\n  private nzRadioService = inject(NzRadioService, { optional: true });\n  nzFormStatusService = inject(NzFormStatusService, { optional: true });\n  isChecked = false;\n  name: string | null = null;\n  onChange: OnChangeType = () => {};\n  onTouched: OnTouchedType = () => {};\n  @ViewChild('inputElement', { static: true }) inputElement!: ElementRef<HTMLInputElement>;\n  @Input() nzValue: NzSafeAny | null = null;\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Input({ transform: booleanAttribute }) nzAutoFocus = false;\n  @Input({ alias: 'nz-radio-button', transform: booleanAttribute }) isRadioButton = false;\n\n  dir: Direction = 'ltr';\n\n  focus(): void {\n    this.focusMonitor.focusVia(this.inputElement!, 'keyboard');\n  }\n\n  blur(): void {\n    this.inputElement!.nativeElement.blur();\n  }\n\n  constructor(\n    private ngZone: NgZone,\n    private elementRef: ElementRef,\n    private cdr: ChangeDetectorRef,\n    private focusMonitor: FocusMonitor\n  ) {}\n\n  setDisabledState(disabled: boolean): void {\n    this.nzDisabled = (this.isNzDisableFirstChange && this.nzDisabled) || disabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n\n  writeValue(value: boolean): void {\n    this.isChecked = value;\n    this.cdr.markForCheck();\n  }\n\n  registerOnChange(fn: OnChangeType): void {\n    this.isNgModel = true;\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: OnTouchedType): void {\n    this.onTouched = fn;\n  }\n\n  ngOnInit(): void {\n    if (this.nzRadioService) {\n      this.nzRadioService.name$.pipe(takeUntil(this.destroy$)).subscribe(name => {\n        this.name = name;\n        this.cdr.markForCheck();\n      });\n      this.nzRadioService.disabled$.pipe(takeUntil(this.destroy$)).subscribe(disabled => {\n        this.nzDisabled = (this.isNzDisableFirstChange && this.nzDisabled) || disabled;\n        this.isNzDisableFirstChange = false;\n        this.cdr.markForCheck();\n      });\n      this.nzRadioService.selected$.pipe(takeUntil(this.destroy$)).subscribe(value => {\n        const isChecked = this.isChecked;\n        this.isChecked = this.nzValue === value;\n        // We don't have to run `onChange()` on each `nz-radio` button whenever the `selected$` emits.\n        // If we have 8 `nz-radio` buttons within the `nz-radio-group` and they're all connected with\n        // `ngModel` or `formControl` then `onChange()` will be called 8 times for each `nz-radio` button.\n        // We prevent this by checking if `isChecked` has been changed or not.\n        if (\n          this.isNgModel &&\n          isChecked !== this.isChecked &&\n          // We're only intereted if `isChecked` has been changed to `false` value to emit `false` to the ascendant form,\n          // since we already emit `true` within the `setupClickListener`.\n          this.isChecked === false\n        ) {\n          this.onChange(false);\n        }\n        this.cdr.markForCheck();\n      });\n    }\n    this.focusMonitor\n      .monitor(this.elementRef, true)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(focusOrigin => {\n        if (!focusOrigin) {\n          Promise.resolve().then(() => this.onTouched());\n          if (this.nzRadioService) {\n            this.nzRadioService.touch();\n          }\n        }\n      });\n\n    this.directionality.change.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n\n    this.dir = this.directionality.value;\n\n    this.setupClickListener();\n  }\n\n  ngAfterViewInit(): void {\n    if (this.nzAutoFocus) {\n      this.focus();\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.focusMonitor.stopMonitoring(this.elementRef);\n  }\n\n  private setupClickListener(): void {\n    fromEventOutsideAngular<MouseEvent>(this.elementRef.nativeElement, 'click')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(event => {\n        /** prevent label click triggered twice. **/\n        event.stopPropagation();\n        event.preventDefault();\n        if (this.nzDisabled || this.isChecked) {\n          return;\n        }\n        this.ngZone.run(() => {\n          this.focus();\n          this.nzRadioService?.select(this.nzValue);\n          if (this.isNgModel) {\n            this.isChecked = true;\n            this.onChange(true);\n          }\n          this.cdr.markForCheck();\n        });\n      });\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzRadioGroupComponent } from './radio-group.component';\nimport { NzRadioComponent } from './radio.component';\n\n@NgModule({\n  imports: [NzRadioComponent, NzRadioGroupComponent],\n  exports: [NzRadioComponent, NzRadioGroupComponent]\n})\nexport class NzRadioModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './radio-group.component';\nexport * from './radio.component';\nexport * from './radio.service';\nexport * from './radio.module';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1.NzRadioService"], "mappings": ";;;;;;;;;;;AAAA;;;AAGG;MAQU,cAAc,CAAA;AACzB,IAAA,SAAS,GAAG,IAAI,aAAa,CAAY,CAAC,CAAC;AAC3C,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;AAC9B,IAAA,SAAS,GAAG,IAAI,aAAa,CAAU,CAAC,CAAC;AACzC,IAAA,KAAK,GAAG,IAAI,aAAa,CAAS,CAAC,CAAC;IACpC,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;;AAEtB,IAAA,MAAM,CAAC,KAAgB,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;;AAE5B,IAAA,WAAW,CAAC,KAAc,EAAA;AACxB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;;AAE5B,IAAA,OAAO,CAAC,KAAa,EAAA;AACnB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;;uGAfb,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAAd,cAAc,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B;;;MC0CY,qBAAqB,CAAA;AActB,IAAA,GAAA;AACA,IAAA,cAAA;AACA,IAAA,cAAA;IAfF,KAAK,GAAqB,IAAI;AAC9B,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAW;IACjC,sBAAsB,GAAY,IAAI;AAC9C,IAAA,QAAQ,GAAiB,MAAK,GAAG;AACjC,IAAA,SAAS,GAAkB,MAAK,GAAG;IACK,UAAU,GAAG,KAAK;IACjD,aAAa,GAAuB,SAAS;IAC7C,MAAM,GAAkB,SAAS;IACjC,MAAM,GAAkB,IAAI;IAErC,GAAG,GAAc,KAAK;AAEtB,IAAA,WAAA,CACU,GAAsB,EACtB,cAA8B,EAC9B,cAA8B,EAAA;QAF9B,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAc,CAAA,cAAA,GAAd,cAAc;QACd,IAAc,CAAA,cAAA,GAAd,cAAc;;IAGxB,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AAC7E,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;AACxB,gBAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;AAE7B,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AACzE,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;AAChD,SAAC,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,YAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,SAAC,CAAC;QAEF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;;AAGtC,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,OAAO;QACtC,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;;QAElD,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,MAAO,CAAC;;;IAI7C,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;AAG1B,IAAA,UAAU,CAAC,KAAgB,EAAA;AACzB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC;AACjC,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,IAAA,gBAAgB,CAAC,EAAgB,EAAA;AAC/B,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAGpB,IAAA,iBAAiB,CAAC,EAAiB,EAAA;AACjC,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU;AAChF,QAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;AAChD,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;uGAvEd,qBAAqB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,cAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAArB,qBAAqB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAMZ,gBAAgB,CAtBzB,EAAA,aAAA,EAAA,eAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,6BAAA,EAAA,oBAAA,EAAA,6BAAA,EAAA,oBAAA,EAAA,6BAAA,EAAA,2BAAA,EAAA,2BAAA,EAAA,eAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,SAAA,EAAA;YACT,cAAc;AACd,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,qBAAqB,CAAC;AACpD,gBAAA,KAAK,EAAE;AACR;AACF,SAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAVS,CAA6B,2BAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAmB5B,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAvBjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE,CAA6B,2BAAA,CAAA;oBACvC,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,SAAS,EAAE;wBACT,cAAc;AACd,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,2BAA2B,CAAC;AACpD,4BAAA,KAAK,EAAE;AACR;AACF,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,iBAAiB;AACxB,wBAAA,+BAA+B,EAAE,CAAoB,kBAAA,CAAA;AACrD,wBAAA,+BAA+B,EAAE,CAAoB,kBAAA,CAAA;AACrD,wBAAA,+BAA+B,EAAE,CAA2B,yBAAA,CAAA;AAC5D,wBAAA,6BAA6B,EAAE,CAAe,aAAA;AAC/C;AACF,iBAAA;6IAOyC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,aAAa,EAAA,CAAA;sBAArB;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,MAAM,EAAA,CAAA;sBAAd;;;MCoBU,gBAAgB,CAAA;AA4BjB,IAAA,MAAA;AACA,IAAA,UAAA;AACA,IAAA,GAAA;AACA,IAAA,YAAA;IA9BF,SAAS,GAAG,KAAK;AACjB,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;IAC9B,sBAAsB,GAAY,IAAI;AACtC,IAAA,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;IACvC,cAAc,GAAG,MAAM,CAAC,cAAc,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACnE,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrE,SAAS,GAAG,KAAK;IACjB,IAAI,GAAkB,IAAI;AAC1B,IAAA,QAAQ,GAAiB,MAAK,GAAG;AACjC,IAAA,SAAS,GAAkB,MAAK,GAAG;AACU,IAAA,YAAY;IAChD,OAAO,GAAqB,IAAI;IACD,UAAU,GAAG,KAAK;IAClB,WAAW,GAAG,KAAK;IACO,aAAa,GAAG,KAAK;IAEvF,GAAG,GAAc,KAAK;IAEtB,KAAK,GAAA;QACH,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAa,EAAE,UAAU,CAAC;;IAG5D,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,YAAa,CAAC,aAAa,CAAC,IAAI,EAAE;;AAGzC,IAAA,WAAA,CACU,MAAc,EACd,UAAsB,EACtB,GAAsB,EACtB,YAA0B,EAAA;QAH1B,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAU,CAAA,UAAA,GAAV,UAAU;QACV,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAY,CAAA,YAAA,GAAZ,YAAY;;AAGtB,IAAA,gBAAgB,CAAC,QAAiB,EAAA;AAChC,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ;AAC9E,QAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;AACnC,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,IAAA,UAAU,CAAC,KAAc,EAAA;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,IAAA,gBAAgB,CAAC,EAAgB,EAAA;AAC/B,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAGpB,IAAA,iBAAiB,CAAC,EAAiB,EAAA;AACjC,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;IAGrB,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,IAAG;AACxE,gBAAA,IAAI,CAAC,IAAI,GAAG,IAAI;AAChB,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,IAAG;AAChF,gBAAA,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ;AAC9E,gBAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;AACnC,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AAC7E,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;gBAChC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,KAAK,KAAK;;;;;gBAKvC,IACE,IAAI,CAAC,SAAS;oBACd,SAAS,KAAK,IAAI,CAAC,SAAS;;;AAG5B,oBAAA,IAAI,CAAC,SAAS,KAAK,KAAK,EACxB;AACA,oBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;;AAEtB,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;;AAEJ,QAAA,IAAI,CAAC;AACF,aAAA,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI;AAC7B,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC7B,SAAS,CAAC,WAAW,IAAG;YACvB,IAAI,CAAC,WAAW,EAAE;AAChB,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;AAC9C,gBAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,oBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;;;AAGjC,SAAC,CAAC;QAEJ,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC3F,YAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,SAAC,CAAC;QAEF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;QAEpC,IAAI,CAAC,kBAAkB,EAAE;;IAG3B,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,KAAK,EAAE;;;IAIhB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;QACxB,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC;;IAG3C,kBAAkB,GAAA;QACxB,uBAAuB,CAAa,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,OAAO;AACvE,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC7B,SAAS,CAAC,KAAK,IAAG;;YAEjB,KAAK,CAAC,eAAe,EAAE;YACvB,KAAK,CAAC,cAAc,EAAE;YACtB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE;gBACrC;;AAEF,YAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAK;gBACnB,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AACzC,gBAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,oBAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,oBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;;AAErB,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;AACJ,SAAC,CAAC;;uGAzIK,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,uIAaP,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAChB,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,iBAAA,EAAA,eAAA,EACU,gBAAgB,CAlCnD,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,sCAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,gBAAA,EAAA,gCAAA,EAAA,eAAA,EAAA,iCAAA,EAAA,6BAAA,EAAA,wCAAA,EAAA,4BAAA,EAAA,kCAAA,EAAA,8BAAA,EAAA,yCAAA,EAAA,6BAAA,EAAA,6BAAA,EAAA,iCAAA,EAAA,oCAAA,EAAA,gCAAA,EAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,gBAAgB,CAAC;AAC/C,gBAAA,KAAK,EAAE;AACR;SACF,EA/BS,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,cAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;AAsBT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAsBU,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAhD5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,8BAA8B;AACxC,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;AAsBT,EAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,sBAAsB,CAAC;AAC/C,4BAAA,KAAK,EAAE;AACR;AACF,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,wCAAwC,EAAE,uBAAuB;AACjE,wBAAA,2BAA2B,EAAE,gBAAgB;AAC7C,wBAAA,kCAAkC,EAAE,eAAe;AACnD,wBAAA,mCAAmC,EAAE,6BAA6B;AAClE,wBAAA,0CAA0C,EAAE,4BAA4B;AACxE,wBAAA,oCAAoC,EAAE,8BAA8B;AACpE,wBAAA,2CAA2C,EAAE,6BAA6B;AAC1E,wBAAA,+BAA+B,EAAE,CAAiC,+BAAA,CAAA;AAClE,wBAAA,sCAAsC,EAAE,CAAgC,8BAAA;AACzE;AACF,iBAAA;+JAY8C,YAAY,EAAA,CAAA;sBAAxD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBAClC,OAAO,EAAA,CAAA;sBAAf;gBACuC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC4B,aAAa,EAAA,CAAA;sBAA9E,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAE,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAE,gBAAgB,EAAE;;;AChGlE;;;AAGG;MAWU,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAHd,gBAAgB,EAAE,qBAAqB,CACvC,EAAA,OAAA,EAAA,CAAA,gBAAgB,EAAE,qBAAqB,CAAA,EAAA,CAAA;wGAEtC,aAAa,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;AAClD,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,qBAAqB;AAClD,iBAAA;;;ACbD;;;AAGG;;ACHH;;AAEG;;;;"}