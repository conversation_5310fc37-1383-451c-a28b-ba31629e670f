{"version": 3, "file": "ng-zorro-antd-time-picker.mjs", "sources": ["../../components/time-picker/time-holder.ts", "../../components/time-picker/time-picker-panel.component.ts", "../../components/time-picker/time-picker.component.ts", "../../components/time-picker/time-picker.module.ts", "../../components/time-picker/public-api.ts", "../../components/time-picker/ng-zorro-antd-time-picker.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Observable, Subject } from 'rxjs';\n\nimport { isNil, isNotNil } from 'ng-zorro-antd/core/util';\n\nexport class TimeHolder {\n  selected12Hours: string | undefined = undefined;\n  private _value: Date | undefined;\n  private _use12Hours: boolean = false;\n  private _defaultOpenValue!: Date;\n  private _changes = new Subject<Date>();\n\n  setMinutes(value: number, disabled: boolean): this {\n    if (!disabled) {\n      this.initValue();\n      this.value.setMinutes(value);\n      this.update();\n    }\n    return this;\n  }\n\n  setHours(value: number, disabled: boolean): this {\n    if (!disabled) {\n      this.initValue();\n      if (this._use12Hours) {\n        if (this.selected12Hours === 'PM' && value !== 12) {\n          this.value.setHours((value as number) + 12);\n        } else if (this.selected12Hours === 'AM' && value === 12) {\n          this.value.setHours(0);\n        } else {\n          this.value.setHours(value);\n        }\n      } else {\n        this.value.setHours(value);\n      }\n      this.update();\n    }\n    return this;\n  }\n\n  setSeconds(value: number, disabled: boolean): this {\n    if (!disabled) {\n      this.initValue();\n      this.value.setSeconds(value);\n      this.update();\n    }\n    return this;\n  }\n\n  setUse12Hours(value: boolean): this {\n    this._use12Hours = value;\n    return this;\n  }\n\n  get changes(): Observable<Date> {\n    return this._changes.asObservable();\n  }\n\n  setValue(value: Date | undefined, use12Hours?: boolean): this {\n    if (isNotNil(use12Hours)) {\n      this._use12Hours = use12Hours as boolean;\n    }\n    if (value !== this.value) {\n      this._value = value;\n      if (isNotNil(this.value)) {\n        if (this._use12Hours && isNotNil(this.hours)) {\n          this.selected12Hours = this.hours >= 12 ? 'PM' : 'AM';\n        }\n      } else {\n        this._clear();\n      }\n    }\n\n    return this;\n  }\n\n  initValue(): void {\n    if (isNil(this.value)) {\n      this.setValue(new Date(), this._use12Hours);\n    }\n  }\n\n  clear(): void {\n    this._clear();\n    this.update();\n  }\n\n  get isEmpty(): boolean {\n    return !(isNotNil(this.hours) || isNotNil(this.minutes) || isNotNil(this.seconds));\n  }\n\n  private _clear(): void {\n    this._value = undefined;\n    this.selected12Hours = undefined;\n  }\n\n  private update(): void {\n    if (this.isEmpty) {\n      this._value = undefined;\n    } else {\n      if (isNotNil(this.hours)) {\n        this.value!.setHours(this.hours!);\n      }\n\n      if (isNotNil(this.minutes)) {\n        this.value!.setMinutes(this.minutes!);\n      }\n\n      if (isNotNil(this.seconds)) {\n        this.value!.setSeconds(this.seconds!);\n      }\n\n      if (this._use12Hours) {\n        if (this.selected12Hours === 'PM' && this.hours! < 12) {\n          this.value!.setHours(this.hours! + 12);\n        }\n        if (this.selected12Hours === 'AM' && this.hours! >= 12) {\n          this.value!.setHours(this.hours! - 12);\n        }\n      }\n    }\n    this.changed();\n  }\n\n  changed(): void {\n    this._changes.next(this.value);\n  }\n\n  /**\n   * @description\n   * UI view hours\n   * Get viewHours which is selected in `time-picker-panel` and its range is [12, 1, 2, ..., 11]\n   */\n  get viewHours(): number | undefined {\n    return this._use12Hours && isNotNil(this.hours) ? this.calculateViewHour(this.hours!) : this.hours;\n  }\n\n  setSelected12Hours(value: string | undefined): void {\n    if (value!.toUpperCase() !== this.selected12Hours) {\n      this.selected12Hours = value!.toUpperCase();\n      this.update();\n    }\n  }\n\n  get value(): Date {\n    return this._value || this._defaultOpenValue;\n  }\n\n  get hours(): number | undefined {\n    return this.value?.getHours();\n  }\n\n  get minutes(): number | undefined {\n    return this.value?.getMinutes();\n  }\n\n  get seconds(): number | undefined {\n    return this.value?.getSeconds();\n  }\n\n  setDefaultOpenValue(value: Date): this {\n    this._defaultOpenValue = value;\n    return this;\n  }\n\n  private calculateViewHour(value: number): number {\n    const selected12Hours = this.selected12Hours;\n    if (selected12Hours === 'PM' && value > 12) {\n      return value - 12;\n    }\n    if (selected12Hours === 'AM' && value === 0) {\n      return 12;\n    }\n    return value;\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { DecimalPipe, NgTemplateOutlet } from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  DebugElement,\n  ElementRef,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  forwardRef,\n  numberAttribute\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { reqAnimFrame } from 'ng-zorro-antd/core/polyfill';\nimport { fromEventOutsideAngular, isNotNil } from 'ng-zorro-antd/core/util';\nimport { DateHelperService, NzI18nModule } from 'ng-zorro-antd/i18n';\n\nimport { TimeHolder } from './time-holder';\n\nfunction makeRange(length: number, step: number = 1, start: number = 0): number[] {\n  return new Array(Math.ceil(length / step)).fill(0).map((_, i) => (i + start) * step);\n}\n\nexport type NzTimePickerUnit = 'hour' | 'minute' | 'second' | '12-hour';\n\n@Component({\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  selector: 'nz-time-picker-panel',\n  exportAs: 'nzTimePickerPanel',\n  template: `\n    @if (nzInDatePicker) {\n      <div class=\"ant-picker-header\">\n        <div class=\"ant-picker-header-view\">{{ dateHelper.format($any(time?.value), format) || '&nbsp;' }}</div>\n      </div>\n    }\n    <div class=\"ant-picker-content\">\n      @if (hourEnabled) {\n        <ul #hourListElement class=\"ant-picker-time-panel-column\" style=\"position: relative;\">\n          @for (hour of hourRange; track $index) {\n            @if (!(nzHideDisabledOptions && hour.disabled)) {\n              <li\n                class=\"ant-picker-time-panel-cell\"\n                (click)=\"selectHour(hour)\"\n                [class.ant-picker-time-panel-cell-selected]=\"isSelectedHour(hour)\"\n                [class.ant-picker-time-panel-cell-disabled]=\"hour.disabled\"\n              >\n                <div class=\"ant-picker-time-panel-cell-inner\">{{ hour.index | number: '2.0-0' }}</div>\n              </li>\n            }\n          }\n        </ul>\n      }\n      @if (minuteEnabled) {\n        <ul #minuteListElement class=\"ant-picker-time-panel-column\" style=\"position: relative;\">\n          @for (minute of minuteRange; track $index) {\n            @if (!(nzHideDisabledOptions && minute.disabled)) {\n              <li\n                class=\"ant-picker-time-panel-cell\"\n                (click)=\"selectMinute(minute)\"\n                [class.ant-picker-time-panel-cell-selected]=\"isSelectedMinute(minute)\"\n                [class.ant-picker-time-panel-cell-disabled]=\"minute.disabled\"\n              >\n                <div class=\"ant-picker-time-panel-cell-inner\">{{ minute.index | number: '2.0-0' }}</div>\n              </li>\n            }\n          }\n        </ul>\n      }\n      @if (secondEnabled) {\n        <ul #secondListElement class=\"ant-picker-time-panel-column\" style=\"position: relative;\">\n          @for (second of secondRange; track $index) {\n            @if (!(nzHideDisabledOptions && second.disabled)) {\n              <li\n                class=\"ant-picker-time-panel-cell\"\n                (click)=\"selectSecond(second)\"\n                [class.ant-picker-time-panel-cell-selected]=\"isSelectedSecond(second)\"\n                [class.ant-picker-time-panel-cell-disabled]=\"second.disabled\"\n              >\n                <div class=\"ant-picker-time-panel-cell-inner\">{{ second.index | number: '2.0-0' }}</div>\n              </li>\n            }\n          }\n        </ul>\n      }\n      @if (nzUse12Hours) {\n        <ul #use12HoursListElement class=\"ant-picker-time-panel-column\" style=\"position: relative;\">\n          @for (range of use12HoursRange; track range) {\n            <li\n              (click)=\"select12Hours(range)\"\n              class=\"ant-picker-time-panel-cell\"\n              [class.ant-picker-time-panel-cell-selected]=\"isSelected12Hours(range)\"\n            >\n              <div class=\"ant-picker-time-panel-cell-inner\">{{ range.value }}</div>\n            </li>\n          }\n        </ul>\n      }\n    </div>\n    @if (!nzInDatePicker) {\n      <div class=\"ant-picker-footer\">\n        @if (nzAddOn) {\n          <div class=\"ant-picker-footer-extra\">\n            <ng-template [ngTemplateOutlet]=\"nzAddOn\"></ng-template>\n          </div>\n        }\n        <ul class=\"ant-picker-ranges\">\n          <li class=\"ant-picker-now\">\n            <a (click)=\"onClickNow()\">\n              {{ nzNowText || ('Calendar.lang.now' | nzI18n) }}\n            </a>\n          </li>\n          <li class=\"ant-picker-ok\">\n            <button nz-button type=\"button\" nzSize=\"small\" nzType=\"primary\" (click)=\"onClickOk()\">\n              {{ nzOkText || ('Calendar.lang.ok' | nzI18n) }}\n            </button>\n          </li>\n        </ul>\n      </div>\n    }\n  `,\n  host: {\n    class: 'ant-picker-time-panel',\n    '[class.ant-picker-time-panel-column-0]': `enabledColumns === 0 && !nzInDatePicker`,\n    '[class.ant-picker-time-panel-column-1]': `enabledColumns === 1 && !nzInDatePicker`,\n    '[class.ant-picker-time-panel-column-2]': `enabledColumns === 2 && !nzInDatePicker`,\n    '[class.ant-picker-time-panel-column-3]': `enabledColumns === 3 && !nzInDatePicker`,\n    '[class.ant-picker-time-panel-narrow]': `enabledColumns < 3`,\n    '[class.ant-picker-time-panel-placement-bottomLeft]': `!nzInDatePicker`\n  },\n  providers: [{ provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => NzTimePickerPanelComponent), multi: true }],\n  imports: [DecimalPipe, NgTemplateOutlet, NzI18nModule, NzButtonModule]\n})\nexport class NzTimePickerPanelComponent implements ControlValueAccessor, OnInit, OnDestroy, OnChanges {\n  private _nzHourStep = 1;\n  private _nzMinuteStep = 1;\n  private _nzSecondStep = 1;\n  private unsubscribe$ = new Subject<void>();\n  private onChange?: (value: Date) => void;\n  private onTouch?: () => void;\n  private _format = 'HH:mm:ss';\n  private _disabledHours?: () => number[] = () => [];\n  private _disabledMinutes?: (hour: number) => number[] = () => [];\n  private _disabledSeconds?: (hour: number, minute: number) => number[] = () => [];\n  private _allowEmpty = true;\n  time = new TimeHolder();\n  hourEnabled = true;\n  minuteEnabled = true;\n  secondEnabled = true;\n  firstScrolled = false;\n  enabledColumns = 3;\n  hourRange!: ReadonlyArray<{ index: number; disabled: boolean }>;\n  minuteRange!: ReadonlyArray<{ index: number; disabled: boolean }>;\n  secondRange!: ReadonlyArray<{ index: number; disabled: boolean }>;\n  use12HoursRange!: ReadonlyArray<{ index: number; value: string }>;\n\n  @ViewChild('hourListElement', { static: false })\n  hourListElement?: DebugElement;\n  @ViewChild('minuteListElement', { static: false }) minuteListElement?: DebugElement;\n  @ViewChild('secondListElement', { static: false }) secondListElement?: DebugElement;\n  @ViewChild('use12HoursListElement', { static: false }) use12HoursListElement?: DebugElement;\n\n  @Input({ transform: booleanAttribute }) nzInDatePicker: boolean = false; // If inside a date-picker, more diff works need to be done\n  @Input() nzAddOn?: TemplateRef<void>;\n  @Input() nzHideDisabledOptions = false;\n  @Input() nzClearText?: string;\n  @Input() nzNowText?: string;\n  @Input() nzOkText?: string;\n  @Input() nzPlaceHolder?: string | null;\n  @Input({ transform: booleanAttribute }) nzUse12Hours = false;\n  @Input() nzDefaultOpenValue?: Date;\n\n  @Output() readonly closePanel = new EventEmitter<void>();\n\n  @Input({ transform: booleanAttribute })\n  set nzAllowEmpty(value: boolean) {\n    this._allowEmpty = value;\n  }\n\n  get nzAllowEmpty(): boolean {\n    return this._allowEmpty;\n  }\n\n  @Input()\n  set nzDisabledHours(value: undefined | (() => number[])) {\n    this._disabledHours = value;\n    if (this._disabledHours) {\n      this.buildHours();\n    }\n  }\n\n  get nzDisabledHours(): undefined | (() => number[]) {\n    return this._disabledHours;\n  }\n\n  @Input()\n  set nzDisabledMinutes(value: undefined | ((hour: number) => number[])) {\n    if (isNotNil(value)) {\n      this._disabledMinutes = value;\n      this.buildMinutes();\n    }\n  }\n\n  get nzDisabledMinutes(): undefined | ((hour: number) => number[]) {\n    return this._disabledMinutes;\n  }\n\n  @Input()\n  set nzDisabledSeconds(value: undefined | ((hour: number, minute: number) => number[])) {\n    if (isNotNil(value)) {\n      this._disabledSeconds = value;\n      this.buildSeconds();\n    }\n  }\n\n  get nzDisabledSeconds(): undefined | ((hour: number, minute: number) => number[]) {\n    return this._disabledSeconds;\n  }\n\n  @Input()\n  set format(value: string) {\n    if (isNotNil(value)) {\n      this._format = value;\n      this.enabledColumns = 0;\n      const charSet = new Set(value);\n      this.hourEnabled = charSet.has('H') || charSet.has('h');\n      this.minuteEnabled = charSet.has('m');\n      this.secondEnabled = charSet.has('s');\n      if (this.hourEnabled) {\n        this.enabledColumns++;\n      }\n      if (this.minuteEnabled) {\n        this.enabledColumns++;\n      }\n      if (this.secondEnabled) {\n        this.enabledColumns++;\n      }\n      if (this.nzUse12Hours) {\n        this.build12Hours();\n      }\n    }\n  }\n\n  get format(): string {\n    return this._format;\n  }\n\n  @Input({ transform: numberAttribute })\n  set nzHourStep(value: number) {\n    this._nzHourStep = value || 1;\n    this.buildHours();\n  }\n\n  get nzHourStep(): number {\n    return this._nzHourStep;\n  }\n\n  @Input({ transform: numberAttribute })\n  set nzMinuteStep(value: number) {\n    this._nzMinuteStep = value || 1;\n    this.buildMinutes();\n  }\n\n  get nzMinuteStep(): number {\n    return this._nzMinuteStep;\n  }\n\n  @Input({ transform: numberAttribute })\n  set nzSecondStep(value: number) {\n    this._nzSecondStep = value || 1;\n    this.buildSeconds();\n  }\n\n  get nzSecondStep(): number {\n    return this._nzSecondStep;\n  }\n\n  buildHours(): void {\n    let hourRanges = 24;\n    let disabledHours = this.nzDisabledHours?.();\n    let startIndex = 0;\n    if (this.nzUse12Hours) {\n      hourRanges = 12;\n      if (disabledHours) {\n        if (this.time.selected12Hours === 'PM') {\n          /**\n           * Filter and transform hours which greater or equal to 12\n           * [0, 1, 2, ..., 12, 13, 14, 15, ..., 23] => [12, 1, 2, 3, ..., 11]\n           */\n          disabledHours = disabledHours.filter(i => i >= 12).map(i => (i > 12 ? i - 12 : i));\n        } else {\n          /**\n           * Filter and transform hours which less than 12\n           * [0, 1, 2,..., 12, 13, 14, 15, ...23] => [12, 1, 2, 3, ..., 11]\n           */\n          disabledHours = disabledHours.filter(i => i < 12 || i === 24).map(i => (i === 24 || i === 0 ? 12 : i));\n        }\n      }\n      startIndex = 1;\n    }\n    this.hourRange = makeRange(hourRanges, this.nzHourStep, startIndex).map(r => ({\n      index: r,\n      disabled: !!disabledHours && disabledHours.indexOf(r) !== -1\n    }));\n    if (this.nzUse12Hours && this.hourRange[this.hourRange.length - 1].index === 12) {\n      const temp = [...this.hourRange];\n      temp.unshift(temp[temp.length - 1]);\n      temp.splice(temp.length - 1, 1);\n      this.hourRange = temp;\n    }\n  }\n\n  buildMinutes(): void {\n    this.minuteRange = makeRange(60, this.nzMinuteStep).map(r => ({\n      index: r,\n      disabled: !!this.nzDisabledMinutes && this.nzDisabledMinutes(this.time.hours!).indexOf(r) !== -1\n    }));\n  }\n\n  buildSeconds(): void {\n    this.secondRange = makeRange(60, this.nzSecondStep).map(r => ({\n      index: r,\n      disabled:\n        !!this.nzDisabledSeconds && this.nzDisabledSeconds(this.time.hours!, this.time.minutes!).indexOf(r) !== -1\n    }));\n  }\n\n  build12Hours(): void {\n    const isUpperFormat = this._format.includes('A');\n    this.use12HoursRange = [\n      {\n        index: 0,\n        value: isUpperFormat ? 'AM' : 'am'\n      },\n      {\n        index: 1,\n        value: isUpperFormat ? 'PM' : 'pm'\n      }\n    ];\n  }\n\n  buildTimes(): void {\n    this.buildHours();\n    this.buildMinutes();\n    this.buildSeconds();\n    this.build12Hours();\n  }\n\n  scrollToTime(delay: number = 0): void {\n    if (this.hourEnabled && this.hourListElement) {\n      this.scrollToSelected(this.hourListElement.nativeElement, this.time.viewHours!, delay, 'hour');\n    }\n    if (this.minuteEnabled && this.minuteListElement) {\n      this.scrollToSelected(this.minuteListElement.nativeElement, this.time.minutes!, delay, 'minute');\n    }\n    if (this.secondEnabled && this.secondListElement) {\n      this.scrollToSelected(this.secondListElement.nativeElement, this.time.seconds!, delay, 'second');\n    }\n    if (this.nzUse12Hours && this.use12HoursListElement) {\n      const selectedHours = this.time.selected12Hours;\n      const index = selectedHours === 'AM' ? 0 : 1;\n      this.scrollToSelected(this.use12HoursListElement.nativeElement, index, delay, '12-hour');\n    }\n  }\n\n  selectHour(hour: { index: number; disabled: boolean }): void {\n    this.time.setHours(hour.index, hour.disabled);\n    if (this._disabledMinutes) {\n      this.buildMinutes();\n    }\n    if (this._disabledSeconds || this._disabledMinutes) {\n      this.buildSeconds();\n    }\n  }\n\n  selectMinute(minute: { index: number; disabled: boolean }): void {\n    this.time.setMinutes(minute.index, minute.disabled);\n    if (this._disabledSeconds) {\n      this.buildSeconds();\n    }\n  }\n\n  selectSecond(second: { index: number; disabled: boolean }): void {\n    this.time.setSeconds(second.index, second.disabled);\n  }\n\n  select12Hours(value: { index: number; value: string }): void {\n    this.time.setSelected12Hours(value.value);\n    if (this._disabledHours) {\n      this.buildHours();\n    }\n    if (this._disabledMinutes) {\n      this.buildMinutes();\n    }\n    if (this._disabledSeconds) {\n      this.buildSeconds();\n    }\n  }\n\n  scrollToSelected(instance: HTMLElement, index: number, duration: number = 0, unit: NzTimePickerUnit): void {\n    if (!instance) {\n      return;\n    }\n    const transIndex = this.translateIndex(index, unit);\n    const currentOption = (instance.children[transIndex] || instance.children[0]) as HTMLElement;\n    this.scrollTo(instance, currentOption.offsetTop, duration);\n  }\n\n  translateIndex(index: number, unit: NzTimePickerUnit): number {\n    if (unit === 'hour') {\n      return this.calcIndex(this.nzDisabledHours?.(), this.hourRange.map(item => item.index).indexOf(index));\n    } else if (unit === 'minute') {\n      return this.calcIndex(\n        this.nzDisabledMinutes?.(this.time.hours!),\n        this.minuteRange.map(item => item.index).indexOf(index)\n      );\n    } else if (unit === 'second') {\n      // second\n      return this.calcIndex(\n        this.nzDisabledSeconds?.(this.time.hours!, this.time.minutes!),\n        this.secondRange.map(item => item.index).indexOf(index)\n      );\n    } else {\n      // 12-hour\n      return this.calcIndex([], this.use12HoursRange.map(item => item.index).indexOf(index));\n    }\n  }\n\n  scrollTo(element: HTMLElement, to: number, duration: number): void {\n    if (duration <= 0) {\n      element.scrollTop = to;\n      return;\n    }\n    const difference = to - element.scrollTop;\n    const perTick = (difference / duration) * 10;\n\n    this.ngZone.runOutsideAngular(() => {\n      reqAnimFrame(() => {\n        element.scrollTop = element.scrollTop + perTick;\n        if (element.scrollTop === to) {\n          return;\n        }\n        this.scrollTo(element, to, duration - 10);\n      });\n    });\n  }\n\n  calcIndex(array: number[] | undefined, index: number): number {\n    if (array?.length && this.nzHideDisabledOptions) {\n      return index - array.reduce((pre, value) => pre + (value < index ? 1 : 0), 0);\n    } else {\n      return index;\n    }\n  }\n\n  protected changed(): void {\n    if (this.onChange) {\n      this.onChange(this.time.value!);\n    }\n  }\n\n  protected touched(): void {\n    if (this.onTouch) {\n      this.onTouch();\n    }\n  }\n\n  timeDisabled(value: Date): boolean {\n    const hour = value.getHours();\n    const minute = value.getMinutes();\n    const second = value.getSeconds();\n    return (\n      (this.nzDisabledHours?.().indexOf(hour) ?? -1) > -1 ||\n      (this.nzDisabledMinutes?.(hour).indexOf(minute) ?? -1) > -1 ||\n      (this.nzDisabledSeconds?.(hour, minute).indexOf(second) ?? -1) > -1\n    );\n  }\n\n  onClickNow(): void {\n    const now = new Date();\n    if (this.timeDisabled(now)) {\n      return;\n    }\n    this.time.setValue(now);\n    this.changed();\n    this.closePanel.emit();\n  }\n\n  onClickOk(): void {\n    this.time.setValue(this.time.value, this.nzUse12Hours);\n    this.changed();\n    this.closePanel.emit();\n  }\n\n  isSelectedHour(hour: { index: number; disabled: boolean }): boolean {\n    return hour.index === this.time.viewHours;\n  }\n\n  isSelectedMinute(minute: { index: number; disabled: boolean }): boolean {\n    return minute.index === this.time.minutes;\n  }\n\n  isSelectedSecond(second: { index: number; disabled: boolean }): boolean {\n    return second.index === this.time.seconds;\n  }\n\n  isSelected12Hours(value: { index: number; value: string }): boolean {\n    return value.value.toUpperCase() === this.time.selected12Hours;\n  }\n\n  constructor(\n    private ngZone: NgZone,\n    private cdr: ChangeDetectorRef,\n    public dateHelper: DateHelperService,\n    private elementRef: ElementRef<HTMLElement>\n  ) {}\n\n  ngOnInit(): void {\n    this.time.changes.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.changed();\n      this.touched();\n      this.scrollToTime(120);\n    });\n    this.buildTimes();\n\n    this.ngZone.runOutsideAngular(() => {\n      setTimeout(() => {\n        this.scrollToTime();\n        this.firstScrolled = true;\n      });\n    });\n\n    fromEventOutsideAngular(this.elementRef.nativeElement, 'mousedown')\n      .pipe(takeUntil(this.unsubscribe$))\n      .subscribe(event => {\n        event.preventDefault();\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzUse12Hours, nzDefaultOpenValue } = changes;\n    if (!nzUse12Hours?.previousValue && nzUse12Hours?.currentValue) {\n      this.build12Hours();\n      this.enabledColumns++;\n    }\n    if (nzDefaultOpenValue?.currentValue) {\n      this.time.setDefaultOpenValue(this.nzDefaultOpenValue || new Date());\n    }\n  }\n\n  writeValue(value: Date): void {\n    this.time.setValue(value, this.nzUse12Hours);\n    this.buildTimes();\n\n    if (value && this.firstScrolled) {\n      this.scrollToTime(120);\n    }\n    // Mark this component to be checked manually with internal properties changing (see: https://github.com/angular/angular/issues/10816)\n    this.cdr.markForCheck();\n  }\n\n  registerOnChange(fn: (value: Date) => void): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this.onTouch = fn;\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { ConnectionPositionPair, OverlayModule } from '@angular/cdk/overlay';\nimport { Platform, _getEventTarget } from '@angular/cdk/platform';\nimport { AsyncPipe } from '@angular/common';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnInit,\n  Output,\n  Renderer2,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  computed,\n  forwardRef,\n  inject,\n  signal\n} from '@angular/core';\nimport { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Observable, of } from 'rxjs';\nimport { distinctUntilChanged, map, takeUntil, withLatestFrom } from 'rxjs/operators';\n\nimport { isValid } from 'date-fns';\n\nimport { slideMotion } from 'ng-zorro-antd/core/animation';\nimport { NzConfigKey, NzConfigService, WithConfig } from 'ng-zorro-antd/core/config';\nimport { NzFormItemFeedbackIconComponent, NzFormNoStatusService, NzFormStatusService } from 'ng-zorro-antd/core/form';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzOverlayModule } from 'ng-zorro-antd/core/overlay';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { NgClassInterface, NzSafeAny, NzSizeLDSType, NzStatus, NzValidateStatus } from 'ng-zorro-antd/core/types';\nimport { getStatusClassNames, isNil } from 'ng-zorro-antd/core/util';\nimport { DateHelperService, NzI18nInterface, NzI18nService } from 'ng-zorro-antd/i18n';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NZ_SPACE_COMPACT_ITEM_TYPE, NZ_SPACE_COMPACT_SIZE, NzSpaceCompactItemDirective } from 'ng-zorro-antd/space';\n\nimport { NzTimePickerPanelComponent } from './time-picker-panel.component';\n\nconst NZ_CONFIG_MODULE_NAME: NzConfigKey = 'timePicker';\n\n@Component({\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  selector: 'nz-time-picker',\n  exportAs: 'nzTimePicker',\n  template: `\n    <div class=\"ant-picker-input\">\n      <input\n        #inputElement\n        [attr.id]=\"nzId\"\n        type=\"text\"\n        [size]=\"inputSize\"\n        autocomplete=\"off\"\n        [placeholder]=\"nzPlaceHolder || (i18nPlaceHolder$ | async)\"\n        [(ngModel)]=\"inputValue\"\n        [disabled]=\"nzDisabled\"\n        [readOnly]=\"nzInputReadOnly\"\n        (focus)=\"onFocus(true)\"\n        (blur)=\"onFocus(false)\"\n        (keyup.enter)=\"onKeyupEnter()\"\n        (keyup.escape)=\"onKeyupEsc()\"\n        (ngModelChange)=\"onInputChange($event)\"\n      />\n      <span class=\"ant-picker-suffix\">\n        <ng-container *nzStringTemplateOutlet=\"nzSuffixIcon; let suffixIcon\">\n          <nz-icon [nzType]=\"suffixIcon\" />\n        </ng-container>\n        @if (hasFeedback && !!status) {\n          <nz-form-item-feedback-icon [status]=\"status\"></nz-form-item-feedback-icon>\n        }\n      </span>\n      @if (nzAllowEmpty && !nzDisabled && value) {\n        <span class=\"ant-picker-clear\" (click)=\"onClickClearBtn($event)\">\n          <nz-icon nzType=\"close-circle\" nzTheme=\"fill\" [attr.aria-label]=\"nzClearText\" [attr.title]=\"nzClearText\" />\n        </span>\n      }\n    </div>\n\n    <ng-template\n      cdkConnectedOverlay\n      nzConnectedOverlay\n      [cdkConnectedOverlayHasBackdrop]=\"nzBackdrop\"\n      [cdkConnectedOverlayPositions]=\"overlayPositions\"\n      [cdkConnectedOverlayOrigin]=\"origin\"\n      [cdkConnectedOverlayOpen]=\"nzOpen\"\n      [cdkConnectedOverlayTransformOriginOn]=\"'.ant-picker-dropdown'\"\n      (detach)=\"close()\"\n      (overlayOutsideClick)=\"onClickOutside($event)\"\n    >\n      <div [@slideMotion]=\"'enter'\" class=\"ant-picker-dropdown\" style=\"position: relative\">\n        <div class=\"ant-picker-panel-container\">\n          <div tabindex=\"-1\" class=\"ant-picker-panel\">\n            <nz-time-picker-panel\n              [class]=\"nzPopupClassName\"\n              [format]=\"nzFormat\"\n              [nzHourStep]=\"nzHourStep\"\n              [nzMinuteStep]=\"nzMinuteStep\"\n              [nzSecondStep]=\"nzSecondStep\"\n              [nzDisabledHours]=\"nzDisabledHours\"\n              [nzDisabledMinutes]=\"nzDisabledMinutes\"\n              [nzDisabledSeconds]=\"nzDisabledSeconds\"\n              [nzPlaceHolder]=\"nzPlaceHolder || (i18nPlaceHolder$ | async)\"\n              [nzHideDisabledOptions]=\"nzHideDisabledOptions\"\n              [nzUse12Hours]=\"nzUse12Hours\"\n              [nzDefaultOpenValue]=\"nzDefaultOpenValue\"\n              [nzAddOn]=\"nzAddOn\"\n              [nzClearText]=\"nzClearText\"\n              [nzNowText]=\"nzNowText\"\n              [nzOkText]=\"nzOkText\"\n              [nzAllowEmpty]=\"nzAllowEmpty\"\n              [(ngModel)]=\"value\"\n              (ngModelChange)=\"onPanelValueChange($event)\"\n              (closePanel)=\"closePanel()\"\n            ></nz-time-picker-panel>\n          </div>\n        </div>\n      </div>\n    </ng-template>\n  `,\n  host: {\n    class: 'ant-picker',\n    '[class.ant-picker-large]': `finalSize() === 'large'`,\n    '[class.ant-picker-small]': `finalSize() === 'small'`,\n    '[class.ant-picker-disabled]': `nzDisabled`,\n    '[class.ant-picker-focused]': `focused`,\n    '[class.ant-picker-rtl]': `dir === 'rtl'`,\n    '[class.ant-picker-borderless]': `nzBorderless`,\n    '(click)': 'open()'\n  },\n  hostDirectives: [NzSpaceCompactItemDirective],\n  animations: [slideMotion],\n  providers: [\n    NzDestroyService,\n    { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => NzTimePickerComponent), multi: true },\n    { provide: NZ_SPACE_COMPACT_ITEM_TYPE, useValue: 'picker' }\n  ],\n  imports: [\n    AsyncPipe,\n    FormsModule,\n    NzOutletModule,\n    NzIconModule,\n    NzFormItemFeedbackIconComponent,\n    NzTimePickerPanelComponent,\n    NzOverlayModule,\n    OverlayModule\n  ]\n})\nexport class NzTimePickerComponent implements ControlValueAccessor, OnInit, AfterViewInit, OnChanges {\n  readonly _nzModuleName: NzConfigKey = NZ_CONFIG_MODULE_NAME;\n\n  private _onChange?: (value: Date | null) => void;\n  private _onTouched?: () => void;\n  private destroy$ = inject(NzDestroyService);\n  private isNzDisableFirstChange: boolean = true;\n  isInit = false;\n  focused = false;\n  inputValue: string = '';\n  value: Date | null = null;\n  preValue: Date | null = null;\n  inputSize?: number;\n  i18nPlaceHolder$: Observable<string | undefined> = of(undefined);\n  overlayPositions: ConnectionPositionPair[] = [\n    {\n      offsetY: 3,\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top'\n    },\n    {\n      offsetY: -3,\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom'\n    },\n    {\n      offsetY: 3,\n      originX: 'end',\n      originY: 'bottom',\n      overlayX: 'end',\n      overlayY: 'top'\n    },\n    {\n      offsetY: -3,\n      originX: 'end',\n      originY: 'top',\n      overlayX: 'end',\n      overlayY: 'bottom'\n    }\n  ] as ConnectionPositionPair[];\n  dir: Direction = 'ltr';\n  // status\n  prefixCls: string = 'ant-picker';\n  statusCls: NgClassInterface = {};\n  status: NzValidateStatus = '';\n  hasFeedback: boolean = false;\n\n  get origin(): ElementRef {\n    return this.element;\n  }\n\n  @ViewChild('inputElement', { static: true }) inputRef!: ElementRef<HTMLInputElement>;\n  @Input() nzId: string | null = null;\n  @Input() nzSize: NzSizeLDSType = 'default';\n  @Input() nzStatus: NzStatus = '';\n  @Input() @WithConfig() nzHourStep: number = 1;\n  @Input() @WithConfig() nzMinuteStep: number = 1;\n  @Input() @WithConfig() nzSecondStep: number = 1;\n  @Input() @WithConfig() nzClearText: string = 'clear';\n  @Input() @WithConfig() nzNowText: string = '';\n  @Input() @WithConfig() nzOkText: string = '';\n  @Input() @WithConfig() nzPopupClassName: string = '';\n  @Input() nzPlaceHolder = '';\n  @Input() nzAddOn?: TemplateRef<void>;\n  @Input() nzDefaultOpenValue?: Date;\n  @Input() nzDisabledHours?: () => number[];\n  @Input() nzDisabledMinutes?: (hour: number) => number[];\n  @Input() nzDisabledSeconds?: (hour: number, minute: number) => number[];\n  @Input() @WithConfig() nzFormat: string = 'HH:mm:ss';\n  @Input() nzOpen = false;\n  @Input({ transform: booleanAttribute }) @WithConfig() nzUse12Hours: boolean = false;\n  @Input() @WithConfig() nzSuffixIcon: string | TemplateRef<NzSafeAny> = 'clock-circle';\n\n  @Output() readonly nzOpenChange = new EventEmitter<boolean>();\n\n  @Input({ transform: booleanAttribute }) nzHideDisabledOptions = false;\n  @Input({ transform: booleanAttribute }) @WithConfig() nzAllowEmpty: boolean = true;\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Input({ transform: booleanAttribute }) nzAutoFocus = false;\n  @Input() @WithConfig() nzBackdrop = false;\n  @Input({ transform: booleanAttribute }) nzBorderless: boolean = false;\n  @Input({ transform: booleanAttribute }) nzInputReadOnly: boolean = false;\n\n  emitValue(value: Date | null): void {\n    this.setValue(value, true);\n\n    if (this._onChange) {\n      this._onChange(this.value);\n    }\n\n    if (this._onTouched) {\n      this._onTouched();\n    }\n  }\n\n  setValue(value: Date | null, syncPreValue: boolean = false): void {\n    if (syncPreValue) {\n      this.preValue = isValid(value) ? new Date(value!) : null;\n    }\n    this.value = isValid(value) ? new Date(value!) : null;\n    this.inputValue = this.dateHelper.format(value, this.nzFormat);\n    this.cdr.markForCheck();\n  }\n\n  open(): void {\n    if (this.nzDisabled || this.nzOpen) {\n      return;\n    }\n    this.focus();\n    this.nzOpen = true;\n    this.nzOpenChange.emit(this.nzOpen);\n  }\n\n  close(): void {\n    this.nzOpen = false;\n    this.cdr.markForCheck();\n    this.nzOpenChange.emit(this.nzOpen);\n  }\n\n  updateAutoFocus(): void {\n    if (this.isInit && !this.nzDisabled) {\n      if (this.nzAutoFocus) {\n        this.renderer.setAttribute(this.inputRef.nativeElement, 'autofocus', 'autofocus');\n      } else {\n        this.renderer.removeAttribute(this.inputRef.nativeElement, 'autofocus');\n      }\n    }\n  }\n\n  onClickClearBtn(event: MouseEvent): void {\n    event.stopPropagation();\n    this.emitValue(null);\n  }\n\n  onClickOutside(event: MouseEvent): void {\n    const target = _getEventTarget(event);\n    if (!this.element.nativeElement.contains(target)) {\n      this.setCurrentValueAndClose();\n    }\n  }\n\n  onFocus(value: boolean): void {\n    this.focused = value;\n    if (!value) {\n      if (this.checkTimeValid(this.value)) {\n        this.setCurrentValueAndClose();\n      } else {\n        this.setValue(this.preValue);\n        this.close();\n      }\n    }\n  }\n\n  focus(): void {\n    if (this.inputRef.nativeElement) {\n      this.inputRef.nativeElement.focus();\n    }\n  }\n\n  blur(): void {\n    if (this.inputRef.nativeElement) {\n      this.inputRef.nativeElement.blur();\n    }\n  }\n\n  onKeyupEsc(): void {\n    this.setValue(this.preValue);\n  }\n\n  onKeyupEnter(): void {\n    if (this.nzOpen && isValid(this.value)) {\n      this.setCurrentValueAndClose();\n    } else if (!this.nzOpen) {\n      this.open();\n    }\n  }\n\n  onInputChange(str: string): void {\n    if (!this.platform.TRIDENT && document.activeElement === this.inputRef.nativeElement) {\n      this.open();\n      this.parseTimeString(str);\n    }\n  }\n\n  onPanelValueChange(value: Date): void {\n    this.setValue(value);\n    this.focus();\n  }\n\n  closePanel(): void {\n    this.inputRef.nativeElement.blur();\n  }\n\n  setCurrentValueAndClose(): void {\n    this.emitValue(this.value);\n    this.close();\n  }\n\n  protected finalSize = computed(() => {\n    if (this.compactSize) {\n      return this.compactSize();\n    }\n    return this.size();\n  });\n\n  private size = signal<NzSizeLDSType>(this.nzSize);\n  private compactSize = inject(NZ_SPACE_COMPACT_SIZE, { optional: true });\n  private nzFormStatusService = inject(NzFormStatusService, { optional: true });\n  private nzFormNoStatusService = inject(NzFormNoStatusService, { optional: true });\n\n  constructor(\n    public nzConfigService: NzConfigService,\n    protected i18n: NzI18nService,\n    private element: ElementRef,\n    private renderer: Renderer2,\n    private cdr: ChangeDetectorRef,\n    private dateHelper: DateHelperService,\n    private platform: Platform,\n    private directionality: Directionality\n  ) {}\n\n  ngOnInit(): void {\n    this.nzFormStatusService?.formStatusChanges\n      .pipe(\n        distinctUntilChanged((pre, cur) => {\n          return pre.status === cur.status && pre.hasFeedback === cur.hasFeedback;\n        }),\n        withLatestFrom(this.nzFormNoStatusService ? this.nzFormNoStatusService.noFormStatus : of(false)),\n        map(([{ status, hasFeedback }, noStatus]) => ({ status: noStatus ? '' : status, hasFeedback })),\n        takeUntil(this.destroy$)\n      )\n      .subscribe(({ status, hasFeedback }) => {\n        this.setStatusStyles(status, hasFeedback);\n      });\n\n    this.inputSize = Math.max(8, this.nzFormat.length) + 2;\n    this.i18nPlaceHolder$ = this.i18n.localeChange.pipe(\n      map((nzLocale: NzI18nInterface) => nzLocale.TimePicker.placeholder)\n    );\n\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n    });\n  }\n\n  ngOnChanges({ nzUse12Hours, nzFormat, nzDisabled, nzAutoFocus, nzStatus, nzSize }: SimpleChanges): void {\n    if (nzUse12Hours && !nzUse12Hours.previousValue && nzUse12Hours.currentValue && !nzFormat) {\n      this.nzFormat = 'h:mm:ss a';\n    }\n    if (nzDisabled) {\n      const value = nzDisabled.currentValue;\n      const input = this.inputRef.nativeElement as HTMLInputElement;\n      if (value) {\n        this.renderer.setAttribute(input, 'disabled', '');\n      } else {\n        this.renderer.removeAttribute(input, 'disabled');\n      }\n    }\n    if (nzAutoFocus) {\n      this.updateAutoFocus();\n    }\n    if (nzStatus) {\n      this.setStatusStyles(this.nzStatus, this.hasFeedback);\n    }\n    if (nzSize) {\n      this.size.set(nzSize.currentValue);\n    }\n  }\n\n  parseTimeString(str: string): void {\n    const value = this.dateHelper.parseTime(str, this.nzFormat) || null;\n    if (isValid(value)) {\n      this.value = value;\n      this.cdr.markForCheck();\n    }\n  }\n\n  ngAfterViewInit(): void {\n    this.isInit = true;\n    this.updateAutoFocus();\n  }\n\n  writeValue(time: Date | null | undefined): void {\n    let result: Date | null;\n\n    if (time instanceof Date) {\n      result = time;\n    } else if (isNil(time)) {\n      result = null;\n    } else {\n      warn('Non-Date type is not recommended for time-picker, use \"Date\" type.');\n      result = new Date(time);\n    }\n\n    this.setValue(result, true);\n  }\n\n  registerOnChange(fn: (time: Date | null) => void): void {\n    this._onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this._onTouched = fn;\n  }\n\n  setDisabledState(isDisabled: boolean): void {\n    this.nzDisabled = (this.isNzDisableFirstChange && this.nzDisabled) || isDisabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n\n  private checkTimeValid(value: Date | null): boolean {\n    if (!value) {\n      return true;\n    }\n\n    const disabledHours = this.nzDisabledHours?.();\n    const disabledMinutes = this.nzDisabledMinutes?.(value.getHours());\n    const disabledSeconds = this.nzDisabledSeconds?.(value.getHours(), value.getMinutes());\n\n    return !(\n      disabledHours?.includes(value.getHours()) ||\n      disabledMinutes?.includes(value.getMinutes()) ||\n      disabledSeconds?.includes(value.getSeconds())\n    );\n  }\n\n  private setStatusStyles(status: NzValidateStatus, hasFeedback: boolean): void {\n    // set inner status\n    this.status = status;\n    this.hasFeedback = hasFeedback;\n    this.cdr.markForCheck();\n    // render status if nzStatus is set\n    this.statusCls = getStatusClassNames(this.prefixCls, status, hasFeedback);\n    Object.keys(this.statusCls).forEach(status => {\n      if (this.statusCls[status]) {\n        this.renderer.addClass(this.element.nativeElement, status);\n      } else {\n        this.renderer.removeClass(this.element.nativeElement, status);\n      }\n    });\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzTimePickerPanelComponent } from './time-picker-panel.component';\nimport { NzTimePickerComponent } from './time-picker.component';\n\n@NgModule({\n  imports: [NzTimePickerComponent, NzTimePickerPanelComponent],\n  exports: [NzTimePickerPanelComponent, NzTimePickerComponent]\n})\nexport class NzTimePickerModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './time-picker.component';\nexport * from './time-picker.module';\nexport * from './time-picker-panel.component';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1", "i2", "i3", "i4"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;MAMU,UAAU,CAAA;IACrB,eAAe,GAAuB,SAAS;AACvC,IAAA,MAAM;IACN,WAAW,GAAY,KAAK;AAC5B,IAAA,iBAAiB;AACjB,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;IAEtC,UAAU,CAAC,KAAa,EAAE,QAAiB,EAAA;QACzC,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;YAC5B,IAAI,CAAC,MAAM,EAAE;;AAEf,QAAA,OAAO,IAAI;;IAGb,QAAQ,CAAC,KAAa,EAAE,QAAiB,EAAA;QACvC,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,EAAE;oBACjD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAE,KAAgB,GAAG,EAAE,CAAC;;qBACtC,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,EAAE;AACxD,oBAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;;qBACjB;AACL,oBAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;;;iBAEvB;AACL,gBAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;;YAE5B,IAAI,CAAC,MAAM,EAAE;;AAEf,QAAA,OAAO,IAAI;;IAGb,UAAU,CAAC,KAAa,EAAE,QAAiB,EAAA;QACzC,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;YAC5B,IAAI,CAAC,MAAM,EAAE;;AAEf,QAAA,OAAO,IAAI;;AAGb,IAAA,aAAa,CAAC,KAAc,EAAA;AAC1B,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;AACxB,QAAA,OAAO,IAAI;;AAGb,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;;IAGrC,QAAQ,CAAC,KAAuB,EAAE,UAAoB,EAAA;AACpD,QAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE;AACxB,YAAA,IAAI,CAAC,WAAW,GAAG,UAAqB;;AAE1C,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AACxB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,YAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACxB,IAAI,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC5C,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;;;iBAElD;gBACL,IAAI,CAAC,MAAM,EAAE;;;AAIjB,QAAA,OAAO,IAAI;;IAGb,SAAS,GAAA;AACP,QAAA,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC;;;IAI/C,KAAK,GAAA;QACH,IAAI,CAAC,MAAM,EAAE;QACb,IAAI,CAAC,MAAM,EAAE;;AAGf,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;IAG5E,MAAM,GAAA;AACZ,QAAA,IAAI,CAAC,MAAM,GAAG,SAAS;AACvB,QAAA,IAAI,CAAC,eAAe,GAAG,SAAS;;IAG1B,MAAM,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,GAAG,SAAS;;aAClB;AACL,YAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACxB,IAAI,CAAC,KAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAM,CAAC;;AAGnC,YAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC1B,IAAI,CAAC,KAAM,CAAC,UAAU,CAAC,IAAI,CAAC,OAAQ,CAAC;;AAGvC,YAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC1B,IAAI,CAAC,KAAM,CAAC,UAAU,CAAC,IAAI,CAAC,OAAQ,CAAC;;AAGvC,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,KAAM,GAAG,EAAE,EAAE;oBACrD,IAAI,CAAC,KAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAM,GAAG,EAAE,CAAC;;AAExC,gBAAA,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,KAAM,IAAI,EAAE,EAAE;oBACtD,IAAI,CAAC,KAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAM,GAAG,EAAE,CAAC;;;;QAI5C,IAAI,CAAC,OAAO,EAAE;;IAGhB,OAAO,GAAA;QACL,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;;AAGhC;;;;AAIG;AACH,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAM,CAAC,GAAG,IAAI,CAAC,KAAK;;AAGpG,IAAA,kBAAkB,CAAC,KAAyB,EAAA;QAC1C,IAAI,KAAM,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,eAAe,EAAE;AACjD,YAAA,IAAI,CAAC,eAAe,GAAG,KAAM,CAAC,WAAW,EAAE;YAC3C,IAAI,CAAC,MAAM,EAAE;;;AAIjB,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB;;AAG9C,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE;;AAG/B,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE;;AAGjC,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE;;AAGjC,IAAA,mBAAmB,CAAC,KAAW,EAAA;AAC7B,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;AAC9B,QAAA,OAAO,IAAI;;AAGL,IAAA,iBAAiB,CAAC,KAAa,EAAA;AACrC,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe;QAC5C,IAAI,eAAe,KAAK,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE;YAC1C,OAAO,KAAK,GAAG,EAAE;;QAEnB,IAAI,eAAe,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE;AAC3C,YAAA,OAAO,EAAE;;AAEX,QAAA,OAAO,KAAK;;AAEf;;ACnLD;;;AAGG;AAmCH,SAAS,SAAS,CAAC,MAAc,EAAE,OAAe,CAAC,EAAE,QAAgB,CAAC,EAAA;AACpE,IAAA,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC;AACtF;MAgHa,0BAA0B,CAAA;AA0X3B,IAAA,MAAA;AACA,IAAA,GAAA;AACD,IAAA,UAAA;AACC,IAAA,UAAA;IA5XF,WAAW,GAAG,CAAC;IACf,aAAa,GAAG,CAAC;IACjB,aAAa,GAAG,CAAC;AACjB,IAAA,YAAY,GAAG,IAAI,OAAO,EAAQ;AAClC,IAAA,QAAQ;AACR,IAAA,OAAO;IACP,OAAO,GAAG,UAAU;AACpB,IAAA,cAAc,GAAoB,MAAM,EAAE;AAC1C,IAAA,gBAAgB,GAAgC,MAAM,EAAE;AACxD,IAAA,gBAAgB,GAAgD,MAAM,EAAE;IACxE,WAAW,GAAG,IAAI;AAC1B,IAAA,IAAI,GAAG,IAAI,UAAU,EAAE;IACvB,WAAW,GAAG,IAAI;IAClB,aAAa,GAAG,IAAI;IACpB,aAAa,GAAG,IAAI;IACpB,aAAa,GAAG,KAAK;IACrB,cAAc,GAAG,CAAC;AAClB,IAAA,SAAS;AACT,IAAA,WAAW;AACX,IAAA,WAAW;AACX,IAAA,eAAe;AAGf,IAAA,eAAe;AACoC,IAAA,iBAAiB;AACjB,IAAA,iBAAiB;AACb,IAAA,qBAAqB;AAEpC,IAAA,cAAc,GAAY,KAAK,CAAC;AAC/D,IAAA,OAAO;IACP,qBAAqB,GAAG,KAAK;AAC7B,IAAA,WAAW;AACX,IAAA,SAAS;AACT,IAAA,QAAQ;AACR,IAAA,aAAa;IACkB,YAAY,GAAG,KAAK;AACnD,IAAA,kBAAkB;AAER,IAAA,UAAU,GAAG,IAAI,YAAY,EAAQ;IAExD,IACI,YAAY,CAAC,KAAc,EAAA;AAC7B,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;;AAG1B,IAAA,IAAI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,WAAW;;IAGzB,IACI,eAAe,CAAC,KAAmC,EAAA;AACrD,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC3B,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,UAAU,EAAE;;;AAIrB,IAAA,IAAI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,cAAc;;IAG5B,IACI,iBAAiB,CAAC,KAA+C,EAAA;AACnE,QAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACnB,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAAC,YAAY,EAAE;;;AAIvB,IAAA,IAAI,iBAAiB,GAAA;QACnB,OAAO,IAAI,CAAC,gBAAgB;;IAG9B,IACI,iBAAiB,CAAC,KAA+D,EAAA;AACnF,QAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACnB,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAAC,YAAY,EAAE;;;AAIvB,IAAA,IAAI,iBAAiB,GAAA;QACnB,OAAO,IAAI,CAAC,gBAAgB;;IAG9B,IACI,MAAM,CAAC,KAAa,EAAA;AACtB,QAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACnB,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACpB,YAAA,IAAI,CAAC,cAAc,GAAG,CAAC;AACvB,YAAA,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC;AAC9B,YAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;YACvD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;YACrC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;AACrC,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,cAAc,EAAE;;AAEvB,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,IAAI,CAAC,cAAc,EAAE;;AAEvB,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,IAAI,CAAC,cAAc,EAAE;;AAEvB,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,EAAE;;;;AAKzB,IAAA,IAAI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO;;IAGrB,IACI,UAAU,CAAC,KAAa,EAAA;AAC1B,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,IAAI,CAAC;QAC7B,IAAI,CAAC,UAAU,EAAE;;AAGnB,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;IAGzB,IACI,YAAY,CAAC,KAAa,EAAA;AAC5B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,IAAI,CAAC;QAC/B,IAAI,CAAC,YAAY,EAAE;;AAGrB,IAAA,IAAI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,aAAa;;IAG3B,IACI,YAAY,CAAC,KAAa,EAAA;AAC5B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,IAAI,CAAC;QAC/B,IAAI,CAAC,YAAY,EAAE;;AAGrB,IAAA,IAAI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,aAAa;;IAG3B,UAAU,GAAA;QACR,IAAI,UAAU,GAAG,EAAE;AACnB,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,eAAe,IAAI;QAC5C,IAAI,UAAU,GAAG,CAAC;AAClB,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,UAAU,GAAG,EAAE;YACf,IAAI,aAAa,EAAE;gBACjB,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;AACtC;;;AAGG;AACH,oBAAA,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;;qBAC7E;AACL;;;AAGG;AACH,oBAAA,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;;;YAG1G,UAAU,GAAG,CAAC;;QAEhB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK;AAC5E,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,QAAQ,EAAE,CAAC,CAAC,aAAa,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;AAC5D,SAAA,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,EAAE;YAC/E,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAChC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;AAC/B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;;IAIzB,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK;AAC5D,YAAA,KAAK,EAAE,CAAC;YACR,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;AAChG,SAAA,CAAC,CAAC;;IAGL,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK;AAC5D,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,QAAQ,EACN,CAAC,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAM,EAAE,IAAI,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;AAC5G,SAAA,CAAC,CAAC;;IAGL,YAAY,GAAA;QACV,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;QAChD,IAAI,CAAC,eAAe,GAAG;AACrB,YAAA;AACE,gBAAA,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,aAAa,GAAG,IAAI,GAAG;AAC/B,aAAA;AACD,YAAA;AACE,gBAAA,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,aAAa,GAAG,IAAI,GAAG;AAC/B;SACF;;IAGH,UAAU,GAAA;QACR,IAAI,CAAC,UAAU,EAAE;QACjB,IAAI,CAAC,YAAY,EAAE;QACnB,IAAI,CAAC,YAAY,EAAE;QACnB,IAAI,CAAC,YAAY,EAAE;;IAGrB,YAAY,CAAC,QAAgB,CAAC,EAAA;QAC5B,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,EAAE;AAC5C,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,SAAU,EAAE,KAAK,EAAE,MAAM,CAAC;;QAEhG,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAChD,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,OAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC;;QAElG,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAChD,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,OAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC;;QAElG,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,qBAAqB,EAAE;AACnD,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe;AAC/C,YAAA,MAAM,KAAK,GAAG,aAAa,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAC5C,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC;;;AAI5F,IAAA,UAAU,CAAC,IAA0C,EAAA;AACnD,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC;AAC7C,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,YAAY,EAAE;;QAErB,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAClD,IAAI,CAAC,YAAY,EAAE;;;AAIvB,IAAA,YAAY,CAAC,MAA4C,EAAA;AACvD,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC;AACnD,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,YAAY,EAAE;;;AAIvB,IAAA,YAAY,CAAC,MAA4C,EAAA;AACvD,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC;;AAGrD,IAAA,aAAa,CAAC,KAAuC,EAAA;QACnD,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAC;AACzC,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,UAAU,EAAE;;AAEnB,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,YAAY,EAAE;;AAErB,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,YAAY,EAAE;;;IAIvB,gBAAgB,CAAC,QAAqB,EAAE,KAAa,EAAE,QAAmB,GAAA,CAAC,EAAE,IAAsB,EAAA;QACjG,IAAI,CAAC,QAAQ,EAAE;YACb;;QAEF,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC;AACnD,QAAA,MAAM,aAAa,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAgB;QAC5F,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC;;IAG5D,cAAc,CAAC,KAAa,EAAE,IAAsB,EAAA;AAClD,QAAA,IAAI,IAAI,KAAK,MAAM,EAAE;AACnB,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;AACjG,aAAA,IAAI,IAAI,KAAK,QAAQ,EAAE;AAC5B,YAAA,OAAO,IAAI,CAAC,SAAS,CACnB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,KAAM,CAAC,EAC1C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CACxD;;AACI,aAAA,IAAI,IAAI,KAAK,QAAQ,EAAE;;AAE5B,YAAA,OAAO,IAAI,CAAC,SAAS,CACnB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,KAAM,EAAE,IAAI,CAAC,IAAI,CAAC,OAAQ,CAAC,EAC9D,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CACxD;;aACI;;YAEL,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;;AAI1F,IAAA,QAAQ,CAAC,OAAoB,EAAE,EAAU,EAAE,QAAgB,EAAA;AACzD,QAAA,IAAI,QAAQ,IAAI,CAAC,EAAE;AACjB,YAAA,OAAO,CAAC,SAAS,GAAG,EAAE;YACtB;;AAEF,QAAA,MAAM,UAAU,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS;QACzC,MAAM,OAAO,GAAG,CAAC,UAAU,GAAG,QAAQ,IAAI,EAAE;AAE5C,QAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAK;YACjC,YAAY,CAAC,MAAK;gBAChB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,OAAO;AAC/C,gBAAA,IAAI,OAAO,CAAC,SAAS,KAAK,EAAE,EAAE;oBAC5B;;gBAEF,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,EAAE,QAAQ,GAAG,EAAE,CAAC;AAC3C,aAAC,CAAC;AACJ,SAAC,CAAC;;IAGJ,SAAS,CAAC,KAA2B,EAAE,KAAa,EAAA;QAClD,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC/C,YAAA,OAAO,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK,GAAG,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;aACxE;AACL,YAAA,OAAO,KAAK;;;IAIN,OAAO,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAM,CAAC;;;IAIzB,OAAO,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,EAAE;;;AAIlB,IAAA,YAAY,CAAC,KAAW,EAAA;AACtB,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE;AAC7B,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,EAAE;AACjC,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,EAAE;AACjC,QAAA,QACE,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACnD,YAAA,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;YAC3D,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;;IAIvE,UAAU,GAAA;AACR,QAAA,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE;AACtB,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;YAC1B;;AAEF,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QACvB,IAAI,CAAC,OAAO,EAAE;AACd,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;;IAGxB,SAAS,GAAA;AACP,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC;QACtD,IAAI,CAAC,OAAO,EAAE;AACd,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;;AAGxB,IAAA,cAAc,CAAC,IAA0C,EAAA;QACvD,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,SAAS;;AAG3C,IAAA,gBAAgB,CAAC,MAA4C,EAAA;QAC3D,OAAO,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO;;AAG3C,IAAA,gBAAgB,CAAC,MAA4C,EAAA;QAC3D,OAAO,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO;;AAG3C,IAAA,iBAAiB,CAAC,KAAuC,EAAA;AACvD,QAAA,OAAO,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,eAAe;;AAGhE,IAAA,WAAA,CACU,MAAc,EACd,GAAsB,EACvB,UAA6B,EAC5B,UAAmC,EAAA;QAHnC,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAG,CAAA,GAAA,GAAH,GAAG;QACJ,IAAU,CAAA,UAAA,GAAV,UAAU;QACT,IAAU,CAAA,UAAA,GAAV,UAAU;;IAGpB,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YAClE,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;AACxB,SAAC,CAAC;QACF,IAAI,CAAC,UAAU,EAAE;AAEjB,QAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAK;YACjC,UAAU,CAAC,MAAK;gBACd,IAAI,CAAC,YAAY,EAAE;AACnB,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI;AAC3B,aAAC,CAAC;AACJ,SAAC,CAAC;QAEF,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,WAAW;AAC/D,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC;aACjC,SAAS,CAAC,KAAK,IAAG;YACjB,KAAK,CAAC,cAAc,EAAE;AACxB,SAAC,CAAC;;IAGN,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AACxB,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;;AAG9B,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,EAAE,YAAY,EAAE,kBAAkB,EAAE,GAAG,OAAO;QACpD,IAAI,CAAC,YAAY,EAAE,aAAa,IAAI,YAAY,EAAE,YAAY,EAAE;YAC9D,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,cAAc,EAAE;;AAEvB,QAAA,IAAI,kBAAkB,EAAE,YAAY,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,IAAI,EAAE,CAAC;;;AAIxE,IAAA,UAAU,CAAC,KAAW,EAAA;QACpB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC;QAC5C,IAAI,CAAC,UAAU,EAAE;AAEjB,QAAA,IAAI,KAAK,IAAI,IAAI,CAAC,aAAa,EAAE;AAC/B,YAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;;;AAGxB,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,IAAA,gBAAgB,CAAC,EAAyB,EAAA;AACxC,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAGpB,IAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,QAAA,IAAI,CAAC,OAAO,GAAG,EAAE;;uGAtbR,0BAA0B,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAA1B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,0BAA0B,EA6BjB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAgB,CAOhB,EAAA,OAAA,EAAA,SAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,aAAA,EAAA,eAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,4FAKhB,gBAAgB,CAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAyEhB,eAAe,CAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAUf,eAAe,CAUf,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,eAAe,CAzIxB,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,sCAAA,EAAA,yCAAA,EAAA,sCAAA,EAAA,yCAAA,EAAA,sCAAA,EAAA,yCAAA,EAAA,sCAAA,EAAA,yCAAA,EAAA,oCAAA,EAAA,oBAAA,EAAA,kDAAA,EAAA,iBAAA,EAAA,EAAA,cAAA,EAAA,uBAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,0BAA0B,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EApGzG,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,uBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAWS,WAAW,EAAE,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,EAAE,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,qFAAE,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAC,IAAA,CAAA,iBAAA,EAAA,QAAA,EAAA,iCAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,QAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,2BAAA,EAAA,QAAA,EAAA,8IAAA,EAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,2EAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE1D,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBA5GtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,uBAAuB;AAC9B,wBAAA,wCAAwC,EAAE,CAAyC,uCAAA,CAAA;AACnF,wBAAA,wCAAwC,EAAE,CAAyC,uCAAA,CAAA;AACnF,wBAAA,wCAAwC,EAAE,CAAyC,uCAAA,CAAA;AACnF,wBAAA,wCAAwC,EAAE,CAAyC,uCAAA,CAAA;AACnF,wBAAA,sCAAsC,EAAE,CAAoB,kBAAA,CAAA;AAC5D,wBAAA,oDAAoD,EAAE,CAAiB,eAAA;AACxE,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,CAAC,gCAAgC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;oBACnH,OAAO,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,YAAY,EAAE,cAAc;AACtE,iBAAA;oKAyBC,eAAe,EAAA,CAAA;sBADd,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBAEI,iBAAiB,EAAA,CAAA;sBAAnE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,mBAAmB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACE,iBAAiB,EAAA,CAAA;sBAAnE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,mBAAmB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACM,qBAAqB,EAAA,CAAA;sBAA3E,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,uBAAuB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBAEb,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,OAAO,EAAA,CAAA;sBAAf;gBACQ,qBAAqB,EAAA,CAAA;sBAA7B;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACuC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,kBAAkB,EAAA,CAAA;sBAA1B;gBAEkB,UAAU,EAAA,CAAA;sBAA5B;gBAGG,YAAY,EAAA,CAAA;sBADf,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAUlC,eAAe,EAAA,CAAA;sBADlB;gBAaG,iBAAiB,EAAA,CAAA;sBADpB;gBAaG,iBAAiB,EAAA,CAAA;sBADpB;gBAaG,MAAM,EAAA,CAAA;sBADT;gBA6BG,UAAU,EAAA,CAAA;sBADb,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBAWjC,YAAY,EAAA,CAAA;sBADf,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBAWjC,YAAY,EAAA,CAAA;sBADf,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;;;AC1OvC,MAAM,qBAAqB,GAAgB,YAAY;IA6G1C,qBAAqB,GAAA,CAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iBAArB,qBAAqB,CAAA;;;AA2DtB,YAAA,sBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,wBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,wBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,uBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,qBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,oBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,4BAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAOZ,YAAA,oBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAEmB,YAAA,wBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAC3C,YAAA,wBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAKmB,YAAA,wBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAG3C,YAAA,sBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;YAxBC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,YAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,UAAU,EAAV,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,UAAU,GAAa,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,wBAAA,EAAA,6BAAA,CAAA;YACvB,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,wBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,cAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,cAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,YAAY,EAAZ,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,YAAY,GAAa,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,0BAAA,EAAA,+BAAA,CAAA;YACzB,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,wBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,cAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,cAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,YAAY,EAAZ,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,YAAY,GAAa,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,0BAAA,EAAA,+BAAA,CAAA;YACzB,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,uBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,aAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,aAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,WAAW,EAAX,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,WAAW,GAAmB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,yBAAA,EAAA,8BAAA,CAAA;YAC9B,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,qBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,WAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,WAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,SAAS,EAAT,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,SAAS,GAAc,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,uBAAA,EAAA,4BAAA,CAAA;YACvB,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,oBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,UAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,UAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,QAAQ,EAAR,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,QAAQ,GAAc,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,sBAAA,EAAA,2BAAA,CAAA;YACtB,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,4BAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,kBAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,gBAAgB,EAAhB,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,gBAAgB,GAAc,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,8BAAA,EAAA,mCAAA,CAAA;YAO9B,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,oBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,UAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,UAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,QAAQ,EAAR,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,QAAQ,GAAsB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,sBAAA,EAAA,2BAAA,CAAA;YAEC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,wBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,cAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,cAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,YAAY,EAAZ,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,YAAY,GAAkB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,0BAAA,EAAA,+BAAA,CAAA;YAC7D,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,wBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,cAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,cAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,YAAY,EAAZ,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,YAAY,GAAmD,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,0BAAA,EAAA,+BAAA,CAAA;YAKhC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,wBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,cAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,cAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,YAAY,EAAZ,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,YAAY,GAAiB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,0BAAA,EAAA,+BAAA,CAAA;YAG5D,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,YAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,UAAU,EAAV,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,UAAU,GAAS,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,wBAAA,EAAA,6BAAA,CAAA;;;QAoIjC,eAAe;QACZ,IAAI;QACN,OAAO;QACP,QAAQ;QACR,GAAG;QACH,UAAU;QACV,QAAQ;QACR,cAAc;QA7Nf,aAAa,GAAgB,qBAAqB;AAEnD,QAAA,SAAS;AACT,QAAA,UAAU;AACV,QAAA,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC;QACnC,sBAAsB,GAAY,IAAI;QAC9C,MAAM,GAAG,KAAK;QACd,OAAO,GAAG,KAAK;QACf,UAAU,GAAW,EAAE;QACvB,KAAK,GAAgB,IAAI;QACzB,QAAQ,GAAgB,IAAI;AAC5B,QAAA,SAAS;AACT,QAAA,gBAAgB,GAAmC,EAAE,CAAC,SAAS,CAAC;AAChE,QAAA,gBAAgB,GAA6B;AAC3C,YAAA;AACE,gBAAA,OAAO,EAAE,CAAC;AACV,gBAAA,OAAO,EAAE,OAAO;AAChB,gBAAA,OAAO,EAAE,QAAQ;AACjB,gBAAA,QAAQ,EAAE,OAAO;AACjB,gBAAA,QAAQ,EAAE;AACX,aAAA;AACD,YAAA;gBACE,OAAO,EAAE,CAAC,CAAC;AACX,gBAAA,OAAO,EAAE,OAAO;AAChB,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,QAAQ,EAAE,OAAO;AACjB,gBAAA,QAAQ,EAAE;AACX,aAAA;AACD,YAAA;AACE,gBAAA,OAAO,EAAE,CAAC;AACV,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,OAAO,EAAE,QAAQ;AACjB,gBAAA,QAAQ,EAAE,KAAK;AACf,gBAAA,QAAQ,EAAE;AACX,aAAA;AACD,YAAA;gBACE,OAAO,EAAE,CAAC,CAAC;AACX,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,QAAQ,EAAE,KAAK;AACf,gBAAA,QAAQ,EAAE;AACX;SAC0B;QAC7B,GAAG,GAAc,KAAK;;QAEtB,SAAS,GAAW,YAAY;QAChC,SAAS,GAAqB,EAAE;QAChC,MAAM,GAAqB,EAAE;QAC7B,WAAW,GAAY,KAAK;AAE5B,QAAA,IAAI,MAAM,GAAA;YACR,OAAO,IAAI,CAAC,OAAO;;AAGwB,QAAA,QAAQ;QAC5C,IAAI,GAAkB,IAAI;QAC1B,MAAM,GAAkB,SAAS;QACjC,QAAQ,GAAa,EAAE;QACT,UAAU,GAAA,iBAAA,CAAA,IAAA,EAAA,wBAAA,EAAW,CAAC,CAAC;QACvB,YAAY,IAAA,iBAAA,CAAA,IAAA,EAAA,6BAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,0BAAA,EAAW,CAAC,CAAC;QACzB,YAAY,IAAA,iBAAA,CAAA,IAAA,EAAA,+BAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,0BAAA,EAAW,CAAC,CAAC;QACzB,WAAW,IAAA,iBAAA,CAAA,IAAA,EAAA,+BAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,yBAAA,EAAW,OAAO,CAAC;QAC9B,SAAS,IAAA,iBAAA,CAAA,IAAA,EAAA,8BAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,uBAAA,EAAW,EAAE,CAAC;QACvB,QAAQ,IAAA,iBAAA,CAAA,IAAA,EAAA,4BAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,sBAAA,EAAW,EAAE,CAAC;QACtB,gBAAgB,IAAA,iBAAA,CAAA,IAAA,EAAA,2BAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,8BAAA,EAAW,EAAE,CAAC;QAC5C,aAAa,IAAA,iBAAA,CAAA,IAAA,EAAA,mCAAA,CAAA,EAAG,EAAE;AAClB,QAAA,OAAO;AACP,QAAA,kBAAkB;AAClB,QAAA,eAAe;AACf,QAAA,iBAAiB;AACjB,QAAA,iBAAiB;QACH,QAAQ,GAAA,iBAAA,CAAA,IAAA,EAAA,sBAAA,EAAW,UAAU,CAAC;QAC5C,MAAM,IAAA,iBAAA,CAAA,IAAA,EAAA,2BAAA,CAAA,EAAG,KAAK;QAC+B,YAAY,GAAA,iBAAA,CAAA,IAAA,EAAA,0BAAA,EAAY,KAAK,CAAC;QAC7D,YAAY,IAAA,iBAAA,CAAA,IAAA,EAAA,+BAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,0BAAA,EAAoC,cAAc,CAAC;AAEnE,QAAA,YAAY,IAAG,iBAAA,CAAA,IAAA,EAAA,+BAAA,CAAA,EAAA,IAAI,YAAY,EAAW;QAErB,qBAAqB,GAAG,KAAK;QACf,YAAY,GAAA,iBAAA,CAAA,IAAA,EAAA,0BAAA,EAAY,IAAI,CAAC;QAC3C,UAAU,IAAA,iBAAA,CAAA,IAAA,EAAA,+BAAA,CAAA,EAAG,KAAK;QAClB,WAAW,GAAG,KAAK;QACpC,UAAU,GAAA,iBAAA,CAAA,IAAA,EAAA,wBAAA,EAAG,KAAK,CAAC;QACF,YAAY,IAAA,iBAAA,CAAA,IAAA,EAAA,6BAAA,CAAA,EAAY,KAAK;QAC7B,eAAe,GAAY,KAAK;AAExE,QAAA,SAAS,CAAC,KAAkB,EAAA;AAC1B,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC;AAE1B,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;;AAG5B,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,UAAU,EAAE;;;AAIrB,QAAA,QAAQ,CAAC,KAAkB,EAAE,YAAA,GAAwB,KAAK,EAAA;YACxD,IAAI,YAAY,EAAE;AAChB,gBAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,KAAM,CAAC,GAAG,IAAI;;AAE1D,YAAA,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,KAAM,CAAC,GAAG,IAAI;AACrD,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC;AAC9D,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;QAGzB,IAAI,GAAA;YACF,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,EAAE;gBAClC;;YAEF,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI;YAClB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;;QAGrC,KAAK,GAAA;AACH,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;YACvB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;;QAGrC,eAAe,GAAA;YACb,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACnC,gBAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,oBAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,WAAW,EAAE,WAAW,CAAC;;qBAC5E;AACL,oBAAA,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,WAAW,CAAC;;;;AAK7E,QAAA,eAAe,CAAC,KAAiB,EAAA;YAC/B,KAAK,CAAC,eAAe,EAAE;AACvB,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;AAGtB,QAAA,cAAc,CAAC,KAAiB,EAAA;AAC9B,YAAA,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC;AACrC,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBAChD,IAAI,CAAC,uBAAuB,EAAE;;;AAIlC,QAAA,OAAO,CAAC,KAAc,EAAA;AACpB,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK;YACpB,IAAI,CAAC,KAAK,EAAE;gBACV,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBACnC,IAAI,CAAC,uBAAuB,EAAE;;qBACzB;AACL,oBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAC5B,IAAI,CAAC,KAAK,EAAE;;;;QAKlB,KAAK,GAAA;AACH,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;AAC/B,gBAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE;;;QAIvC,IAAI,GAAA;AACF,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;AAC/B,gBAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE;;;QAItC,UAAU,GAAA;AACR,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;;QAG9B,YAAY,GAAA;YACV,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACtC,IAAI,CAAC,uBAAuB,EAAE;;AACzB,iBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACvB,IAAI,CAAC,IAAI,EAAE;;;AAIf,QAAA,aAAa,CAAC,GAAW,EAAA;AACvB,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,aAAa,KAAK,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;gBACpF,IAAI,CAAC,IAAI,EAAE;AACX,gBAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;;;AAI7B,QAAA,kBAAkB,CAAC,KAAW,EAAA;AAC5B,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YACpB,IAAI,CAAC,KAAK,EAAE;;QAGd,UAAU,GAAA;AACR,YAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE;;QAGpC,uBAAuB,GAAA;AACrB,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;YAC1B,IAAI,CAAC,KAAK,EAAE;;AAGJ,QAAA,SAAS,GAAG,QAAQ,CAAC,MAAK;AAClC,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,OAAO,IAAI,CAAC,WAAW,EAAE;;AAE3B,YAAA,OAAO,IAAI,CAAC,IAAI,EAAE;AACpB,SAAC,CAAC;AAEM,QAAA,IAAI,GAAG,MAAM,CAAgB,IAAI,CAAC,MAAM,CAAC;QACzC,WAAW,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;QAC/D,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;QACrE,qBAAqB,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAEjF,QAAA,WAAA,CACS,eAAgC,EAC7B,IAAmB,EACrB,OAAmB,EACnB,QAAmB,EACnB,GAAsB,EACtB,UAA6B,EAC7B,QAAkB,EAClB,cAA8B,EAAA;YAP/B,IAAe,CAAA,eAAA,GAAf,eAAe;YACZ,IAAI,CAAA,IAAA,GAAJ,IAAI;YACN,IAAO,CAAA,OAAA,GAAP,OAAO;YACP,IAAQ,CAAA,QAAA,GAAR,QAAQ;YACR,IAAG,CAAA,GAAA,GAAH,GAAG;YACH,IAAU,CAAA,UAAA,GAAV,UAAU;YACV,IAAQ,CAAA,QAAA,GAAR,QAAQ;YACR,IAAc,CAAA,cAAA,GAAd,cAAc;;QAGxB,QAAQ,GAAA;YACN,IAAI,CAAC,mBAAmB,EAAE;iBACvB,IAAI,CACH,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,KAAI;AAChC,gBAAA,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,KAAK,GAAG,CAAC,WAAW;AACzE,aAAC,CAAC,EACF,cAAc,CAAC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EAChG,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,GAAG,EAAE,GAAG,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC,EAC/F,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAEzB,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,KAAI;AACrC,gBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,CAAC;AAC3C,aAAC,CAAC;AAEJ,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC;YACtD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CACjD,GAAG,CAAC,CAAC,QAAyB,KAAK,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,CACpE;YAED,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;YACpC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,gBAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACtB,aAAC,CAAC;;AAGJ,QAAA,WAAW,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAiB,EAAA;AAC9F,YAAA,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,aAAa,IAAI,YAAY,CAAC,YAAY,IAAI,CAAC,QAAQ,EAAE;AACzF,gBAAA,IAAI,CAAC,QAAQ,GAAG,WAAW;;YAE7B,IAAI,UAAU,EAAE;AACd,gBAAA,MAAM,KAAK,GAAG,UAAU,CAAC,YAAY;AACrC,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAiC;gBAC7D,IAAI,KAAK,EAAE;oBACT,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE,CAAC;;qBAC5C;oBACL,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,EAAE,UAAU,CAAC;;;YAGpD,IAAI,WAAW,EAAE;gBACf,IAAI,CAAC,eAAe,EAAE;;YAExB,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC;;YAEvD,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC;;;AAItC,QAAA,eAAe,CAAC,GAAW,EAAA;AACzB,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI;AACnE,YAAA,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;AAClB,gBAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;;QAI3B,eAAe,GAAA;AACb,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI;YAClB,IAAI,CAAC,eAAe,EAAE;;AAGxB,QAAA,UAAU,CAAC,IAA6B,EAAA;AACtC,YAAA,IAAI,MAAmB;AAEvB,YAAA,IAAI,IAAI,YAAY,IAAI,EAAE;gBACxB,MAAM,GAAG,IAAI;;AACR,iBAAA,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtB,MAAM,GAAG,IAAI;;iBACR;gBACL,IAAI,CAAC,oEAAoE,CAAC;AAC1E,gBAAA,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC;;AAGzB,YAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC;;AAG7B,QAAA,gBAAgB,CAAC,EAA+B,EAAA;AAC9C,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB,QAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,YAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;AAGtB,QAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,YAAA,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU;AAChF,YAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;AACnC,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGjB,QAAA,cAAc,CAAC,KAAkB,EAAA;YACvC,IAAI,CAAC,KAAK,EAAE;AACV,gBAAA,OAAO,IAAI;;AAGb,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,IAAI;AAC9C,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;AAClE,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC;YAEtF,OAAO,EACL,aAAa,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;AACzC,gBAAA,eAAe,EAAE,QAAQ,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC7C,eAAe,EAAE,QAAQ,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAC9C;;QAGK,eAAe,CAAC,MAAwB,EAAE,WAAoB,EAAA;;AAEpE,YAAA,IAAI,CAAC,MAAM,GAAG,MAAM;AACpB,YAAA,IAAI,CAAC,WAAW,GAAG,WAAW;AAC9B,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAEvB,YAAA,IAAI,CAAC,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,CAAC;AACzE,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,IAAG;AAC3C,gBAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AAC1B,oBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC;;qBACrD;AACL,oBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC;;AAEjE,aAAC,CAAC;;2GAzVO,qBAAqB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAArB,QAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,qBAAqB,EA0EZ,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,EAAA,YAAA,EAAA,cAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,OAAA,EAAA,SAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAKhB,EAAA,YAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,CAAA,uBAAA,EAAA,uBAAA,EAAA,gBAAgB,kDAChB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAChB,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAChB,gBAAgB,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAEhB,gBAAgB,CAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAChB,gBAAgB,CArGzB,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,QAAA,EAAA,EAAA,UAAA,EAAA,EAAA,wBAAA,EAAA,yBAAA,EAAA,wBAAA,EAAA,yBAAA,EAAA,2BAAA,EAAA,YAAA,EAAA,0BAAA,EAAA,SAAA,EAAA,sBAAA,EAAA,eAAA,EAAA,6BAAA,EAAA,cAAA,EAAA,EAAA,cAAA,EAAA,YAAA,EAAA,EAAA,SAAA,EAAA;gBACT,gBAAgB;AAChB,gBAAA,EAAE,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,qBAAqB,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE;AACjG,gBAAA,EAAE,OAAO,EAAE,0BAA0B,EAAE,QAAQ,EAAE,QAAQ;aAC1D,EA1FS,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,cAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,cAAA,EAAA,CAAA,EAAA,SAAA,EAAA,EAAA,CAAA,2BAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyET,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAmBC,SAAS,EACT,IAAA,EAAA,OAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,WAAW,8mBACX,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EACd,YAAY,EACZ,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,+BAA+B,EAC/B,QAAA,EAAA,4BAAA,EAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,0BAA0B,8ZAC1B,eAAe,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,2BAAA,EAAA,QAAA,EAAA,2CAAA,EAAA,MAAA,EAAA,CAAA,sBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EACf,aAAa,EAdH,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,GAAA,CAAA,mBAAA,EAAA,QAAA,EAAA,qEAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,8BAAA,EAAA,qCAAA,EAAA,4BAAA,EAAA,4BAAA,EAAA,0BAAA,EAAA,2BAAA,EAAA,6BAAA,EAAA,8BAAA,EAAA,kCAAA,EAAA,+BAAA,EAAA,mCAAA,EAAA,mCAAA,EAAA,yBAAA,EAAA,iCAAA,EAAA,sCAAA,EAAA,gCAAA,EAAA,iCAAA,EAAA,uCAAA,EAAA,kCAAA,EAAA,yBAAA,EAAA,wCAAA,CAAA,EAAA,OAAA,EAAA,CAAA,eAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,qBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA,CAAC,WAAW,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;;2FAiBd,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBA3GjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyET,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,YAAY;AACnB,wBAAA,0BAA0B,EAAE,CAAyB,uBAAA,CAAA;AACrD,wBAAA,0BAA0B,EAAE,CAAyB,uBAAA,CAAA;AACrD,wBAAA,6BAA6B,EAAE,CAAY,UAAA,CAAA;AAC3C,wBAAA,4BAA4B,EAAE,CAAS,OAAA,CAAA;AACvC,wBAAA,wBAAwB,EAAE,CAAe,aAAA,CAAA;AACzC,wBAAA,+BAA+B,EAAE,CAAc,YAAA,CAAA;AAC/C,wBAAA,SAAS,EAAE;AACZ,qBAAA;oBACD,cAAc,EAAE,CAAC,2BAA2B,CAAC;oBAC7C,UAAU,EAAE,CAAC,WAAW,CAAC;AACzB,oBAAA,SAAS,EAAE;wBACT,gBAAgB;AAChB,wBAAA,EAAE,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,CAAC,2BAA2B,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE;AACjG,wBAAA,EAAE,OAAO,EAAE,0BAA0B,EAAE,QAAQ,EAAE,QAAQ;AAC1D,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,SAAS;wBACT,WAAW;wBACX,cAAc;wBACd,YAAY;wBACZ,+BAA+B;wBAC/B,0BAA0B;wBAC1B,eAAe;wBACf;AACD;AACF,iBAAA;yRAwD8C,QAAQ,EAAA,CAAA;sBAApD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBAClC,IAAI,EAAA,CAAA;sBAAZ;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACsB,UAAU,EAAA,CAAA;sBAAhC;gBACsB,YAAY,EAAA,CAAA;sBAAlC;gBACsB,YAAY,EAAA,CAAA;sBAAlC;gBACsB,WAAW,EAAA,CAAA;sBAAjC;gBACsB,SAAS,EAAA,CAAA;sBAA/B;gBACsB,QAAQ,EAAA,CAAA;sBAA9B;gBACsB,gBAAgB,EAAA,CAAA;sBAAtC;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,kBAAkB,EAAA,CAAA;sBAA1B;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,iBAAiB,EAAA,CAAA;sBAAzB;gBACsB,QAAQ,EAAA,CAAA;sBAA9B;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACqD,YAAY,EAAA,CAAA;sBAAjE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACf,YAAY,EAAA,CAAA;sBAAlC;gBAEkB,YAAY,EAAA,CAAA;sBAA9B;gBAEuC,qBAAqB,EAAA,CAAA;sBAA5D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACgB,YAAY,EAAA,CAAA;sBAAjE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACf,UAAU,EAAA,CAAA;sBAAhC;gBACuC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;;;ACtPxC;;;AAGG;MAWU,kBAAkB,CAAA;uGAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,YAHnB,qBAAqB,EAAE,0BAA0B,CACjD,EAAA,OAAA,EAAA,CAAA,0BAA0B,EAAE,qBAAqB,CAAA,EAAA,CAAA;wGAEhD,kBAAkB,EAAA,OAAA,EAAA,CAHnB,qBAAqB,EAAE,0BAA0B,CAAA,EAAA,CAAA;;2FAGhD,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAJ9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,qBAAqB,EAAE,0BAA0B,CAAC;AAC5D,oBAAA,OAAO,EAAE,CAAC,0BAA0B,EAAE,qBAAqB;AAC5D,iBAAA;;;ACbD;;;AAGG;;ACHH;;AAEG;;;;"}