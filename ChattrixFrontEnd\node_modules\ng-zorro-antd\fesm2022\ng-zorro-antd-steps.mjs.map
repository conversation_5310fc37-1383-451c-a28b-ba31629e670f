{"version": 3, "file": "ng-zorro-antd-steps.mjs", "sources": ["../../components/steps/step.component.ts", "../../components/steps/steps.component.ts", "../../components/steps/steps.module.ts", "../../components/steps/public-api.ts", "../../components/steps/ng-zorro-antd-steps.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  Input,\n  OnInit,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute\n} from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { NgClassType, NzSizeDSType } from 'ng-zorro-antd/core/types';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzProgressFormatter, NzProgressModule } from 'ng-zorro-antd/progress';\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  selector: 'nz-step',\n  exportAs: 'nzStep',\n  preserveWhitespaces: false,\n  template: `\n    <div\n      #itemContainer\n      class=\"ant-steps-item-container\"\n      [attr.role]=\"clickable && !nzDisabled ? 'button' : null\"\n      [tabindex]=\"clickable && !nzDisabled ? 0 : null\"\n    >\n      @if (!last) {\n        <div class=\"ant-steps-item-tail\"></div>\n      }\n      <div class=\"ant-steps-item-icon\">\n        @if (!showProcessDot) {\n          @if (showProgress) {\n            <div class=\"ant-steps-progress-icon\">\n              <nz-progress\n                [nzPercent]=\"nzPercentage\"\n                nzType=\"circle\"\n                [nzWidth]=\"nzSize === 'small' ? 32 : 40\"\n                [nzFormat]=\"nullProcessFormat\"\n                [nzStrokeWidth]=\"4\"\n              ></nz-progress>\n            </div>\n          }\n          @if (nzStatus === 'finish' && !nzIcon) {\n            <span class=\"ant-steps-icon\"><nz-icon nzType=\"check\" /></span>\n          }\n          @if (nzStatus === 'error') {\n            <span class=\"ant-steps-icon\"><nz-icon nzType=\"close\" /></span>\n          }\n          @if ((nzStatus === 'process' || nzStatus === 'wait') && !nzIcon) {\n            <span class=\"ant-steps-icon\">\n              {{ index + 1 }}\n            </span>\n          }\n          @if (nzIcon) {\n            <span class=\"ant-steps-icon\">\n              <ng-container *nzStringTemplateOutlet=\"nzIcon; let icon\">\n                <nz-icon [nzType]=\"icon\" />\n              </ng-container>\n            </span>\n          }\n        }\n        @if (showProcessDot) {\n          <span class=\"ant-steps-icon\">\n            <ng-template #processDotTemplate>\n              <span class=\"ant-steps-icon-dot\"></span>\n            </ng-template>\n            <ng-template\n              [ngTemplateOutlet]=\"customProcessTemplate || processDotTemplate\"\n              [ngTemplateOutletContext]=\"{\n                $implicit: processDotTemplate,\n                status: nzStatus,\n                index: index\n              }\"\n            ></ng-template>\n          </span>\n        }\n      </div>\n      <div class=\"ant-steps-item-content\">\n        <div class=\"ant-steps-item-title\">\n          <ng-container *nzStringTemplateOutlet=\"nzTitle\">{{ nzTitle }}</ng-container>\n          @if (nzSubtitle) {\n            <div class=\"ant-steps-item-subtitle\">\n              <ng-container *nzStringTemplateOutlet=\"nzSubtitle\">{{ nzSubtitle }}</ng-container>\n            </div>\n          }\n        </div>\n        <div class=\"ant-steps-item-description\">\n          <ng-container *nzStringTemplateOutlet=\"nzDescription\">{{ nzDescription }}</ng-container>\n        </div>\n      </div>\n    </div>\n  `,\n  host: {\n    class: 'ant-steps-item',\n    '[class.ant-steps-item-wait]': 'nzStatus === \"wait\"',\n    '[class.ant-steps-item-process]': 'nzStatus === \"process\"',\n    '[class.ant-steps-item-finish]': 'nzStatus === \"finish\"',\n    '[class.ant-steps-item-error]': 'nzStatus === \"error\"',\n    '[class.ant-steps-item-active]': 'currentIndex === index',\n    '[class.ant-steps-item-disabled]': 'nzDisabled',\n    '[class.ant-steps-item-custom]': '!!nzIcon',\n    '[class.ant-steps-next-error]': '(outStatus === \"error\") && (currentIndex === index + 1)'\n  },\n  providers: [NzDestroyService],\n  imports: [NzProgressModule, NzIconModule, NzOutletModule, NgTemplateOutlet]\n})\nexport class NzStepComponent implements OnInit {\n  @ViewChild('processDotTemplate', { static: false }) processDotTemplate?: TemplateRef<void>;\n  @ViewChild('itemContainer', { static: true }) itemContainer!: ElementRef<HTMLElement>;\n\n  @Input() nzTitle?: string | TemplateRef<void>;\n  @Input() nzSubtitle?: string | TemplateRef<void>;\n  @Input() nzDescription?: string | TemplateRef<void>;\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Input() nzPercentage: number | null = null;\n  @Input() nzSize: NzSizeDSType = 'default';\n\n  @Input()\n  get nzStatus(): string {\n    return this._status;\n  }\n\n  set nzStatus(status: string) {\n    this._status = status;\n    this.isCustomStatus = true;\n  }\n\n  isCustomStatus = false;\n  private _status = 'wait';\n\n  @Input()\n  get nzIcon(): NgClassType | TemplateRef<void> | undefined {\n    return this._icon;\n  }\n\n  set nzIcon(value: NgClassType | TemplateRef<void> | undefined) {\n    if (!(value instanceof TemplateRef)) {\n      this.oldAPIIcon = typeof value === 'string' && value.indexOf('anticon') > -1;\n    }\n    this._icon = value;\n  }\n\n  oldAPIIcon = true;\n  private _icon?: NgClassType | TemplateRef<void>;\n\n  customProcessTemplate?: TemplateRef<{ $implicit: TemplateRef<void>; status: string; index: number }>; // Set by parent.\n  direction = 'horizontal';\n  index = 0;\n  last = false;\n  outStatus = 'process';\n  showProcessDot = false;\n  clickable = false;\n\n  clickOutsideAngular$ = new Subject<number>();\n\n  readonly nullProcessFormat: NzProgressFormatter = () => null;\n\n  get showProgress(): boolean {\n    return (\n      this.nzPercentage !== null &&\n      !this.nzIcon &&\n      this.nzStatus === 'process' &&\n      this.nzPercentage >= 0 &&\n      this.nzPercentage <= 100\n    );\n  }\n\n  get currentIndex(): number {\n    return this._currentIndex;\n  }\n\n  set currentIndex(current: number) {\n    this._currentIndex = current;\n    if (!this.isCustomStatus) {\n      this._status = current > this.index ? 'finish' : current === this.index ? this.outStatus || '' : 'wait';\n    }\n  }\n\n  private _currentIndex = 0;\n\n  constructor(\n    private cdr: ChangeDetectorRef,\n    private destroy$: NzDestroyService\n  ) {}\n\n  ngOnInit(): void {\n    fromEventOutsideAngular(this.itemContainer.nativeElement, 'click')\n      .pipe(\n        filter(() => this.clickable && this.currentIndex !== this.index && !this.nzDisabled),\n        takeUntil(this.destroy$)\n      )\n      .subscribe(() => {\n        this.clickOutsideAngular$.next(this.index);\n      });\n  }\n\n  enable(): void {\n    this.nzDisabled = false;\n    this.cdr.markForCheck();\n  }\n\n  disable(): void {\n    this.nzDisabled = true;\n    this.cdr.markForCheck();\n  }\n\n  markForCheck(): void {\n    this.cdr.markForCheck();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnChanges,\n  OnInit,\n  Output,\n  QueryList,\n  SimpleChanges,\n  TemplateRef,\n  ViewEncapsulation\n} from '@angular/core';\nimport { Subscription, merge } from 'rxjs';\nimport { startWith, takeUntil } from 'rxjs/operators';\n\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { BooleanInput, NzSizeDSType } from 'ng-zorro-antd/core/types';\nimport { toBoolean } from 'ng-zorro-antd/core/util';\n\nimport { NzStepComponent } from './step.component';\n\nexport type NzDirectionType = 'horizontal' | 'vertical';\nexport type NzStatusType = 'wait' | 'process' | 'finish' | 'error';\nexport type NzProgressDotTemplate = TemplateRef<{ $implicit: TemplateRef<void>; status: string; index: number }>;\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  selector: 'nz-steps',\n  exportAs: 'nzSteps',\n  template: `<ng-content></ng-content>`,\n  host: {\n    class: 'ant-steps',\n    '[class.ant-steps-horizontal]': `nzDirection === 'horizontal'`,\n    '[class.ant-steps-vertical]': `nzDirection === 'vertical'`,\n    '[class.ant-steps-label-horizontal]': `nzDirection === 'horizontal'`,\n    '[class.ant-steps-label-vertical]': `(showProcessDot || nzLabelPlacement === 'vertical') && nzDirection === 'horizontal'`,\n    '[class.ant-steps-dot]': 'showProcessDot',\n    '[class.ant-steps-small]': `nzSize === 'small'`,\n    '[class.ant-steps-navigation]': `nzType === 'navigation'`,\n    '[class.ant-steps-rtl]': `dir === 'rtl'`,\n    '[class.ant-steps-with-progress]': 'showProgress'\n  },\n  providers: [NzDestroyService]\n})\nexport class NzStepsComponent implements OnChanges, OnInit, AfterContentInit {\n  static ngAcceptInputType_nzProgressDot: BooleanInput | NzProgressDotTemplate | undefined | null;\n\n  @ContentChildren(NzStepComponent) steps!: QueryList<NzStepComponent>;\n\n  @Input() nzCurrent = 0;\n  @Input() nzDirection: NzDirectionType = 'horizontal';\n  @Input() nzLabelPlacement: 'horizontal' | 'vertical' = 'horizontal';\n  @Input() nzType: 'default' | 'navigation' = 'default';\n  @Input() nzSize: NzSizeDSType = 'default';\n  @Input() nzStartIndex = 0;\n  @Input() nzStatus: NzStatusType = 'process';\n\n  @Input()\n  set nzProgressDot(value: boolean | NzProgressDotTemplate | undefined | null) {\n    if (value instanceof TemplateRef) {\n      this.showProcessDot = true;\n      this.customProcessDotTemplate = value;\n    } else {\n      this.showProcessDot = toBoolean(value);\n    }\n    this.updateChildrenSteps();\n  }\n\n  @Output() readonly nzIndexChange = new EventEmitter<number>();\n\n  private indexChangeSubscription = Subscription.EMPTY;\n\n  showProcessDot = false;\n  showProgress = false;\n  customProcessDotTemplate?: TemplateRef<{ $implicit: TemplateRef<void>; status: string; index: number }>;\n  dir: Direction = 'ltr';\n\n  constructor(\n    private ngZone: NgZone,\n    private cdr: ChangeDetectorRef,\n    private directionality: Directionality,\n    private destroy$: NzDestroyService\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes.nzStartIndex || changes.nzDirection || changes.nzStatus || changes.nzCurrent || changes.nzSize) {\n      this.updateChildrenSteps();\n    }\n  }\n\n  ngOnInit(): void {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n\n    this.dir = this.directionality.value;\n    this.updateChildrenSteps();\n  }\n\n  ngAfterContentInit(): void {\n    if (this.steps) {\n      this.steps.changes.pipe(startWith(null), takeUntil(this.destroy$)).subscribe(() => {\n        this.updateHostProgressClass();\n        this.updateChildrenSteps();\n      });\n    }\n  }\n\n  private updateHostProgressClass(): void {\n    if (this.steps && !this.showProcessDot) {\n      this.showProgress = !!this.steps.toArray().find(step => step.nzPercentage !== null);\n    }\n  }\n\n  private updateChildrenSteps(): void {\n    if (this.steps) {\n      const length = this.steps.length;\n      this.steps.toArray().forEach((step, index) => {\n        Promise.resolve().then(() => {\n          step.nzSize = this.nzSize;\n          step.outStatus = this.nzStatus;\n          step.showProcessDot = this.showProcessDot;\n          if (this.customProcessDotTemplate) {\n            step.customProcessTemplate = this.customProcessDotTemplate;\n          }\n          step.clickable = this.nzIndexChange.observers.length > 0;\n          step.direction = this.nzDirection;\n          step.index = index + this.nzStartIndex;\n          step.currentIndex = this.nzCurrent;\n          step.last = length === index + 1;\n          step.markForCheck();\n        });\n      });\n      this.indexChangeSubscription.unsubscribe();\n      this.indexChangeSubscription = merge(...this.steps.map(step => step.clickOutsideAngular$))\n        .pipe(takeUntil(this.destroy$))\n        .subscribe(index => {\n          if (this.nzIndexChange.observers.length) {\n            this.ngZone.run(() => this.nzIndexChange.emit(index));\n          }\n        });\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzStepComponent } from './step.component';\nimport { NzStepsComponent } from './steps.component';\n\n@NgModule({\n  imports: [NzStepsComponent, NzStepComponent],\n  exports: [NzStepsComponent, NzStepComponent]\n})\nexport class NzStepsModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './steps.component';\nexport * from './step.component';\nexport * from './steps.module';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1", "i2"], "mappings": ";;;;;;;;;;;;;;;;AAAA;;;AAGG;MAsHU,eAAe,CAAA;AA2EhB,IAAA,GAAA;AACA,IAAA,QAAA;AA3E0C,IAAA,kBAAkB;AACxB,IAAA,aAAa;AAElD,IAAA,OAAO;AACP,IAAA,UAAU;AACV,IAAA,aAAa;IACkB,UAAU,GAAG,KAAK;IACjD,YAAY,GAAkB,IAAI;IAClC,MAAM,GAAiB,SAAS;AAEzC,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,OAAO;;IAGrB,IAAI,QAAQ,CAAC,MAAc,EAAA;AACzB,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM;AACrB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;IAG5B,cAAc,GAAG,KAAK;IACd,OAAO,GAAG,MAAM;AAExB,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,KAAK;;IAGnB,IAAI,MAAM,CAAC,KAAkD,EAAA;AAC3D,QAAA,IAAI,EAAE,KAAK,YAAY,WAAW,CAAC,EAAE;AACnC,YAAA,IAAI,CAAC,UAAU,GAAG,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;;AAE9E,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;IAGpB,UAAU,GAAG,IAAI;AACT,IAAA,KAAK;IAEb,qBAAqB,CAAgF;IACrG,SAAS,GAAG,YAAY;IACxB,KAAK,GAAG,CAAC;IACT,IAAI,GAAG,KAAK;IACZ,SAAS,GAAG,SAAS;IACrB,cAAc,GAAG,KAAK;IACtB,SAAS,GAAG,KAAK;AAEjB,IAAA,oBAAoB,GAAG,IAAI,OAAO,EAAU;AAEnC,IAAA,iBAAiB,GAAwB,MAAM,IAAI;AAE5D,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,QACE,IAAI,CAAC,YAAY,KAAK,IAAI;YAC1B,CAAC,IAAI,CAAC,MAAM;YACZ,IAAI,CAAC,QAAQ,KAAK,SAAS;YAC3B,IAAI,CAAC,YAAY,IAAI,CAAC;AACtB,YAAA,IAAI,CAAC,YAAY,IAAI,GAAG;;AAI5B,IAAA,IAAI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,aAAa;;IAG3B,IAAI,YAAY,CAAC,OAAe,EAAA;AAC9B,QAAA,IAAI,CAAC,aAAa,GAAG,OAAO;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxB,YAAA,IAAI,CAAC,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,OAAO,KAAK,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,GAAG,MAAM;;;IAInG,aAAa,GAAG,CAAC;IAEzB,WACU,CAAA,GAAsB,EACtB,QAA0B,EAAA;QAD1B,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAQ,CAAA,QAAA,GAAR,QAAQ;;IAGlB,QAAQ,GAAA;QACN,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,OAAO;AAC9D,aAAA,IAAI,CACH,MAAM,CAAC,MAAM,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EACpF,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAEzB,SAAS,CAAC,MAAK;YACd,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AAC5C,SAAC,CAAC;;IAGN,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK;AACvB,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;IAGzB,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;AACtB,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;IAGzB,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;uGArGd,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,4KAON,gBAAgB,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,2BAAA,EAAA,uBAAA,EAAA,8BAAA,EAAA,0BAAA,EAAA,6BAAA,EAAA,yBAAA,EAAA,4BAAA,EAAA,wBAAA,EAAA,6BAAA,EAAA,wBAAA,EAAA,+BAAA,EAAA,YAAA,EAAA,6BAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,2DAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,SAAA,EAVzB,CAAC,gBAAgB,CAAC,EApFnB,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,eAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwET,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAaS,gBAAgB,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,mBAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,YAAA,EAAA,SAAA,EAAA,eAAA,EAAA,QAAA,EAAA,UAAA,EAAA,kBAAA,EAAA,WAAA,EAAA,eAAA,EAAA,aAAA,EAAA,UAAA,EAAA,QAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,cAAc,iPAAE,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE/D,eAAe,EAAA,UAAA,EAAA,CAAA;kBA7F3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,QAAQ,EAAE,QAAQ;AAClB,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwET,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,gBAAgB;AACvB,wBAAA,6BAA6B,EAAE,qBAAqB;AACpD,wBAAA,gCAAgC,EAAE,wBAAwB;AAC1D,wBAAA,+BAA+B,EAAE,uBAAuB;AACxD,wBAAA,8BAA8B,EAAE,sBAAsB;AACtD,wBAAA,+BAA+B,EAAE,wBAAwB;AACzD,wBAAA,iCAAiC,EAAE,YAAY;AAC/C,wBAAA,+BAA+B,EAAE,UAAU;AAC3C,wBAAA,8BAA8B,EAAE;AACjC,qBAAA;oBACD,SAAS,EAAE,CAAC,gBAAgB,CAAC;oBAC7B,OAAO,EAAE,CAAC,gBAAgB,EAAE,YAAY,EAAE,cAAc,EAAE,gBAAgB;AAC3E,iBAAA;qHAEqD,kBAAkB,EAAA,CAAA;sBAArE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,oBAAoB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACJ,aAAa,EAAA,CAAA;sBAA1D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,eAAe,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBAEnC,OAAO,EAAA,CAAA;sBAAf;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACuC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,YAAY,EAAA,CAAA;sBAApB;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBAGG,QAAQ,EAAA,CAAA;sBADX;gBAcG,MAAM,EAAA,CAAA;sBADT;;;MCxFU,gBAAgB,CAAA;AAkCjB,IAAA,MAAA;AACA,IAAA,GAAA;AACA,IAAA,cAAA;AACA,IAAA,QAAA;IApCV,OAAO,+BAA+B;AAEJ,IAAA,KAAK;IAE9B,SAAS,GAAG,CAAC;IACb,WAAW,GAAoB,YAAY;IAC3C,gBAAgB,GAA8B,YAAY;IAC1D,MAAM,GAA6B,SAAS;IAC5C,MAAM,GAAiB,SAAS;IAChC,YAAY,GAAG,CAAC;IAChB,QAAQ,GAAiB,SAAS;IAE3C,IACI,aAAa,CAAC,KAAyD,EAAA;AACzE,QAAA,IAAI,KAAK,YAAY,WAAW,EAAE;AAChC,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI;AAC1B,YAAA,IAAI,CAAC,wBAAwB,GAAG,KAAK;;aAChC;AACL,YAAA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC;;QAExC,IAAI,CAAC,mBAAmB,EAAE;;AAGT,IAAA,aAAa,GAAG,IAAI,YAAY,EAAU;AAErD,IAAA,uBAAuB,GAAG,YAAY,CAAC,KAAK;IAEpD,cAAc,GAAG,KAAK;IACtB,YAAY,GAAG,KAAK;AACpB,IAAA,wBAAwB;IACxB,GAAG,GAAc,KAAK;AAEtB,IAAA,WAAA,CACU,MAAc,EACd,GAAsB,EACtB,cAA8B,EAC9B,QAA0B,EAAA;QAH1B,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAc,CAAA,cAAA,GAAd,cAAc;QACd,IAAQ,CAAA,QAAA,GAAR,QAAQ;;AAGlB,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,IAAI,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,EAAE;YAC1G,IAAI,CAAC,mBAAmB,EAAE;;;IAI9B,QAAQ,GAAA;QACN,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,YAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,SAAC,CAAC;QAEF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;QACpC,IAAI,CAAC,mBAAmB,EAAE;;IAG5B,kBAAkB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;gBAChF,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,mBAAmB,EAAE;AAC5B,aAAC,CAAC;;;IAIE,uBAAuB,GAAA;QAC7B,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC;;;IAI/E,mBAAmB,GAAA;AACzB,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM;AAChC,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;AAC3C,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,oBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;AACzB,oBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ;AAC9B,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc;AACzC,oBAAA,IAAI,IAAI,CAAC,wBAAwB,EAAE;AACjC,wBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,wBAAwB;;AAE5D,oBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;AACxD,oBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW;oBACjC,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,YAAY;AACtC,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS;oBAClC,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,KAAK,GAAG,CAAC;oBAChC,IAAI,CAAC,YAAY,EAAE;AACrB,iBAAC,CAAC;AACJ,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE;YAC1C,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC;AACtF,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,KAAK,IAAG;gBACjB,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE;AACvC,oBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;AAEzD,aAAC,CAAC;;;uGAjGG,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,cAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,w5BAFhB,CAAC,gBAAgB,CAAC,EAKZ,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,eAAe,yEAlBtB,CAA2B,yBAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAe1B,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBArB5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,QAAQ,EAAE,CAA2B,yBAAA,CAAA;AACrC,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,WAAW;AAClB,wBAAA,8BAA8B,EAAE,CAA8B,4BAAA,CAAA;AAC9D,wBAAA,4BAA4B,EAAE,CAA4B,0BAAA,CAAA;AAC1D,wBAAA,oCAAoC,EAAE,CAA8B,4BAAA,CAAA;AACpE,wBAAA,kCAAkC,EAAE,CAAqF,mFAAA,CAAA;AACzH,wBAAA,uBAAuB,EAAE,gBAAgB;AACzC,wBAAA,yBAAyB,EAAE,CAAoB,kBAAA,CAAA;AAC/C,wBAAA,8BAA8B,EAAE,CAAyB,uBAAA,CAAA;AACzD,wBAAA,uBAAuB,EAAE,CAAe,aAAA,CAAA;AACxC,wBAAA,iCAAiC,EAAE;AACpC,qBAAA;oBACD,SAAS,EAAE,CAAC,gBAAgB;AAC7B,iBAAA;yKAImC,KAAK,EAAA,CAAA;sBAAtC,eAAe;uBAAC,eAAe;gBAEvB,SAAS,EAAA,CAAA;sBAAjB;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,gBAAgB,EAAA,CAAA;sBAAxB;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBAGG,aAAa,EAAA,CAAA;sBADhB;gBAWkB,aAAa,EAAA,CAAA;sBAA/B;;;ACjFH;;;AAGG;MAWU,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAHd,gBAAgB,EAAE,eAAe,CACjC,EAAA,OAAA,EAAA,CAAA,gBAAgB,EAAE,eAAe,CAAA,EAAA,CAAA;AAEhC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAHI,eAAe,CAAA,EAAA,CAAA;;2FAGhC,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,eAAe,CAAC;AAC5C,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,eAAe;AAC5C,iBAAA;;;ACbD;;;AAGG;;ACHH;;AAEG;;;;"}