{"version": 3, "file": "ng-zorro-antd-upload.mjs", "sources": ["../../components/upload/interface.ts", "../../components/upload/upload-btn.component.ts", "../../components/upload/upload-btn.component.html", "../../components/upload/upload-list.component.ts", "../../components/upload/upload-list.component.html", "../../components/upload/upload.component.ts", "../../components/upload/upload.component.html", "../../components/upload/upload.module.ts", "../../components/upload/public-api.ts", "../../components/upload/ng-zorro-antd-upload.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { TemplateRef } from '@angular/core';\nimport { Observable, Subscription } from 'rxjs';\n\nimport { IndexableObject, NzSafeAny } from 'ng-zorro-antd/core/types';\n\n/** Status */\nexport type UploadFileStatus = 'error' | 'success' | 'done' | 'uploading' | 'removed';\n\nexport type NzUploadType = 'select' | 'drag';\n\n/** Built-in styles of the uploading list. */\nexport type NzUploadListType = 'text' | 'picture' | 'picture-card';\n\nexport interface NzUploadFile {\n  uid: string;\n  size?: number;\n  name: string;\n  filename?: string;\n  lastModified?: string;\n  lastModifiedDate?: Date;\n  url?: string;\n  status?: UploadFileStatus;\n  originFileObj?: File;\n  percent?: number;\n  thumbUrl?: string;\n  response?: NzSafeAny;\n  error?: NzSafeAny;\n  linkProps?: { download: string };\n  type?: string;\n\n  [key: string]: NzSafeAny;\n}\n\nexport interface NzUploadChangeParam {\n  file: NzUploadFile;\n  fileList: NzUploadFile[];\n  event?: { percent: number };\n  /** Callback type. */\n  type?: string;\n}\n\nexport interface NzShowUploadList {\n  showRemoveIcon?: boolean;\n  showPreviewIcon?: boolean;\n  showDownloadIcon?: boolean;\n}\n\nexport type NzUploadTransformFileType = string | Blob | NzUploadFile | Observable<string | Blob | File>;\n\nexport interface ZipButtonOptions {\n  disabled?: boolean;\n  accept?: string | string[];\n  action?: string | ((file: NzUploadFile) => string | Observable<string>);\n  directory?: boolean;\n  openFileDialogOnClick?: boolean;\n  beforeUpload?(file: NzUploadFile, fileList: NzUploadFile[]): boolean | Observable<NzSafeAny>;\n  customRequest?(item: NzSafeAny): Subscription;\n  data?: {} | ((file: NzUploadFile) => {} | Observable<{}>);\n  headers?: {} | ((file: NzUploadFile) => {} | Observable<{}>);\n  name?: string;\n  multiple?: boolean;\n  withCredentials?: boolean;\n  filters?: UploadFilter[];\n  transformFile?(file: NzUploadFile): NzUploadTransformFileType;\n  onStart?(file: NzUploadFile): void;\n  onProgress?(e: NzSafeAny, file: NzUploadFile): void;\n  onSuccess?(ret: NzSafeAny, file: NzUploadFile, xhr: NzSafeAny): void;\n  onError?(err: NzSafeAny, file: NzUploadFile): void;\n}\n\nexport interface UploadFilter {\n  name: string;\n  fn(fileList: NzUploadFile[]): NzUploadFile[] | Observable<NzUploadFile[]>;\n}\n\nexport interface NzUploadXHRArgs {\n  action?: string;\n  name?: string;\n  headers?: IndexableObject;\n  file: NzUploadFile;\n  postFile: string | Blob | File | NzUploadFile;\n  data?: IndexableObject;\n  withCredentials?: boolean;\n  onProgress?(e: NzSafeAny, file: NzUploadFile): void;\n  onSuccess?(ret: NzSafeAny, file: NzUploadFile, xhr: NzSafeAny): void;\n  onError?(err: NzSafeAny, file: NzUploadFile): void;\n}\n\nexport type NzIconRenderTemplate = TemplateRef<{ $implicit: NzUploadFile }>;\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { ENTER } from '@angular/cdk/keycodes';\nimport { HttpClient, HttpEvent, HttpEventType, HttpHeaders, HttpRequest, HttpResponse } from '@angular/common/http';\nimport { Component, ElementRef, inject, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';\nimport { Observable, of, Subject, Subscription } from 'rxjs';\nimport { map, switchMap, takeUntil, tap } from 'rxjs/operators';\n\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\n\nimport { NzUploadFile, NzUploadXHRArgs, ZipButtonOptions } from './interface';\n\n@Component({\n  selector: '[nz-upload-btn]',\n  exportAs: 'nzUploadBtn',\n  templateUrl: './upload-btn.component.html',\n  host: {\n    class: 'ant-upload',\n    '[attr.tabindex]': '\"0\"',\n    '[attr.role]': '\"button\"',\n    '[class.ant-upload-disabled]': 'options.disabled',\n    '(drop)': 'onFileDrop($event)',\n    '(dragover)': 'onFileDrop($event)'\n  },\n  preserveWhitespaces: false,\n  encapsulation: ViewEncapsulation.None\n})\nexport class NzUploadBtnComponent implements OnInit, OnDestroy {\n  reqs: Record<string, Subscription> = {};\n  private destroy = false;\n  private destroy$ = new Subject<void>();\n  @ViewChild('file', { static: true }) file!: ElementRef<HTMLInputElement>;\n  @Input() options!: ZipButtonOptions;\n\n  onClick(): void {\n    if (this.options.disabled || !this.options.openFileDialogOnClick) {\n      return;\n    }\n    this.file.nativeElement.click();\n  }\n\n  // skip safari bug\n  onFileDrop(e: DragEvent): void {\n    if (this.options.disabled || e.type === 'dragover') {\n      e.preventDefault();\n      return;\n    }\n    if (this.options.directory) {\n      this.traverseFileTree(e.dataTransfer!.items);\n    } else {\n      const files: File[] = Array.prototype.slice\n        .call(e.dataTransfer!.files)\n        .filter((file: File) => this.attrAccept(file, this.options.accept));\n      if (files.length) {\n        this.uploadFiles(files);\n      }\n    }\n\n    e.preventDefault();\n  }\n\n  onChange(e: Event): void {\n    if (this.options.disabled) {\n      return;\n    }\n    const hie = e.target as HTMLInputElement;\n    this.uploadFiles(hie.files!);\n    hie.value = '';\n  }\n\n  private traverseFileTree(files: DataTransferItemList): void {\n    const _traverseFileTree = (item: NzSafeAny, path: string): void => {\n      if (item.isFile) {\n        item.file((file: File) => {\n          if (this.attrAccept(file, this.options.accept)) {\n            this.uploadFiles([file]);\n          }\n        });\n      } else if (item.isDirectory) {\n        const dirReader = item.createReader();\n\n        dirReader.readEntries((entries: NzSafeAny) => {\n          for (const entrieItem of entries) {\n            _traverseFileTree(entrieItem, `${path}${item.name}/`);\n          }\n        });\n      }\n    };\n\n    for (const file of files as NzSafeAny) {\n      _traverseFileTree(file.webkitGetAsEntry(), '');\n    }\n  }\n\n  private attrAccept(file: File, acceptedFiles?: string | string[]): boolean {\n    if (file && acceptedFiles) {\n      const acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n      const fileName = `${file.name}`;\n      const mimeType = `${file.type}`;\n      const baseMimeType = mimeType.replace(/\\/.*$/, '');\n\n      return acceptedFilesArray.some(type => {\n        const validType = type.trim();\n        if (validType.charAt(0) === '.') {\n          return (\n            fileName\n              .toLowerCase()\n              .indexOf(validType.toLowerCase(), fileName.toLowerCase().length - validType.toLowerCase().length) !== -1\n          );\n        } else if (/\\/\\*$/.test(validType)) {\n          // This is something like an image/* mime type\n          return baseMimeType === validType.replace(/\\/.*$/, '');\n        }\n        return mimeType === validType;\n      });\n    }\n    return true;\n  }\n\n  private attachUid(file: NzUploadFile): NzUploadFile {\n    if (!file.uid) {\n      file.uid = Math.random().toString(36).substring(2);\n    }\n    return file;\n  }\n\n  uploadFiles(fileList: FileList | File[]): void {\n    let filters$: Observable<NzUploadFile[]> = of(Array.prototype.slice.call(fileList));\n    if (this.options.filters) {\n      this.options.filters.forEach(f => {\n        filters$ = filters$.pipe(\n          switchMap(list => {\n            const fnRes = f.fn(list);\n            return fnRes instanceof Observable ? fnRes : of(fnRes);\n          })\n        );\n      });\n    }\n    filters$.subscribe({\n      next: list => {\n        list.forEach((file: NzUploadFile) => {\n          this.attachUid(file);\n          this.upload(file, list);\n        });\n      },\n      error: e => {\n        warn(`Unhandled upload filter error`, e);\n      }\n    });\n  }\n\n  private upload(file: NzUploadFile, fileList: NzUploadFile[]): void {\n    if (!this.options.beforeUpload) {\n      return this.post(file);\n    }\n    const before = this.options.beforeUpload(file, fileList);\n    if (before instanceof Observable) {\n      before.subscribe({\n        next: (processedFile: NzUploadFile) => {\n          const processedFileType = Object.prototype.toString.call(processedFile);\n          if (processedFileType === '[object File]' || processedFileType === '[object Blob]') {\n            this.attachUid(processedFile);\n            this.post(processedFile);\n          } else if (processedFile) {\n            this.post(file);\n          }\n        },\n        error: e => {\n          warn(`Unhandled upload beforeUpload error`, e);\n        }\n      });\n    } else if (before) {\n      return this.post(file);\n    }\n  }\n\n  private post(file: NzUploadFile): void {\n    if (this.destroy) {\n      return;\n    }\n    let process$: Observable<string | Blob | File | NzUploadFile> = of(file);\n    let transformedFile: string | Blob | File | NzUploadFile | undefined;\n    const opt = this.options;\n    const { uid } = file;\n    const { action, data, headers, transformFile } = opt;\n\n    const args: NzUploadXHRArgs = {\n      action: typeof action === 'string' ? action : '',\n      name: opt.name,\n      headers,\n      file,\n      postFile: file,\n      data,\n      withCredentials: opt.withCredentials,\n      onProgress: opt.onProgress\n        ? e => {\n            opt.onProgress!(e, file);\n          }\n        : undefined,\n      onSuccess: (ret, xhr) => {\n        this.clean(uid);\n        opt.onSuccess!(ret, file, xhr);\n      },\n      onError: xhr => {\n        this.clean(uid);\n        opt.onError!(xhr, file);\n      }\n    };\n\n    if (typeof action === 'function') {\n      const actionResult = (action as (file: NzUploadFile) => string | Observable<string>)(file);\n      if (actionResult instanceof Observable) {\n        process$ = process$.pipe(\n          switchMap(() => actionResult),\n          map(res => {\n            args.action = res;\n            return file;\n          })\n        );\n      } else {\n        args.action = actionResult;\n      }\n    }\n\n    if (typeof transformFile === 'function') {\n      const transformResult = transformFile(file);\n      process$ = process$.pipe(\n        switchMap(() => (transformResult instanceof Observable ? transformResult : of(transformResult))),\n        tap(newFile => (transformedFile = newFile))\n      );\n    }\n\n    if (typeof data === 'function') {\n      const dataResult = (data as (file: NzUploadFile) => {} | Observable<{}>)(file);\n      if (dataResult instanceof Observable) {\n        process$ = process$.pipe(\n          switchMap(() => dataResult),\n          map(res => {\n            args.data = res;\n            return transformedFile ?? file;\n          })\n        );\n      } else {\n        args.data = dataResult;\n      }\n    }\n\n    if (typeof headers === 'function') {\n      const headersResult = (headers as (file: NzUploadFile) => {} | Observable<{}>)(file);\n      if (headersResult instanceof Observable) {\n        process$ = process$.pipe(\n          switchMap(() => headersResult),\n          map(res => {\n            args.headers = res;\n            return transformedFile ?? file;\n          })\n        );\n      } else {\n        args.headers = headersResult;\n      }\n    }\n\n    process$.subscribe(newFile => {\n      args.postFile = newFile;\n      const req$ = (opt.customRequest || this.xhr).call(this, args);\n      if (!(req$ instanceof Subscription)) {\n        warn(`Must return Subscription type in '[nzCustomRequest]' property`);\n      }\n      this.reqs[uid] = req$;\n      opt.onStart!(file);\n    });\n  }\n\n  private xhr(args: NzUploadXHRArgs): Subscription {\n    const formData = new FormData();\n\n    if (args.data) {\n      Object.keys(args.data).map(key => {\n        formData.append(key, args.data![key]);\n      });\n    }\n\n    formData.append(args.name!, args.postFile as NzSafeAny);\n\n    if (!args.headers) {\n      args.headers = {};\n    }\n    if (args.headers['X-Requested-With'] !== null) {\n      args.headers['X-Requested-With'] = `XMLHttpRequest`;\n    } else {\n      delete args.headers['X-Requested-With'];\n    }\n    const req = new HttpRequest('POST', args.action!, formData, {\n      reportProgress: true,\n      withCredentials: args.withCredentials,\n      headers: new HttpHeaders(args.headers)\n    });\n    return this.http!.request(req).subscribe({\n      next: (event: HttpEvent<NzSafeAny>) => {\n        if (event.type === HttpEventType.UploadProgress) {\n          if (event.total! > 0) {\n            (event as NzSafeAny).percent = (event.loaded / event.total!) * 100;\n          }\n          args.onProgress!(event, args.file);\n        } else if (event instanceof HttpResponse) {\n          args.onSuccess!(event.body, args.file, event);\n        }\n      },\n      error: err => {\n        this.abort(args.file);\n        args.onError!(err, args.file);\n      }\n    });\n  }\n\n  private clean(uid: string): void {\n    const req$ = this.reqs[uid];\n    if (req$ instanceof Subscription) {\n      req$.unsubscribe();\n    }\n    delete this.reqs[uid];\n  }\n\n  abort(file?: NzUploadFile): void {\n    if (file) {\n      this.clean(file && file.uid);\n    } else {\n      Object.keys(this.reqs).forEach(uid => this.clean(uid));\n    }\n  }\n\n  private http = inject(HttpClient, { optional: true });\n\n  constructor(private elementRef: ElementRef) {\n    if (!this.http) {\n      throw new Error(\n        `Not found 'HttpClient', You can configure 'HttpClient' with 'provideHttpClient()' in your root module.`\n      );\n    }\n  }\n\n  ngOnInit(): void {\n    // Caretaker note: `input[type=file].click()` will open a native OS file picker,\n    // it doesn't require Angular to run `ApplicationRef.tick()`.\n    fromEventOutsideAngular(this.elementRef.nativeElement, 'click')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => this.onClick());\n\n    fromEventOutsideAngular<KeyboardEvent>(this.elementRef.nativeElement, 'keydown')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(event => {\n        if (this.options.disabled) {\n          return;\n        }\n        if (event.key === 'Enter' || event.keyCode === ENTER) {\n          this.onClick();\n        }\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy = true;\n    this.destroy$.next();\n    this.abort();\n  }\n}\n", "<!--\n  We explicitly bind `style.display` to avoid using an inline style\n  attribute property (which is not allowed when CSP `unsafe-inline`\n  is not specified).\n-->\n<input\n  type=\"file\"\n  #file\n  (change)=\"onChange($event)\"\n  [attr.accept]=\"options.accept\"\n  [attr.directory]=\"options.directory ? 'directory' : null\"\n  [attr.webkitdirectory]=\"options.directory ? 'webkitdirectory' : null\"\n  [multiple]=\"options.multiple\"\n  [style.display]=\"'none'\"\n/>\n<ng-content></ng-content>\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { animate, style, transition, trigger } from '@angular/animations';\nimport { Direction } from '@angular/cdk/bidi';\nimport { Platform } from '@angular/cdk/platform';\nimport { DOCUMENT, NgTemplateOutlet } from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  ViewEncapsulation,\n  inject\n} from '@angular/core';\nimport { Observable, Subject, fromEvent, of } from 'rxjs';\nimport { map, takeUntil } from 'rxjs/operators';\n\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzProgressModule } from 'ng-zorro-antd/progress';\nimport { NzToolTipModule } from 'ng-zorro-antd/tooltip';\n\nimport { NzIconRenderTemplate, NzShowUploadList, NzUploadFile, NzUploadListType } from './interface';\n\nconst isImageFileType = (type: string): boolean => !!type && type.indexOf('image/') === 0;\n\nconst MEASURE_SIZE = 200;\n\ntype UploadListIconType = '' | 'uploading' | 'thumbnail';\n\ninterface UploadListFile extends NzUploadFile {\n  isImageUrl?: boolean;\n  isUploading?: boolean;\n  iconType?: UploadListIconType;\n  showDownload?: boolean;\n}\n\n@Component({\n  selector: 'nz-upload-list',\n  exportAs: 'nzUploadList',\n  templateUrl: './upload-list.component.html',\n  animations: [\n    trigger('itemState', [\n      transition(':enter', [\n        style({ height: '0', width: '0', opacity: 0 }),\n        animate(150, style({ height: '*', width: '*', opacity: 1 }))\n      ]),\n      transition(':leave', [animate(150, style({ height: '0', width: '0', opacity: 0 }))])\n    ])\n  ],\n  host: {\n    class: 'ant-upload-list',\n    '[class.ant-upload-list-rtl]': `dir === 'rtl'`,\n    '[class.ant-upload-list-text]': `listType === 'text'`,\n    '[class.ant-upload-list-picture]': `listType === 'picture'`,\n    '[class.ant-upload-list-picture-card]': `listType === 'picture-card'`\n  },\n  preserveWhitespaces: false,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [NzToolTipModule, NgTemplateOutlet, NzIconModule, NzButtonModule, NzProgressModule]\n})\nexport class NzUploadListComponent implements OnChanges, OnDestroy {\n  list: UploadListFile[] = [];\n\n  private get showPic(): boolean {\n    return this.listType === 'picture' || this.listType === 'picture-card';\n  }\n\n  @Input() locale: NzSafeAny = {};\n  @Input() listType!: NzUploadListType;\n  @Input()\n  set items(list: NzUploadFile[]) {\n    this.list = list;\n  }\n  @Input() icons!: NzShowUploadList;\n  @Input() onPreview?: (file: NzUploadFile) => void;\n  @Input() onRemove!: (file: NzUploadFile) => void;\n  @Input() onDownload?: (file: NzUploadFile) => void;\n  @Input() previewFile?: (file: NzUploadFile) => Observable<string>;\n  @Input() previewIsImage?: (file: NzUploadFile) => boolean;\n  @Input() iconRender: NzIconRenderTemplate | null = null;\n  @Input() dir: Direction = 'ltr';\n\n  private document: Document = inject(DOCUMENT);\n  private destroy$ = new Subject<void>();\n\n  private genErr(file: NzUploadFile): string {\n    if (file.response && typeof file.response === 'string') {\n      return file.response;\n    }\n    return (file.error && file.error.statusText) || this.locale.uploadError;\n  }\n\n  private extname(url: string): string {\n    const temp = url.split('/');\n    const filename = temp[temp.length - 1];\n    const filenameWithoutSuffix = filename.split(/#|\\?/)[0];\n    return (/\\.[^./\\\\]*$/.exec(filenameWithoutSuffix) || [''])[0];\n  }\n\n  isImageUrl(file: NzUploadFile): boolean {\n    if (isImageFileType(file.type!)) {\n      return true;\n    }\n    const url: string = (file.thumbUrl || file.url || '') as string;\n    if (!url) {\n      return false;\n    }\n    const extension = this.extname(url);\n    if (/^data:image\\//.test(url) || /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg)$/i.test(extension)) {\n      return true;\n    } else if (/^data:/.test(url)) {\n      // other file types of base64\n      return false;\n    } else if (extension) {\n      // other file types which have extension\n      return false;\n    }\n    return true;\n  }\n\n  private getIconType(file: UploadListFile): UploadListIconType {\n    if (!this.showPic) {\n      return '';\n    }\n    if (file.isUploading || (!file.thumbUrl && !file.url)) {\n      return 'uploading';\n    } else {\n      return 'thumbnail';\n    }\n  }\n\n  private previewImage(file: File | Blob): Observable<string> {\n    if (!isImageFileType(file.type) || !this.platform.isBrowser) {\n      return of('');\n    }\n\n    const canvas = this.document.createElement('canvas');\n    canvas.width = MEASURE_SIZE;\n    canvas.height = MEASURE_SIZE;\n    canvas.style.cssText = `position: fixed; left: 0; top: 0; width: ${MEASURE_SIZE}px; height: ${MEASURE_SIZE}px; z-index: 9999; display: none;`;\n    this.document.body.appendChild(canvas);\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n    const objectUrl = URL.createObjectURL(file);\n    img.src = objectUrl;\n    return fromEvent(img, 'load').pipe(\n      map(() => {\n        const { width, height } = img;\n\n        let drawWidth = MEASURE_SIZE;\n        let drawHeight = MEASURE_SIZE;\n        let offsetX = 0;\n        let offsetY = 0;\n\n        if (width < height) {\n          drawHeight = height * (MEASURE_SIZE / width);\n          offsetY = -(drawHeight - drawWidth) / 2;\n        } else {\n          drawWidth = width * (MEASURE_SIZE / height);\n          offsetX = -(drawWidth - drawHeight) / 2;\n        }\n\n        try {\n          ctx!.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);\n        } catch {\n          // noop\n        }\n        const dataURL = canvas.toDataURL();\n        this.document.body.removeChild(canvas);\n\n        URL.revokeObjectURL(objectUrl);\n        return dataURL;\n      })\n    );\n  }\n\n  private genThumb(): void {\n    if (!this.platform.isBrowser) {\n      return;\n    }\n\n    const win = window as NzSafeAny;\n    if (\n      !this.showPic ||\n      typeof document === 'undefined' ||\n      typeof win === 'undefined' ||\n      !win.FileReader ||\n      !win.File\n    ) {\n      return;\n    }\n    this.list\n      .filter(file => file.originFileObj instanceof File && file.thumbUrl === undefined)\n      .forEach(file => {\n        file.thumbUrl = '';\n        // Caretaker note: we shouldn't use promises here since they're not cancellable.\n        // A promise microtask can be resolved after the view is destroyed. Thus running `detectChanges()`\n        // will cause a runtime exception (`detectChanges()` cannot be run on destroyed views).\n        const dataUrl$ = (this.previewFile ? this.previewFile(file) : this.previewImage(file.originFileObj!)).pipe(\n          takeUntil(this.destroy$)\n        );\n        this.ngZone.runOutsideAngular(() => {\n          dataUrl$.subscribe(dataUrl => {\n            this.ngZone.run(() => {\n              file.thumbUrl = dataUrl;\n              this.detectChanges();\n            });\n          });\n        });\n      });\n  }\n\n  private showDownload(file: NzUploadFile): boolean {\n    return !!(this.icons.showDownloadIcon && file.status === 'done');\n  }\n\n  private fixData(): void {\n    this.list.forEach(file => {\n      file.isUploading = file.status === 'uploading';\n      file.message = this.genErr(file);\n      file.linkProps = typeof file.linkProps === 'string' ? JSON.parse(file.linkProps) : file.linkProps;\n      file.isImageUrl = this.previewIsImage ? this.previewIsImage(file) : this.isImageUrl(file);\n      file.iconType = this.getIconType(file);\n      file.showDownload = this.showDownload(file);\n    });\n  }\n\n  handlePreview(file: NzUploadFile, e: Event): void {\n    if (!this.onPreview) {\n      return;\n    }\n\n    e.preventDefault();\n    return this.onPreview(file);\n  }\n\n  handleRemove(file: NzUploadFile, e: Event): void {\n    e.preventDefault();\n    if (this.onRemove) {\n      this.onRemove(file);\n    }\n    return;\n  }\n\n  handleDownload(file: NzUploadFile): void {\n    if (typeof this.onDownload === 'function') {\n      this.onDownload(file);\n    } else if (file.url) {\n      window.open(file.url);\n    }\n  }\n\n  // #endregion\n\n  constructor(\n    private cdr: ChangeDetectorRef,\n    private ngZone: NgZone,\n    private platform: Platform\n  ) {}\n\n  detectChanges(): void {\n    this.fixData();\n    this.cdr.detectChanges();\n  }\n\n  ngOnChanges(): void {\n    this.fixData();\n    this.genThumb();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n  }\n}\n", "@for (file of list; track file) {\n  <div class=\"ant-upload-list-{{ listType }}-container\">\n    <div\n      class=\"ant-upload-list-item ant-upload-list-item-{{ file.status }} ant-upload-list-item-list-type-{{ listType }}\"\n      [attr.data-key]=\"file.key\"\n      @itemState\n      nz-tooltip\n      [nzTooltipTitle]=\"file.status === 'error' ? file.message : null\"\n    >\n      <ng-template #icon>\n        @switch (file.iconType) {\n          @case ('uploading') {\n            <div class=\"ant-upload-list-item-thumbnail\" [class.ant-upload-list-item-file]=\"!file.isUploading\">\n              <ng-template [ngTemplateOutlet]=\"iconNode\" [ngTemplateOutletContext]=\"{ $implicit: file }\"></ng-template>\n            </div>\n          }\n          @case ('thumbnail') {\n            <a\n              class=\"ant-upload-list-item-thumbnail\"\n              [class.ant-upload-list-item-file]=\"!file.isImageUrl\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              [href]=\"file.url || file.thumbUrl\"\n              (click)=\"handlePreview(file, $event)\"\n            >\n              @if (file.isImageUrl) {\n                <img class=\"ant-upload-list-item-image\" [src]=\"file.thumbUrl || file.url\" [attr.alt]=\"file.name\" />\n              } @else {\n                <ng-template\n                  [ngTemplateOutlet]=\"iconNode\"\n                  [ngTemplateOutletContext]=\"{ $implicit: file }\"\n                ></ng-template>\n              }\n            </a>\n          }\n          @default {\n            <div class=\"ant-upload-text-icon\">\n              <ng-template [ngTemplateOutlet]=\"iconNode\" [ngTemplateOutletContext]=\"{ $implicit: file }\"></ng-template>\n            </div>\n          }\n        }\n      </ng-template>\n\n      <ng-template #iconNode let-file>\n        @if (!iconRender) {\n          @switch (listType) {\n            @case ('picture') {\n              @if (file.isUploading) {\n                <nz-icon nzType=\"loading\" />\n              } @else {\n                <nz-icon [nzType]=\"file.isImageUrl ? 'picture' : 'file'\" nzTheme=\"twotone\" />\n              }\n            }\n            @case ('picture-card') {\n              @if (file.isUploading) {\n                {{ locale.uploading }}\n              } @else {\n                <nz-icon [nzType]=\"file.isImageUrl ? 'picture' : 'file'\" nzTheme=\"twotone\" />\n              }\n            }\n            @default {\n              <nz-icon [nzType]=\"file.isUploading ? 'loading' : 'paper-clip'\" />\n            }\n          }\n        } @else {\n          <ng-template [ngTemplateOutlet]=\"iconRender\" [ngTemplateOutletContext]=\"{ $implicit: file }\"></ng-template>\n        }\n      </ng-template>\n\n      <ng-template #removeIcon>\n        @if (icons.showRemoveIcon) {\n          <button\n            type=\"button\"\n            nz-button\n            nzType=\"text\"\n            nzSize=\"small\"\n            (click)=\"handleRemove(file, $event)\"\n            [attr.title]=\"locale.removeFile\"\n            class=\"ant-upload-list-item-card-actions-btn\"\n          >\n            <nz-icon nzType=\"delete\" />\n          </button>\n        }\n      </ng-template>\n\n      <ng-template #downloadIcon>\n        @if (file.showDownload) {\n          <button\n            type=\"button\"\n            nz-button\n            nzType=\"text\"\n            nzSize=\"small\"\n            (click)=\"handleDownload(file)\"\n            [attr.title]=\"locale.downloadFile\"\n            class=\"ant-upload-list-item-card-actions-btn\"\n          >\n            <nz-icon nzType=\"download\" />\n          </button>\n        }\n      </ng-template>\n\n      <ng-template #downloadOrDelete>\n        @if (listType !== 'picture-card') {\n          <span class=\"ant-upload-list-item-card-actions {{ listType === 'picture' ? 'picture' : '' }}\">\n            <ng-template [ngTemplateOutlet]=\"downloadIcon\"></ng-template>\n            <ng-template [ngTemplateOutlet]=\"removeIcon\"></ng-template>\n          </span>\n        }\n      </ng-template>\n\n      <ng-template #preview>\n        @if (file.url) {\n          <a\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            class=\"ant-upload-list-item-name\"\n            [attr.title]=\"file.name\"\n            [href]=\"file.url\"\n            [attr.download]=\"file.linkProps && file.linkProps.download\"\n            (click)=\"handlePreview(file, $event)\"\n          >\n            {{ file.name }}\n          </a>\n        } @else {\n          <span class=\"ant-upload-list-item-name\" [attr.title]=\"file.name\" (click)=\"handlePreview(file, $event)\">\n            {{ file.name }}\n          </span>\n        }\n        <ng-template [ngTemplateOutlet]=\"downloadOrDelete\"></ng-template>\n      </ng-template>\n\n      <div class=\"ant-upload-list-item-info\">\n        <span class=\"ant-upload-span\">\n          <ng-template [ngTemplateOutlet]=\"icon\"></ng-template>\n          <ng-template [ngTemplateOutlet]=\"preview\"></ng-template>\n        </span>\n      </div>\n      @if (listType === 'picture-card' && !file.isUploading) {\n        <span class=\"ant-upload-list-item-actions\">\n          @if (icons.showPreviewIcon) {\n            <a\n              [href]=\"file.url || file.thumbUrl\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              [attr.title]=\"locale.previewFile\"\n              [style]=\"!(file.url || file.thumbUrl) ? { opacity: 0.5, 'pointer-events': 'none' } : null\"\n              (click)=\"handlePreview(file, $event)\"\n            >\n              <nz-icon nzType=\"eye\" />\n            </a>\n          }\n          @if (file.status === 'done') {\n            <ng-template [ngTemplateOutlet]=\"downloadIcon\"></ng-template>\n          }\n          <ng-template [ngTemplateOutlet]=\"removeIcon\"></ng-template>\n        </span>\n      }\n      @if (file.isUploading) {\n        <div class=\"ant-upload-list-item-progress\">\n          <nz-progress [nzPercent]=\"file.percent!\" nzType=\"line\" [nzShowInfo]=\"false\" [nzStrokeWidth]=\"2\"></nz-progress>\n        </div>\n      }\n    </div>\n  </div>\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { Platform } from '@angular/cdk/platform';\nimport { DOCUMENT, NgTemplateOutlet } from '@angular/common';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject,\n  numberAttribute\n} from '@angular/core';\nimport { Observable, Subject, Subscription, of } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\n\nimport { BooleanInput, NzSafeAny } from 'ng-zorro-antd/core/types';\nimport { fromEventOutsideAngular, toBoolean } from 'ng-zorro-antd/core/util';\nimport { NzI18nService, NzUploadI18nInterface } from 'ng-zorro-antd/i18n';\n\nimport {\n  NzIconRenderTemplate,\n  NzShowUploadList,\n  NzUploadChangeParam,\n  NzUploadFile,\n  NzUploadListType,\n  NzUploadTransformFileType,\n  NzUploadType,\n  NzUploadXHRArgs,\n  UploadFilter,\n  ZipButtonOptions\n} from './interface';\nimport { NzUploadBtnComponent } from './upload-btn.component';\nimport { NzUploadListComponent } from './upload-list.component';\n\n@Component({\n  selector: 'nz-upload',\n  exportAs: 'nzUpload',\n  templateUrl: './upload.component.html',\n  preserveWhitespaces: false,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    '[class.ant-upload-picture-card-wrapper]': 'nzListType === \"picture-card\"'\n  },\n  imports: [NzUploadListComponent, NgTemplateOutlet, NzUploadBtnComponent]\n})\nexport class NzUploadComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {\n  static ngAcceptInputType_nzShowUploadList: BooleanInput | NzShowUploadList;\n\n  private destroy$ = new Subject<void>();\n  @ViewChild('uploadComp', { static: false }) uploadComp!: NzUploadBtnComponent;\n  @ViewChild('listComp', { static: false }) listComp!: NzUploadListComponent;\n\n  locale!: NzUploadI18nInterface;\n  dir: Direction = 'ltr';\n\n  // #region fields\n\n  @Input() nzType: NzUploadType = 'select';\n  @Input({ transform: numberAttribute }) nzLimit = 0;\n  @Input({ transform: numberAttribute }) nzSize = 0;\n\n  @Input() nzFileType?: string;\n  @Input() nzAccept?: string | string[];\n  @Input() nzAction?: string | ((file: NzUploadFile) => string | Observable<string>);\n  @Input({ transform: booleanAttribute }) nzDirectory = false;\n  @Input({ transform: booleanAttribute }) nzOpenFileDialogOnClick = true;\n  @Input() nzBeforeUpload?: (file: NzUploadFile, fileList: NzUploadFile[]) => boolean | Observable<boolean>;\n  @Input() nzCustomRequest?: (item: NzUploadXHRArgs) => Subscription;\n  @Input() nzData?: {} | ((file: NzUploadFile) => {} | Observable<{}>);\n  @Input() nzFilter: UploadFilter[] = [];\n  @Input() nzFileList: NzUploadFile[] = [];\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Input() nzHeaders?: {} | ((file: NzUploadFile) => {} | Observable<{}>);\n  @Input() nzListType: NzUploadListType = 'text';\n  @Input({ transform: booleanAttribute }) nzMultiple = false;\n  @Input() nzName = 'file';\n\n  private _showUploadList: boolean | NzShowUploadList = true;\n  private document: Document = inject(DOCUMENT);\n\n  @Input()\n  set nzShowUploadList(value: boolean | NzShowUploadList) {\n    this._showUploadList = typeof value === 'boolean' ? toBoolean(value) : value;\n  }\n\n  get nzShowUploadList(): boolean | NzShowUploadList {\n    return this._showUploadList;\n  }\n\n  @Input({ transform: booleanAttribute }) nzShowButton = true;\n  @Input({ transform: booleanAttribute }) nzWithCredentials = false;\n\n  @Input() nzRemove?: (file: NzUploadFile) => boolean | Observable<boolean>;\n  @Input() nzPreview?: (file: NzUploadFile) => void;\n  @Input() nzPreviewFile?: (file: NzUploadFile) => Observable<string>;\n  @Input() nzPreviewIsImage?: (file: NzUploadFile) => boolean;\n  @Input() nzTransformFile?: (file: NzUploadFile) => NzUploadTransformFileType;\n  @Input() nzDownload?: (file: NzUploadFile) => void;\n  @Input() nzIconRender: NzIconRenderTemplate | null = null;\n  @Input() nzFileListRender: TemplateRef<{ $implicit: NzUploadFile[] }> | null = null;\n\n  @Output() readonly nzChange: EventEmitter<NzUploadChangeParam> = new EventEmitter<NzUploadChangeParam>();\n  @Output() readonly nzFileListChange: EventEmitter<NzUploadFile[]> = new EventEmitter<NzUploadFile[]>();\n\n  _btnOptions?: ZipButtonOptions;\n\n  private zipOptions(): this {\n    if (typeof this.nzShowUploadList === 'boolean' && this.nzShowUploadList) {\n      this.nzShowUploadList = {\n        showPreviewIcon: true,\n        showRemoveIcon: true,\n        showDownloadIcon: true\n      };\n    }\n    // filters\n    const filters: UploadFilter[] = this.nzFilter.slice();\n    if (this.nzMultiple && this.nzLimit > 0 && filters.findIndex(w => w.name === 'limit') === -1) {\n      filters.push({\n        name: 'limit',\n        fn: (fileList: NzUploadFile[]) => fileList.slice(-this.nzLimit)\n      });\n    }\n    if (this.nzSize > 0 && filters.findIndex(w => w.name === 'size') === -1) {\n      filters.push({\n        name: 'size',\n        fn: (fileList: NzUploadFile[]) => fileList.filter(w => w.size! / 1024 <= this.nzSize)\n      });\n    }\n    if (this.nzFileType && this.nzFileType.length > 0 && filters.findIndex(w => w.name === 'type') === -1) {\n      const types = this.nzFileType.split(',');\n      filters.push({\n        name: 'type',\n        fn: (fileList: NzUploadFile[]) => fileList.filter(w => ~types.indexOf(w.type!))\n      });\n    }\n    this._btnOptions = {\n      disabled: this.nzDisabled,\n      accept: this.nzAccept,\n      action: this.nzAction,\n      directory: this.nzDirectory,\n      openFileDialogOnClick: this.nzOpenFileDialogOnClick,\n      beforeUpload: this.nzBeforeUpload,\n      customRequest: this.nzCustomRequest,\n      data: this.nzData,\n      headers: this.nzHeaders,\n      name: this.nzName,\n      multiple: this.nzMultiple,\n      withCredentials: this.nzWithCredentials,\n      filters,\n      transformFile: this.nzTransformFile,\n      onStart: this.onStart,\n      onProgress: this.onProgress,\n      onSuccess: this.onSuccess,\n      onError: this.onError\n    };\n    return this;\n  }\n\n  private readonly platform = inject(Platform);\n\n  // #endregion\n\n  constructor(\n    private cdr: ChangeDetectorRef,\n    private i18n: NzI18nService,\n    private directionality: Directionality\n  ) {}\n\n  // #region upload\n\n  private fileToObject(file: NzUploadFile): NzUploadFile {\n    return {\n      lastModified: file.lastModified,\n      lastModifiedDate: file.lastModifiedDate,\n      name: file.filename || file.name,\n      size: file.size,\n      type: file.type,\n      uid: file.uid,\n      response: file.response,\n      error: file.error,\n      percent: 0,\n      originFileObj: file as NzSafeAny\n    };\n  }\n\n  private getFileItem(file: NzUploadFile, fileList: NzUploadFile[]): NzUploadFile {\n    return fileList.filter(item => item.uid === file.uid)[0];\n  }\n\n  private removeFileItem(file: NzUploadFile, fileList: NzUploadFile[]): NzUploadFile[] {\n    return fileList.filter(item => item.uid !== file.uid);\n  }\n\n  private onStart = (file: NzUploadFile): void => {\n    if (!this.nzFileList) {\n      this.nzFileList = [];\n    }\n    const targetItem = this.fileToObject(file);\n    targetItem.status = 'uploading';\n    this.nzFileList = this.nzFileList.concat(targetItem);\n    this.nzFileListChange.emit(this.nzFileList);\n    this.nzChange.emit({ file: targetItem, fileList: this.nzFileList, type: 'start' });\n    this.detectChangesList();\n  };\n\n  private onProgress = (e: { percent: number }, file: NzUploadFile): void => {\n    const fileList = this.nzFileList;\n    const targetItem = this.getFileItem(file, fileList);\n    targetItem.percent = e.percent;\n    this.nzChange.emit({\n      event: e,\n      file: { ...targetItem },\n      fileList: this.nzFileList,\n      type: 'progress'\n    });\n    this.detectChangesList();\n  };\n\n  private onSuccess = (res: {}, file: NzUploadFile): void => {\n    const fileList = this.nzFileList;\n    const targetItem = this.getFileItem(file, fileList);\n    targetItem.status = 'done';\n    targetItem.response = res;\n    this.nzChange.emit({\n      file: { ...targetItem },\n      fileList,\n      type: 'success'\n    });\n    this.detectChangesList();\n  };\n\n  private onError = (err: {}, file: NzUploadFile): void => {\n    const fileList = this.nzFileList;\n    const targetItem = this.getFileItem(file, fileList);\n    targetItem.error = err;\n    targetItem.status = 'error';\n    this.nzChange.emit({\n      file: { ...targetItem },\n      fileList,\n      type: 'error'\n    });\n    this.detectChangesList();\n  };\n\n  // #endregion\n\n  // #region drag\n\n  private dragState?: string;\n\n  // skip safari bug\n  fileDrop(e: DragEvent): void {\n    if (e.type === this.dragState) {\n      return;\n    }\n    this.dragState = e.type;\n    this.setClassMap();\n  }\n\n  // #endregion\n\n  // #region list\n\n  private detectChangesList(): void {\n    this.cdr.detectChanges();\n    this.listComp?.detectChanges();\n  }\n\n  onRemove = (file: NzUploadFile): void => {\n    this.uploadComp.abort(file);\n    file.status = 'removed';\n    const fnRes =\n      typeof this.nzRemove === 'function' ? this.nzRemove(file) : this.nzRemove == null ? true : this.nzRemove;\n    (fnRes instanceof Observable ? fnRes : of(fnRes)).pipe(filter((res: boolean) => res)).subscribe(() => {\n      this.nzFileList = this.removeFileItem(file, this.nzFileList);\n      this.nzChange.emit({\n        file,\n        fileList: this.nzFileList,\n        type: 'removed'\n      });\n      this.nzFileListChange.emit(this.nzFileList);\n      this.cdr.detectChanges();\n    });\n  };\n\n  // #endregion\n\n  // #region styles\n\n  private prefixCls = 'ant-upload';\n  classList: string[] = [];\n\n  private setClassMap(): void {\n    let subCls: string[] = [];\n    if (this.nzType === 'drag') {\n      if (this.nzFileList.some(file => file.status === 'uploading')) {\n        subCls.push(`${this.prefixCls}-drag-uploading`);\n      }\n      if (this.dragState === 'dragover') {\n        subCls.push(`${this.prefixCls}-drag-hover`);\n      }\n    } else {\n      subCls = [`${this.prefixCls}-select-${this.nzListType}`];\n    }\n\n    this.classList = [\n      this.prefixCls,\n      `${this.prefixCls}-${this.nzType}`,\n      ...subCls,\n      (this.nzDisabled && `${this.prefixCls}-disabled`) || '',\n      (this.dir === 'rtl' && `${this.prefixCls}-rtl`) || ''\n    ].filter(item => !!item);\n\n    this.cdr.detectChanges();\n  }\n\n  // #endregion\n\n  ngOnInit(): void {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.setClassMap();\n      this.cdr.detectChanges();\n    });\n\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Upload');\n      this.detectChangesList();\n    });\n  }\n\n  ngAfterViewInit(): void {\n    if (this.platform.FIREFOX) {\n      // fix firefox drop open new tab\n      fromEventOutsideAngular<MouseEvent>(this.document.body, 'drop')\n        .pipe(takeUntil(this.destroy$))\n        .subscribe(event => {\n          event.preventDefault();\n          event.stopPropagation();\n        });\n    }\n  }\n\n  ngOnChanges(): void {\n    this.zipOptions().setClassMap();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n}\n", "<ng-template #list>\n  @if (locale && !nzFileListRender) {\n    <nz-upload-list\n      #listComp\n      [style.display]=\"nzShowUploadList ? '' : 'none'\"\n      [locale]=\"locale\"\n      [listType]=\"nzListType\"\n      [items]=\"nzFileList || []\"\n      [icons]=\"$any(nzShowUploadList)\"\n      [iconRender]=\"nzIconRender\"\n      [previewFile]=\"nzPreviewFile\"\n      [previewIsImage]=\"nzPreviewIsImage\"\n      [onPreview]=\"nzPreview\"\n      [onRemove]=\"onRemove\"\n      [onDownload]=\"nzDownload\"\n      [dir]=\"dir\"\n    ></nz-upload-list>\n  }\n  @if (nzFileListRender) {\n    <ng-container *ngTemplateOutlet=\"nzFileListRender; context: { $implicit: nzFileList }\"></ng-container>\n  }\n</ng-template>\n<ng-template #con><ng-content></ng-content></ng-template>\n<ng-template #btn>\n  <div [class]=\"classList\" [style.display]=\"nzShowButton ? '' : 'none'\">\n    <div nz-upload-btn #uploadComp [options]=\"_btnOptions!\">\n      <ng-template [ngTemplateOutlet]=\"con\"></ng-template>\n    </div>\n  </div>\n</ng-template>\n@if (nzType === 'drag') {\n  <div [class]=\"classList\" (drop)=\"fileDrop($event)\" (dragover)=\"fileDrop($event)\" (dragleave)=\"fileDrop($event)\">\n    <div nz-upload-btn #uploadComp [options]=\"_btnOptions!\" class=\"ant-upload-btn\">\n      <div class=\"ant-upload-drag-container\">\n        <ng-template [ngTemplateOutlet]=\"con\"></ng-template>\n      </div>\n    </div>\n  </div>\n  <ng-template [ngTemplateOutlet]=\"list\"></ng-template>\n} @else {\n  @if (nzListType === 'picture-card') {\n    <ng-template [ngTemplateOutlet]=\"list\"></ng-template>\n    <ng-template [ngTemplateOutlet]=\"btn\"></ng-template>\n  } @else {\n    <ng-template [ngTemplateOutlet]=\"btn\"></ng-template>\n    <ng-template [ngTemplateOutlet]=\"list\"></ng-template>\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzUploadBtnComponent } from './upload-btn.component';\nimport { NzUploadListComponent } from './upload-list.component';\nimport { NzUploadComponent } from './upload.component';\n\n@NgModule({\n  imports: [NzUploadComponent, NzUploadBtnComponent, NzUploadListComponent],\n  exports: [NzUploadComponent]\n})\nexport class NzUploadModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './interface';\nexport * from './upload-btn.component';\nexport * from './upload-list.component';\nexport * from './upload.component';\nexport * from './upload.module';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1", "i2"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;;ACHH;;;AAGG;MA6BU,oBAAoB,CAAA;AAkTX,IAAA,UAAA;IAjTpB,IAAI,GAAiC,EAAE;IAC/B,OAAO,GAAG,KAAK;AACf,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;AACD,IAAA,IAAI;AAChC,IAAA,OAAO;IAEhB,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;YAChE;;AAEF,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;;;AAIjC,IAAA,UAAU,CAAC,CAAY,EAAA;AACrB,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,EAAE;YAClD,CAAC,CAAC,cAAc,EAAE;YAClB;;AAEF,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YAC1B,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,YAAa,CAAC,KAAK,CAAC;;aACvC;AACL,YAAA,MAAM,KAAK,GAAW,KAAK,CAAC,SAAS,CAAC;AACnC,iBAAA,IAAI,CAAC,CAAC,CAAC,YAAa,CAAC,KAAK;AAC1B,iBAAA,MAAM,CAAC,CAAC,IAAU,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACrE,YAAA,IAAI,KAAK,CAAC,MAAM,EAAE;AAChB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;;QAI3B,CAAC,CAAC,cAAc,EAAE;;AAGpB,IAAA,QAAQ,CAAC,CAAQ,EAAA;AACf,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACzB;;AAEF,QAAA,MAAM,GAAG,GAAG,CAAC,CAAC,MAA0B;AACxC,QAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAM,CAAC;AAC5B,QAAA,GAAG,CAAC,KAAK,GAAG,EAAE;;AAGR,IAAA,gBAAgB,CAAC,KAA2B,EAAA;AAClD,QAAA,MAAM,iBAAiB,GAAG,CAAC,IAAe,EAAE,IAAY,KAAU;AAChE,YAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,gBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,IAAU,KAAI;AACvB,oBAAA,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AAC9C,wBAAA,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC;;AAE5B,iBAAC,CAAC;;AACG,iBAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AAC3B,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE;AAErC,gBAAA,SAAS,CAAC,WAAW,CAAC,CAAC,OAAkB,KAAI;AAC3C,oBAAA,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE;wBAChC,iBAAiB,CAAC,UAAU,EAAE,CAAG,EAAA,IAAI,CAAG,EAAA,IAAI,CAAC,IAAI,CAAG,CAAA,CAAA,CAAC;;AAEzD,iBAAC,CAAC;;AAEN,SAAC;AAED,QAAA,KAAK,MAAM,IAAI,IAAI,KAAkB,EAAE;YACrC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAE,CAAC;;;IAI1C,UAAU,CAAC,IAAU,EAAE,aAAiC,EAAA;AAC9D,QAAA,IAAI,IAAI,IAAI,aAAa,EAAE;YACzB,MAAM,kBAAkB,GAAG,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC;AAClG,YAAA,MAAM,QAAQ,GAAG,CAAA,EAAG,IAAI,CAAC,IAAI,EAAE;AAC/B,YAAA,MAAM,QAAQ,GAAG,CAAA,EAAG,IAAI,CAAC,IAAI,EAAE;YAC/B,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;AAElD,YAAA,OAAO,kBAAkB,CAAC,IAAI,CAAC,IAAI,IAAG;AACpC,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE;gBAC7B,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AAC/B,oBAAA,QACE;AACG,yBAAA,WAAW;yBACX,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE,QAAQ,CAAC,WAAW,EAAE,CAAC,MAAM,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;AAEvG,qBAAA,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;;oBAElC,OAAO,YAAY,KAAK,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;;gBAExD,OAAO,QAAQ,KAAK,SAAS;AAC/B,aAAC,CAAC;;AAEJ,QAAA,OAAO,IAAI;;AAGL,IAAA,SAAS,CAAC,IAAkB,EAAA;AAClC,QAAA,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;AACb,YAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;;AAEpD,QAAA,OAAO,IAAI;;AAGb,IAAA,WAAW,CAAC,QAA2B,EAAA;AACrC,QAAA,IAAI,QAAQ,GAA+B,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACnF,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAG;gBAC/B,QAAQ,GAAG,QAAQ,CAAC,IAAI,CACtB,SAAS,CAAC,IAAI,IAAG;oBACf,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;AACxB,oBAAA,OAAO,KAAK,YAAY,UAAU,GAAG,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;iBACvD,CAAC,CACH;AACH,aAAC,CAAC;;QAEJ,QAAQ,CAAC,SAAS,CAAC;YACjB,IAAI,EAAE,IAAI,IAAG;AACX,gBAAA,IAAI,CAAC,OAAO,CAAC,CAAC,IAAkB,KAAI;AAClC,oBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AACpB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;AACzB,iBAAC,CAAC;aACH;YACD,KAAK,EAAE,CAAC,IAAG;AACT,gBAAA,IAAI,CAAC,CAAA,6BAAA,CAA+B,EAAE,CAAC,CAAC;;AAE3C,SAAA,CAAC;;IAGI,MAAM,CAAC,IAAkB,EAAE,QAAwB,EAAA;AACzD,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;AAC9B,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;;AAExB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC;AACxD,QAAA,IAAI,MAAM,YAAY,UAAU,EAAE;YAChC,MAAM,CAAC,SAAS,CAAC;AACf,gBAAA,IAAI,EAAE,CAAC,aAA2B,KAAI;AACpC,oBAAA,MAAM,iBAAiB,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;oBACvE,IAAI,iBAAiB,KAAK,eAAe,IAAI,iBAAiB,KAAK,eAAe,EAAE;AAClF,wBAAA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;AAC7B,wBAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;;yBACnB,IAAI,aAAa,EAAE;AACxB,wBAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;;iBAElB;gBACD,KAAK,EAAE,CAAC,IAAG;AACT,oBAAA,IAAI,CAAC,CAAA,mCAAA,CAAqC,EAAE,CAAC,CAAC;;AAEjD,aAAA,CAAC;;aACG,IAAI,MAAM,EAAE;AACjB,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;;;AAIlB,IAAA,IAAI,CAAC,IAAkB,EAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB;;AAEF,QAAA,IAAI,QAAQ,GAAoD,EAAE,CAAC,IAAI,CAAC;AACxE,QAAA,IAAI,eAAgE;AACpE,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO;AACxB,QAAA,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;QACpB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,GAAG;AAEpD,QAAA,MAAM,IAAI,GAAoB;AAC5B,YAAA,MAAM,EAAE,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,EAAE;YAChD,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,OAAO;YACP,IAAI;AACJ,YAAA,QAAQ,EAAE,IAAI;YACd,IAAI;YACJ,eAAe,EAAE,GAAG,CAAC,eAAe;YACpC,UAAU,EAAE,GAAG,CAAC;kBACZ,CAAC,IAAG;AACF,oBAAA,GAAG,CAAC,UAAW,CAAC,CAAC,EAAE,IAAI,CAAC;;AAE5B,kBAAE,SAAS;AACb,YAAA,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,KAAI;AACtB,gBAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gBACf,GAAG,CAAC,SAAU,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;aAC/B;YACD,OAAO,EAAE,GAAG,IAAG;AACb,gBAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;AACf,gBAAA,GAAG,CAAC,OAAQ,CAAC,GAAG,EAAE,IAAI,CAAC;;SAE1B;AAED,QAAA,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;AAChC,YAAA,MAAM,YAAY,GAAI,MAA8D,CAAC,IAAI,CAAC;AAC1F,YAAA,IAAI,YAAY,YAAY,UAAU,EAAE;AACtC,gBAAA,QAAQ,GAAG,QAAQ,CAAC,IAAI,CACtB,SAAS,CAAC,MAAM,YAAY,CAAC,EAC7B,GAAG,CAAC,GAAG,IAAG;AACR,oBAAA,IAAI,CAAC,MAAM,GAAG,GAAG;AACjB,oBAAA,OAAO,IAAI;iBACZ,CAAC,CACH;;iBACI;AACL,gBAAA,IAAI,CAAC,MAAM,GAAG,YAAY;;;AAI9B,QAAA,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE;AACvC,YAAA,MAAM,eAAe,GAAG,aAAa,CAAC,IAAI,CAAC;AAC3C,YAAA,QAAQ,GAAG,QAAQ,CAAC,IAAI,CACtB,SAAS,CAAC,OAAO,eAAe,YAAY,UAAU,GAAG,eAAe,GAAG,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,EAChG,GAAG,CAAC,OAAO,KAAK,eAAe,GAAG,OAAO,CAAC,CAAC,CAC5C;;AAGH,QAAA,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;AAC9B,YAAA,MAAM,UAAU,GAAI,IAAoD,CAAC,IAAI,CAAC;AAC9E,YAAA,IAAI,UAAU,YAAY,UAAU,EAAE;AACpC,gBAAA,QAAQ,GAAG,QAAQ,CAAC,IAAI,CACtB,SAAS,CAAC,MAAM,UAAU,CAAC,EAC3B,GAAG,CAAC,GAAG,IAAG;AACR,oBAAA,IAAI,CAAC,IAAI,GAAG,GAAG;oBACf,OAAO,eAAe,IAAI,IAAI;iBAC/B,CAAC,CACH;;iBACI;AACL,gBAAA,IAAI,CAAC,IAAI,GAAG,UAAU;;;AAI1B,QAAA,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;AACjC,YAAA,MAAM,aAAa,GAAI,OAAuD,CAAC,IAAI,CAAC;AACpF,YAAA,IAAI,aAAa,YAAY,UAAU,EAAE;AACvC,gBAAA,QAAQ,GAAG,QAAQ,CAAC,IAAI,CACtB,SAAS,CAAC,MAAM,aAAa,CAAC,EAC9B,GAAG,CAAC,GAAG,IAAG;AACR,oBAAA,IAAI,CAAC,OAAO,GAAG,GAAG;oBAClB,OAAO,eAAe,IAAI,IAAI;iBAC/B,CAAC,CACH;;iBACI;AACL,gBAAA,IAAI,CAAC,OAAO,GAAG,aAAa;;;AAIhC,QAAA,QAAQ,CAAC,SAAS,CAAC,OAAO,IAAG;AAC3B,YAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;AACvB,YAAA,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,aAAa,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AAC7D,YAAA,IAAI,EAAE,IAAI,YAAY,YAAY,CAAC,EAAE;gBACnC,IAAI,CAAC,CAA+D,6DAAA,CAAA,CAAC;;AAEvE,YAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI;AACrB,YAAA,GAAG,CAAC,OAAQ,CAAC,IAAI,CAAC;AACpB,SAAC,CAAC;;AAGI,IAAA,GAAG,CAAC,IAAqB,EAAA;AAC/B,QAAA,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE;AAE/B,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAG;AAC/B,gBAAA,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAK,CAAC,GAAG,CAAC,CAAC;AACvC,aAAC,CAAC;;QAGJ,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAK,EAAE,IAAI,CAAC,QAAqB,CAAC;AAEvD,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,IAAI,CAAC,OAAO,GAAG,EAAE;;QAEnB,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;AAC7C,YAAA,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,gBAAgB;;aAC9C;AACL,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;;AAEzC,QAAA,MAAM,GAAG,GAAG,IAAI,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,MAAO,EAAE,QAAQ,EAAE;AAC1D,YAAA,cAAc,EAAE,IAAI;YACpB,eAAe,EAAE,IAAI,CAAC,eAAe;AACrC,YAAA,OAAO,EAAE,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO;AACtC,SAAA,CAAC;QACF,OAAO,IAAI,CAAC,IAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;AACvC,YAAA,IAAI,EAAE,CAAC,KAA2B,KAAI;gBACpC,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC,cAAc,EAAE;AAC/C,oBAAA,IAAI,KAAK,CAAC,KAAM,GAAG,CAAC,EAAE;AACnB,wBAAA,KAAmB,CAAC,OAAO,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,KAAM,IAAI,GAAG;;oBAEpE,IAAI,CAAC,UAAW,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC;;AAC7B,qBAAA,IAAI,KAAK,YAAY,YAAY,EAAE;AACxC,oBAAA,IAAI,CAAC,SAAU,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;;aAEhD;YACD,KAAK,EAAE,GAAG,IAAG;AACX,gBAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;gBACrB,IAAI,CAAC,OAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC;;AAEhC,SAAA,CAAC;;AAGI,IAAA,KAAK,CAAC,GAAW,EAAA;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;AAC3B,QAAA,IAAI,IAAI,YAAY,YAAY,EAAE;YAChC,IAAI,CAAC,WAAW,EAAE;;AAEpB,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;;AAGvB,IAAA,KAAK,CAAC,IAAmB,EAAA;QACvB,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC;;aACvB;YACL,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;;IAIlD,IAAI,GAAG,MAAM,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAErD,IAAA,WAAA,CAAoB,UAAsB,EAAA;QAAtB,IAAU,CAAA,UAAA,GAAV,UAAU;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACd,YAAA,MAAM,IAAI,KAAK,CACb,CAAA,sGAAA,CAAwG,CACzG;;;IAIL,QAAQ,GAAA;;;QAGN,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,OAAO;AAC3D,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC7B,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QAElC,uBAAuB,CAAgB,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,SAAS;AAC5E,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC7B,SAAS,CAAC,KAAK,IAAG;AACjB,YAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;gBACzB;;AAEF,YAAA,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,EAAE;gBACpD,IAAI,CAAC,OAAO,EAAE;;AAElB,SAAC,CAAC;;IAGN,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI;AACnB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;QACpB,IAAI,CAAC,KAAK,EAAE;;uGAhVH,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAApB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,oBAAoB,+dChCjC,8fAgBA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDgBa,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAfhC,SAAS;+BACE,iBAAiB,EAAA,QAAA,EACjB,aAAa,EAEjB,IAAA,EAAA;AACJ,wBAAA,KAAK,EAAE,YAAY;AACnB,wBAAA,iBAAiB,EAAE,KAAK;AACxB,wBAAA,aAAa,EAAE,UAAU;AACzB,wBAAA,6BAA6B,EAAE,kBAAkB;AACjD,wBAAA,QAAQ,EAAE,oBAAoB;AAC9B,wBAAA,YAAY,EAAE;AACf,qBAAA,EAAA,mBAAA,EACoB,KAAK,EAAA,aAAA,EACX,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,8fAAA,EAAA;+EAMA,IAAI,EAAA,CAAA;sBAAxC,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBAC1B,OAAO,EAAA,CAAA;sBAAf;;;AErCH;;;AAGG;AA4BH,MAAM,eAAe,GAAG,CAAC,IAAY,KAAc,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;AAEzF,MAAM,YAAY,GAAG,GAAG;MAoCX,qBAAqB,CAAA;AAmMtB,IAAA,GAAA;AACA,IAAA,MAAA;AACA,IAAA,QAAA;IApMV,IAAI,GAAqB,EAAE;AAE3B,IAAA,IAAY,OAAO,GAAA;QACjB,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,cAAc;;IAG/D,MAAM,GAAc,EAAE;AACtB,IAAA,QAAQ;IACjB,IACI,KAAK,CAAC,IAAoB,EAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI;;AAET,IAAA,KAAK;AACL,IAAA,SAAS;AACT,IAAA,QAAQ;AACR,IAAA,UAAU;AACV,IAAA,WAAW;AACX,IAAA,cAAc;IACd,UAAU,GAAgC,IAAI;IAC9C,GAAG,GAAc,KAAK;AAEvB,IAAA,QAAQ,GAAa,MAAM,CAAC,QAAQ,CAAC;AACrC,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;AAE9B,IAAA,MAAM,CAAC,IAAkB,EAAA;QAC/B,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACtD,OAAO,IAAI,CAAC,QAAQ;;AAEtB,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,KAAK,IAAI,CAAC,MAAM,CAAC,WAAW;;AAGjE,IAAA,OAAO,CAAC,GAAW,EAAA;QACzB,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QACtC,MAAM,qBAAqB,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACvD,QAAA,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;AAG/D,IAAA,UAAU,CAAC,IAAkB,EAAA;AAC3B,QAAA,IAAI,eAAe,CAAC,IAAI,CAAC,IAAK,CAAC,EAAE;AAC/B,YAAA,OAAO,IAAI;;AAEb,QAAA,MAAM,GAAG,IAAY,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,CAAW;QAC/D,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,OAAO,KAAK;;QAEd,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;AACnC,QAAA,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,4CAA4C,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AAC7F,YAAA,OAAO,IAAI;;AACN,aAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;;AAE7B,YAAA,OAAO,KAAK;;aACP,IAAI,SAAS,EAAE;;AAEpB,YAAA,OAAO,KAAK;;AAEd,QAAA,OAAO,IAAI;;AAGL,IAAA,WAAW,CAAC,IAAoB,EAAA;AACtC,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,OAAO,EAAE;;AAEX,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AACrD,YAAA,OAAO,WAAW;;aACb;AACL,YAAA,OAAO,WAAW;;;AAId,IAAA,YAAY,CAAC,IAAiB,EAAA;AACpC,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;AAC3D,YAAA,OAAO,EAAE,CAAC,EAAE,CAAC;;QAGf,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC;AACpD,QAAA,MAAM,CAAC,KAAK,GAAG,YAAY;AAC3B,QAAA,MAAM,CAAC,MAAM,GAAG,YAAY;QAC5B,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,4CAA4C,YAAY,CAAA,YAAA,EAAe,YAAY,CAAA,iCAAA,CAAmC;QAC7I,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACtC,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;AACnC,QAAA,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE;QACvB,MAAM,SAAS,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC;AAC3C,QAAA,GAAG,CAAC,GAAG,GAAG,SAAS;AACnB,QAAA,OAAO,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,IAAI,CAChC,GAAG,CAAC,MAAK;AACP,YAAA,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,GAAG;YAE7B,IAAI,SAAS,GAAG,YAAY;YAC5B,IAAI,UAAU,GAAG,YAAY;YAC7B,IAAI,OAAO,GAAG,CAAC;YACf,IAAI,OAAO,GAAG,CAAC;AAEf,YAAA,IAAI,KAAK,GAAG,MAAM,EAAE;gBAClB,UAAU,GAAG,MAAM,IAAI,YAAY,GAAG,KAAK,CAAC;gBAC5C,OAAO,GAAG,EAAE,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC;;iBAClC;gBACL,SAAS,GAAG,KAAK,IAAI,YAAY,GAAG,MAAM,CAAC;gBAC3C,OAAO,GAAG,EAAE,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC;;AAGzC,YAAA,IAAI;AACF,gBAAA,GAAI,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC;;AAC5D,YAAA,MAAM;;;AAGR,YAAA,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,EAAE;YAClC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;AAEtC,YAAA,GAAG,CAAC,eAAe,CAAC,SAAS,CAAC;AAC9B,YAAA,OAAO,OAAO;SACf,CAAC,CACH;;IAGK,QAAQ,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YAC5B;;QAGF,MAAM,GAAG,GAAG,MAAmB;QAC/B,IACE,CAAC,IAAI,CAAC,OAAO;YACb,OAAO,QAAQ,KAAK,WAAW;YAC/B,OAAO,GAAG,KAAK,WAAW;YAC1B,CAAC,GAAG,CAAC,UAAU;AACf,YAAA,CAAC,GAAG,CAAC,IAAI,EACT;YACA;;AAEF,QAAA,IAAI,CAAC;AACF,aAAA,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,YAAY,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS;aAChF,OAAO,CAAC,IAAI,IAAG;AACd,YAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;;;AAIlB,YAAA,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAc,CAAC,EAAE,IAAI,CACxG,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CACzB;AACD,YAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAK;AACjC,gBAAA,QAAQ,CAAC,SAAS,CAAC,OAAO,IAAG;AAC3B,oBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAK;AACnB,wBAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;wBACvB,IAAI,CAAC,aAAa,EAAE;AACtB,qBAAC,CAAC;AACJ,iBAAC,CAAC;AACJ,aAAC,CAAC;AACJ,SAAC,CAAC;;AAGE,IAAA,YAAY,CAAC,IAAkB,EAAA;AACrC,QAAA,OAAO,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC;;IAG1D,OAAO,GAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAG;YACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,KAAK,WAAW;YAC9C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YAChC,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS;YACjG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACzF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACtC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AAC7C,SAAC,CAAC;;IAGJ,aAAa,CAAC,IAAkB,EAAE,CAAQ,EAAA;AACxC,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB;;QAGF,CAAC,CAAC,cAAc,EAAE;AAClB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;IAG7B,YAAY,CAAC,IAAkB,EAAE,CAAQ,EAAA;QACvC,CAAC,CAAC,cAAc,EAAE;AAClB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;;QAErB;;AAGF,IAAA,cAAc,CAAC,IAAkB,EAAA;AAC/B,QAAA,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,UAAU,EAAE;AACzC,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;;AAChB,aAAA,IAAI,IAAI,CAAC,GAAG,EAAE;AACnB,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;;;;AAMzB,IAAA,WAAA,CACU,GAAsB,EACtB,MAAc,EACd,QAAkB,EAAA;QAFlB,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAQ,CAAA,QAAA,GAAR,QAAQ;;IAGlB,aAAa,GAAA;QACX,IAAI,CAAC,OAAO,EAAE;AACd,QAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;;IAG1B,WAAW,GAAA;QACT,IAAI,CAAC,OAAO,EAAE;QACd,IAAI,CAAC,QAAQ,EAAE;;IAGjB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;;uGAnNX,qBAAqB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,qBAAqB,ECrElC,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,GAAA,EAAA,KAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,2BAAA,EAAA,eAAA,EAAA,4BAAA,EAAA,qBAAA,EAAA,+BAAA,EAAA,wBAAA,EAAA,oCAAA,EAAA,6BAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,qlMAqKA,EDlGY,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,eAAe,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,kBAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,uBAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,iBAAA,EAAA,kBAAA,EAAA,0BAAA,EAAA,0BAAA,EAAA,2BAAA,EAAA,uBAAA,EAAA,6BAAA,EAAA,yBAAA,EAAA,gBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,EAAE,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,cAAc,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,QAAA,EAAA,iCAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,QAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,2BAAA,EAAA,QAAA,EAAA,8IAAA,EAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,gBAAgB,EAnB/E,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,mBAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,YAAA,EAAA,SAAA,EAAA,eAAA,EAAA,QAAA,EAAA,UAAA,EAAA,kBAAA,EAAA,WAAA,EAAA,eAAA,EAAA,aAAA,EAAA,UAAA,EAAA,QAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA;YACV,OAAO,CAAC,WAAW,EAAE;gBACnB,UAAU,CAAC,QAAQ,EAAE;AACnB,oBAAA,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;AAC9C,oBAAA,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;iBAC5D,CAAC;gBACF,UAAU,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACpF;AACF,SAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAaU,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAzBjC,SAAS;+BACE,gBAAgB,EAAA,QAAA,EAChB,cAAc,EAEZ,UAAA,EAAA;wBACV,OAAO,CAAC,WAAW,EAAE;4BACnB,UAAU,CAAC,QAAQ,EAAE;AACnB,gCAAA,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;AAC9C,gCAAA,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;6BAC5D,CAAC;4BACF,UAAU,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;yBACpF;qBACF,EACK,IAAA,EAAA;AACJ,wBAAA,KAAK,EAAE,iBAAiB;AACxB,wBAAA,6BAA6B,EAAE,CAAe,aAAA,CAAA;AAC9C,wBAAA,8BAA8B,EAAE,CAAqB,mBAAA,CAAA;AACrD,wBAAA,iCAAiC,EAAE,CAAwB,sBAAA,CAAA;AAC3D,wBAAA,sCAAsC,EAAE,CAA6B,2BAAA;qBACtE,EACoB,mBAAA,EAAA,KAAK,iBACX,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,OAAA,EACtC,CAAC,eAAe,EAAE,gBAAgB,EAAE,YAAY,EAAE,cAAc,EAAE,gBAAgB,CAAC,EAAA,QAAA,EAAA,qlMAAA,EAAA;kIASnF,MAAM,EAAA,CAAA;sBAAd;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBAEG,KAAK,EAAA,CAAA;sBADR;gBAIQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,GAAG,EAAA,CAAA;sBAAX;;;ME7BU,iBAAiB,CAAA;AAsHlB,IAAA,GAAA;AACA,IAAA,IAAA;AACA,IAAA,cAAA;IAvHV,OAAO,kCAAkC;AAEjC,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;AACM,IAAA,UAAU;AACZ,IAAA,QAAQ;AAElD,IAAA,MAAM;IACN,GAAG,GAAc,KAAK;;IAIb,MAAM,GAAiB,QAAQ;IACD,OAAO,GAAG,CAAC;IACX,MAAM,GAAG,CAAC;AAExC,IAAA,UAAU;AACV,IAAA,QAAQ;AACR,IAAA,QAAQ;IACuB,WAAW,GAAG,KAAK;IACnB,uBAAuB,GAAG,IAAI;AAC7D,IAAA,cAAc;AACd,IAAA,eAAe;AACf,IAAA,MAAM;IACN,QAAQ,GAAmB,EAAE;IAC7B,UAAU,GAAmB,EAAE;IACA,UAAU,GAAG,KAAK;AACjD,IAAA,SAAS;IACT,UAAU,GAAqB,MAAM;IACN,UAAU,GAAG,KAAK;IACjD,MAAM,GAAG,MAAM;IAEhB,eAAe,GAA+B,IAAI;AAClD,IAAA,QAAQ,GAAa,MAAM,CAAC,QAAQ,CAAC;IAE7C,IACI,gBAAgB,CAAC,KAAiC,EAAA;AACpD,QAAA,IAAI,CAAC,eAAe,GAAG,OAAO,KAAK,KAAK,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK;;AAG9E,IAAA,IAAI,gBAAgB,GAAA;QAClB,OAAO,IAAI,CAAC,eAAe;;IAGW,YAAY,GAAG,IAAI;IACnB,iBAAiB,GAAG,KAAK;AAExD,IAAA,QAAQ;AACR,IAAA,SAAS;AACT,IAAA,aAAa;AACb,IAAA,gBAAgB;AAChB,IAAA,eAAe;AACf,IAAA,UAAU;IACV,YAAY,GAAgC,IAAI;IAChD,gBAAgB,GAAsD,IAAI;AAEhE,IAAA,QAAQ,GAAsC,IAAI,YAAY,EAAuB;AACrF,IAAA,gBAAgB,GAAiC,IAAI,YAAY,EAAkB;AAEtG,IAAA,WAAW;IAEH,UAAU,GAAA;QAChB,IAAI,OAAO,IAAI,CAAC,gBAAgB,KAAK,SAAS,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvE,IAAI,CAAC,gBAAgB,GAAG;AACtB,gBAAA,eAAe,EAAE,IAAI;AACrB,gBAAA,cAAc,EAAE,IAAI;AACpB,gBAAA,gBAAgB,EAAE;aACnB;;;QAGH,MAAM,OAAO,GAAmB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACrD,QAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5F,OAAO,CAAC,IAAI,CAAC;AACX,gBAAA,IAAI,EAAE,OAAO;AACb,gBAAA,EAAE,EAAE,CAAC,QAAwB,KAAK,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO;AAC/D,aAAA,CAAC;;QAEJ,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;YACvE,OAAO,CAAC,IAAI,CAAC;AACX,gBAAA,IAAI,EAAE,MAAM;gBACZ,EAAE,EAAE,CAAC,QAAwB,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,IAAK,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM;AACrF,aAAA,CAAC;;AAEJ,QAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;YACrG,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC;AACX,gBAAA,IAAI,EAAE,MAAM;gBACZ,EAAE,EAAE,CAAC,QAAwB,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAK,CAAC;AAC/E,aAAA,CAAC;;QAEJ,IAAI,CAAC,WAAW,GAAG;YACjB,QAAQ,EAAE,IAAI,CAAC,UAAU;YACzB,MAAM,EAAE,IAAI,CAAC,QAAQ;YACrB,MAAM,EAAE,IAAI,CAAC,QAAQ;YACrB,SAAS,EAAE,IAAI,CAAC,WAAW;YAC3B,qBAAqB,EAAE,IAAI,CAAC,uBAAuB;YACnD,YAAY,EAAE,IAAI,CAAC,cAAc;YACjC,aAAa,EAAE,IAAI,CAAC,eAAe;YACnC,IAAI,EAAE,IAAI,CAAC,MAAM;YACjB,OAAO,EAAE,IAAI,CAAC,SAAS;YACvB,IAAI,EAAE,IAAI,CAAC,MAAM;YACjB,QAAQ,EAAE,IAAI,CAAC,UAAU;YACzB,eAAe,EAAE,IAAI,CAAC,iBAAiB;YACvC,OAAO;YACP,aAAa,EAAE,IAAI,CAAC,eAAe;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,OAAO,EAAE,IAAI,CAAC;SACf;AACD,QAAA,OAAO,IAAI;;AAGI,IAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;;AAI5C,IAAA,WAAA,CACU,GAAsB,EACtB,IAAmB,EACnB,cAA8B,EAAA;QAF9B,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAc,CAAA,cAAA,GAAd,cAAc;;;AAKhB,IAAA,YAAY,CAAC,IAAkB,EAAA;QACrC,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;AACvC,YAAA,IAAI,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI;YAChC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,KAAK,EAAE,IAAI,CAAC,KAAK;AACjB,YAAA,OAAO,EAAE,CAAC;AACV,YAAA,aAAa,EAAE;SAChB;;IAGK,WAAW,CAAC,IAAkB,EAAE,QAAwB,EAAA;AAC9D,QAAA,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAGlD,cAAc,CAAC,IAAkB,EAAE,QAAwB,EAAA;AACjE,QAAA,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC;;AAG/C,IAAA,OAAO,GAAG,CAAC,IAAkB,KAAU;AAC7C,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;QAEtB,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AAC1C,QAAA,UAAU,CAAC,MAAM,GAAG,WAAW;QAC/B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC;QACpD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;QAC3C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;QAClF,IAAI,CAAC,iBAAiB,EAAE;AAC1B,KAAC;AAEO,IAAA,UAAU,GAAG,CAAC,CAAsB,EAAE,IAAkB,KAAU;AACxE,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC;AACnD,QAAA,UAAU,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO;AAC9B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,IAAI,EAAE,EAAE,GAAG,UAAU,EAAE;YACvB,QAAQ,EAAE,IAAI,CAAC,UAAU;AACzB,YAAA,IAAI,EAAE;AACP,SAAA,CAAC;QACF,IAAI,CAAC,iBAAiB,EAAE;AAC1B,KAAC;AAEO,IAAA,SAAS,GAAG,CAAC,GAAO,EAAE,IAAkB,KAAU;AACxD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC;AACnD,QAAA,UAAU,CAAC,MAAM,GAAG,MAAM;AAC1B,QAAA,UAAU,CAAC,QAAQ,GAAG,GAAG;AACzB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,YAAA,IAAI,EAAE,EAAE,GAAG,UAAU,EAAE;YACvB,QAAQ;AACR,YAAA,IAAI,EAAE;AACP,SAAA,CAAC;QACF,IAAI,CAAC,iBAAiB,EAAE;AAC1B,KAAC;AAEO,IAAA,OAAO,GAAG,CAAC,GAAO,EAAE,IAAkB,KAAU;AACtD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC;AACnD,QAAA,UAAU,CAAC,KAAK,GAAG,GAAG;AACtB,QAAA,UAAU,CAAC,MAAM,GAAG,OAAO;AAC3B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,YAAA,IAAI,EAAE,EAAE,GAAG,UAAU,EAAE;YACvB,QAAQ;AACR,YAAA,IAAI,EAAE;AACP,SAAA,CAAC;QACF,IAAI,CAAC,iBAAiB,EAAE;AAC1B,KAAC;;;AAMO,IAAA,SAAS;;AAGjB,IAAA,QAAQ,CAAC,CAAY,EAAA;QACnB,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE;YAC7B;;AAEF,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI;QACvB,IAAI,CAAC,WAAW,EAAE;;;;IAOZ,iBAAiB,GAAA;AACvB,QAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AACxB,QAAA,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE;;AAGhC,IAAA,QAAQ,GAAG,CAAC,IAAkB,KAAU;AACtC,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;AAC3B,QAAA,IAAI,CAAC,MAAM,GAAG,SAAS;AACvB,QAAA,MAAM,KAAK,GACT,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,QAAQ;AAC1G,QAAA,CAAC,KAAK,YAAY,UAAU,GAAG,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAY,KAAK,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AACnG,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC;AAC5D,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjB,IAAI;gBACJ,QAAQ,EAAE,IAAI,CAAC,UAAU;AACzB,gBAAA,IAAI,EAAE;AACP,aAAA,CAAC;YACF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,SAAC,CAAC;AACJ,KAAC;;;IAMO,SAAS,GAAG,YAAY;IAChC,SAAS,GAAa,EAAE;IAEhB,WAAW,GAAA;QACjB,IAAI,MAAM,GAAa,EAAE;AACzB,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE;AAC1B,YAAA,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC,EAAE;gBAC7D,MAAM,CAAC,IAAI,CAAC,CAAA,EAAG,IAAI,CAAC,SAAS,CAAiB,eAAA,CAAA,CAAC;;AAEjD,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,UAAU,EAAE;gBACjC,MAAM,CAAC,IAAI,CAAC,CAAA,EAAG,IAAI,CAAC,SAAS,CAAa,WAAA,CAAA,CAAC;;;aAExC;AACL,YAAA,MAAM,GAAG,CAAC,CAAG,EAAA,IAAI,CAAC,SAAS,CAAW,QAAA,EAAA,IAAI,CAAC,UAAU,CAAE,CAAA,CAAC;;QAG1D,IAAI,CAAC,SAAS,GAAG;AACf,YAAA,IAAI,CAAC,SAAS;AACd,YAAA,CAAA,EAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAE,CAAA;AAClC,YAAA,GAAG,MAAM;YACT,CAAC,IAAI,CAAC,UAAU,IAAI,CAAA,EAAG,IAAI,CAAC,SAAS,CAAA,SAAA,CAAW,KAAK,EAAE;AACvD,YAAA,CAAC,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,CAAG,EAAA,IAAI,CAAC,SAAS,CAAM,IAAA,CAAA,KAAK;SACpD,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC;AAExB,QAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;;;IAK1B,QAAQ,GAAA;QACN,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;QACpC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,YAAA,IAAI,CAAC,GAAG,GAAG,SAAS;YACpB,IAAI,CAAC,WAAW,EAAE;AAClB,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YACnE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC/C,IAAI,CAAC,iBAAiB,EAAE;AAC1B,SAAC,CAAC;;IAGJ,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;;YAEzB,uBAAuB,CAAa,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM;AAC3D,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,KAAK,IAAG;gBACjB,KAAK,CAAC,cAAc,EAAE;gBACtB,KAAK,CAAC,eAAe,EAAE;AACzB,aAAC,CAAC;;;IAIR,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE;;IAGjC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;uGAjTf,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAjB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAaR,eAAe,CACf,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,eAAe,qHAKf,gBAAgB,CAAA,EAAA,uBAAA,EAAA,CAAA,yBAAA,EAAA,yBAAA,EAChB,gBAAgB,CAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAMhB,gBAAgB,CAAA,EAAA,SAAA,EAAA,WAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAGhB,gBAAgB,CAehB,EAAA,MAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAChB,EAAA,iBAAA,EAAA,CAAA,mBAAA,EAAA,mBAAA,EAAA,gBAAgB,CCzGtC,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,uCAAA,EAAA,iCAAA,EAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,ixDAgDA,4CDUY,qBAAqB,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,OAAA,EAAA,WAAA,EAAA,UAAA,EAAA,YAAA,EAAA,aAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,KAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,oBAAoB,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE5D,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAZ7B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,UAAU,EAEC,mBAAA,EAAA,KAAK,EACX,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA;AACJ,wBAAA,yCAAyC,EAAE;AAC5C,qBAAA,EAAA,OAAA,EACQ,CAAC,qBAAqB,EAAE,gBAAgB,EAAE,oBAAoB,CAAC,EAAA,QAAA,EAAA,ixDAAA,EAAA;mJAM5B,UAAU,EAAA,CAAA;sBAArD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,YAAY,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACA,QAAQ,EAAA,CAAA;sBAAjD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBAO/B,MAAM,EAAA,CAAA;sBAAd;gBACsC,OAAO,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACE,MAAM,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBAE5B,UAAU,EAAA,CAAA;sBAAlB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACuC,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,uBAAuB,EAAA,CAAA;sBAA9D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,cAAc,EAAA,CAAA;sBAAtB;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACuC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,SAAS,EAAA,CAAA;sBAAjB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACuC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,MAAM,EAAA,CAAA;sBAAd;gBAMG,gBAAgB,EAAA,CAAA;sBADnB;gBASuC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,iBAAiB,EAAA,CAAA;sBAAxD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAE7B,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,gBAAgB,EAAA,CAAA;sBAAxB;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,gBAAgB,EAAA,CAAA;sBAAxB;gBAEkB,QAAQ,EAAA,CAAA;sBAA1B;gBACkB,gBAAgB,EAAA,CAAA;sBAAlC;;;AErHH;;;AAGG;MAYU,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAHf,iBAAiB,EAAE,oBAAoB,EAAE,qBAAqB,aAC9D,iBAAiB,CAAA,EAAA,CAAA;wGAEhB,cAAc,EAAA,OAAA,EAAA,CAHf,iBAAiB,EAAwB,qBAAqB,CAAA,EAAA,CAAA;;2FAG7D,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,iBAAiB,EAAE,oBAAoB,EAAE,qBAAqB,CAAC;oBACzE,OAAO,EAAE,CAAC,iBAAiB;AAC5B,iBAAA;;;ACdD;;;AAGG;;ACHH;;AAEG;;;;"}