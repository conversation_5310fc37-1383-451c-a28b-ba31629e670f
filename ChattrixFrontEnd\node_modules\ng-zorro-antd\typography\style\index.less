@import '../../style/themes/index';
@import '../../style/mixins/index';

@typography-prefix-cls: ~'@{ant-prefix}-typography';

// =============== Basic ===============
.@{typography-prefix-cls} {
  color: @text-color;
  word-break: break-word;

  &&-secondary {
    color: @text-color-secondary;
  }

  &&-success {
    color: @success-color;
  }

  &&-warning {
    color: @warning-color;
  }

  &&-danger {
    color: @error-color;

    a&:active,
    a&:focus {
      color: @error-color-active;
    }

    a&:hover {
      color: @error-color-hover;
    }
  }

  &&-disabled {
    color: @disabled-color;
    cursor: not-allowed;
    user-select: none;
  }

  // Tag
  div&,
  p {
    .typography-paragraph();
  }

  h1&,
  div&-h1,
  div&-h1 > textarea,
  h1 {
    .typography-title-1();
  }

  h2&,
  div&-h2,
  div&-h2 > textarea,
  h2 {
    .typography-title-2();
  }

  h3&,
  div&-h3,
  div&-h3 > textarea,
  h3 {
    .typography-title-3();
  }

  h4&,
  div&-h4,
  div&-h4 > textarea,
  h4 {
    .typography-title-4();
  }

  h5&,
  div&-h5,
  div&-h5 > textarea,
  h5 {
    .typography-title-5();
  }

  h1&,
  h2&,
  h3&,
  h4&,
  h5& {
    .@{typography-prefix-cls} + & {
      margin-top: @typography-title-margin-top;
    }
  }

  div,
  ul,
  li,
  p,
  h1,
  h2,
  h3,
  h4,
  h5 {
    + h1,
    + h2,
    + h3,
    + h4,
    + h5 {
      margin-top: @typography-title-margin-top;
    }
  }

  a&-ellipsis,
  span&-ellipsis {
    display: inline-block;
    max-width: 100%;
  }

  a&,
  a {
    .operation-unit();
    text-decoration: @link-decoration;

    &:active,
    &:hover {
      text-decoration: @link-hover-decoration;
    }

    &[disabled],
    &.@{typography-prefix-cls}-disabled {
      color: @disabled-color;
      cursor: not-allowed;

      &:active,
      &:hover {
        color: @disabled-color;
      }

      &:active {
        pointer-events: none;
      }
    }
  }

  code {
    margin: 0 0.2em;
    padding: 0.2em 0.4em 0.1em;
    font-size: 85%;
    background: rgba(150, 150, 150, 0.1);
    border: 1px solid rgba(100, 100, 100, 0.2);
    border-radius: 3px;
  }

  kbd {
    margin: 0 0.2em;
    padding: 0.15em 0.4em 0.1em;
    font-size: 90%;
    background: rgba(150, 150, 150, 0.06);
    border: 1px solid rgba(100, 100, 100, 0.2);
    border-bottom-width: 2px;
    border-radius: 3px;
  }

  mark {
    padding: 0;
    background-color: @gold-3;
  }

  u,
  ins {
    text-decoration: underline;
    text-decoration-skip-ink: auto;
  }

  s,
  del {
    text-decoration: line-through;
  }

  strong {
    font-weight: 600;
  }

  // Operation
  &-expand,
  &-edit,
  &-copy {
    .operation-unit();

    margin-left: 4px;
  }

  &-copy-success {
    &,
    &:hover,
    &:focus {
      color: @success-color;
    }
  }

  // Text input area
  &-edit-content {
    position: relative;

    div& {
      left: -@input-padding-horizontal - 1px;
      margin-top: -@input-padding-vertical-base - 1px;
      margin-bottom: calc(1em - @input-padding-vertical-base - 1px);
    }

    &-confirm {
      position: absolute;
      right: 10px;
      bottom: 8px;
      color: @text-color-secondary;
      // default style
      font-weight: normal;
      font-size: @font-size-base;
      font-style: normal;
      pointer-events: none;
    }

    // Fix Editable Textarea flash in Firefox
    textarea {
      // https://stackoverflow.com/a/7695964/3040605
      height: 1em;
      margin: 0 !important;
      /* stylelint-disable-next-line property-no-vendor-prefix */
      -moz-transition: none;
    }
  }

  // list
  ul,
  ol {
    margin: 0 0 1em;
    padding: 0;

    li {
      margin: 0 0 0 20px;
      padding: 0 0 0 4px;
    }
  }

  ul {
    list-style-type: circle;

    ul {
      list-style-type: disc;
    }
  }

  ol {
    list-style-type: decimal;
  }

  // pre & block
  pre,
  blockquote {
    margin: 1em 0;
  }

  pre {
    padding: 0.4em 0.6em;
    white-space: pre-wrap;
    word-wrap: break-word;
    background: rgba(150, 150, 150, 0.1);
    border: 1px solid rgba(100, 100, 100, 0.2);
    border-radius: 3px;

    // Compatible for marked
    code {
      display: inline;
      margin: 0;
      padding: 0;
      font-size: inherit;
      font-family: inherit;
      background: transparent;
      border: 0;
    }
  }

  blockquote {
    padding: 0 0 0 0.6em;
    border-left: 4px solid rgba(100, 100, 100, 0.2);
    opacity: 0.85;
  }

  // ============ Ellipsis ============
  &-single-line {
    white-space: nowrap;
  }

  &-ellipsis-single-line {
    overflow: hidden;
    text-overflow: ellipsis;

    // https://blog.csdn.net/iefreer/article/details/50421025
    a&,
    span& {
      vertical-align: bottom;
    }
  }

  &-ellipsis-multiple-line {
    /* stylelint-disable-next-line value-no-vendor-prefix */
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 3;

    /*! autoprefixer: ignore next */
    -webkit-box-orient: vertical;
  }
}

@import './rtl';
