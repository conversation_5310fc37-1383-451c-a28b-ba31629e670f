{"version": 3, "file": "ng-zorro-antd-tree.mjs", "sources": ["../../components/tree/tree-drop-indicator.component.ts", "../../components/tree/tree-indent.component.ts", "../../components/tree/tree-node-checkbox.component.ts", "../../components/tree/tree-node-switcher.component.ts", "../../components/tree/tree-node-title.component.ts", "../../components/tree/tree-node.component.ts", "../../components/tree/tree.service.ts", "../../components/tree/tree.component.ts", "../../components/tree/tree.module.ts", "../../components/tree/public-api.ts", "../../components/tree/ng-zorro-antd-tree.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Input,\n  OnChanges,\n  SimpleChanges,\n  numberAttribute\n} from '@angular/core';\n\nimport { NgStyleInterface } from 'ng-zorro-antd/core/types';\n\n@Component({\n  selector: 'nz-tree-drop-indicator',\n  exportAs: 'NzTreeDropIndicator',\n  template: ``,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  preserveWhitespaces: false,\n  host: {\n    '[class.ant-tree-drop-indicator]': 'true',\n    '[style]': 'style'\n  }\n})\nexport class NzTreeDropIndicatorComponent implements OnChanges {\n  @Input() dropPosition?: number;\n  @Input({ transform: numberAttribute }) level: number = 1;\n  @Input() direction: string = 'ltr';\n  style: NgStyleInterface = {};\n\n  constructor(private cdr: ChangeDetectorRef) {}\n\n  ngOnChanges(_changes: SimpleChanges): void {\n    this.renderIndicator(this.dropPosition!, this.direction);\n  }\n\n  renderIndicator(dropPosition: number, direction: string = 'ltr'): void {\n    const offset = 4;\n    const startPosition = direction === 'ltr' ? 'left' : 'right';\n    const endPosition = direction === 'ltr' ? 'right' : 'left';\n    const style: NgStyleInterface = {\n      [startPosition]: `${offset}px`,\n      [endPosition]: '0px'\n    };\n    switch (dropPosition) {\n      case -1:\n        style.top = `${-3}px`;\n        break;\n      case 1:\n        style.bottom = `${-3}px`;\n        break;\n      case 0:\n        // dropPosition === 0\n        style.bottom = `${-3}px`;\n        style[startPosition] = `${offset + 24}px`;\n        break;\n      default:\n        style.display = 'none';\n        break;\n    }\n    this.style = style;\n    this.cdr.markForCheck();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { ChangeDetectionStrategy, Component, Input, OnChanges, SimpleChanges } from '@angular/core';\n\n@Component({\n  selector: 'nz-tree-indent',\n  exportAs: 'nzTreeIndent',\n  template: `\n    @for (_ of listOfUnit; track i; let i = $index) {\n      <span\n        [class.ant-tree-indent-unit]=\"!nzSelectMode\"\n        [class.ant-select-tree-indent-unit]=\"nzSelectMode\"\n        [class.ant-select-tree-indent-unit-start]=\"nzSelectMode && nzIsStart[i]\"\n        [class.ant-tree-indent-unit-start]=\"!nzSelectMode && nzIsStart[i]\"\n        [class.ant-select-tree-indent-unit-end]=\"nzSelectMode && nzIsEnd[i]\"\n        [class.ant-tree-indent-unit-end]=\"!nzSelectMode && nzIsEnd[i]\"\n      ></span>\n    }\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  preserveWhitespaces: false,\n  host: {\n    '[attr.aria-hidden]': 'true',\n    '[class.ant-tree-indent]': '!nzSelectMode',\n    '[class.ant-select-tree-indent]': 'nzSelectMode'\n  }\n})\nexport class NzTreeIndentComponent implements OnChanges {\n  @Input() nzTreeLevel = 0;\n  @Input() nzIsStart: boolean[] = [];\n  @Input() nzIsEnd: boolean[] = [];\n  @Input() nzSelectMode = false;\n\n  listOfUnit: number[] = [];\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzTreeLevel } = changes;\n    if (nzTreeLevel) {\n      this.listOfUnit = [...new Array(nzTreeLevel.currentValue || 0)];\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { ChangeDetectionStrategy, Component, Input, booleanAttribute } from '@angular/core';\n\n@Component({\n  selector: 'nz-tree-node-checkbox[builtin]',\n  template: `\n    <span [class.ant-tree-checkbox-inner]=\"!nzSelectMode\" [class.ant-select-tree-checkbox-inner]=\"nzSelectMode\"></span>\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  preserveWhitespaces: false,\n  host: {\n    '[class.ant-select-tree-checkbox]': `nzSelectMode`,\n    '[class.ant-select-tree-checkbox-checked]': `nzSelectMode && isChecked`,\n    '[class.ant-select-tree-checkbox-indeterminate]': `nzSelectMode && isHalfChecked`,\n    '[class.ant-select-tree-checkbox-disabled]': `nzSelectMode && (isDisabled || isDisableCheckbox)`,\n    '[class.ant-tree-checkbox]': `!nzSelectMode`,\n    '[class.ant-tree-checkbox-checked]': `!nzSelectMode && isChecked`,\n    '[class.ant-tree-checkbox-indeterminate]': `!nzSelectMode && isHalfChecked`,\n    '[class.ant-tree-checkbox-disabled]': `!nzSelectMode && (isDisabled || isDisableCheckbox)`\n  }\n})\nexport class NzTreeNodeBuiltinCheckboxComponent {\n  @Input() nzSelectMode = false;\n  @Input({ transform: booleanAttribute }) isChecked?: boolean;\n  @Input({ transform: booleanAttribute }) isHalfChecked?: boolean;\n  @Input({ transform: booleanAttribute }) isDisabled?: boolean;\n  @Input({ transform: booleanAttribute }) isDisableCheckbox?: boolean;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { ChangeDetectionStrategy, Component, Input, TemplateRef, booleanAttribute } from '@angular/core';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzTreeNode, NzTreeNodeOptions } from 'ng-zorro-antd/core/tree';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\n@Component({\n  selector: 'nz-tree-node-switcher',\n  template: `\n    @if (isShowSwitchIcon) {\n      @if (!isLoading) {\n        <ng-container *nzStringTemplateOutlet=\"nzExpandedIcon; context: { $implicit: context, origin: context.origin }\">\n          <nz-icon\n            nzType=\"caret-down\"\n            [class.ant-select-tree-switcher-icon]=\"nzSelectMode\"\n            [class.ant-tree-switcher-icon]=\"!nzSelectMode\"\n          />\n        </ng-container>\n      } @else {\n        <nz-icon nzType=\"loading\" [nzSpin]=\"true\" class=\"ant-tree-switcher-loading-icon\" />\n      }\n    }\n    @if (nzShowLine) {\n      @if (!isLoading) {\n        <ng-container *nzStringTemplateOutlet=\"nzExpandedIcon; context: { $implicit: context, origin: context.origin }\">\n          @if (isShowLineIcon) {\n            <nz-icon [nzType]=\"isSwitcherOpen ? 'minus-square' : 'plus-square'\" class=\"ant-tree-switcher-line-icon\" />\n          } @else {\n            <nz-icon nzType=\"file\" class=\"ant-tree-switcher-line-icon\" />\n          }\n        </ng-container>\n      } @else {\n        <nz-icon nzType=\"loading\" [nzSpin]=\"true\" class=\"ant-tree-switcher-loading-icon\" />\n      }\n    }\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  preserveWhitespaces: false,\n  host: {\n    '[class.ant-select-tree-switcher]': 'nzSelectMode',\n    '[class.ant-select-tree-switcher-noop]': 'nzSelectMode && isLeaf',\n    '[class.ant-select-tree-switcher_open]': 'nzSelectMode && isSwitcherOpen',\n    '[class.ant-select-tree-switcher_close]': 'nzSelectMode && isSwitcherClose',\n    '[class.ant-tree-switcher]': '!nzSelectMode',\n    '[class.ant-tree-switcher-noop]': '!nzSelectMode && isLeaf',\n    '[class.ant-tree-switcher_open]': '!nzSelectMode && isSwitcherOpen',\n    '[class.ant-tree-switcher_close]': '!nzSelectMode && isSwitcherClose'\n  },\n  imports: [NzIconModule, NzOutletModule]\n})\nexport class NzTreeNodeSwitcherComponent {\n  @Input({ transform: booleanAttribute }) nzShowExpand?: boolean;\n  @Input({ transform: booleanAttribute }) nzShowLine?: boolean;\n  @Input() nzExpandedIcon?: TemplateRef<{ $implicit: NzTreeNode; origin: NzTreeNodeOptions }>;\n  @Input() nzSelectMode = false;\n  @Input() context!: NzTreeNode;\n  @Input({ transform: booleanAttribute }) isLeaf?: boolean;\n  @Input({ transform: booleanAttribute }) isLoading?: boolean;\n  @Input({ transform: booleanAttribute }) isExpanded?: boolean;\n\n  get isShowLineIcon(): boolean {\n    return !this.isLeaf && !!this.nzShowLine;\n  }\n\n  get isShowSwitchIcon(): boolean {\n    return !this.isLeaf && !this.nzShowLine;\n  }\n\n  get isSwitcherOpen(): boolean {\n    return !!this.isExpanded && !this.isLeaf;\n  }\n\n  get isSwitcherClose(): boolean {\n    return !this.isExpanded && !this.isLeaf;\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Input,\n  OnChanges,\n  SimpleChanges,\n  TemplateRef,\n  booleanAttribute\n} from '@angular/core';\n\nimport { NzHighlightModule } from 'ng-zorro-antd/core/highlight';\nimport { NzTreeNode, NzTreeNodeOptions } from 'ng-zorro-antd/core/tree';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\nimport { NzTreeDropIndicatorComponent } from './tree-drop-indicator.component';\n\n@Component({\n  selector: 'nz-tree-node-title',\n  template: `\n    <ng-template\n      [ngTemplateOutlet]=\"treeTemplate\"\n      [ngTemplateOutletContext]=\"{ $implicit: context, origin: context.origin }\"\n    ></ng-template>\n    @if (!treeTemplate) {\n      @if (icon && showIcon) {\n        <span\n          [class.ant-tree-icon__open]=\"isSwitcherOpen\"\n          [class.ant-tree-icon__close]=\"isSwitcherClose\"\n          [class.ant-tree-icon_loading]=\"isLoading\"\n          [class.ant-select-tree-iconEle]=\"selectMode\"\n          [class.ant-tree-iconEle]=\"!selectMode\"\n        >\n          <span\n            [class.ant-select-tree-iconEle]=\"selectMode\"\n            [class.ant-select-tree-icon__customize]=\"selectMode\"\n            [class.ant-tree-iconEle]=\"!selectMode\"\n            [class.ant-tree-icon__customize]=\"!selectMode\"\n          >\n            <nz-icon [nzType]=\"icon\" />\n          </span>\n        </span>\n      }\n      <span class=\"ant-tree-title\" [innerHTML]=\"title | nzHighlight: matchedValue : 'i' : 'font-highlight'\"></span>\n    }\n    @if (showIndicator) {\n      <nz-tree-drop-indicator [dropPosition]=\"dragPosition\" [level]=\"context.level\"></nz-tree-drop-indicator>\n    }\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  preserveWhitespaces: false,\n  host: {\n    '[attr.title]': 'title',\n    '[attr.draggable]': 'canDraggable',\n    '[attr.aria-grabbed]': 'canDraggable',\n    '[class.draggable]': 'canDraggable',\n    '[class.ant-select-tree-node-content-wrapper]': `selectMode`,\n    '[class.ant-select-tree-node-content-wrapper-open]': `selectMode && isSwitcherOpen`,\n    '[class.ant-select-tree-node-content-wrapper-close]': `selectMode && isSwitcherClose`,\n    '[class.ant-select-tree-node-selected]': `selectMode && isSelected`,\n    '[class.ant-tree-node-content-wrapper]': `!selectMode`,\n    '[class.ant-tree-node-content-wrapper-open]': `!selectMode && isSwitcherOpen`,\n    '[class.ant-tree-node-content-wrapper-close]': `!selectMode && isSwitcherClose`,\n    '[class.ant-tree-node-selected]': `!selectMode && isSelected`\n  },\n  imports: [NgTemplateOutlet, NzIconModule, NzHighlightModule, NzTreeDropIndicatorComponent]\n})\nexport class NzTreeNodeTitleComponent implements OnChanges {\n  @Input() searchValue!: string;\n  @Input() treeTemplate: TemplateRef<{ $implicit: NzTreeNode; origin: NzTreeNodeOptions }> | null = null;\n  @Input({ transform: booleanAttribute }) draggable!: boolean;\n  @Input({ transform: booleanAttribute }) showIcon!: boolean;\n  @Input() selectMode = false;\n  @Input() context!: NzTreeNode;\n  @Input() icon!: string;\n  @Input() title!: string;\n  @Input({ transform: booleanAttribute }) isLoading!: boolean;\n  @Input({ transform: booleanAttribute }) isSelected!: boolean;\n  @Input({ transform: booleanAttribute }) isDisabled!: boolean;\n  @Input({ transform: booleanAttribute }) isMatched!: boolean;\n  @Input({ transform: booleanAttribute }) isExpanded!: boolean;\n  @Input({ transform: booleanAttribute }) isLeaf!: boolean;\n  // Drag indicator\n  @Input() showIndicator = true;\n  @Input() dragPosition?: number;\n\n  get canDraggable(): boolean | null {\n    return this.draggable && !this.isDisabled ? true : null;\n  }\n\n  get matchedValue(): string {\n    return this.isMatched ? this.searchValue : '';\n  }\n\n  get isSwitcherOpen(): boolean {\n    return this.isExpanded && !this.isLeaf;\n  }\n\n  get isSwitcherClose(): boolean {\n    return !this.isExpanded && !this.isLeaf;\n  }\n\n  constructor(private cdr: ChangeDetectorRef) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { showIndicator, dragPosition } = changes;\n    if (showIndicator || dragPosition) {\n      this.cdr.markForCheck();\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  Renderer2,\n  SimpleChanges,\n  TemplateRef,\n  booleanAttribute,\n  inject\n} from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport {\n  NzFormatBeforeDropEvent,\n  NzFormatEmitEvent,\n  NzTreeBaseService,\n  NzTreeNode,\n  NzTreeNodeOptions\n} from 'ng-zorro-antd/core/tree';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\n\nimport { NzTreeIndentComponent } from './tree-indent.component';\nimport { NzTreeNodeBuiltinCheckboxComponent } from './tree-node-checkbox.component';\nimport { NzTreeNodeSwitcherComponent } from './tree-node-switcher.component';\nimport { NzTreeNodeTitleComponent } from './tree-node-title.component';\n\n@Component({\n  selector: 'nz-tree-node[builtin]',\n  exportAs: 'nzTreeBuiltinNode',\n  template: `\n    <nz-tree-indent\n      [nzTreeLevel]=\"nzTreeNode.level\"\n      [nzSelectMode]=\"nzSelectMode\"\n      [nzIsStart]=\"isStart\"\n      [nzIsEnd]=\"isEnd\"\n    ></nz-tree-indent>\n    @if (nzShowExpand) {\n      <nz-tree-node-switcher\n        [nzShowExpand]=\"nzShowExpand\"\n        [nzShowLine]=\"nzShowLine\"\n        [nzExpandedIcon]=\"nzExpandedIcon\"\n        [nzSelectMode]=\"nzSelectMode\"\n        [context]=\"nzTreeNode\"\n        [isLeaf]=\"isLeaf\"\n        [isExpanded]=\"isExpanded\"\n        [isLoading]=\"isLoading\"\n        (click)=\"clickExpand($event)\"\n      ></nz-tree-node-switcher>\n    }\n    @if (nzCheckable) {\n      <nz-tree-node-checkbox\n        builtin\n        (click)=\"clickCheckbox($event)\"\n        [nzSelectMode]=\"nzSelectMode\"\n        [isChecked]=\"isChecked\"\n        [isHalfChecked]=\"isHalfChecked\"\n        [isDisabled]=\"isDisabled\"\n        [isDisableCheckbox]=\"isDisableCheckbox\"\n      ></nz-tree-node-checkbox>\n    }\n    <nz-tree-node-title\n      [icon]=\"icon\"\n      [title]=\"title\"\n      [isLoading]=\"isLoading\"\n      [isSelected]=\"isSelected\"\n      [isDisabled]=\"isDisabled\"\n      [isMatched]=\"isMatched\"\n      [isExpanded]=\"isExpanded\"\n      [isLeaf]=\"isLeaf\"\n      [searchValue]=\"nzSearchValue\"\n      [treeTemplate]=\"nzTreeTemplate\"\n      [draggable]=\"nzDraggable\"\n      [showIcon]=\"nzShowIcon\"\n      [selectMode]=\"nzSelectMode\"\n      [context]=\"nzTreeNode\"\n      [showIndicator]=\"showIndicator\"\n      [dragPosition]=\"dragPos\"\n      (dblclick)=\"dblClick($event)\"\n      (click)=\"clickSelect($event)\"\n      (contextmenu)=\"contextMenu($event)\"\n    ></nz-tree-node-title>\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  preserveWhitespaces: false,\n  host: {\n    '[class.ant-select-tree-treenode]': `nzSelectMode`,\n    '[class.ant-select-tree-treenode-disabled]': `nzSelectMode && isDisabled`,\n    '[class.ant-select-tree-treenode-switcher-open]': `nzSelectMode && isSwitcherOpen`,\n    '[class.ant-select-tree-treenode-switcher-close]': `nzSelectMode && isSwitcherClose`,\n    '[class.ant-select-tree-treenode-checkbox-checked]': `nzSelectMode && isChecked`,\n    '[class.ant-select-tree-treenode-checkbox-indeterminate]': `nzSelectMode && isHalfChecked`,\n    '[class.ant-select-tree-treenode-selected]': `nzSelectMode && isSelected`,\n    '[class.ant-select-tree-treenode-loading]': `nzSelectMode && isLoading`,\n    '[class.ant-tree-treenode]': `!nzSelectMode`,\n    '[class.ant-tree-treenode-disabled]': `!nzSelectMode && isDisabled`,\n    '[class.ant-tree-treenode-switcher-open]': `!nzSelectMode && isSwitcherOpen`,\n    '[class.ant-tree-treenode-switcher-close]': `!nzSelectMode && isSwitcherClose`,\n    '[class.ant-tree-treenode-checkbox-checked]': `!nzSelectMode && isChecked`,\n    '[class.ant-tree-treenode-checkbox-indeterminate]': `!nzSelectMode && isHalfChecked`,\n    '[class.ant-tree-treenode-selected]': `!nzSelectMode && isSelected`,\n    '[class.ant-tree-treenode-loading]': `!nzSelectMode && isLoading`,\n    '[class.dragging]': `draggingKey === nzTreeNode.key`,\n    '[style.display]': 'displayStyle'\n  },\n  imports: [\n    NzTreeIndentComponent,\n    NzTreeNodeSwitcherComponent,\n    NzTreeNodeBuiltinCheckboxComponent,\n    NzTreeNodeTitleComponent\n  ]\n})\nexport class NzTreeNodeBuiltinComponent implements OnInit, OnChanges, OnDestroy {\n  /**\n   * for global property\n   */\n  @Input() icon: string = '';\n  @Input() title: string = '';\n  @Input({ transform: booleanAttribute }) isLoading: boolean = false;\n  @Input({ transform: booleanAttribute }) isSelected: boolean = false;\n  @Input({ transform: booleanAttribute }) isDisabled: boolean = false;\n  @Input({ transform: booleanAttribute }) isMatched: boolean = false;\n  @Input({ transform: booleanAttribute }) isExpanded!: boolean;\n  @Input({ transform: booleanAttribute }) isLeaf!: boolean;\n  @Input({ transform: booleanAttribute }) isChecked?: boolean;\n  @Input({ transform: booleanAttribute }) isHalfChecked?: boolean;\n  @Input({ transform: booleanAttribute }) isDisableCheckbox?: boolean;\n  @Input({ transform: booleanAttribute }) isSelectable?: boolean;\n  @Input({ transform: booleanAttribute }) canHide?: boolean;\n  @Input() isStart: boolean[] = [];\n  @Input() isEnd: boolean[] = [];\n  @Input() nzTreeNode!: NzTreeNode;\n  @Input({ transform: booleanAttribute }) nzShowLine?: boolean;\n  @Input({ transform: booleanAttribute }) nzShowExpand?: boolean;\n  @Input({ transform: booleanAttribute }) nzCheckable?: boolean;\n  @Input({ transform: booleanAttribute }) nzAsyncData?: boolean;\n  @Input({ transform: booleanAttribute }) nzHideUnMatched = false;\n  @Input({ transform: booleanAttribute }) nzNoAnimation = false;\n  @Input({ transform: booleanAttribute }) nzSelectMode = false;\n  @Input({ transform: booleanAttribute }) nzShowIcon = false;\n  @Input() nzExpandedIcon?: TemplateRef<{ $implicit: NzTreeNode; origin: NzTreeNodeOptions }>;\n  @Input() nzTreeTemplate: TemplateRef<{ $implicit: NzTreeNode; origin: NzTreeNodeOptions }> | null = null;\n  @Input() nzBeforeDrop?: (confirm: NzFormatBeforeDropEvent) => Observable<boolean>;\n  @Input() nzSearchValue = '';\n  @Input({ transform: booleanAttribute }) nzDraggable: boolean = false;\n  @Output() readonly nzClick = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzDblClick = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzContextMenu = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzCheckboxChange = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzExpandChange = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzOnDragStart = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzOnDragEnter = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzOnDragOver = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzOnDragLeave = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzOnDrop = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzOnDragEnd = new EventEmitter<NzFormatEmitEvent>();\n\n  /**\n   * drag var\n   */\n  destroy$ = new Subject<boolean>();\n  dragPos = 2;\n  dragPosClass: Record<string, string> = {\n    0: 'drag-over',\n    1: 'drag-over-gap-bottom',\n    '-1': 'drag-over-gap-top'\n  };\n  draggingKey: string | null = null;\n  showIndicator = false;\n  /**\n   * default set\n   */\n  get displayStyle(): string {\n    // to hide unmatched nodes\n    return this.nzSearchValue && this.nzHideUnMatched && !this.isMatched && !this.isExpanded && this.canHide\n      ? 'none'\n      : '';\n  }\n\n  get isSwitcherOpen(): boolean {\n    return this.isExpanded && !this.isLeaf;\n  }\n\n  get isSwitcherClose(): boolean {\n    return !this.isExpanded && !this.isLeaf;\n  }\n\n  /**\n   * collapse node\n   *\n   * @param event\n   */\n  clickExpand(event: MouseEvent): void {\n    event.preventDefault();\n    if (!this.isLoading && !this.isLeaf) {\n      // set async state\n      if (this.nzAsyncData && this.nzTreeNode.children.length === 0 && !this.isExpanded) {\n        this.nzTreeNode.isLoading = true;\n      }\n      this.nzTreeNode.setExpanded(!this.isExpanded);\n    }\n    this.nzTreeService.setExpandedNodeList(this.nzTreeNode);\n    const eventNext = this.nzTreeService.formatEvent('expand', this.nzTreeNode, event);\n    this.nzExpandChange.emit(eventNext);\n  }\n\n  clickSelect(event: MouseEvent): void {\n    event.preventDefault();\n    if (this.isSelectable && !this.isDisabled) {\n      this.nzTreeNode.isSelected = !this.nzTreeNode.isSelected;\n    }\n    this.nzTreeService.setSelectedNodeList(this.nzTreeNode);\n    const eventNext = this.nzTreeService.formatEvent('click', this.nzTreeNode, event);\n    this.nzClick.emit(eventNext);\n  }\n\n  dblClick(event: MouseEvent): void {\n    event.preventDefault();\n    const eventNext = this.nzTreeService.formatEvent('dblclick', this.nzTreeNode, event);\n    this.nzDblClick.emit(eventNext);\n  }\n\n  contextMenu(event: MouseEvent): void {\n    event.preventDefault();\n    const eventNext = this.nzTreeService.formatEvent('contextmenu', this.nzTreeNode, event);\n    this.nzContextMenu.emit(eventNext);\n  }\n\n  /**\n   * check node\n   *\n   * @param event\n   */\n  clickCheckbox(event: MouseEvent): void {\n    event.preventDefault();\n    // return if node is disabled\n    if (this.isDisabled || this.isDisableCheckbox) {\n      return;\n    }\n    this.nzTreeNode.isChecked = !this.nzTreeNode.isChecked;\n    this.nzTreeNode.isHalfChecked = false;\n    this.nzTreeService.setCheckedNodeList(this.nzTreeNode);\n    const eventNext = this.nzTreeService.formatEvent('check', this.nzTreeNode, event);\n    this.nzCheckboxChange.emit(eventNext);\n  }\n\n  clearDragClass(): void {\n    const dragClass = ['drag-over-gap-top', 'drag-over-gap-bottom', 'drag-over', 'drop-target'];\n    dragClass.forEach(e => {\n      this.renderer.removeClass(this.elementRef.nativeElement, e);\n    });\n  }\n\n  /**\n   * drag event\n   *\n   * @param e\n   */\n  handleDragStart(e: DragEvent): void {\n    try {\n      // ie throw error\n      // firefox-need-it\n      e.dataTransfer!.setData('text/plain', this.nzTreeNode.key!);\n    } catch {\n      // noop\n    }\n    this.nzTreeService.setSelectedNode(this.nzTreeNode);\n    this.draggingKey = this.nzTreeNode.key;\n    const eventNext = this.nzTreeService.formatEvent('dragstart', this.nzTreeNode, e);\n    this.nzOnDragStart.emit(eventNext);\n  }\n\n  handleDragEnter(e: DragEvent): void {\n    e.preventDefault();\n    // reset position\n    this.showIndicator = this.nzTreeNode.key !== this.nzTreeService.getSelectedNode()?.key;\n    this.renderIndicator(2);\n    this.ngZone.run(() => {\n      const eventNext = this.nzTreeService.formatEvent('dragenter', this.nzTreeNode, e);\n      this.nzOnDragEnter.emit(eventNext);\n    });\n  }\n\n  handleDragOver(e: DragEvent): void {\n    e.preventDefault();\n    const dropPosition = this.nzTreeService.calcDropPosition(e);\n    if (this.dragPos !== dropPosition) {\n      this.clearDragClass();\n      this.renderIndicator(dropPosition);\n      // leaf node will pass\n      if (!(this.dragPos === 0 && this.isLeaf)) {\n        this.renderer.addClass(this.elementRef.nativeElement, this.dragPosClass[this.dragPos]);\n        this.renderer.addClass(this.elementRef.nativeElement, 'drop-target');\n      }\n    }\n    const eventNext = this.nzTreeService.formatEvent('dragover', this.nzTreeNode, e);\n    this.nzOnDragOver.emit(eventNext);\n  }\n\n  handleDragLeave(e: DragEvent): void {\n    e.preventDefault();\n    this.renderIndicator(2);\n    this.clearDragClass();\n    const eventNext = this.nzTreeService.formatEvent('dragleave', this.nzTreeNode, e);\n    this.nzOnDragLeave.emit(eventNext);\n  }\n\n  handleDragDrop(e: DragEvent): void {\n    e.preventDefault();\n    e.stopPropagation();\n    this.ngZone.run(() => {\n      this.showIndicator = false;\n      this.clearDragClass();\n      const node = this.nzTreeService.getSelectedNode();\n      if (!node || (node && node.key === this.nzTreeNode.key) || (this.dragPos === 0 && this.isLeaf)) {\n        return;\n      }\n      // pass if node is leafNo\n      const dropEvent = this.nzTreeService.formatEvent('drop', this.nzTreeNode, e);\n      const dragEndEvent = this.nzTreeService.formatEvent('dragend', this.nzTreeNode, e);\n      if (this.nzBeforeDrop) {\n        this.nzBeforeDrop({\n          dragNode: this.nzTreeService.getSelectedNode()!,\n          node: this.nzTreeNode,\n          pos: this.dragPos\n        }).subscribe((canDrop: boolean) => {\n          if (canDrop) {\n            this.nzTreeService.dropAndApply(this.nzTreeNode, this.dragPos);\n          }\n          this.nzOnDrop.emit(dropEvent);\n          this.nzOnDragEnd.emit(dragEndEvent);\n        });\n      } else if (this.nzTreeNode) {\n        this.nzTreeService.dropAndApply(this.nzTreeNode, this.dragPos);\n        this.nzOnDrop.emit(dropEvent);\n      }\n    });\n  }\n\n  handleDragEnd(e: DragEvent): void {\n    e.preventDefault();\n    this.ngZone.run(() => {\n      // if user do not custom beforeDrop\n      if (!this.nzBeforeDrop) {\n        // clear dragging state\n        this.draggingKey = null;\n        const eventNext = this.nzTreeService.formatEvent('dragend', this.nzTreeNode, e);\n        this.nzOnDragEnd.emit(eventNext);\n      } else {\n        // clear dragging state\n        this.draggingKey = null;\n        this.markForCheck();\n      }\n    });\n  }\n\n  /**\n   * Listening to dragging events.\n   */\n  handDragEvent(): void {\n    if (this.nzDraggable) {\n      const nativeElement = this.elementRef.nativeElement;\n      this.destroy$ = new Subject();\n      fromEventOutsideAngular<DragEvent>(nativeElement, 'dragstart')\n        .pipe(takeUntil(this.destroy$))\n        .subscribe((e: DragEvent) => this.handleDragStart(e));\n      fromEventOutsideAngular<DragEvent>(nativeElement, 'dragenter')\n        .pipe(takeUntil(this.destroy$))\n        .subscribe((e: DragEvent) => this.handleDragEnter(e));\n      fromEventOutsideAngular<DragEvent>(nativeElement, 'dragover')\n        .pipe(takeUntil(this.destroy$))\n        .subscribe((e: DragEvent) => this.handleDragOver(e));\n      fromEventOutsideAngular<DragEvent>(nativeElement, 'dragleave')\n        .pipe(takeUntil(this.destroy$))\n        .subscribe((e: DragEvent) => this.handleDragLeave(e));\n      fromEventOutsideAngular<DragEvent>(nativeElement, 'drop')\n        .pipe(takeUntil(this.destroy$))\n        .subscribe((e: DragEvent) => this.handleDragDrop(e));\n      fromEventOutsideAngular<DragEvent>(nativeElement, 'dragend')\n        .pipe(takeUntil(this.destroy$))\n        .subscribe((e: DragEvent) => this.handleDragEnd(e));\n    } else {\n      this.destroy$.next(true);\n      this.destroy$.complete();\n    }\n  }\n\n  markForCheck(): void {\n    this.cdr.markForCheck();\n  }\n\n  noAnimation = inject(NzNoAnimationDirective, { host: true, optional: true });\n\n  constructor(\n    public nzTreeService: NzTreeBaseService,\n    private ngZone: NgZone,\n    private renderer: Renderer2,\n    private elementRef: ElementRef<HTMLElement>,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  ngOnInit(): void {\n    this.nzTreeNode.component = this;\n\n    fromEventOutsideAngular(this.elementRef.nativeElement, 'mousedown')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(event => {\n        if (this.nzSelectMode) {\n          event.preventDefault();\n        }\n      });\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzDraggable } = changes;\n    if (nzDraggable) {\n      this.handDragEvent();\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n\n  private renderIndicator(dropPosition: number): void {\n    this.ngZone.run(() => {\n      this.showIndicator = dropPosition !== 2;\n      if (this.nzTreeNode.key === this.nzTreeService.getSelectedNode()?.key || (dropPosition === 0 && this.isLeaf)) {\n        return;\n      }\n      this.dragPos = dropPosition;\n      this.cdr.markForCheck();\n    });\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Injectable } from '@angular/core';\n\nimport { NzTreeBaseService } from 'ng-zorro-antd/core/tree';\n\n@Injectable()\nexport class NzTreeService extends NzTreeBaseService {\n  constructor() {\n    super();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  booleanAttribute,\n  forwardRef,\n  inject\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Observable, Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { treeCollapseMotion } from 'ng-zorro-antd/core/animation';\nimport { NzConfigKey, NzConfigService, WithConfig } from 'ng-zorro-antd/core/config';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport {\n  NzFormatBeforeDropEvent,\n  NzFormatEmitEvent,\n  NzTreeBase,\n  NzTreeBaseService,\n  NzTreeHigherOrderServiceToken,\n  NzTreeNode,\n  NzTreeNodeKey,\n  NzTreeNodeOptions,\n  flattenTreeData\n} from 'ng-zorro-antd/core/tree';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\nimport { NzTreeNodeBuiltinComponent } from './tree-node.component';\nimport { NzTreeService } from './tree.service';\n\nexport function NzTreeServiceFactory(): NzTreeBaseService {\n  const higherOrderService = inject(NzTreeHigherOrderServiceToken, { skipSelf: true, optional: true });\n  const treeService = inject(NzTreeService);\n  return higherOrderService ?? treeService;\n}\n\nconst NZ_CONFIG_MODULE_NAME: NzConfigKey = 'tree';\n\n@Component({\n  selector: 'nz-tree',\n  exportAs: 'nzTree',\n  animations: [treeCollapseMotion],\n  template: `\n    <div>\n      <input [style]=\"HIDDEN_STYLE\" />\n    </div>\n    <div class=\"ant-tree-treenode\" [style]=\"HIDDEN_NODE_STYLE\">\n      <div class=\"ant-tree-indent\">\n        <div class=\"ant-tree-indent-unit\"></div>\n      </div>\n    </div>\n    <div class=\"ant-tree-list\" [class.ant-select-tree-list]=\"nzSelectMode\" style=\"position: relative\">\n      @if (nzVirtualHeight) {\n        <cdk-virtual-scroll-viewport\n          [class.ant-select-tree-list-holder-inner]=\"nzSelectMode\"\n          [class.ant-tree-list-holder-inner]=\"!nzSelectMode\"\n          [itemSize]=\"nzVirtualItemSize\"\n          [minBufferPx]=\"nzVirtualMinBufferPx\"\n          [maxBufferPx]=\"nzVirtualMaxBufferPx\"\n          [style.height]=\"nzVirtualHeight\"\n        >\n          <ng-container *cdkVirtualFor=\"let node of nzFlattenNodes; trackBy: trackByFlattenNode\">\n            <ng-template\n              [ngTemplateOutlet]=\"nodeTemplate\"\n              [ngTemplateOutletContext]=\"{ $implicit: node }\"\n            ></ng-template>\n          </ng-container>\n        </cdk-virtual-scroll-viewport>\n      } @else {\n        <div\n          [class.ant-select-tree-list-holder-inner]=\"nzSelectMode\"\n          [class.ant-tree-list-holder-inner]=\"!nzSelectMode\"\n          [@.disabled]=\"beforeInit || !!noAnimation?.nzNoAnimation\"\n          [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n          [@treeCollapseMotion]=\"nzFlattenNodes.length\"\n        >\n          @for (node of nzFlattenNodes; track trackByFlattenNode($index, node)) {\n            <ng-template\n              [ngTemplateOutlet]=\"nodeTemplate\"\n              [ngTemplateOutletContext]=\"{ $implicit: node }\"\n            ></ng-template>\n          }\n        </div>\n      }\n    </div>\n    <ng-template #nodeTemplate let-treeNode>\n      <nz-tree-node\n        builtin\n        [icon]=\"treeNode.icon\"\n        [title]=\"treeNode.title\"\n        [isLoading]=\"treeNode.isLoading\"\n        [isSelected]=\"treeNode.isSelected\"\n        [isDisabled]=\"treeNode.isDisabled\"\n        [isMatched]=\"treeNode.isMatched\"\n        [isExpanded]=\"treeNode.isExpanded\"\n        [isLeaf]=\"treeNode.isLeaf\"\n        [isStart]=\"treeNode.isStart\"\n        [isEnd]=\"treeNode.isEnd\"\n        [isChecked]=\"treeNode.isChecked\"\n        [isHalfChecked]=\"treeNode.isHalfChecked\"\n        [isDisableCheckbox]=\"treeNode.isDisableCheckbox\"\n        [isSelectable]=\"treeNode.isSelectable\"\n        [canHide]=\"treeNode.canHide\"\n        [nzTreeNode]=\"treeNode\"\n        [nzSelectMode]=\"nzSelectMode\"\n        [nzShowLine]=\"nzShowLine\"\n        [nzExpandedIcon]=\"nzExpandedIcon\"\n        [nzDraggable]=\"nzDraggable\"\n        [nzCheckable]=\"nzCheckable\"\n        [nzShowExpand]=\"nzShowExpand\"\n        [nzAsyncData]=\"nzAsyncData\"\n        [nzSearchValue]=\"nzSearchValue\"\n        [nzHideUnMatched]=\"nzHideUnMatched\"\n        [nzBeforeDrop]=\"nzBeforeDrop\"\n        [nzShowIcon]=\"nzShowIcon\"\n        [nzTreeTemplate]=\"nzTreeTemplate || nzTreeTemplateChild\"\n        (nzExpandChange)=\"eventTriggerChanged($event)\"\n        (nzClick)=\"eventTriggerChanged($event)\"\n        (nzDblClick)=\"eventTriggerChanged($event)\"\n        (nzContextMenu)=\"eventTriggerChanged($event)\"\n        (nzCheckboxChange)=\"eventTriggerChanged($event)\"\n        (nzOnDragStart)=\"eventTriggerChanged($event)\"\n        (nzOnDragEnter)=\"eventTriggerChanged($event)\"\n        (nzOnDragOver)=\"eventTriggerChanged($event)\"\n        (nzOnDragLeave)=\"eventTriggerChanged($event)\"\n        (nzOnDragEnd)=\"eventTriggerChanged($event)\"\n        (nzOnDrop)=\"eventTriggerChanged($event)\"\n      ></nz-tree-node>\n    </ng-template>\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    NzTreeService,\n    {\n      provide: NzTreeBaseService,\n      useFactory: NzTreeServiceFactory\n    },\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => NzTreeComponent),\n      multi: true\n    }\n  ],\n  host: {\n    '[class.ant-select-tree]': `nzSelectMode`,\n    '[class.ant-select-tree-show-line]': `nzSelectMode && nzShowLine`,\n    '[class.ant-select-tree-icon-hide]': `nzSelectMode && !nzShowIcon`,\n    '[class.ant-select-tree-block-node]': `nzSelectMode && nzBlockNode`,\n    '[class.ant-tree]': `!nzSelectMode`,\n    '[class.ant-tree-rtl]': `dir === 'rtl'`,\n    '[class.ant-tree-show-line]': `!nzSelectMode && nzShowLine`,\n    '[class.ant-tree-icon-hide]': `!nzSelectMode && !nzShowIcon`,\n    '[class.ant-tree-block-node]': `!nzSelectMode && nzBlockNode`,\n    '[class.draggable-tree]': `nzDraggable`\n  },\n  imports: [\n    CdkVirtualScrollViewport,\n    CdkFixedSizeVirtualScroll,\n    CdkVirtualForOf,\n    NgTemplateOutlet,\n    NzNoAnimationDirective,\n    NzTreeNodeBuiltinComponent\n  ]\n})\nexport class NzTreeComponent\n  extends NzTreeBase\n  implements OnInit, OnDestroy, ControlValueAccessor, OnChanges, AfterViewInit\n{\n  readonly _nzModuleName: NzConfigKey = NZ_CONFIG_MODULE_NAME;\n\n  @Input({ transform: booleanAttribute }) @WithConfig() nzShowIcon: boolean = false;\n  @Input({ transform: booleanAttribute }) @WithConfig() nzHideUnMatched: boolean = false;\n  @Input({ transform: booleanAttribute }) @WithConfig() nzBlockNode: boolean = false;\n  @Input({ transform: booleanAttribute }) nzExpandAll = false;\n  @Input({ transform: booleanAttribute }) nzSelectMode = false;\n  @Input({ transform: booleanAttribute }) nzCheckStrictly = false;\n  @Input({ transform: booleanAttribute }) nzShowExpand: boolean = true;\n  @Input({ transform: booleanAttribute }) nzShowLine = false;\n  @Input({ transform: booleanAttribute }) nzCheckable = false;\n  @Input({ transform: booleanAttribute }) nzAsyncData = false;\n  @Input({ transform: booleanAttribute }) nzDraggable: boolean = false;\n  @Input({ transform: booleanAttribute }) nzMultiple = false;\n  @Input() nzExpandedIcon?: TemplateRef<{ $implicit: NzTreeNode; origin: NzTreeNodeOptions }>;\n  @Input() nzVirtualItemSize = 28;\n  @Input() nzVirtualMaxBufferPx = 500;\n  @Input() nzVirtualMinBufferPx = 28;\n  @Input() nzVirtualHeight: string | null = null;\n  @Input() nzTreeTemplate?: TemplateRef<{ $implicit: NzTreeNode; origin: NzTreeNodeOptions }>;\n  @Input() nzBeforeDrop?: (confirm: NzFormatBeforeDropEvent) => Observable<boolean>;\n  @Input() nzData: NzTreeNodeOptions[] | NzTreeNode[] = [];\n  @Input() nzExpandedKeys: NzTreeNodeKey[] = [];\n  @Input() nzSelectedKeys: NzTreeNodeKey[] = [];\n  @Input() nzCheckedKeys: NzTreeNodeKey[] = [];\n  @Input() nzSearchValue: string = '';\n  @Input() nzSearchFunc?: (node: NzTreeNodeOptions) => boolean;\n  @ContentChild('nzTreeTemplate', { static: true }) nzTreeTemplateChild!: TemplateRef<{\n    $implicit: NzTreeNode;\n    origin: NzTreeNodeOptions;\n  }>;\n  @ViewChild(CdkVirtualScrollViewport, { read: CdkVirtualScrollViewport })\n  cdkVirtualScrollViewport!: CdkVirtualScrollViewport;\n  nzFlattenNodes: NzTreeNode[] = [];\n  beforeInit = true;\n  dir: Direction = 'ltr';\n\n  @Output() readonly nzExpandedKeysChange: EventEmitter<string[]> = new EventEmitter<string[]>();\n  @Output() readonly nzSelectedKeysChange: EventEmitter<string[]> = new EventEmitter<string[]>();\n  @Output() readonly nzCheckedKeysChange: EventEmitter<NzTreeNodeKey[]> = new EventEmitter<NzTreeNodeKey[]>();\n  @Output() readonly nzSearchValueChange = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzClick = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzDblClick = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzContextMenu = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzCheckboxChange = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzExpandChange = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzOnDragStart = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzOnDragEnter = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzOnDragOver = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzOnDragLeave = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzOnDrop = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzOnDragEnd = new EventEmitter<NzFormatEmitEvent>();\n\n  HIDDEN_STYLE = {\n    width: 0,\n    height: 0,\n    display: 'flex',\n    overflow: 'hidden',\n    opacity: 0,\n    border: 0,\n    padding: 0,\n    margin: 0\n  };\n\n  HIDDEN_NODE_STYLE = {\n    position: 'absolute',\n    pointerEvents: 'none',\n    visibility: 'hidden',\n    height: 0,\n    overflow: 'hidden'\n  };\n\n  destroy$ = new Subject<boolean>();\n\n  onChange: (value: NzTreeNode[]) => void = () => null;\n  onTouched: () => void = () => null;\n\n  writeValue(value: NzTreeNode[]): void {\n    this.handleNzData(value);\n  }\n\n  registerOnChange(fn: (_: NzTreeNode[]) => void): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this.onTouched = fn;\n  }\n\n  /**\n   * Render all properties of nzTree\n   *\n   * @param changes all changes from @Input\n   */\n  renderTreeProperties(changes: SimpleChanges): void {\n    let useDefaultExpandedKeys = false;\n    let expandAll = false;\n    const {\n      nzData,\n      nzExpandedKeys,\n      nzSelectedKeys,\n      nzCheckedKeys,\n      nzCheckStrictly,\n      nzExpandAll,\n      nzMultiple,\n      nzSearchValue\n    } = changes;\n\n    if (nzExpandAll) {\n      useDefaultExpandedKeys = true;\n      expandAll = this.nzExpandAll;\n    }\n\n    if (nzMultiple) {\n      this.nzTreeService.isMultiple = this.nzMultiple;\n    }\n\n    if (nzCheckStrictly) {\n      this.nzTreeService.isCheckStrictly = this.nzCheckStrictly;\n    }\n\n    if (nzData) {\n      this.handleNzData(this.nzData);\n    }\n\n    if (nzCheckedKeys) {\n      this.handleCheckedKeys(this.nzCheckedKeys);\n    }\n\n    if (nzCheckStrictly) {\n      this.handleCheckedKeys(null);\n    }\n\n    if (nzExpandedKeys || nzExpandAll) {\n      useDefaultExpandedKeys = true;\n      this.handleExpandedKeys(expandAll || this.nzExpandedKeys);\n    }\n\n    if (nzSelectedKeys) {\n      this.handleSelectedKeys(this.nzSelectedKeys, this.nzMultiple);\n    }\n\n    if (nzSearchValue) {\n      if (!(nzSearchValue.firstChange && !this.nzSearchValue)) {\n        useDefaultExpandedKeys = false;\n        this.handleSearchValue(nzSearchValue.currentValue, this.nzSearchFunc);\n        this.nzSearchValueChange.emit(this.nzTreeService.formatEvent('search', null, null));\n      }\n    }\n\n    // flatten data\n    const currentExpandedKeys = this.getExpandedNodeList().map(v => v.key);\n    const newExpandedKeys = useDefaultExpandedKeys ? expandAll || this.nzExpandedKeys : currentExpandedKeys;\n    this.handleFlattenNodes(this.nzTreeService.rootNodes, newExpandedKeys);\n  }\n\n  trackByFlattenNode(_: number, node: NzTreeNode): string {\n    return node.key;\n  }\n  // Deal with properties\n  /**\n   * nzData\n   *\n   * @param value\n   */\n  handleNzData(value: NzSafeAny[]): void {\n    if (Array.isArray(value)) {\n      const data = this.coerceTreeNodes(value);\n      this.nzTreeService.initTree(data);\n    }\n  }\n\n  handleFlattenNodes(data: NzTreeNode[], expandKeys: NzTreeNodeKey[] | true = []): void {\n    this.nzTreeService.flattenTreeData(data, expandKeys);\n  }\n\n  handleCheckedKeys(keys: NzTreeNodeKey[] | null): void {\n    this.nzTreeService.conductCheck(keys, this.nzCheckStrictly);\n  }\n\n  handleExpandedKeys(keys: NzTreeNodeKey[] | true = []): void {\n    this.nzTreeService.conductExpandedKeys(keys);\n  }\n\n  handleSelectedKeys(keys: NzTreeNodeKey[], isMulti: boolean): void {\n    this.nzTreeService.conductSelectedKeys(keys, isMulti);\n  }\n\n  handleSearchValue(value: string, searchFunc?: (node: NzTreeNodeOptions) => boolean): void {\n    const dataList = flattenTreeData(this.nzTreeService.rootNodes, true).map(v => v.data);\n    const checkIfMatched = (node: NzTreeNode): boolean => {\n      if (searchFunc) {\n        return searchFunc(node.origin);\n      }\n      return !!value && node.title.toLowerCase().includes(value.toLowerCase());\n    };\n    dataList.forEach(v => {\n      v.isMatched = checkIfMatched(v);\n      v.canHide = !v.isMatched;\n      if (!v.isMatched) {\n        v.setExpanded(false);\n        this.nzTreeService.setExpandedNodeList(v);\n      } else {\n        // expand\n        this.nzTreeService.expandNodeAllParentBySearch(v);\n      }\n      this.nzTreeService.setMatchedNodeList(v);\n    });\n  }\n\n  /**\n   * Handle emit event\n   *\n   * @param event\n   * handle each event\n   */\n  eventTriggerChanged(event: NzFormatEmitEvent): void {\n    const node = event.node!;\n    switch (event.eventName) {\n      case 'expand':\n        this.renderTree();\n        this.nzExpandChange.emit(event);\n        break;\n      case 'click':\n        this.nzClick.emit(event);\n        break;\n      case 'dblclick':\n        this.nzDblClick.emit(event);\n        break;\n      case 'contextmenu':\n        this.nzContextMenu.emit(event);\n        break;\n      case 'check': {\n        // Render checked state with nodes' property `isChecked`\n        this.nzTreeService.setCheckedNodeList(node);\n        if (!this.nzCheckStrictly) {\n          this.nzTreeService.conduct(node);\n        }\n        // Cause check method will rerender list, so we need recover it and next the new event to user\n        const eventNext = this.nzTreeService.formatEvent('check', node, event.event!);\n        this.nzCheckboxChange.emit(eventNext);\n        const checkedKeys = this.nzTreeService.getCheckedNodeKeys();\n        this.nzCheckedKeysChange.emit(checkedKeys);\n        break;\n      }\n      case 'dragstart':\n        // if node is expanded\n        if (node.isExpanded) {\n          node.setExpanded(!node.isExpanded);\n          this.renderTree();\n        }\n        this.nzOnDragStart.emit(event);\n        break;\n      case 'dragenter': {\n        const selectedNode = this.nzTreeService.getSelectedNode();\n        if (selectedNode && selectedNode.key !== node.key && !node.isExpanded && !node.isLeaf) {\n          node.setExpanded(true);\n          this.renderTree();\n        }\n        this.nzOnDragEnter.emit(event);\n        break;\n      }\n      case 'dragover':\n        this.nzOnDragOver.emit(event);\n        break;\n      case 'dragleave':\n        this.nzOnDragLeave.emit(event);\n        break;\n      case 'dragend':\n        this.nzOnDragEnd.emit(event);\n        break;\n      case 'drop':\n        this.renderTree();\n        this.nzOnDrop.emit(event);\n        break;\n    }\n  }\n\n  /**\n   * Click expand icon\n   */\n  renderTree(): void {\n    this.handleFlattenNodes(\n      this.nzTreeService.rootNodes,\n      this.getExpandedNodeList().map(v => v.key)\n    );\n    this.cdr.markForCheck();\n  }\n  // Handle emit event end\n\n  noAnimation = inject(NzNoAnimationDirective, { host: true, optional: true });\n\n  constructor(\n    nzTreeService: NzTreeBaseService,\n    public nzConfigService: NzConfigService,\n    private cdr: ChangeDetectorRef,\n    private directionality: Directionality\n  ) {\n    super(nzTreeService);\n  }\n\n  ngOnInit(): void {\n    this.nzTreeService.flattenNodes$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.nzFlattenNodes =\n        !!this.nzVirtualHeight && this.nzHideUnMatched && this.nzSearchValue?.length > 0\n          ? data.filter(d => !d.canHide)\n          : data;\n      this.cdr.markForCheck();\n    });\n\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    this.renderTreeProperties(changes);\n  }\n\n  ngAfterViewInit(): void {\n    this.beforeInit = false;\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzTreeDropIndicatorComponent } from './tree-drop-indicator.component';\nimport { NzTreeIndentComponent } from './tree-indent.component';\nimport { NzTreeNodeBuiltinCheckboxComponent } from './tree-node-checkbox.component';\nimport { NzTreeNodeSwitcherComponent } from './tree-node-switcher.component';\nimport { NzTreeNodeTitleComponent } from './tree-node-title.component';\nimport { NzTreeNodeBuiltinComponent } from './tree-node.component';\nimport { NzTreeComponent } from './tree.component';\n\n@NgModule({\n  imports: [\n    NzTreeComponent,\n    NzTreeNodeBuiltinComponent,\n    NzTreeIndentComponent,\n    NzTreeNodeSwitcherComponent,\n    NzTreeNodeBuiltinCheckboxComponent,\n    NzTreeNodeTitleComponent,\n    NzTreeDropIndicatorComponent\n  ],\n\n  exports: [NzTreeComponent, NzTreeNodeBuiltinComponent, NzTreeIndentComponent]\n})\nexport class NzTreeModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './tree.module';\nexport * from './tree.component';\nexport * from './tree-node.component';\nexport * from './tree-indent.component';\nexport * from './tree.service';\nexport * from './tree-node-switcher.component';\nexport * from './tree-node-checkbox.component';\nexport * from './tree-node-title.component';\nexport type { NzTreeNodeOptions, NzFormatEmitEvent, NzFormatBeforeDropEvent } from 'ng-zorro-antd/core/tree';\nexport { NzTreeNode } from 'ng-zorro-antd/core/tree';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1", "i2"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;MAyBU,4BAA4B,CAAA;AAMnB,IAAA,GAAA;AALX,IAAA,YAAY;IACkB,KAAK,GAAW,CAAC;IAC/C,SAAS,GAAW,KAAK;IAClC,KAAK,GAAqB,EAAE;AAE5B,IAAA,WAAA,CAAoB,GAAsB,EAAA;QAAtB,IAAG,CAAA,GAAA,GAAH,GAAG;;AAEvB,IAAA,WAAW,CAAC,QAAuB,EAAA;QACjC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,YAAa,EAAE,IAAI,CAAC,SAAS,CAAC;;AAG1D,IAAA,eAAe,CAAC,YAAoB,EAAE,SAAA,GAAoB,KAAK,EAAA;QAC7D,MAAM,MAAM,GAAG,CAAC;AAChB,QAAA,MAAM,aAAa,GAAG,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;AAC5D,QAAA,MAAM,WAAW,GAAG,SAAS,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;AAC1D,QAAA,MAAM,KAAK,GAAqB;AAC9B,YAAA,CAAC,aAAa,GAAG,CAAA,EAAG,MAAM,CAAI,EAAA,CAAA;YAC9B,CAAC,WAAW,GAAG;SAChB;QACD,QAAQ,YAAY;AAClB,YAAA,KAAK,CAAC,CAAC;AACL,gBAAA,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI;gBACrB;AACF,YAAA,KAAK,CAAC;AACJ,gBAAA,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,IAAI;gBACxB;AACF,YAAA,KAAK,CAAC;;AAEJ,gBAAA,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,IAAI;gBACxB,KAAK,CAAC,aAAa,CAAC,GAAG,GAAG,MAAM,GAAG,EAAE,CAAA,EAAA,CAAI;gBACzC;AACF,YAAA;AACE,gBAAA,KAAK,CAAC,OAAO,GAAG,MAAM;gBACtB;;AAEJ,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;uGArCd,4BAA4B,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA5B,4BAA4B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,MAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAEnB,eAAe,CAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,+BAAA,EAAA,MAAA,EAAA,OAAA,EAAA,OAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAVzB,CAAE,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAQD,4BAA4B,EAAA,UAAA,EAAA,CAAA;kBAXxC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wBAAwB;AAClC,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,QAAQ,EAAE,CAAE,CAAA;oBACZ,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,iCAAiC,EAAE,MAAM;AACzC,wBAAA,SAAS,EAAE;AACZ;AACF,iBAAA;sFAEU,YAAY,EAAA,CAAA;sBAApB;gBACsC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBAC5B,SAAS,EAAA,CAAA;sBAAjB;;;AC/BH;;;AAGG;MA2BU,qBAAqB,CAAA;IACvB,WAAW,GAAG,CAAC;IACf,SAAS,GAAc,EAAE;IACzB,OAAO,GAAc,EAAE;IACvB,YAAY,GAAG,KAAK;IAE7B,UAAU,GAAa,EAAE;AAEzB,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO;QAC/B,IAAI,WAAW,EAAE;AACf,YAAA,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,IAAI,KAAK,CAAC,WAAW,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC;;;uGAXxD,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,qBAAqB,EApBtB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,WAAA,EAAA,OAAA,EAAA,SAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,uBAAA,EAAA,eAAA,EAAA,8BAAA,EAAA,cAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;AAWT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FASU,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAvBjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE;;;;;;;;;;;AAWT,EAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,oBAAoB,EAAE,MAAM;AAC5B,wBAAA,yBAAyB,EAAE,eAAe;AAC1C,wBAAA,gCAAgC,EAAE;AACnC;AACF,iBAAA;8BAEU,WAAW,EAAA,CAAA;sBAAnB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,YAAY,EAAA,CAAA;sBAApB;;;AClCH;;;AAGG;MAsBU,kCAAkC,CAAA;IACpC,YAAY,GAAG,KAAK;AACW,IAAA,SAAS;AACT,IAAA,aAAa;AACb,IAAA,UAAU;AACV,IAAA,iBAAiB;uGAL9C,kCAAkC,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAlC,kCAAkC,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gCAAA,EAAA,MAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAEzB,gBAAgB,CAChB,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,4CAChB,gBAAgB,CAAA,EAAA,iBAAA,EAAA,CAAA,mBAAA,EAAA,mBAAA,EAChB,gBAAgB,CArB1B,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gCAAA,EAAA,cAAA,EAAA,wCAAA,EAAA,2BAAA,EAAA,8CAAA,EAAA,+BAAA,EAAA,yCAAA,EAAA,mDAAA,EAAA,yBAAA,EAAA,eAAA,EAAA,iCAAA,EAAA,4BAAA,EAAA,uCAAA,EAAA,gCAAA,EAAA,kCAAA,EAAA,oDAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;AAET,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAcU,kCAAkC,EAAA,UAAA,EAAA,CAAA;kBAlB9C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gCAAgC;AAC1C,oBAAA,QAAQ,EAAE;;AAET,EAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,kCAAkC,EAAE,CAAc,YAAA,CAAA;AAClD,wBAAA,0CAA0C,EAAE,CAA2B,yBAAA,CAAA;AACvE,wBAAA,gDAAgD,EAAE,CAA+B,6BAAA,CAAA;AACjF,wBAAA,2CAA2C,EAAE,CAAmD,iDAAA,CAAA;AAChG,wBAAA,2BAA2B,EAAE,CAAe,aAAA,CAAA;AAC5C,wBAAA,mCAAmC,EAAE,CAA4B,0BAAA,CAAA;AACjE,wBAAA,yCAAyC,EAAE,CAAgC,8BAAA,CAAA;AAC3E,wBAAA,oCAAoC,EAAE,CAAoD,kDAAA;AAC3F;AACF,iBAAA;8BAEU,YAAY,EAAA,CAAA;sBAApB;gBACuC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,iBAAiB,EAAA,CAAA;sBAAxD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;;;AC9BxC;;;AAGG;MAoDU,2BAA2B,CAAA;AACE,IAAA,YAAY;AACZ,IAAA,UAAU;AACzC,IAAA,cAAc;IACd,YAAY,GAAG,KAAK;AACpB,IAAA,OAAO;AACwB,IAAA,MAAM;AACN,IAAA,SAAS;AACT,IAAA,UAAU;AAElD,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU;;AAG1C,IAAA,IAAI,gBAAgB,GAAA;QAClB,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU;;AAGzC,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM;;AAG1C,IAAA,IAAI,eAAe,GAAA;QACjB,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM;;uGAvB9B,2BAA2B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA3B,2BAA2B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAClB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAChB,gBAAgB,CAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAIhB,gBAAgB,CAChB,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAlD1B,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gCAAA,EAAA,cAAA,EAAA,qCAAA,EAAA,wBAAA,EAAA,qCAAA,EAAA,gCAAA,EAAA,sCAAA,EAAA,iCAAA,EAAA,yBAAA,EAAA,eAAA,EAAA,8BAAA,EAAA,yBAAA,EAAA,8BAAA,EAAA,iCAAA,EAAA,+BAAA,EAAA,kCAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BT,EAaS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,yNAAE,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAE3B,2BAA2B,EAAA,UAAA,EAAA,CAAA;kBA5CvC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BT,EAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,kCAAkC,EAAE,cAAc;AAClD,wBAAA,uCAAuC,EAAE,wBAAwB;AACjE,wBAAA,uCAAuC,EAAE,gCAAgC;AACzE,wBAAA,wCAAwC,EAAE,iCAAiC;AAC3E,wBAAA,2BAA2B,EAAE,eAAe;AAC5C,wBAAA,gCAAgC,EAAE,yBAAyB;AAC3D,wBAAA,gCAAgC,EAAE,iCAAiC;AACnE,wBAAA,iCAAiC,EAAE;AACpC,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,cAAc;AACvC,iBAAA;8BAEyC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,cAAc,EAAA,CAAA;sBAAtB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACuC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;;;AC/DxC;;;AAGG;MAsEU,wBAAwB,CAAA;AAmCf,IAAA,GAAA;AAlCX,IAAA,WAAW;IACX,YAAY,GAA6E,IAAI;AAC9D,IAAA,SAAS;AACT,IAAA,QAAQ;IACvC,UAAU,GAAG,KAAK;AAClB,IAAA,OAAO;AACP,IAAA,IAAI;AACJ,IAAA,KAAK;AAC0B,IAAA,SAAS;AACT,IAAA,UAAU;AACV,IAAA,UAAU;AACV,IAAA,SAAS;AACT,IAAA,UAAU;AACV,IAAA,MAAM;;IAErC,aAAa,GAAG,IAAI;AACpB,IAAA,YAAY;AAErB,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,IAAI;;AAGzD,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE;;AAG/C,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM;;AAGxC,IAAA,IAAI,eAAe,GAAA;QACjB,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM;;AAGzC,IAAA,WAAA,CAAoB,GAAsB,EAAA;QAAtB,IAAG,CAAA,GAAA,GAAH,GAAG;;AAEvB,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,OAAO;AAC/C,QAAA,IAAI,aAAa,IAAI,YAAY,EAAE;AACjC,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;;uGAxChB,wBAAwB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAxB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,wBAAwB,gKAGf,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAChB,gBAAgB,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAKhB,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,yCAChB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAChB,gBAAgB,CAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAChB,gBAAgB,CA9D1B,EAAA,aAAA,EAAA,eAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,YAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,cAAA,EAAA,4CAAA,EAAA,YAAA,EAAA,iDAAA,EAAA,8BAAA,EAAA,kDAAA,EAAA,+BAAA,EAAA,qCAAA,EAAA,0BAAA,EAAA,qCAAA,EAAA,aAAA,EAAA,0CAAA,EAAA,+BAAA,EAAA,2CAAA,EAAA,gCAAA,EAAA,8BAAA,EAAA,2BAAA,EAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAiBS,gBAAgB,EAAE,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,iBAAiB,kGAAE,4BAA4B,EAAA,QAAA,EAAA,wBAAA,EAAA,MAAA,EAAA,CAAA,cAAA,EAAA,OAAA,EAAA,WAAA,CAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAE9E,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBAlDpC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BT,EAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,cAAc,EAAE,OAAO;AACvB,wBAAA,kBAAkB,EAAE,cAAc;AAClC,wBAAA,qBAAqB,EAAE,cAAc;AACrC,wBAAA,mBAAmB,EAAE,cAAc;AACnC,wBAAA,8CAA8C,EAAE,CAAY,UAAA,CAAA;AAC5D,wBAAA,mDAAmD,EAAE,CAA8B,4BAAA,CAAA;AACnF,wBAAA,oDAAoD,EAAE,CAA+B,6BAAA,CAAA;AACrF,wBAAA,uCAAuC,EAAE,CAA0B,wBAAA,CAAA;AACnE,wBAAA,uCAAuC,EAAE,CAAa,WAAA,CAAA;AACtD,wBAAA,4CAA4C,EAAE,CAA+B,6BAAA,CAAA;AAC7E,wBAAA,6CAA6C,EAAE,CAAgC,8BAAA,CAAA;AAC/E,wBAAA,gCAAgC,EAAE,CAA2B,yBAAA;AAC9D,qBAAA;oBACD,OAAO,EAAE,CAAC,gBAAgB,EAAE,YAAY,EAAE,iBAAiB,EAAE,4BAA4B;AAC1F,iBAAA;sFAEU,WAAW,EAAA,CAAA;sBAAnB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACuC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,UAAU,EAAA,CAAA;sBAAlB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACuC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAE7B,aAAa,EAAA,CAAA;sBAArB;gBACQ,YAAY,EAAA,CAAA;sBAApB;;;AC1FH;;;AAGG;MA2HU,0BAA0B,CAAA;AA0R5B,IAAA,aAAA;AACC,IAAA,MAAA;AACA,IAAA,QAAA;AACA,IAAA,UAAA;AACA,IAAA,GAAA;AA7RV;;AAEG;IACM,IAAI,GAAW,EAAE;IACjB,KAAK,GAAW,EAAE;IACa,SAAS,GAAY,KAAK;IAC1B,UAAU,GAAY,KAAK;IAC3B,UAAU,GAAY,KAAK;IAC3B,SAAS,GAAY,KAAK;AAC1B,IAAA,UAAU;AACV,IAAA,MAAM;AACN,IAAA,SAAS;AACT,IAAA,aAAa;AACb,IAAA,iBAAiB;AACjB,IAAA,YAAY;AACZ,IAAA,OAAO;IACtC,OAAO,GAAc,EAAE;IACvB,KAAK,GAAc,EAAE;AACrB,IAAA,UAAU;AACqB,IAAA,UAAU;AACV,IAAA,YAAY;AACZ,IAAA,WAAW;AACX,IAAA,WAAW;IACX,eAAe,GAAG,KAAK;IACvB,aAAa,GAAG,KAAK;IACrB,YAAY,GAAG,KAAK;IACpB,UAAU,GAAG,KAAK;AACjD,IAAA,cAAc;IACd,cAAc,GAA6E,IAAI;AAC/F,IAAA,YAAY;IACZ,aAAa,GAAG,EAAE;IACa,WAAW,GAAY,KAAK;AACjD,IAAA,OAAO,GAAG,IAAI,YAAY,EAAqB;AAC/C,IAAA,UAAU,GAAG,IAAI,YAAY,EAAqB;AAClD,IAAA,aAAa,GAAG,IAAI,YAAY,EAAqB;AACrD,IAAA,gBAAgB,GAAG,IAAI,YAAY,EAAqB;AACxD,IAAA,cAAc,GAAG,IAAI,YAAY,EAAqB;AACtD,IAAA,aAAa,GAAG,IAAI,YAAY,EAAqB;AACrD,IAAA,aAAa,GAAG,IAAI,YAAY,EAAqB;AACrD,IAAA,YAAY,GAAG,IAAI,YAAY,EAAqB;AACpD,IAAA,aAAa,GAAG,IAAI,YAAY,EAAqB;AACrD,IAAA,QAAQ,GAAG,IAAI,YAAY,EAAqB;AAChD,IAAA,WAAW,GAAG,IAAI,YAAY,EAAqB;AAEtE;;AAEG;AACH,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAW;IACjC,OAAO,GAAG,CAAC;AACX,IAAA,YAAY,GAA2B;AACrC,QAAA,CAAC,EAAE,WAAW;AACd,QAAA,CAAC,EAAE,sBAAsB;AACzB,QAAA,IAAI,EAAE;KACP;IACD,WAAW,GAAkB,IAAI;IACjC,aAAa,GAAG,KAAK;AACrB;;AAEG;AACH,IAAA,IAAI,YAAY,GAAA;;QAEd,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC;AAC/F,cAAE;cACA,EAAE;;AAGR,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM;;AAGxC,IAAA,IAAI,eAAe,GAAA;QACjB,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM;;AAGzC;;;;AAIG;AACH,IAAA,WAAW,CAAC,KAAiB,EAAA;QAC3B,KAAK,CAAC,cAAc,EAAE;QACtB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;;AAEnC,YAAA,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACjF,gBAAA,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI;;YAElC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;;QAE/C,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC;AACvD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;AAClF,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGrC,IAAA,WAAW,CAAC,KAAiB,EAAA;QAC3B,KAAK,CAAC,cAAc,EAAE;QACtB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACzC,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU;;QAE1D,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC;AACvD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;AACjF,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;;AAG9B,IAAA,QAAQ,CAAC,KAAiB,EAAA;QACxB,KAAK,CAAC,cAAc,EAAE;AACtB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;AACpF,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGjC,IAAA,WAAW,CAAC,KAAiB,EAAA;QAC3B,KAAK,CAAC,cAAc,EAAE;AACtB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;AACvF,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGpC;;;;AAIG;AACH,IAAA,aAAa,CAAC,KAAiB,EAAA;QAC7B,KAAK,CAAC,cAAc,EAAE;;QAEtB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC7C;;QAEF,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS;AACtD,QAAA,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,KAAK;QACrC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC;AACtD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;AACjF,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC;;IAGvC,cAAc,GAAA;QACZ,MAAM,SAAS,GAAG,CAAC,mBAAmB,EAAE,sBAAsB,EAAE,WAAW,EAAE,aAAa,CAAC;AAC3F,QAAA,SAAS,CAAC,OAAO,CAAC,CAAC,IAAG;AACpB,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC;AAC7D,SAAC,CAAC;;AAGJ;;;;AAIG;AACH,IAAA,eAAe,CAAC,CAAY,EAAA;AAC1B,QAAA,IAAI;;;AAGF,YAAA,CAAC,CAAC,YAAa,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,GAAI,CAAC;;AAC3D,QAAA,MAAM;;;QAGR,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC;QACnD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG;AACtC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;AACjF,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGpC,IAAA,eAAe,CAAC,CAAY,EAAA;QAC1B,CAAC,CAAC,cAAc,EAAE;;AAElB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,EAAE,GAAG;AACtF,QAAA,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAK;AACnB,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;AACjF,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC;AACpC,SAAC,CAAC;;AAGJ,IAAA,cAAc,CAAC,CAAY,EAAA;QACzB,CAAC,CAAC,cAAc,EAAE;QAClB,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC3D,QAAA,IAAI,IAAI,CAAC,OAAO,KAAK,YAAY,EAAE;YACjC,IAAI,CAAC,cAAc,EAAE;AACrB,YAAA,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;;AAElC,YAAA,IAAI,EAAE,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;gBACxC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACtF,gBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,aAAa,CAAC;;;AAGxE,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;AAChF,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGnC,IAAA,eAAe,CAAC,CAAY,EAAA;QAC1B,CAAC,CAAC,cAAc,EAAE;AAClB,QAAA,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,cAAc,EAAE;AACrB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;AACjF,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGpC,IAAA,cAAc,CAAC,CAAY,EAAA;QACzB,CAAC,CAAC,cAAc,EAAE;QAClB,CAAC,CAAC,eAAe,EAAE;AACnB,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAK;AACnB,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;YAC1B,IAAI,CAAC,cAAc,EAAE;YACrB,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE;AACjD,YAAA,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC9F;;;AAGF,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;AAC5E,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;AAClF,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC;AAChB,oBAAA,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,eAAe,EAAG;oBAC/C,IAAI,EAAE,IAAI,CAAC,UAAU;oBACrB,GAAG,EAAE,IAAI,CAAC;AACX,iBAAA,CAAC,CAAC,SAAS,CAAC,CAAC,OAAgB,KAAI;oBAChC,IAAI,OAAO,EAAE;AACX,wBAAA,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC;;AAEhE,oBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;AAC7B,oBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;AACrC,iBAAC,CAAC;;AACG,iBAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AAC1B,gBAAA,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC;AAC9D,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;;AAEjC,SAAC,CAAC;;AAGJ,IAAA,aAAa,CAAC,CAAY,EAAA;QACxB,CAAC,CAAC,cAAc,EAAE;AAClB,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAK;;AAEnB,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;;AAEtB,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI;AACvB,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;AAC/E,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC;;iBAC3B;;AAEL,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI;gBACvB,IAAI,CAAC,YAAY,EAAE;;AAEvB,SAAC,CAAC;;AAGJ;;AAEG;IACH,aAAa,GAAA;AACX,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;AACnD,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,EAAE;AAC7B,YAAA,uBAAuB,CAAY,aAAa,EAAE,WAAW;AAC1D,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7B,iBAAA,SAAS,CAAC,CAAC,CAAY,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;AACvD,YAAA,uBAAuB,CAAY,aAAa,EAAE,WAAW;AAC1D,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7B,iBAAA,SAAS,CAAC,CAAC,CAAY,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;AACvD,YAAA,uBAAuB,CAAY,aAAa,EAAE,UAAU;AACzD,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7B,iBAAA,SAAS,CAAC,CAAC,CAAY,KAAK,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AACtD,YAAA,uBAAuB,CAAY,aAAa,EAAE,WAAW;AAC1D,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7B,iBAAA,SAAS,CAAC,CAAC,CAAY,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;AACvD,YAAA,uBAAuB,CAAY,aAAa,EAAE,MAAM;AACrD,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7B,iBAAA,SAAS,CAAC,CAAC,CAAY,KAAK,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AACtD,YAAA,uBAAuB,CAAY,aAAa,EAAE,SAAS;AACxD,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7B,iBAAA,SAAS,CAAC,CAAC,CAAY,KAAK,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;;aAChD;AACL,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;;IAI5B,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,IAAA,WAAW,GAAG,MAAM,CAAC,sBAAsB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAE5E,WACS,CAAA,aAAgC,EAC/B,MAAc,EACd,QAAmB,EACnB,UAAmC,EACnC,GAAsB,EAAA;QAJvB,IAAa,CAAA,aAAA,GAAb,aAAa;QACZ,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAU,CAAA,UAAA,GAAV,UAAU;QACV,IAAG,CAAA,GAAA,GAAH,GAAG;;IAGb,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI;QAEhC,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,WAAW;AAC/D,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC7B,SAAS,CAAC,KAAK,IAAG;AACjB,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,KAAK,CAAC,cAAc,EAAE;;AAE1B,SAAC,CAAC;;AAGN,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO;QAC/B,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,aAAa,EAAE;;;IAIxB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;AAGlB,IAAA,eAAe,CAAC,YAAoB,EAAA;AAC1C,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAK;AACnB,YAAA,IAAI,CAAC,aAAa,GAAG,YAAY,KAAK,CAAC;YACvC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,EAAE,GAAG,KAAK,YAAY,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC5G;;AAEF,YAAA,IAAI,CAAC,OAAO,GAAG,YAAY;AAC3B,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,SAAC,CAAC;;uGAjUO,0BAA0B,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAA1B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,0BAA0B,uIAMjB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAChB,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,yCAChB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAChB,gBAAgB,CAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAChB,gBAAgB,CAChB,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,qDAChB,gBAAgB,CAAA,EAAA,iBAAA,EAAA,CAAA,mBAAA,EAAA,mBAAA,EAChB,gBAAgB,CAChB,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAChB,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,0GAIhB,gBAAgB,CAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAChB,gBAAgB,CAChB,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,+CAChB,gBAAgB,CAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAChB,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAChB,gBAAgB,CAChB,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,4CAChB,gBAAgB,CAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,eAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAKhB,gBAAgB,CAlH1B,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,aAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,aAAA,EAAA,eAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,eAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gCAAA,EAAA,cAAA,EAAA,yCAAA,EAAA,4BAAA,EAAA,8CAAA,EAAA,gCAAA,EAAA,+CAAA,EAAA,iCAAA,EAAA,iDAAA,EAAA,2BAAA,EAAA,uDAAA,EAAA,+BAAA,EAAA,yCAAA,EAAA,4BAAA,EAAA,wCAAA,EAAA,2BAAA,EAAA,yBAAA,EAAA,eAAA,EAAA,kCAAA,EAAA,6BAAA,EAAA,uCAAA,EAAA,iCAAA,EAAA,wCAAA,EAAA,kCAAA,EAAA,0CAAA,EAAA,4BAAA,EAAA,gDAAA,EAAA,gCAAA,EAAA,kCAAA,EAAA,6BAAA,EAAA,iCAAA,EAAA,4BAAA,EAAA,gBAAA,EAAA,gCAAA,EAAA,eAAA,EAAA,cAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAwBC,qBAAqB,EACrB,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,aAAA,EAAA,WAAA,EAAA,SAAA,EAAA,cAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,2BAA2B,EAC3B,QAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,CAAA,cAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,SAAA,EAAA,QAAA,EAAA,WAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,kCAAkC,sKAClC,wBAAwB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,aAAA,EAAA,cAAA,EAAA,WAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,MAAA,EAAA,OAAA,EAAA,WAAA,EAAA,YAAA,EAAA,YAAA,EAAA,WAAA,EAAA,YAAA,EAAA,QAAA,EAAA,eAAA,EAAA,cAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAGf,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBArFtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDT,EAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,kCAAkC,EAAE,CAAc,YAAA,CAAA;AAClD,wBAAA,2CAA2C,EAAE,CAA4B,0BAAA,CAAA;AACzE,wBAAA,gDAAgD,EAAE,CAAgC,8BAAA,CAAA;AAClF,wBAAA,iDAAiD,EAAE,CAAiC,+BAAA,CAAA;AACpF,wBAAA,mDAAmD,EAAE,CAA2B,yBAAA,CAAA;AAChF,wBAAA,yDAAyD,EAAE,CAA+B,6BAAA,CAAA;AAC1F,wBAAA,2CAA2C,EAAE,CAA4B,0BAAA,CAAA;AACzE,wBAAA,0CAA0C,EAAE,CAA2B,yBAAA,CAAA;AACvE,wBAAA,2BAA2B,EAAE,CAAe,aAAA,CAAA;AAC5C,wBAAA,oCAAoC,EAAE,CAA6B,2BAAA,CAAA;AACnE,wBAAA,yCAAyC,EAAE,CAAiC,+BAAA,CAAA;AAC5E,wBAAA,0CAA0C,EAAE,CAAkC,gCAAA,CAAA;AAC9E,wBAAA,4CAA4C,EAAE,CAA4B,0BAAA,CAAA;AAC1E,wBAAA,kDAAkD,EAAE,CAAgC,8BAAA,CAAA;AACpF,wBAAA,oCAAoC,EAAE,CAA6B,2BAAA,CAAA;AACnE,wBAAA,mCAAmC,EAAE,CAA4B,0BAAA,CAAA;AACjE,wBAAA,kBAAkB,EAAE,CAAgC,8BAAA,CAAA;AACpD,wBAAA,iBAAiB,EAAE;AACpB,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,qBAAqB;wBACrB,2BAA2B;wBAC3B,kCAAkC;wBAClC;AACD;AACF,iBAAA;8LAKU,IAAI,EAAA,CAAA;sBAAZ;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACuC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,iBAAiB,EAAA,CAAA;sBAAxD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,OAAO,EAAA,CAAA;sBAAf;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACuC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,cAAc,EAAA,CAAA;sBAAtB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACuC,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACnB,OAAO,EAAA,CAAA;sBAAzB;gBACkB,UAAU,EAAA,CAAA;sBAA5B;gBACkB,aAAa,EAAA,CAAA;sBAA/B;gBACkB,gBAAgB,EAAA,CAAA;sBAAlC;gBACkB,cAAc,EAAA,CAAA;sBAAhC;gBACkB,aAAa,EAAA,CAAA;sBAA/B;gBACkB,aAAa,EAAA,CAAA;sBAA/B;gBACkB,YAAY,EAAA,CAAA;sBAA9B;gBACkB,aAAa,EAAA,CAAA;sBAA/B;gBACkB,QAAQ,EAAA,CAAA;sBAA1B;gBACkB,WAAW,EAAA,CAAA;sBAA7B;;;ACzKH;;;AAGG;AAOG,MAAO,aAAc,SAAQ,iBAAiB,CAAA;AAClD,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;;uGAFE,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAAb,aAAa,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB;;;SCyCe,oBAAoB,GAAA;AAClC,IAAA,MAAM,kBAAkB,GAAG,MAAM,CAAC,6BAA6B,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AACpG,IAAA,MAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC;IACzC,OAAO,kBAAkB,IAAI,WAAW;AAC1C;AAEA,MAAM,qBAAqB,GAAgB,MAAM;IAgIpC,eAAe,GAAA,CAAA,MAAA;sBAClB,UAAU;;;;;;;;;;AADP,IAAA,OAAA,MAAA,eACX,SAAQ,WAAU,CAAA;;;AAKuB,YAAA,sBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,2BAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,uBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;YAFC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,YAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,UAAU,EAAV,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,UAAU,GAAkB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,wBAAA,EAAA,6BAAA,CAAA;YAC5B,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,2BAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,iBAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,eAAe,EAAf,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,eAAe,GAAkB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,6BAAA,EAAA,kCAAA,CAAA;YACjC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,uBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,aAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,aAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,WAAW,EAAX,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,WAAW,GAAkB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,yBAAA,EAAA,8BAAA,CAAA;;;QAkS1E,eAAe;QACd,GAAG;QACH,cAAc;QAxSf,aAAa,GAAgB,qBAAqB;QAEL,UAAU,GAAA,iBAAA,CAAA,IAAA,EAAA,wBAAA,EAAY,KAAK,CAAC;QAC5B,eAAe,IAAA,iBAAA,CAAA,IAAA,EAAA,6BAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,6BAAA,EAAY,KAAK,CAAC;QACjC,WAAW,IAAA,iBAAA,CAAA,IAAA,EAAA,kCAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,yBAAA,EAAY,KAAK,CAAC;QAC3C,WAAW,IAAA,iBAAA,CAAA,IAAA,EAAA,8BAAA,CAAA,EAAG,KAAK;QACnB,YAAY,GAAG,KAAK;QACpB,eAAe,GAAG,KAAK;QACvB,YAAY,GAAY,IAAI;QAC5B,UAAU,GAAG,KAAK;QAClB,WAAW,GAAG,KAAK;QACnB,WAAW,GAAG,KAAK;QACnB,WAAW,GAAY,KAAK;QAC5B,UAAU,GAAG,KAAK;AACjD,QAAA,cAAc;QACd,iBAAiB,GAAG,EAAE;QACtB,oBAAoB,GAAG,GAAG;QAC1B,oBAAoB,GAAG,EAAE;QACzB,eAAe,GAAkB,IAAI;AACrC,QAAA,cAAc;AACd,QAAA,YAAY;QACZ,MAAM,GAAuC,EAAE;QAC/C,cAAc,GAAoB,EAAE;QACpC,cAAc,GAAoB,EAAE;QACpC,aAAa,GAAoB,EAAE;QACnC,aAAa,GAAW,EAAE;AAC1B,QAAA,YAAY;AAC6B,QAAA,mBAAmB;AAKrE,QAAA,wBAAwB;QACxB,cAAc,GAAiB,EAAE;QACjC,UAAU,GAAG,IAAI;QACjB,GAAG,GAAc,KAAK;AAEH,QAAA,oBAAoB,GAA2B,IAAI,YAAY,EAAY;AAC3E,QAAA,oBAAoB,GAA2B,IAAI,YAAY,EAAY;AAC3E,QAAA,mBAAmB,GAAkC,IAAI,YAAY,EAAmB;AACxF,QAAA,mBAAmB,GAAG,IAAI,YAAY,EAAqB;AAC3D,QAAA,OAAO,GAAG,IAAI,YAAY,EAAqB;AAC/C,QAAA,UAAU,GAAG,IAAI,YAAY,EAAqB;AAClD,QAAA,aAAa,GAAG,IAAI,YAAY,EAAqB;AACrD,QAAA,gBAAgB,GAAG,IAAI,YAAY,EAAqB;AACxD,QAAA,cAAc,GAAG,IAAI,YAAY,EAAqB;AACtD,QAAA,aAAa,GAAG,IAAI,YAAY,EAAqB;AACrD,QAAA,aAAa,GAAG,IAAI,YAAY,EAAqB;AACrD,QAAA,YAAY,GAAG,IAAI,YAAY,EAAqB;AACpD,QAAA,aAAa,GAAG,IAAI,YAAY,EAAqB;AACrD,QAAA,QAAQ,GAAG,IAAI,YAAY,EAAqB;AAChD,QAAA,WAAW,GAAG,IAAI,YAAY,EAAqB;AAEtE,QAAA,YAAY,GAAG;AACb,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,MAAM,EAAE,CAAC;AACT,YAAA,OAAO,EAAE,MAAM;AACf,YAAA,QAAQ,EAAE,QAAQ;AAClB,YAAA,OAAO,EAAE,CAAC;AACV,YAAA,MAAM,EAAE,CAAC;AACT,YAAA,OAAO,EAAE,CAAC;AACV,YAAA,MAAM,EAAE;SACT;AAED,QAAA,iBAAiB,GAAG;AAClB,YAAA,QAAQ,EAAE,UAAU;AACpB,YAAA,aAAa,EAAE,MAAM;AACrB,YAAA,UAAU,EAAE,QAAQ;AACpB,YAAA,MAAM,EAAE,CAAC;AACT,YAAA,QAAQ,EAAE;SACX;AAED,QAAA,QAAQ,GAAG,IAAI,OAAO,EAAW;AAEjC,QAAA,QAAQ,GAAkC,MAAM,IAAI;AACpD,QAAA,SAAS,GAAe,MAAM,IAAI;AAElC,QAAA,UAAU,CAAC,KAAmB,EAAA;AAC5B,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;AAG1B,QAAA,gBAAgB,CAAC,EAA6B,EAAA;AAC5C,YAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAGpB,QAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB;;;;AAIG;AACH,QAAA,oBAAoB,CAAC,OAAsB,EAAA;YACzC,IAAI,sBAAsB,GAAG,KAAK;YAClC,IAAI,SAAS,GAAG,KAAK;AACrB,YAAA,MAAM,EACJ,MAAM,EACN,cAAc,EACd,cAAc,EACd,aAAa,EACb,eAAe,EACf,WAAW,EACX,UAAU,EACV,aAAa,EACd,GAAG,OAAO;YAEX,IAAI,WAAW,EAAE;gBACf,sBAAsB,GAAG,IAAI;AAC7B,gBAAA,SAAS,GAAG,IAAI,CAAC,WAAW;;YAG9B,IAAI,UAAU,EAAE;gBACd,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;;YAGjD,IAAI,eAAe,EAAE;gBACnB,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe;;YAG3D,IAAI,MAAM,EAAE;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;;YAGhC,IAAI,aAAa,EAAE;AACjB,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC;;YAG5C,IAAI,eAAe,EAAE;AACnB,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;;AAG9B,YAAA,IAAI,cAAc,IAAI,WAAW,EAAE;gBACjC,sBAAsB,GAAG,IAAI;gBAC7B,IAAI,CAAC,kBAAkB,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC;;YAG3D,IAAI,cAAc,EAAE;gBAClB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC;;YAG/D,IAAI,aAAa,EAAE;AACjB,gBAAA,IAAI,EAAE,aAAa,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;oBACvD,sBAAsB,GAAG,KAAK;oBAC9B,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC;AACrE,oBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;;;;AAKvF,YAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;AACtE,YAAA,MAAM,eAAe,GAAG,sBAAsB,GAAG,SAAS,IAAI,IAAI,CAAC,cAAc,GAAG,mBAAmB;YACvG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,eAAe,CAAC;;QAGxE,kBAAkB,CAAC,CAAS,EAAE,IAAgB,EAAA;YAC5C,OAAO,IAAI,CAAC,GAAG;;;AAGjB;;;;AAIG;AACH,QAAA,YAAY,CAAC,KAAkB,EAAA;AAC7B,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxB,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;AACxC,gBAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC;;;AAIrC,QAAA,kBAAkB,CAAC,IAAkB,EAAE,UAAA,GAAqC,EAAE,EAAA;YAC5E,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC;;AAGtD,QAAA,iBAAiB,CAAC,IAA4B,EAAA;YAC5C,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC;;QAG7D,kBAAkB,CAAC,OAA+B,EAAE,EAAA;AAClD,YAAA,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC;;QAG9C,kBAAkB,CAAC,IAAqB,EAAE,OAAgB,EAAA;YACxD,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC;;QAGvD,iBAAiB,CAAC,KAAa,EAAE,UAAiD,EAAA;YAChF,MAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACrF,YAAA,MAAM,cAAc,GAAG,CAAC,IAAgB,KAAa;gBACnD,IAAI,UAAU,EAAE;AACd,oBAAA,OAAO,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC;;AAEhC,gBAAA,OAAO,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;AAC1E,aAAC;AACD,YAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAG;AACnB,gBAAA,CAAC,CAAC,SAAS,GAAG,cAAc,CAAC,CAAC,CAAC;AAC/B,gBAAA,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,SAAS;AACxB,gBAAA,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE;AAChB,oBAAA,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC;AACpB,oBAAA,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC;;qBACpC;;AAEL,oBAAA,IAAI,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC,CAAC;;AAEnD,gBAAA,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC;AAC1C,aAAC,CAAC;;AAGJ;;;;;AAKG;AACH,QAAA,mBAAmB,CAAC,KAAwB,EAAA;AAC1C,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC,IAAK;AACxB,YAAA,QAAQ,KAAK,CAAC,SAAS;AACrB,gBAAA,KAAK,QAAQ;oBACX,IAAI,CAAC,UAAU,EAAE;AACjB,oBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC/B;AACF,gBAAA,KAAK,OAAO;AACV,oBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;oBACxB;AACF,gBAAA,KAAK,UAAU;AACb,oBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC3B;AACF,gBAAA,KAAK,aAAa;AAChB,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC9B;gBACF,KAAK,OAAO,EAAE;;AAEZ,oBAAA,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC;AAC3C,oBAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AACzB,wBAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC;;;AAGlC,oBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,KAAM,CAAC;AAC7E,oBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC;oBACrC,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE;AAC3D,oBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC;oBAC1C;;AAEF,gBAAA,KAAK,WAAW;;AAEd,oBAAA,IAAI,IAAI,CAAC,UAAU,EAAE;wBACnB,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;wBAClC,IAAI,CAAC,UAAU,EAAE;;AAEnB,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC9B;gBACF,KAAK,WAAW,EAAE;oBAChB,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE;oBACzD,IAAI,YAAY,IAAI,YAAY,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACrF,wBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;wBACtB,IAAI,CAAC,UAAU,EAAE;;AAEnB,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC9B;;AAEF,gBAAA,KAAK,UAAU;AACb,oBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC7B;AACF,gBAAA,KAAK,WAAW;AACd,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC9B;AACF,gBAAA,KAAK,SAAS;AACZ,oBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC5B;AACF,gBAAA,KAAK,MAAM;oBACT,IAAI,CAAC,UAAU,EAAE;AACjB,oBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;oBACzB;;;AAIN;;AAEG;QACH,UAAU,GAAA;YACR,IAAI,CAAC,kBAAkB,CACrB,IAAI,CAAC,aAAa,CAAC,SAAS,EAC5B,IAAI,CAAC,mBAAmB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAC3C;AACD,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;;AAIzB,QAAA,WAAW,GAAG,MAAM,CAAC,sBAAsB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAE5E,QAAA,WAAA,CACE,aAAgC,EACzB,eAAgC,EAC/B,GAAsB,EACtB,cAA8B,EAAA;YAEtC,KAAK,CAAC,aAAa,CAAC;YAJb,IAAe,CAAA,eAAA,GAAf,eAAe;YACd,IAAG,CAAA,GAAA,GAAH,GAAG;YACH,IAAc,CAAA,cAAA,GAAd,cAAc;;QAKxB,QAAQ,GAAA;AACN,YAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,IAAG;AAC/E,gBAAA,IAAI,CAAC,cAAc;AACjB,oBAAA,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,aAAa,EAAE,MAAM,GAAG;AAC7E,0BAAE,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO;0BAC3B,IAAI;AACV,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;YAEF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;YACpC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,gBAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,gBAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,aAAC,CAAC;;AAGJ,QAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,YAAA,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;;QAGpC,eAAe,GAAA;AACb,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK;;QAGzB,WAAW,GAAA;AACT,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;2GA3Uf,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;+FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAMN,gBAAgB,CAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAChB,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAChB,gBAAgB,CAChB,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,CAChB,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAChB,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,gBAAgB,kDAChB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAChB,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAChB,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAChB,gBAAgB,CAChB,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAlDzB,EAAA,cAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,MAAA,EAAA,QAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,aAAA,EAAA,eAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,OAAA,EAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,aAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,aAAA,EAAA,eAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,eAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,uBAAA,EAAA,cAAA,EAAA,iCAAA,EAAA,4BAAA,EAAA,iCAAA,EAAA,6BAAA,EAAA,kCAAA,EAAA,6BAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,0BAAA,EAAA,6BAAA,EAAA,0BAAA,EAAA,8BAAA,EAAA,2BAAA,EAAA,8BAAA,EAAA,sBAAA,EAAA,aAAA,EAAA,EAAA,EAAA,SAAA,EAAA;gBACT,aAAa;AACb,gBAAA;AACE,oBAAA,OAAO,EAAE,iBAAiB;AAC1B,oBAAA,UAAU,EAAE;AACb,iBAAA;AACD,gBAAA;AACE,oBAAA,OAAO,EAAE,iBAAiB;AAC1B,oBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,eAAe,CAAC;AAC9C,oBAAA,KAAK,EAAE;AACR;aACF,EAyDU,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,0BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,wBAAwB,EAAU,WAAA,EAAA,IAAA,EAAA,IAAA,EAAA,wBAAwB,EA7J3D,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuFT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EA2BC,wBAAwB,EAAA,QAAA,EAAA,6BAAA,EAAA,MAAA,EAAA,CAAA,aAAA,EAAA,YAAA,CAAA,EAAA,OAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACxB,yBAAyB,EAAA,QAAA,EAAA,uCAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,aAAA,EAAA,aAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACzB,eAAe,EACf,QAAA,EAAA,kCAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,sBAAA,EAAA,uBAAA,EAAA,gCAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,EAChB,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,sBAAsB,EACtB,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,0BAA0B,EAxHhB,QAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,OAAA,EAAA,WAAA,EAAA,YAAA,EAAA,YAAA,EAAA,WAAA,EAAA,YAAA,EAAA,QAAA,EAAA,WAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,cAAA,EAAA,SAAA,EAAA,SAAA,EAAA,OAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,aAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,cAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,eAAA,EAAA,aAAA,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,YAAA,EAAA,eAAA,EAAA,kBAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,eAAA,EAAA,cAAA,EAAA,eAAA,EAAA,UAAA,EAAA,aAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA,CAAC,kBAAkB,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;;2FA2HrB,eAAe,EAAA,UAAA,EAAA,CAAA;kBA9H3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,QAAQ,EAAE,QAAQ;oBAClB,UAAU,EAAE,CAAC,kBAAkB,CAAC;AAChC,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuFT,EAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,SAAS,EAAE;wBACT,aAAa;AACb,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,UAAU,EAAE;AACb,yBAAA;AACD,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,qBAAqB,CAAC;AAC9C,4BAAA,KAAK,EAAE;AACR;AACF,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,yBAAyB,EAAE,CAAc,YAAA,CAAA;AACzC,wBAAA,mCAAmC,EAAE,CAA4B,0BAAA,CAAA;AACjE,wBAAA,mCAAmC,EAAE,CAA6B,2BAAA,CAAA;AAClE,wBAAA,oCAAoC,EAAE,CAA6B,2BAAA,CAAA;AACnE,wBAAA,kBAAkB,EAAE,CAAe,aAAA,CAAA;AACnC,wBAAA,sBAAsB,EAAE,CAAe,aAAA,CAAA;AACvC,wBAAA,4BAA4B,EAAE,CAA6B,2BAAA,CAAA;AAC3D,wBAAA,4BAA4B,EAAE,CAA8B,4BAAA,CAAA;AAC5D,wBAAA,6BAA6B,EAAE,CAA8B,4BAAA,CAAA;AAC7D,wBAAA,wBAAwB,EAAE,CAAa,WAAA;AACxC,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,wBAAwB;wBACxB,yBAAyB;wBACzB,eAAe;wBACf,gBAAgB;wBAChB,sBAAsB;wBACtB;AACD;AACF,iBAAA;qLAOuD,UAAU,EAAA,CAAA;sBAA/D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACgB,eAAe,EAAA,CAAA;sBAApE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACgB,WAAW,EAAA,CAAA;sBAAhE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,cAAc,EAAA,CAAA;sBAAtB;gBACQ,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,oBAAoB,EAAA,CAAA;sBAA5B;gBACQ,oBAAoB,EAAA,CAAA;sBAA5B;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACiD,mBAAmB,EAAA,CAAA;sBAApE,YAAY;AAAC,gBAAA,IAAA,EAAA,CAAA,gBAAgB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBAKhD,wBAAwB,EAAA,CAAA;sBADvB,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,wBAAwB,EAAE,EAAE,IAAI,EAAE,wBAAwB,EAAE;gBAMpD,oBAAoB,EAAA,CAAA;sBAAtC;gBACkB,oBAAoB,EAAA,CAAA;sBAAtC;gBACkB,mBAAmB,EAAA,CAAA;sBAArC;gBACkB,mBAAmB,EAAA,CAAA;sBAArC;gBACkB,OAAO,EAAA,CAAA;sBAAzB;gBACkB,UAAU,EAAA,CAAA;sBAA5B;gBACkB,aAAa,EAAA,CAAA;sBAA/B;gBACkB,gBAAgB,EAAA,CAAA;sBAAlC;gBACkB,cAAc,EAAA,CAAA;sBAAhC;gBACkB,aAAa,EAAA,CAAA;sBAA/B;gBACkB,aAAa,EAAA,CAAA;sBAA/B;gBACkB,YAAY,EAAA,CAAA;sBAA9B;gBACkB,aAAa,EAAA,CAAA;sBAA/B;gBACkB,QAAQ,EAAA,CAAA;sBAA1B;gBACkB,WAAW,EAAA,CAAA;sBAA7B;;;AC/OH;;;AAGG;MAyBU,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,YAXrB,eAAe;YACf,0BAA0B;YAC1B,qBAAqB;YACrB,2BAA2B;YAC3B,kCAAkC;YAClC,wBAAwB;AACxB,YAAA,4BAA4B,CAGpB,EAAA,OAAA,EAAA,CAAA,eAAe,EAAE,0BAA0B,EAAE,qBAAqB,CAAA,EAAA,CAAA;AAEjE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,YAXrB,eAAe;YACf,0BAA0B;YAE1B,2BAA2B;YAE3B,wBAAwB,CAAA,EAAA,CAAA;;2FAMf,YAAY,EAAA,UAAA,EAAA,CAAA;kBAbxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,0BAA0B;wBAC1B,qBAAqB;wBACrB,2BAA2B;wBAC3B,kCAAkC;wBAClC,wBAAwB;wBACxB;AACD,qBAAA;AAED,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,0BAA0B,EAAE,qBAAqB;AAC7E,iBAAA;;;AC3BD;;;AAGG;;ACHH;;AAEG;;;;"}