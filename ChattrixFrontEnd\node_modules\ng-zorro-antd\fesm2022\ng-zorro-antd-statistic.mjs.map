{"version": 3, "file": "ng-zorro-antd-statistic.mjs", "sources": ["../../components/statistic/statistic-number.component.ts", "../../components/statistic/statistic.component.ts", "../../components/statistic/countdown.component.ts", "../../components/statistic/statistic.module.ts", "../../components/statistic/public-api.ts", "../../components/statistic/ng-zorro-antd-statistic.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { getLocaleNumberSymbol, NgTemplateOutlet, NumberSymbol } from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  inject,\n  Input,\n  LOCALE_ID,\n  OnChanges,\n  TemplateRef,\n  ViewEncapsulation\n} from '@angular/core';\n\nimport { NzStatisticValueType } from './typings';\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  selector: 'nz-statistic-number',\n  exportAs: 'nzStatisticNumber',\n  template: `\n    <span class=\"ant-statistic-content-value\">\n      @if (nzValueTemplate) {\n        <ng-container\n          [ngTemplateOutlet]=\"nzValueTemplate\"\n          [ngTemplateOutletContext]=\"{ $implicit: nzValue }\"\n        ></ng-container>\n      } @else {\n        @if (displayInt) {\n          <span class=\"ant-statistic-content-value-int\">{{ displayInt }}</span>\n        }\n        @if (displayDecimal) {\n          <span class=\"ant-statistic-content-value-decimal\">{{ displayDecimal }}</span>\n        }\n      }\n    </span>\n  `,\n  imports: [NgTemplateOutlet]\n})\nexport class NzStatisticNumberComponent implements OnChanges {\n  @Input() nzValue?: NzStatisticValueType;\n  @Input() nzValueTemplate?: TemplateRef<{ $implicit: NzStatisticValueType }>;\n\n  displayInt = '';\n  displayDecimal = '';\n\n  private locale_id = inject(LOCALE_ID);\n\n  ngOnChanges(): void {\n    this.formatNumber();\n  }\n\n  private formatNumber(): void {\n    const decimalSeparator: string =\n      typeof this.nzValue === 'number' ? '.' : getLocaleNumberSymbol(this.locale_id, NumberSymbol.Decimal);\n    const value = String(this.nzValue);\n    const [int, decimal] = value.split(decimalSeparator);\n\n    this.displayInt = int;\n    this.displayDecimal = decimal ? `${decimalSeparator}${decimal}` : '';\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Input,\n  OnDestroy,\n  OnInit,\n  TemplateRef,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject\n} from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NgStyleInterface } from 'ng-zorro-antd/core/types';\nimport { NzSkeletonModule } from 'ng-zorro-antd/skeleton';\n\nimport { NzStatisticNumberComponent } from './statistic-number.component';\nimport { NzStatisticValueType } from './typings';\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  selector: 'nz-statistic',\n  exportAs: 'nzStatistic',\n  template: `\n    <div class=\"ant-statistic-title\">\n      <ng-container *nzStringTemplateOutlet=\"nzTitle\">{{ nzTitle }}</ng-container>\n    </div>\n    @if (nzLoading) {\n      <nz-skeleton class=\"ant-statistic-skeleton\" [nzParagraph]=\"false\" />\n    } @else {\n      <div class=\"ant-statistic-content\" [style]=\"nzValueStyle\">\n        @if (nzPrefix) {\n          <span class=\"ant-statistic-content-prefix\">\n            <ng-container *nzStringTemplateOutlet=\"nzPrefix\">{{ nzPrefix }}</ng-container>\n          </span>\n        }\n        <nz-statistic-number [nzValue]=\"nzValue\" [nzValueTemplate]=\"nzValueTemplate\"></nz-statistic-number>\n        @if (nzSuffix) {\n          <span class=\"ant-statistic-content-suffix\">\n            <ng-container *nzStringTemplateOutlet=\"nzSuffix\">{{ nzSuffix }}</ng-container>\n          </span>\n        }\n      </div>\n    }\n  `,\n  host: {\n    class: 'ant-statistic',\n    '[class.ant-statistic-rtl]': `dir === 'rtl'`\n  },\n  imports: [NzSkeletonModule, NzStatisticNumberComponent, NzOutletModule]\n})\nexport class NzStatisticComponent implements OnDestroy, OnInit {\n  @Input() nzPrefix?: string | TemplateRef<void>;\n  @Input() nzSuffix?: string | TemplateRef<void>;\n  @Input() nzTitle?: string | TemplateRef<void>;\n  @Input() nzValue?: NzStatisticValueType;\n  @Input() nzValueStyle: NgStyleInterface = {};\n  @Input() nzValueTemplate?: TemplateRef<{ $implicit: NzStatisticValueType }>;\n  @Input({ transform: booleanAttribute }) nzLoading: boolean = false;\n  dir: Direction = 'ltr';\n\n  private destroy$ = new Subject<void>();\n  protected cdr = inject(ChangeDetectorRef);\n  private directionality = inject(Directionality);\n\n  ngOnInit(): void {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n\n    this.dir = this.directionality.value;\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Platform } from '@angular/cdk/platform';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  SimpleChanges,\n  ViewEncapsulation\n} from '@angular/core';\nimport { Subscription, interval } from 'rxjs';\n\nimport { NzPipesModule } from 'ng-zorro-antd/core/pipe';\n\nimport { NzStatisticComponent } from './statistic.component';\n\nconst REFRESH_INTERVAL = 1000 / 30;\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  selector: 'nz-countdown',\n  exportAs: 'nzCountdown',\n  template: `\n    <nz-statistic\n      [nzValue]=\"diff\"\n      [nzValueStyle]=\"nzValueStyle\"\n      [nzValueTemplate]=\"nzValueTemplate || countDownTpl\"\n      [nzTitle]=\"nzTitle\"\n      [nzPrefix]=\"nzPrefix\"\n      [nzSuffix]=\"nzSuffix\"\n    ></nz-statistic>\n\n    <ng-template #countDownTpl>{{ diff | nzTimeRange: nzFormat }}</ng-template>\n  `,\n  imports: [NzStatisticComponent, NzPipesModule]\n})\nexport class NzCountdownComponent extends NzStatisticComponent implements OnInit, OnChanges, OnDestroy {\n  @Input() nzFormat: string = 'HH:mm:ss';\n  @Output() readonly nzCountdownFinish = new EventEmitter<void>();\n\n  diff!: number;\n\n  private target: number = 0;\n  private updater_?: Subscription | null;\n\n  constructor(\n    private ngZone: NgZone,\n    private platform: Platform\n  ) {\n    super();\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes.nzValue) {\n      this.target = Number(changes.nzValue.currentValue);\n      if (!changes.nzValue.isFirstChange()) {\n        this.syncTimer();\n      }\n    }\n  }\n\n  override ngOnInit(): void {\n    super.ngOnInit();\n    this.syncTimer();\n  }\n\n  override ngOnDestroy(): void {\n    this.stopTimer();\n  }\n\n  syncTimer(): void {\n    if (this.target >= Date.now()) {\n      this.startTimer();\n    } else {\n      this.stopTimer();\n    }\n  }\n\n  startTimer(): void {\n    if (this.platform.isBrowser) {\n      this.ngZone.runOutsideAngular(() => {\n        this.stopTimer();\n        this.updater_ = interval(REFRESH_INTERVAL).subscribe(() => {\n          this.updateValue();\n          this.cdr.detectChanges();\n        });\n      });\n    }\n  }\n\n  stopTimer(): void {\n    if (this.updater_) {\n      this.updater_.unsubscribe();\n      this.updater_ = null;\n    }\n  }\n\n  /**\n   * Update time that should be displayed on the screen.\n   */\n  protected updateValue(): void {\n    this.diff = Math.max(this.target - Date.now(), 0);\n\n    if (this.diff === 0) {\n      this.stopTimer();\n\n      if (this.nzCountdownFinish.observers.length) {\n        this.ngZone.run(() => this.nzCountdownFinish.emit());\n      }\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzCountdownComponent } from './countdown.component';\nimport { NzStatisticNumberComponent } from './statistic-number.component';\nimport { NzStatisticComponent } from './statistic.component';\n\n@NgModule({\n  imports: [NzStatisticComponent, NzCountdownComponent, NzStatisticNumberComponent],\n  exports: [NzStatisticComponent, NzCountdownComponent, NzStatisticNumberComponent]\n})\nexport class NzStatisticModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './countdown.component';\nexport * from './statistic.component';\nexport * from './statistic.module';\nexport * from './statistic-number.component';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1", "i2"], "mappings": ";;;;;;;;;;;;;;AAAA;;;AAGG;MAyCU,0BAA0B,CAAA;AAC5B,IAAA,OAAO;AACP,IAAA,eAAe;IAExB,UAAU,GAAG,EAAE;IACf,cAAc,GAAG,EAAE;AAEX,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;IAErC,WAAW,GAAA;QACT,IAAI,CAAC,YAAY,EAAE;;IAGb,YAAY,GAAA;QAClB,MAAM,gBAAgB,GACpB,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,GAAG,GAAG,GAAG,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC;QACtG,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AAClC,QAAA,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC;AAEpD,QAAA,IAAI,CAAC,UAAU,GAAG,GAAG;AACrB,QAAA,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,CAAA,EAAG,gBAAgB,CAAA,EAAG,OAAO,CAAE,CAAA,GAAG,EAAE;;uGApB3D,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAA1B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,0BAA0B,EAnB3B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;AAgBT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACS,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEf,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBAzBtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;AAgBT,EAAA,CAAA;oBACD,OAAO,EAAE,CAAC,gBAAgB;AAC3B,iBAAA;8BAEU,OAAO,EAAA,CAAA;sBAAf;gBACQ,eAAe,EAAA,CAAA;sBAAvB;;;AC9CH;;;AAGG;MA0DU,oBAAoB,CAAA;AACtB,IAAA,QAAQ;AACR,IAAA,QAAQ;AACR,IAAA,OAAO;AACP,IAAA,OAAO;IACP,YAAY,GAAqB,EAAE;AACnC,IAAA,eAAe;IACgB,SAAS,GAAY,KAAK;IAClE,GAAG,GAAc,KAAK;AAEd,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;AAC5B,IAAA,GAAG,GAAG,MAAM,CAAC,iBAAiB,CAAC;AACjC,IAAA,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;IAE/C,QAAQ,GAAA;QACN,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,YAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,SAAC,CAAC;QAEF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;;IAGtC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;uGAzBf,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAApB,oBAAoB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,YAAA,EAAA,cAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAOX,gBAAgB,CAnC1B,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,yBAAA,EAAA,eAAA,EAAA,EAAA,cAAA,EAAA,eAAA,EAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;AAqBT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAKS,gBAAgB,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,mBAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,WAAA,EAAA,SAAA,EAAA,SAAA,EAAA,UAAA,EAAA,aAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,0BAA0B,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAE,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE3D,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAjChC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AAqBT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,eAAe;AACtB,wBAAA,2BAA2B,EAAE,CAAe,aAAA;AAC7C,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,0BAA0B,EAAE,cAAc;AACvE,iBAAA;8BAEU,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACuC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;;;AC3CxC,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAE;AAqB5B,MAAO,oBAAqB,SAAQ,oBAAoB,CAAA;AAUlD,IAAA,MAAA;AACA,IAAA,QAAA;IAVD,QAAQ,GAAW,UAAU;AACnB,IAAA,iBAAiB,GAAG,IAAI,YAAY,EAAQ;AAE/D,IAAA,IAAI;IAEI,MAAM,GAAW,CAAC;AAClB,IAAA,QAAQ;IAEhB,WACU,CAAA,MAAc,EACd,QAAkB,EAAA;AAE1B,QAAA,KAAK,EAAE;QAHC,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAQ,CAAA,QAAA,GAAR,QAAQ;;AAKlB,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;YAClD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE;gBACpC,IAAI,CAAC,SAAS,EAAE;;;;IAKb,QAAQ,GAAA;QACf,KAAK,CAAC,QAAQ,EAAE;QAChB,IAAI,CAAC,SAAS,EAAE;;IAGT,WAAW,GAAA;QAClB,IAAI,CAAC,SAAS,EAAE;;IAGlB,SAAS,GAAA;QACP,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;YAC7B,IAAI,CAAC,UAAU,EAAE;;aACZ;YACL,IAAI,CAAC,SAAS,EAAE;;;IAIpB,UAAU,GAAA;AACR,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;AAC3B,YAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAK;gBACjC,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,MAAK;oBACxD,IAAI,CAAC,WAAW,EAAE;AAClB,oBAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,iBAAC,CAAC;AACJ,aAAC,CAAC;;;IAIN,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;AAC3B,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;;AAIxB;;AAEG;IACO,WAAW,GAAA;AACnB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAEjD,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;YACnB,IAAI,CAAC,SAAS,EAAE;YAEhB,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,MAAM,EAAE;AAC3C,gBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;;;;uGAvE/C,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,QAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAApB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,oBAAoB,EAdrB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;GAWT,EACS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,oBAAoB,2LAAE,aAAa,EAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAAC,IAAA,CAAA,eAAA,EAAA,IAAA,EAAA,aAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAElC,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAnBhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,QAAQ,EAAE;;;;;;;;;;;AAWT,EAAA,CAAA;AACD,oBAAA,OAAO,EAAE,CAAC,oBAAoB,EAAE,aAAa;AAC9C,iBAAA;oGAEU,QAAQ,EAAA,CAAA;sBAAhB;gBACkB,iBAAiB,EAAA,CAAA;sBAAnC;;;AChDH;;;AAGG;MAYU,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAjB,iBAAiB,EAAA,OAAA,EAAA,CAHlB,oBAAoB,EAAE,oBAAoB,EAAE,0BAA0B,CAAA,EAAA,OAAA,EAAA,CACtE,oBAAoB,EAAE,oBAAoB,EAAE,0BAA0B,CAAA,EAAA,CAAA;wGAErE,iBAAiB,EAAA,OAAA,EAAA,CAHlB,oBAAoB,EAAE,oBAAoB,CAAA,EAAA,CAAA;;2FAGzC,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAJ7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,0BAA0B,CAAC;AACjF,oBAAA,OAAO,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,0BAA0B;AACjF,iBAAA;;;ACdD;;;AAGG;;ACHH;;AAEG;;;;"}