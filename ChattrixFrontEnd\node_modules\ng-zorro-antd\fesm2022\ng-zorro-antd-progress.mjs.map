{"version": 3, "file": "ng-zorro-antd-progress.mjs", "sources": ["../../components/progress/utils.ts", "../../components/progress/progress.component.ts", "../../components/progress/progress.module.ts", "../../components/progress/typings.ts", "../../components/progress/public-api.ts", "../../components/progress/ng-zorro-antd-progress.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NzProgressColorGradient, NzProgressGradientProgress } from './typings';\n\nfunction stripPercentToNumber(percent: string): number {\n  return +percent.replace('%', '');\n}\n\nexport const sortGradient = (gradients: NzProgressGradientProgress): Array<{ key: number; value: string }> => {\n  let tempArr: Array<{ key: number; value: string }> = [];\n\n  Object.keys(gradients).forEach(key => {\n    const value = gradients[key];\n    const formatKey = stripPercentToNumber(key);\n    if (!isNaN(formatKey)) {\n      tempArr.push({\n        key: formatKey,\n        value\n      });\n    }\n  });\n\n  tempArr = tempArr.sort((a, b) => a.key - b.key);\n  return tempArr;\n};\n\nexport const handleCircleGradient = (\n  strokeColor: NzProgressGradientProgress\n): Array<{ offset: string; color: string }> =>\n  sortGradient(strokeColor).map(({ key, value }) => ({ offset: `${key}%`, color: value }));\n\nexport const handleLinearGradient = (strokeColor: NzProgressColorGradient): string => {\n  const { from = '#1890ff', to = '#1890ff', direction = 'to right', ...rest } = strokeColor;\n  if (Object.keys(rest).length !== 0) {\n    const sortedGradients = sortGradient(rest as NzProgressGradientProgress)\n      .map(({ key, value }) => `${value} ${key}%`)\n      .join(', ');\n    return `linear-gradient(${direction}, ${sortedGradients})`;\n  }\n  return `linear-gradient(${direction}, ${from}, ${to})`;\n};\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  SimpleChanges,\n  ViewEncapsulation,\n  numberAttribute\n} from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzConfigKey, NzConfigService, WithConfig } from 'ng-zorro-antd/core/config';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NgStyleInterface } from 'ng-zorro-antd/core/types';\nimport { isNotNil, numberAttributeWithZeroFallback } from 'ng-zorro-antd/core/util';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\nimport {\n  NzProgressCirclePath,\n  NzProgressColorGradient,\n  NzProgressFormatter,\n  NzProgressGapPositionType,\n  NzProgressGradientProgress,\n  NzProgressStatusType,\n  NzProgressStepItem,\n  NzProgressStrokeColorType,\n  NzProgressStrokeLinecapType,\n  NzProgressTypeType\n} from './typings';\nimport { handleCircleGradient, handleLinearGradient } from './utils';\n\nlet gradientIdSeed = 0;\n\nconst NZ_CONFIG_MODULE_NAME: NzConfigKey = 'progress';\nconst statusIconNameMap = new Map([\n  ['success', 'check'],\n  ['exception', 'close']\n]);\nconst statusColorMap = new Map([\n  ['normal', '#108ee9'],\n  ['exception', '#ff5500'],\n  ['success', '#87d068']\n]);\nconst defaultFormatter: NzProgressFormatter = (p: number): string => `${p}%`;\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  selector: 'nz-progress',\n  exportAs: 'nzProgress',\n  preserveWhitespaces: false,\n  imports: [NzIconModule, NzOutletModule, NgTemplateOutlet],\n  template: `\n    <ng-template #progressInfoTemplate>\n      @if (nzShowInfo) {\n        <span class=\"ant-progress-text\">\n          @if ((status === 'exception' || status === 'success') && !nzFormat) {\n            <nz-icon [nzType]=\"icon\" />\n          } @else {\n            <ng-container *nzStringTemplateOutlet=\"formatter; context: { $implicit: nzPercent }; let formatter\">\n              {{ formatter(nzPercent) }}\n            </ng-container>\n          }\n        </span>\n      }\n    </ng-template>\n\n    <div\n      [class]=\"'ant-progress ant-progress-status-' + status\"\n      [class.ant-progress-line]=\"nzType === 'line'\"\n      [class.ant-progress-small]=\"nzSize === 'small'\"\n      [class.ant-progress-default]=\"nzSize === 'default'\"\n      [class.ant-progress-show-info]=\"nzShowInfo\"\n      [class.ant-progress-circle]=\"isCircleStyle\"\n      [class.ant-progress-steps]=\"isSteps\"\n      [class.ant-progress-rtl]=\"dir === 'rtl'\"\n    >\n      @if (nzType === 'line') {\n        <div>\n          <!-- normal line style -->\n          @if (isSteps) {\n            <div class=\"ant-progress-steps-outer\">\n              @for (step of steps; track $index) {\n                <div class=\"ant-progress-steps-item\" [style]=\"step\"></div>\n              }\n              <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n            </div>\n          } @else {\n            <div class=\"ant-progress-outer\">\n              <div class=\"ant-progress-inner\">\n                <div\n                  class=\"ant-progress-bg\"\n                  [style.width.%]=\"nzPercent\"\n                  [style.border-radius]=\"nzStrokeLinecap === 'round' ? '100px' : '0'\"\n                  [style.background]=\"!isGradient ? nzStrokeColor : null\"\n                  [style.background-image]=\"isGradient ? lineGradient : null\"\n                  [style.height.px]=\"strokeWidth\"\n                ></div>\n                @if (nzSuccessPercent || nzSuccessPercent === 0) {\n                  <div\n                    class=\"ant-progress-success-bg\"\n                    [style.width.%]=\"nzSuccessPercent\"\n                    [style.border-radius]=\"nzStrokeLinecap === 'round' ? '100px' : '0'\"\n                    [style.height.px]=\"strokeWidth\"\n                  ></div>\n                }\n              </div>\n            </div>\n            <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n          }\n        </div>\n      }\n      <!-- line progress -->\n\n      <!-- circle / dashboard progress -->\n\n      @if (isCircleStyle) {\n        <div\n          [style.width.px]=\"this.nzWidth\"\n          [style.height.px]=\"this.nzWidth\"\n          [style.fontSize.px]=\"this.nzWidth * 0.15 + 6\"\n          class=\"ant-progress-inner\"\n          [class.ant-progress-circle-gradient]=\"isGradient\"\n        >\n          <svg class=\"ant-progress-circle \" viewBox=\"0 0 100 100\">\n            @if (isGradient) {\n              <defs>\n                <linearGradient [id]=\"'gradient-' + gradientId\" x1=\"100%\" y1=\"0%\" x2=\"0%\" y2=\"0%\">\n                  @for (i of circleGradient; track $index) {\n                    <stop [attr.offset]=\"i.offset\" [attr.stop-color]=\"i.color\"></stop>\n                  }\n                </linearGradient>\n              </defs>\n            }\n\n            <path\n              class=\"ant-progress-circle-trail\"\n              stroke=\"#f3f3f3\"\n              fill-opacity=\"0\"\n              [attr.stroke-width]=\"strokeWidth\"\n              [attr.d]=\"pathString\"\n              [style]=\"trailPathStyle\"\n            ></path>\n            @for (p of progressCirclePath; track $index) {\n              <path\n                class=\"ant-progress-circle-path\"\n                fill-opacity=\"0\"\n                [attr.d]=\"pathString\"\n                [attr.stroke-linecap]=\"nzStrokeLinecap\"\n                [attr.stroke]=\"p.stroke\"\n                [attr.stroke-width]=\"nzPercent ? strokeWidth : 0\"\n                [style]=\"p.strokePathStyle\"\n              ></path>\n            }\n          </svg>\n          <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n        </div>\n      }\n    </div>\n  `\n})\nexport class NzProgressComponent implements OnChanges, OnInit, OnDestroy {\n  readonly _nzModuleName: NzConfigKey = NZ_CONFIG_MODULE_NAME;\n\n  @Input() @WithConfig() nzShowInfo: boolean = true;\n  @Input() nzWidth = 132;\n  @Input() @WithConfig() nzStrokeColor?: NzProgressStrokeColorType = undefined;\n  @Input() @WithConfig() nzSize: 'default' | 'small' = 'default';\n  @Input() nzFormat?: NzProgressFormatter;\n  @Input({ transform: numberAttributeWithZeroFallback }) nzSuccessPercent?: number;\n  @Input({ transform: numberAttribute }) nzPercent: number = 0;\n  @Input({ transform: numberAttributeWithZeroFallback }) @WithConfig() nzStrokeWidth?: number;\n  @Input({ transform: numberAttributeWithZeroFallback }) @WithConfig() nzGapDegree?: number;\n  @Input() nzStatus?: NzProgressStatusType;\n  @Input() nzType: NzProgressTypeType = 'line';\n  @Input() @WithConfig() nzGapPosition: NzProgressGapPositionType = 'top';\n  @Input() @WithConfig() nzStrokeLinecap: NzProgressStrokeLinecapType = 'round';\n\n  @Input({ transform: numberAttribute }) nzSteps: number = 0;\n\n  steps: NzProgressStepItem[] = [];\n\n  /** Gradient style when `nzType` is `line`. */\n  lineGradient: string | null = null;\n\n  /** If user uses gradient color. */\n  isGradient = false;\n\n  /** If the linear progress is a step progress. */\n  isSteps = false;\n\n  /**\n   * Each progress whose `nzType` is circle or dashboard should have unique id to\n   * define `<linearGradient>`.\n   */\n  gradientId = gradientIdSeed++;\n\n  /** Paths to rendered in the template. */\n  progressCirclePath: NzProgressCirclePath[] = [];\n  circleGradient?: Array<{ offset: string; color: string }>;\n  trailPathStyle: NgStyleInterface | null = null;\n  pathString?: string;\n  icon!: string;\n\n  dir: Direction = 'ltr';\n\n  get formatter(): NzProgressFormatter {\n    return this.nzFormat || defaultFormatter;\n  }\n\n  get status(): NzProgressStatusType {\n    return this.nzStatus || this.inferredStatus;\n  }\n\n  get strokeWidth(): number {\n    return this.nzStrokeWidth || (this.nzType === 'line' && this.nzSize !== 'small' ? 8 : 6);\n  }\n\n  get isCircleStyle(): boolean {\n    return this.nzType === 'circle' || this.nzType === 'dashboard';\n  }\n\n  private cachedStatus: NzProgressStatusType = 'normal';\n  private inferredStatus: NzProgressStatusType = 'normal';\n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private cdr: ChangeDetectorRef,\n    public nzConfigService: NzConfigService,\n    private directionality: Directionality\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const {\n      nzSteps,\n      nzGapPosition,\n      nzStrokeLinecap,\n      nzStrokeColor,\n      nzGapDegree,\n      nzType,\n      nzStatus,\n      nzPercent,\n      nzSuccessPercent,\n      nzStrokeWidth\n    } = changes;\n\n    if (nzStatus) {\n      this.cachedStatus = this.nzStatus || this.cachedStatus;\n    }\n\n    if (nzPercent || nzSuccessPercent) {\n      const fillAll = parseInt(this.nzPercent.toString(), 10) >= 100;\n      if (fillAll) {\n        if ((isNotNil(this.nzSuccessPercent) && this.nzSuccessPercent! >= 100) || this.nzSuccessPercent === undefined) {\n          this.inferredStatus = 'success';\n        }\n      } else {\n        this.inferredStatus = this.cachedStatus;\n      }\n    }\n\n    if (nzStatus || nzPercent || nzSuccessPercent || nzStrokeColor) {\n      this.updateIcon();\n    }\n\n    if (nzStrokeColor) {\n      this.setStrokeColor();\n    }\n\n    if (nzGapPosition || nzStrokeLinecap || nzGapDegree || nzType || nzPercent || nzStrokeColor || nzStrokeColor) {\n      this.getCirclePaths();\n    }\n\n    if (nzPercent || nzSteps || nzStrokeWidth) {\n      this.isSteps = this.nzSteps > 0;\n      if (this.isSteps) {\n        this.getSteps();\n      }\n    }\n  }\n\n  ngOnInit(): void {\n    this.nzConfigService\n      .getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.updateIcon();\n        this.setStrokeColor();\n        this.getCirclePaths();\n      });\n\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n\n    this.dir = this.directionality.value;\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private updateIcon(): void {\n    const ret = statusIconNameMap.get(this.status);\n    this.icon = ret ? ret + (this.isCircleStyle ? '-o' : '-circle-fill') : '';\n  }\n\n  /**\n   * Calculate step render configs.\n   */\n  private getSteps(): void {\n    const current = Math.floor(this.nzSteps * (this.nzPercent / 100));\n    const stepWidth = this.nzSize === 'small' ? 2 : 14;\n\n    const steps = [];\n\n    for (let i = 0; i < this.nzSteps; i++) {\n      let color;\n      if (i <= current - 1) {\n        color = this.nzStrokeColor;\n      }\n      const stepStyle = {\n        backgroundColor: `${color}`,\n        width: `${stepWidth}px`,\n        height: `${this.strokeWidth}px`\n      };\n      steps.push(stepStyle);\n    }\n\n    this.steps = steps;\n  }\n\n  /**\n   * Calculate paths when the type is circle or dashboard.\n   */\n  private getCirclePaths(): void {\n    if (!this.isCircleStyle) {\n      return;\n    }\n\n    const values = isNotNil(this.nzSuccessPercent) ? [this.nzSuccessPercent!, this.nzPercent] : [this.nzPercent];\n\n    // Calculate shared styles.\n    const radius = 50 - this.strokeWidth / 2;\n    const gapPosition = this.nzGapPosition || (this.nzType === 'circle' ? 'top' : 'bottom');\n    const len = Math.PI * 2 * radius;\n    const gapDegree = this.nzGapDegree || (this.nzType === 'circle' ? 0 : 75);\n\n    let beginPositionX = 0;\n    let beginPositionY = -radius;\n    let endPositionX = 0;\n    let endPositionY = radius * -2;\n\n    switch (gapPosition) {\n      case 'left':\n        beginPositionX = -radius;\n        beginPositionY = 0;\n        endPositionX = radius * 2;\n        endPositionY = 0;\n        break;\n      case 'right':\n        beginPositionX = radius;\n        beginPositionY = 0;\n        endPositionX = radius * -2;\n        endPositionY = 0;\n        break;\n      case 'bottom':\n        beginPositionY = radius;\n        endPositionY = radius * 2;\n        break;\n      default:\n    }\n\n    this.pathString = `M 50,50 m ${beginPositionX},${beginPositionY}\n       a ${radius},${radius} 0 1 1 ${endPositionX},${-endPositionY}\n       a ${radius},${radius} 0 1 1 ${-endPositionX},${endPositionY}`;\n\n    this.trailPathStyle = {\n      strokeDasharray: `${len - gapDegree}px ${len}px`,\n      strokeDashoffset: `-${gapDegree / 2}px`,\n      transition: 'stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s'\n    };\n\n    // Calculate styles for each path.\n    this.progressCirclePath = values\n      .map((value, index) => {\n        const isSuccessPercent = values.length === 2 && index === 0;\n        return {\n          stroke: this.isGradient && !isSuccessPercent ? `url(#gradient-${this.gradientId})` : null,\n          strokePathStyle: {\n            stroke: !this.isGradient\n              ? isSuccessPercent\n                ? statusColorMap.get('success')\n                : (this.nzStrokeColor as string)\n              : null,\n            transition:\n              'stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s',\n            strokeDasharray: `${((value || 0) / 100) * (len - gapDegree)}px ${len}px`,\n            strokeDashoffset: `-${gapDegree / 2}px`\n          }\n        };\n      })\n      .reverse();\n  }\n\n  private setStrokeColor(): void {\n    const color = this.nzStrokeColor;\n    const isGradient = (this.isGradient = !!color && typeof color !== 'string');\n    if (isGradient && !this.isCircleStyle) {\n      this.lineGradient = handleLinearGradient(color as NzProgressColorGradient);\n    } else if (isGradient && this.isCircleStyle) {\n      this.circleGradient = handleCircleGradient(this.nzStrokeColor as NzProgressGradientProgress);\n    } else {\n      this.lineGradient = null;\n      this.circleGradient = [];\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzProgressComponent } from './progress.component';\n\n@NgModule({\n  imports: [NzProgressComponent],\n  exports: [NzProgressComponent]\n})\nexport class NzProgressModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { TemplateRef } from '@angular/core';\n\nimport { NgStyleInterface } from 'ng-zorro-antd/core/types';\n\nexport type NzProgressGapPositionType = 'top' | 'bottom' | 'left' | 'right';\n\nexport type NzProgressStatusType = 'success' | 'exception' | 'active' | 'normal';\n\nexport type NzProgressTypeType = 'line' | 'circle' | 'dashboard';\n\nexport type NzProgressStrokeLinecapType = 'round' | 'square';\n\nexport interface NzProgressGradientProgress {\n  [percent: string]: string;\n}\n\nexport interface NzProgressGradientFromTo {\n  from: string;\n  to: string;\n}\n\nexport type NzProgressColorGradient = { direction?: string } & (NzProgressGradientProgress | NzProgressGradientFromTo);\n\nexport type NzProgressStrokeColorType = string | NzProgressColorGradient;\n\nexport type NzProgressFormatter = ((percent: number) => string | null) | TemplateRef<{ $implicit: number }>;\n\nexport interface NzProgressCirclePath {\n  stroke: string | null;\n  strokePathStyle: NgStyleInterface;\n}\n\nexport interface NzProgressStepItem {\n  backgroundColor: string;\n  width: string;\n  height: string;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport { NzProgressModule } from './progress.module';\nexport { NzProgressComponent } from './progress.component';\nexport * from './typings';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;;AAGG;AAIH,SAAS,oBAAoB,CAAC,OAAe,EAAA;IAC3C,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;AAClC;AAEO,MAAM,YAAY,GAAG,CAAC,SAAqC,KAA2C;IAC3G,IAAI,OAAO,GAA0C,EAAE;IAEvD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,GAAG,IAAG;AACnC,QAAA,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC;AAC5B,QAAA,MAAM,SAAS,GAAG,oBAAoB,CAAC,GAAG,CAAC;AAC3C,QAAA,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;YACrB,OAAO,CAAC,IAAI,CAAC;AACX,gBAAA,GAAG,EAAE,SAAS;gBACd;AACD,aAAA,CAAC;;AAEN,KAAC,CAAC;IAEF,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;AAC/C,IAAA,OAAO,OAAO;AAChB,CAAC;AAEM,MAAM,oBAAoB,GAAG,CAClC,WAAuC,KAEvC,YAAY,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAA,EAAG,GAAG,CAAA,CAAA,CAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AAEnF,MAAM,oBAAoB,GAAG,CAAC,WAAoC,KAAY;AACnF,IAAA,MAAM,EAAE,IAAI,GAAG,SAAS,EAAE,EAAE,GAAG,SAAS,EAAE,SAAS,GAAG,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,WAAW;IACzF,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AAClC,QAAA,MAAM,eAAe,GAAG,YAAY,CAAC,IAAkC;AACpE,aAAA,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAG,EAAA,KAAK,CAAI,CAAA,EAAA,GAAG,GAAG;aAC1C,IAAI,CAAC,IAAI,CAAC;AACb,QAAA,OAAO,CAAmB,gBAAA,EAAA,SAAS,CAAK,EAAA,EAAA,eAAe,GAAG;;AAE5D,IAAA,OAAO,mBAAmB,SAAS,CAAA,EAAA,EAAK,IAAI,CAAK,EAAA,EAAA,EAAE,GAAG;AACxD,CAAC;;ACDD,IAAI,cAAc,GAAG,CAAC;AAEtB,MAAM,qBAAqB,GAAgB,UAAU;AACrD,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAC;IAChC,CAAC,SAAS,EAAE,OAAO,CAAC;IACpB,CAAC,WAAW,EAAE,OAAO;AACtB,CAAA,CAAC;AACF,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC;IAC7B,CAAC,QAAQ,EAAE,SAAS,CAAC;IACrB,CAAC,WAAW,EAAE,SAAS,CAAC;IACxB,CAAC,SAAS,EAAE,SAAS;AACtB,CAAA,CAAC;AACF,MAAM,gBAAgB,GAAwB,CAAC,CAAS,KAAa,CAAA,EAAG,CAAC,CAAA,CAAA,CAAG;IAsH/D,mBAAmB,GAAA,CAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;iBAAnB,mBAAmB,CAAA;;;AAGpB,YAAA,sBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAEZ,YAAA,yBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,kBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAIkC,YAAA,yBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,uBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAG1D,YAAA,yBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,2BAAA,GAAA,CAAA,UAAU,EAAE,CAAA;YAZC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,YAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,UAAU,EAAV,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,UAAU,GAAiB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,wBAAA,EAAA,6BAAA,CAAA;YAE3B,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,yBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,eAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,eAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,aAAa,EAAb,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,aAAa,GAAyC,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,2BAAA,EAAA,gCAAA,CAAA;YACtD,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,kBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,QAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,QAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,MAAM,EAAN,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,MAAM,GAAkC,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,oBAAA,EAAA,yBAAA,CAAA;YAIM,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,yBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,eAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,eAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,aAAa,EAAb,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,aAAa,GAAU,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,2BAAA,EAAA,gCAAA,CAAA;YACvB,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,uBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,aAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,aAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,WAAW,EAAX,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,WAAW,GAAU,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,yBAAA,EAAA,8BAAA,CAAA;YAGnE,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,yBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,eAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,eAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,aAAa,EAAb,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,aAAa,GAAoC,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,2BAAA,EAAA,gCAAA,CAAA;YACjD,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,2BAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,iBAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,eAAe,EAAf,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,eAAe,GAAwC,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,6BAAA,EAAA,kCAAA,CAAA;;;QAmDpE,GAAG;QACJ,eAAe;QACd,cAAc;QAnEf,aAAa,GAAgB,qBAAqB;QAEpC,UAAU,GAAA,iBAAA,CAAA,IAAA,EAAA,wBAAA,EAAY,IAAI,CAAC;QACzC,OAAO,IAAA,iBAAA,CAAA,IAAA,EAAA,6BAAA,CAAA,EAAG,GAAG;QACC,aAAa,GAAA,iBAAA,CAAA,IAAA,EAAA,2BAAA,EAA+B,SAAS,CAAC;QACtD,MAAM,IAAA,iBAAA,CAAA,IAAA,EAAA,gCAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,oBAAA,EAAwB,SAAS,CAAC;AACtD,QAAA,QAAQ,GAAuB,iBAAA,CAAA,IAAA,EAAA,yBAAA,CAAA;AACe,QAAA,gBAAgB;QAChC,SAAS,GAAW,CAAC;AACS,QAAA,aAAa,GAAU,iBAAA,CAAA,IAAA,EAAA,2BAAA,EAAA,KAAA,CAAA,CAAA;AACvB,QAAA,WAAW,IAAU,iBAAA,CAAA,IAAA,EAAA,gCAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,yBAAA,EAAA,KAAA,CAAA,CAAA;AACjF,QAAA,QAAQ,GAAwB,iBAAA,CAAA,IAAA,EAAA,8BAAA,CAAA;QAChC,MAAM,GAAuB,MAAM;QACrB,aAAa,GAAA,iBAAA,CAAA,IAAA,EAAA,2BAAA,EAA8B,KAAK,CAAC;QACjD,eAAe,IAAA,iBAAA,CAAA,IAAA,EAAA,gCAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,6BAAA,EAAgC,OAAO,CAAC;QAEvC,OAAO,IAAA,iBAAA,CAAA,IAAA,EAAA,kCAAA,CAAA,EAAW,CAAC;QAE1D,KAAK,GAAyB,EAAE;;QAGhC,YAAY,GAAkB,IAAI;;QAGlC,UAAU,GAAG,KAAK;;QAGlB,OAAO,GAAG,KAAK;AAEf;;;AAGG;QACH,UAAU,GAAG,cAAc,EAAE;;QAG7B,kBAAkB,GAA2B,EAAE;AAC/C,QAAA,cAAc;QACd,cAAc,GAA4B,IAAI;AAC9C,QAAA,UAAU;AACV,QAAA,IAAI;QAEJ,GAAG,GAAc,KAAK;AAEtB,QAAA,IAAI,SAAS,GAAA;AACX,YAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,gBAAgB;;AAG1C,QAAA,IAAI,MAAM,GAAA;AACR,YAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc;;AAG7C,QAAA,IAAI,WAAW,GAAA;YACb,OAAO,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,MAAM,KAAK,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;;AAG1F,QAAA,IAAI,aAAa,GAAA;YACf,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW;;QAGxD,YAAY,GAAyB,QAAQ;QAC7C,cAAc,GAAyB,QAAQ;AAC/C,QAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;AAEtC,QAAA,WAAA,CACU,GAAsB,EACvB,eAAgC,EAC/B,cAA8B,EAAA;YAF9B,IAAG,CAAA,GAAA,GAAH,GAAG;YACJ,IAAe,CAAA,eAAA,GAAf,eAAe;YACd,IAAc,CAAA,cAAA,GAAd,cAAc;;AAGxB,QAAA,WAAW,CAAC,OAAsB,EAAA;YAChC,MAAM,EACJ,OAAO,EACP,aAAa,EACb,eAAe,EACf,aAAa,EACb,WAAW,EACX,MAAM,EACN,QAAQ,EACR,SAAS,EACT,gBAAgB,EAChB,aAAa,EACd,GAAG,OAAO;YAEX,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY;;AAGxD,YAAA,IAAI,SAAS,IAAI,gBAAgB,EAAE;AACjC,gBAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,IAAI,GAAG;gBAC9D,IAAI,OAAO,EAAE;oBACX,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC,gBAAiB,IAAI,GAAG,KAAK,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;AAC7G,wBAAA,IAAI,CAAC,cAAc,GAAG,SAAS;;;qBAE5B;AACL,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY;;;YAI3C,IAAI,QAAQ,IAAI,SAAS,IAAI,gBAAgB,IAAI,aAAa,EAAE;gBAC9D,IAAI,CAAC,UAAU,EAAE;;YAGnB,IAAI,aAAa,EAAE;gBACjB,IAAI,CAAC,cAAc,EAAE;;AAGvB,YAAA,IAAI,aAAa,IAAI,eAAe,IAAI,WAAW,IAAI,MAAM,IAAI,SAAS,IAAI,aAAa,IAAI,aAAa,EAAE;gBAC5G,IAAI,CAAC,cAAc,EAAE;;AAGvB,YAAA,IAAI,SAAS,IAAI,OAAO,IAAI,aAAa,EAAE;gBACzC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC;AAC/B,gBAAA,IAAI,IAAI,CAAC,OAAO,EAAE;oBAChB,IAAI,CAAC,QAAQ,EAAE;;;;QAKrB,QAAQ,GAAA;AACN,YAAA,IAAI,CAAC;iBACF,gCAAgC,CAAC,qBAAqB;AACtD,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,MAAK;gBACd,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,CAAC,cAAc,EAAE;AACvB,aAAC,CAAC;YAEJ,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,gBAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,gBAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,aAAC,CAAC;YAEF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;;QAGtC,WAAW,GAAA;AACT,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;QAGlB,UAAU,GAAA;YAChB,MAAM,GAAG,GAAG,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;YAC9C,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,cAAc,CAAC,GAAG,EAAE;;AAG3E;;AAEG;QACK,QAAQ,GAAA;AACd,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;AACjE,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,KAAK,OAAO,GAAG,CAAC,GAAG,EAAE;YAElD,MAAM,KAAK,GAAG,EAAE;AAEhB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE;AACrC,gBAAA,IAAI,KAAK;AACT,gBAAA,IAAI,CAAC,IAAI,OAAO,GAAG,CAAC,EAAE;AACpB,oBAAA,KAAK,GAAG,IAAI,CAAC,aAAa;;AAE5B,gBAAA,MAAM,SAAS,GAAG;oBAChB,eAAe,EAAE,CAAG,EAAA,KAAK,CAAE,CAAA;oBAC3B,KAAK,EAAE,CAAG,EAAA,SAAS,CAAI,EAAA,CAAA;AACvB,oBAAA,MAAM,EAAE,CAAA,EAAG,IAAI,CAAC,WAAW,CAAI,EAAA;iBAChC;AACD,gBAAA,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;;AAGvB,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;AAGpB;;AAEG;QACK,cAAc,GAAA;AACpB,YAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACvB;;AAGF,YAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;;YAG5G,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC;YACxC,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAG,QAAQ,CAAC;YACvF,MAAM,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,MAAM;YAChC,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,MAAM,KAAK,QAAQ,GAAG,CAAC,GAAG,EAAE,CAAC;YAEzE,IAAI,cAAc,GAAG,CAAC;AACtB,YAAA,IAAI,cAAc,GAAG,CAAC,MAAM;YAC5B,IAAI,YAAY,GAAG,CAAC;AACpB,YAAA,IAAI,YAAY,GAAG,MAAM,GAAG,CAAC,CAAC;YAE9B,QAAQ,WAAW;AACjB,gBAAA,KAAK,MAAM;oBACT,cAAc,GAAG,CAAC,MAAM;oBACxB,cAAc,GAAG,CAAC;AAClB,oBAAA,YAAY,GAAG,MAAM,GAAG,CAAC;oBACzB,YAAY,GAAG,CAAC;oBAChB;AACF,gBAAA,KAAK,OAAO;oBACV,cAAc,GAAG,MAAM;oBACvB,cAAc,GAAG,CAAC;AAClB,oBAAA,YAAY,GAAG,MAAM,GAAG,CAAC,CAAC;oBAC1B,YAAY,GAAG,CAAC;oBAChB;AACF,gBAAA,KAAK,QAAQ;oBACX,cAAc,GAAG,MAAM;AACvB,oBAAA,YAAY,GAAG,MAAM,GAAG,CAAC;oBACzB;gBACF;;AAGF,YAAA,IAAI,CAAC,UAAU,GAAG,CAAa,UAAA,EAAA,cAAc,IAAI,cAAc;AACxD,SAAA,EAAA,MAAM,IAAI,MAAM,CAAA,OAAA,EAAU,YAAY,CAAA,CAAA,EAAI,CAAC,YAAY;WACvD,MAAM,CAAA,CAAA,EAAI,MAAM,CAAU,OAAA,EAAA,CAAC,YAAY,CAAI,CAAA,EAAA,YAAY,EAAE;YAEhE,IAAI,CAAC,cAAc,GAAG;AACpB,gBAAA,eAAe,EAAE,CAAG,EAAA,GAAG,GAAG,SAAS,CAAA,GAAA,EAAM,GAAG,CAAI,EAAA,CAAA;AAChD,gBAAA,gBAAgB,EAAE,CAAA,CAAA,EAAI,SAAS,GAAG,CAAC,CAAI,EAAA,CAAA;AACvC,gBAAA,UAAU,EAAE;aACb;;YAGD,IAAI,CAAC,kBAAkB,GAAG;AACvB,iBAAA,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;gBACpB,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC;gBAC3D,OAAO;AACL,oBAAA,MAAM,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC,gBAAgB,GAAG,CAAA,cAAA,EAAiB,IAAI,CAAC,UAAU,GAAG,GAAG,IAAI;AACzF,oBAAA,eAAe,EAAE;AACf,wBAAA,MAAM,EAAE,CAAC,IAAI,CAAC;AACZ,8BAAE;AACA,kCAAE,cAAc,CAAC,GAAG,CAAC,SAAS;kCAC3B,IAAI,CAAC;AACV,8BAAE,IAAI;AACR,wBAAA,UAAU,EACR,qGAAqG;AACvG,wBAAA,eAAe,EAAE,CAAG,EAAA,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,GAAG,SAAS,CAAC,CAAA,GAAA,EAAM,GAAG,CAAI,EAAA,CAAA;AACzE,wBAAA,gBAAgB,EAAE,CAAA,CAAA,EAAI,SAAS,GAAG,CAAC,CAAI,EAAA;AACxC;iBACF;AACH,aAAC;AACA,iBAAA,OAAO,EAAE;;QAGN,cAAc,GAAA;AACpB,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa;AAChC,YAAA,MAAM,UAAU,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;AAC3E,YAAA,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACrC,gBAAA,IAAI,CAAC,YAAY,GAAG,oBAAoB,CAAC,KAAgC,CAAC;;AACrE,iBAAA,IAAI,UAAU,IAAI,IAAI,CAAC,aAAa,EAAE;gBAC3C,IAAI,CAAC,cAAc,GAAG,oBAAoB,CAAC,IAAI,CAAC,aAA2C,CAAC;;iBACvF;AACL,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,gBAAA,IAAI,CAAC,cAAc,GAAG,EAAE;;;2GA/PjB,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;+FAAnB,mBAAmB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,aAAA,EAAA,eAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,CAAA,kBAAA,EAAA,kBAAA,EAQV,+BAA+B,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAC/B,eAAe,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EACf,+BAA+B,CAC/B,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,+BAA+B,CAM/B,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,aAAA,EAAA,eAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,eAAe,CA9HzB,EAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2GT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EA5GS,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAE,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;;2FA8G7C,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBApH/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,gBAAgB,CAAC;AACzD,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2GT,EAAA;AACF,iBAAA;iJAIwB,UAAU,EAAA,CAAA;sBAAhC;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACsB,aAAa,EAAA,CAAA;sBAAnC;gBACsB,MAAM,EAAA,CAAA;sBAA5B;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACsD,gBAAgB,EAAA,CAAA;sBAAtE,KAAK;uBAAC,EAAE,SAAS,EAAE,+BAA+B,EAAE;gBACd,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACgC,aAAa,EAAA,CAAA;sBAAjF,KAAK;uBAAC,EAAE,SAAS,EAAE,+BAA+B,EAAE;gBACgB,WAAW,EAAA,CAAA;sBAA/E,KAAK;uBAAC,EAAE,SAAS,EAAE,+BAA+B,EAAE;gBAC5C,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACsB,aAAa,EAAA,CAAA;sBAAnC;gBACsB,eAAe,EAAA,CAAA;sBAArC;gBAEsC,OAAO,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;;;AC7LvC;;;AAGG;MAUU,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAhB,gBAAgB,EAAA,OAAA,EAAA,CAHjB,mBAAmB,CAAA,EAAA,OAAA,EAAA,CACnB,mBAAmB,CAAA,EAAA,CAAA;AAElB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAHjB,mBAAmB,CAAA,EAAA,CAAA;;2FAGlB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,mBAAmB,CAAC;oBAC9B,OAAO,EAAE,CAAC,mBAAmB;AAC9B,iBAAA;;;ACZD;;;AAGG;;ACHH;;;AAGG;;ACHH;;AAEG;;;;"}