{"version": 3, "file": "ng-zorro-antd-qr-code.mjs", "sources": ["../../components/qr-code/qrcodegen.ts", "../../components/qr-code/qrcode.ts", "../../components/qr-code/qrcode.component.ts", "../../components/qr-code/qrcode.module.ts", "../../components/qr-code/public-api.ts", "../../components/qr-code/ng-zorro-antd-qr-code.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * QR Code generator library (TypeScript)\n *\n * Copyright (c) Project Nayuki.\n * https://www.nayuki.io/page/qr-code-generator-library\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\n'use strict';\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace qrcodegen {\n  type bit = number;\n  type byte = number;\n  type int = number;\n\n  /*---- QR Code symbol class ----*/\n\n  /*\n   * A QR Code symbol, which is a type of two-dimension barcode.\n   * Invented by Denso Wave and described in the ISO/IEC 18004 standard.\n   * Instances of this class represent an immutable square grid of dark and light cells.\n   * The class provides static factory functions to create a QR Code from text or binary data.\n   * The class covers the QR Code Model 2 specification, supporting all versions (sizes)\n   * from 1 to 40, all 4 error correction levels, and 4 character encoding modes.\n   *\n   * Ways to create a QR Code object:\n   * - High level: Take the payload data and call QrCode.encodeText() or QrCode.encodeBinary().\n   * - Mid level: Custom-make the list of segments and call QrCode.encodeSegments().\n   * - Low level: Custom-make the array of data codeword bytes (including\n   *   segment headers and final padding, excluding error correction codewords),\n   *   supply the appropriate version number, and call the QrCode() constructor.\n   * (Note that all ways require supplying the desired error correction level.)\n   */\n  export class QrCode {\n    /*-- Static factory functions (high level) --*/\n\n    // Returns a QR Code representing the given Unicode text string at the given error correction level.\n    // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer\n    // Unicode code points (not UTF-16 code units) if the low error correction level is used. The smallest possible\n    // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the\n    // ecl argument if it can be done without increasing the version.\n    public static encodeText(text: string, ecl: QrCode.Ecc): QrCode {\n      const segs: QrSegment[] = qrcodegen.QrSegment.makeSegments(text);\n      return QrCode.encodeSegments(segs, ecl);\n    }\n\n    // Returns a QR Code representing the given binary data at the given error correction level.\n    // This function always encodes using the binary segment mode, not any text mode. The maximum number of\n    // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.\n    // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.\n    public static encodeBinary(data: readonly byte[], ecl: QrCode.Ecc): QrCode {\n      const seg: QrSegment = qrcodegen.QrSegment.makeBytes(data);\n      return QrCode.encodeSegments([seg], ecl);\n    }\n\n    /*-- Static factory functions (mid level) --*/\n\n    // Returns a QR Code representing the given segments with the given encoding parameters.\n    // The smallest possible QR Code version within the given range is automatically\n    // chosen for the output. Iff boostEcl is true, then the ECC level of the result\n    // may be higher than the ecl argument if it can be done without increasing the\n    // version. The mask number is either between 0 to 7 (inclusive) to force that\n    // mask, or -1 to automatically choose an appropriate mask (which may be slow).\n    // This function allows the user to create a custom sequence of segments that switches\n    // between modes (such as alphanumeric and byte) to encode text in less space.\n    // This is a mid-level API; the high-level API is encodeText() and encodeBinary().\n    public static encodeSegments(\n      segs: readonly QrSegment[],\n      ecl: QrCode.Ecc,\n      minVersion: int = 1,\n      maxVersion: int = 40,\n      mask: int = -1,\n      boostEcl: boolean = true\n    ): QrCode {\n      if (\n        !(QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= QrCode.MAX_VERSION) ||\n        mask < -1 ||\n        mask > 7\n      )\n        throw new RangeError('Invalid value');\n\n      // Find the minimal version number to use\n      let version: int;\n      let dataUsedBits: int;\n      for (version = minVersion; ; version++) {\n        const dataCapacityBits: int = QrCode.getNumDataCodewords(version, ecl) * 8; // Number of data bits available\n        const usedBits: number = QrSegment.getTotalBits(segs, version);\n        if (usedBits <= dataCapacityBits) {\n          dataUsedBits = usedBits;\n          break; // This version number is found to be suitable\n        }\n        if (version >= maxVersion)\n          // All versions in the range could not fit the given data\n          throw new RangeError('Data too long');\n      }\n\n      // Increase the error correction level while the data still fits in the current version number\n      for (const newEcl of [QrCode.Ecc.MEDIUM, QrCode.Ecc.QUARTILE, QrCode.Ecc.HIGH]) {\n        // From low to high\n        if (boostEcl && dataUsedBits <= QrCode.getNumDataCodewords(version, newEcl) * 8) ecl = newEcl;\n      }\n\n      // Concatenate all segments to create the data bit string\n      const bb: bit[] = [];\n      for (const seg of segs) {\n        appendBits(seg.mode.modeBits, 4, bb);\n        appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n        for (const b of seg.getData()) bb.push(b);\n      }\n      assert(bb.length == dataUsedBits);\n\n      // Add terminator and pad up to a byte if applicable\n      const dataCapacityBits: int = QrCode.getNumDataCodewords(version, ecl) * 8;\n      assert(bb.length <= dataCapacityBits);\n      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n      appendBits(0, (8 - (bb.length % 8)) % 8, bb);\n      assert(bb.length % 8 == 0);\n\n      // Pad with alternating bytes until data capacity is reached\n      for (let padByte = 0xec; bb.length < dataCapacityBits; padByte ^= 0xec ^ 0x11) appendBits(padByte, 8, bb);\n\n      // Pack bits into bytes in big endian\n      const dataCodewords: byte[] = [];\n      while (dataCodewords.length * 8 < bb.length) dataCodewords.push(0);\n      bb.forEach((b: bit, i: int) => (dataCodewords[i >>> 3] |= b << (7 - (i & 7))));\n\n      // Create the QR Code object\n      return new QrCode(version, ecl, dataCodewords, mask);\n    }\n\n    /*-- Fields --*/\n\n    // The width and height of this QR Code, measured in modules, between\n    // 21 and 177 (inclusive). This is equal to version * 4 + 17.\n    public readonly size: int;\n\n    // The index of the mask pattern used in this QR Code, which is between 0 and 7 (inclusive).\n    // Even if a QR Code is created with automatic masking requested (mask = -1),\n    // the resulting object still has a mask value between 0 and 7.\n    public readonly mask: int;\n\n    // The modules of this QR Code (false = light, true = dark).\n    // Immutable after constructor finishes. Accessed through getModule().\n    private readonly modules: boolean[][] = [];\n\n    // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.\n    private readonly isFunction: boolean[][] = [];\n\n    /*-- Constructor (low level) and fields --*/\n\n    // Creates a new QR Code with the given version number,\n    // error correction level, data codeword bytes, and mask number.\n    // This is a low-level API that most users should not use directly.\n    // A mid-level API is the encodeSegments() function.\n    public constructor(\n      // The version number of this QR Code, which is between 1 and 40 (inclusive).\n      // This determines the size of this barcode.\n      public readonly version: int,\n\n      // The error correction level used in this QR Code.\n      public readonly errorCorrectionLevel: QrCode.Ecc,\n\n      dataCodewords: readonly byte[],\n\n      msk: int\n    ) {\n      // Check scalar arguments\n      if (version < QrCode.MIN_VERSION || version > QrCode.MAX_VERSION)\n        throw new RangeError('Version value out of range');\n      if (msk < -1 || msk > 7) throw new RangeError('Mask value out of range');\n      this.size = version * 4 + 17;\n\n      // Initialize both grids to be size*size arrays of Boolean false\n      const row: boolean[] = [];\n      for (let i = 0; i < this.size; i++) row.push(false);\n      for (let i = 0; i < this.size; i++) {\n        this.modules.push(row.slice()); // Initially all light\n        this.isFunction.push(row.slice());\n      }\n\n      // Compute ECC, draw modules\n      this.drawFunctionPatterns();\n      const allCodewords: byte[] = this.addEccAndInterleave(dataCodewords);\n      this.drawCodewords(allCodewords);\n\n      // Do masking\n      if (msk == -1) {\n        // Automatically choose best mask\n        let minPenalty: int = 1000000000;\n        for (let i = 0; i < 8; i++) {\n          this.applyMask(i);\n          this.drawFormatBits(i);\n          const penalty: int = this.getPenaltyScore();\n          if (penalty < minPenalty) {\n            msk = i;\n            minPenalty = penalty;\n          }\n          this.applyMask(i); // Undoes the mask due to XOR\n        }\n      }\n      assert(msk >= 0 && msk <= 7);\n      this.mask = msk;\n      this.applyMask(msk); // Apply the final choice of mask\n      this.drawFormatBits(msk); // Overwrite old format bits\n\n      this.isFunction = [];\n    }\n\n    /*-- Accessor methods --*/\n\n    // Returns the color of the module (pixel) at the given coordinates, which is false\n    // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n    // If the given coordinates are out of bounds, then false (light) is returned.\n    public getModule(x: int, y: int): boolean {\n      return x >= 0 && x < this.size && y >= 0 && y < this.size && this.modules[y][x];\n    }\n\n    // Modified to expose modules for easy access\n    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n    public getModules() {\n      return this.modules;\n    }\n\n    /*-- Private helper methods for constructor: Drawing function modules --*/\n\n    // Reads this object's version field, and draws and marks all function modules.\n    private drawFunctionPatterns(): void {\n      // Draw horizontal and vertical timing patterns\n      for (let i = 0; i < this.size; i++) {\n        this.setFunctionModule(6, i, i % 2 == 0);\n        this.setFunctionModule(i, 6, i % 2 == 0);\n      }\n\n      // Draw 3 finder patterns (all corners except bottom right; overwrites some timing modules)\n      this.drawFinderPattern(3, 3);\n      this.drawFinderPattern(this.size - 4, 3);\n      this.drawFinderPattern(3, this.size - 4);\n\n      // Draw numerous alignment patterns\n      const alignPatPos: int[] = this.getAlignmentPatternPositions();\n      const numAlign: int = alignPatPos.length;\n      for (let i = 0; i < numAlign; i++) {\n        for (let j = 0; j < numAlign; j++) {\n          // Don't draw on the three finder corners\n          if (!((i == 0 && j == 0) || (i == 0 && j == numAlign - 1) || (i == numAlign - 1 && j == 0)))\n            this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n        }\n      }\n\n      // Draw configuration data\n      this.drawFormatBits(0); // Dummy mask value; overwritten later in the constructor\n      this.drawVersion();\n    }\n\n    // Draws two copies of the format bits (with its own error correction code)\n    // based on the given mask and this object's error correction level field.\n    private drawFormatBits(mask: int): void {\n      // Calculate error correction code and pack bits\n      const data: int = (this.errorCorrectionLevel.formatBits << 3) | mask; // errCorrLvl is uint2, mask is uint3\n      let rem: int = data;\n      for (let i = 0; i < 10; i++) rem = (rem << 1) ^ ((rem >>> 9) * 0x537);\n      const bits = ((data << 10) | rem) ^ 0x5412; // uint15\n      assert(bits >>> 15 == 0);\n\n      // Draw first copy\n      for (let i = 0; i <= 5; i++) this.setFunctionModule(8, i, getBit(bits, i));\n      this.setFunctionModule(8, 7, getBit(bits, 6));\n      this.setFunctionModule(8, 8, getBit(bits, 7));\n      this.setFunctionModule(7, 8, getBit(bits, 8));\n      for (let i = 9; i < 15; i++) this.setFunctionModule(14 - i, 8, getBit(bits, i));\n\n      // Draw second copy\n      for (let i = 0; i < 8; i++) this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n      for (let i = 8; i < 15; i++) this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n      this.setFunctionModule(8, this.size - 8, true); // Always dark\n    }\n\n    // Draws two copies of the version bits (with its own error correction code),\n    // based on this object's version field, iff 7 <= version <= 40.\n    private drawVersion(): void {\n      if (this.version < 7) return;\n\n      // Calculate error correction code and pack bits\n      let rem: int = this.version; // version is uint6, in the range [7, 40]\n      for (let i = 0; i < 12; i++) rem = (rem << 1) ^ ((rem >>> 11) * 0x1f25);\n      const bits: int = (this.version << 12) | rem; // uint18\n      assert(bits >>> 18 == 0);\n\n      // Draw two copies\n      for (let i = 0; i < 18; i++) {\n        const color: boolean = getBit(bits, i);\n        const a: int = this.size - 11 + (i % 3);\n        const b: int = Math.floor(i / 3);\n        this.setFunctionModule(a, b, color);\n        this.setFunctionModule(b, a, color);\n      }\n    }\n\n    // Draws a 9*9 finder pattern including the border separator,\n    // with the center module at (x, y). Modules can be out of bounds.\n    private drawFinderPattern(x: int, y: int): void {\n      for (let dy = -4; dy <= 4; dy++) {\n        for (let dx = -4; dx <= 4; dx++) {\n          const dist: int = Math.max(Math.abs(dx), Math.abs(dy)); // Chebyshev/infinity norm\n          const xx: int = x + dx;\n          const yy: int = y + dy;\n          if (xx >= 0 && xx < this.size && yy >= 0 && yy < this.size)\n            this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n        }\n      }\n    }\n\n    // Draws a 5*5 alignment pattern, with the center module\n    // at (x, y). All modules must be in bounds.\n    private drawAlignmentPattern(x: int, y: int): void {\n      for (let dy = -2; dy <= 2; dy++) {\n        for (let dx = -2; dx <= 2; dx++)\n          this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n      }\n    }\n\n    // Sets the color of a module and marks it as a function module.\n    // Only used by the constructor. Coordinates must be in bounds.\n    private setFunctionModule(x: int, y: int, isDark: boolean): void {\n      this.modules[y][x] = isDark;\n      this.isFunction[y][x] = true;\n    }\n\n    /*-- Private helper methods for constructor: Codewords and masking --*/\n\n    // Returns a new byte string representing the given data with the appropriate error correction\n    // codewords appended to it, based on this object's version and error correction level.\n    private addEccAndInterleave(data: readonly byte[]): byte[] {\n      const ver: int = this.version;\n      const ecl: QrCode.Ecc = this.errorCorrectionLevel;\n      if (data.length != QrCode.getNumDataCodewords(ver, ecl)) throw new RangeError('Invalid argument');\n\n      // Calculate parameter numbers\n      const numBlocks: int = QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n      const blockEccLen: int = QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n      const rawCodewords: int = Math.floor(QrCode.getNumRawDataModules(ver) / 8);\n      const numShortBlocks: int = numBlocks - (rawCodewords % numBlocks);\n      const shortBlockLen: int = Math.floor(rawCodewords / numBlocks);\n\n      // Split data into blocks and append ECC to each block\n      const blocks: byte[][] = [];\n      const rsDiv: byte[] = QrCode.reedSolomonComputeDivisor(blockEccLen);\n      for (let i = 0, k = 0; i < numBlocks; i++) {\n        const dat: byte[] = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n        k += dat.length;\n        const ecc: byte[] = QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n        if (i < numShortBlocks) dat.push(0);\n        blocks.push(dat.concat(ecc));\n      }\n\n      // Interleave (not concatenate) the bytes from every block into a single sequence\n      const result: byte[] = [];\n      for (let i = 0; i < blocks[0].length; i++) {\n        blocks.forEach((block, j) => {\n          // Skip the padding byte in short blocks\n          if (i != shortBlockLen - blockEccLen || j >= numShortBlocks) result.push(block[i]);\n        });\n      }\n      assert(result.length == rawCodewords);\n      return result;\n    }\n\n    // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n    // data area of this QR Code. Function modules need to be marked off before this is called.\n    private drawCodewords(data: readonly byte[]): void {\n      if (data.length != Math.floor(QrCode.getNumRawDataModules(this.version) / 8))\n        throw new RangeError('Invalid argument');\n      let i: int = 0; // Bit index into the data\n      // Do the funny zigzag scan\n      for (let right = this.size - 1; right >= 1; right -= 2) {\n        // Index of right column in each column pair\n        if (right == 6) right = 5;\n        for (let vert = 0; vert < this.size; vert++) {\n          // Vertical counter\n          for (let j = 0; j < 2; j++) {\n            const x: int = right - j; // Actual x coordinate\n            const upward: boolean = ((right + 1) & 2) == 0;\n            const y: int = upward ? this.size - 1 - vert : vert; // Actual y coordinate\n            if (!this.isFunction[y][x] && i < data.length * 8) {\n              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n              i++;\n            }\n            // If this QR Code has any remainder bits (0 to 7), they were assigned as\n            // 0/false/light by the constructor and are left unchanged by this method\n          }\n        }\n      }\n      assert(i == data.length * 8);\n    }\n\n    // XORs the codeword modules in this QR Code with the given mask pattern.\n    // The function modules must be marked and the codeword bits must be drawn\n    // before masking. Due to the arithmetic of XOR, calling applyMask() with\n    // the same mask value a second time will undo the mask. A final well-formed\n    // QR Code needs exactly one (not zero, two, etc.) mask applied.\n    private applyMask(mask: int): void {\n      if (mask < 0 || mask > 7) throw new RangeError('Mask value out of range');\n      for (let y = 0; y < this.size; y++) {\n        for (let x = 0; x < this.size; x++) {\n          let invert: boolean;\n          switch (mask) {\n            case 0:\n              invert = (x + y) % 2 == 0;\n              break;\n            case 1:\n              invert = y % 2 == 0;\n              break;\n            case 2:\n              invert = x % 3 == 0;\n              break;\n            case 3:\n              invert = (x + y) % 3 == 0;\n              break;\n            case 4:\n              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n              break;\n            case 5:\n              invert = ((x * y) % 2) + ((x * y) % 3) == 0;\n              break;\n            case 6:\n              invert = (((x * y) % 2) + ((x * y) % 3)) % 2 == 0;\n              break;\n            case 7:\n              invert = (((x + y) % 2) + ((x * y) % 3)) % 2 == 0;\n              break;\n            default:\n              throw new Error('Unreachable');\n          }\n          if (!this.isFunction[y][x] && invert) this.modules[y][x] = !this.modules[y][x];\n        }\n      }\n    }\n\n    // Calculates and returns the penalty score based on state of this QR Code's current modules.\n    // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n    private getPenaltyScore(): int {\n      let result: int = 0;\n\n      // Adjacent modules in row having same color, and finder-like patterns\n      for (let y = 0; y < this.size; y++) {\n        let runColor = false;\n        let runX = 0;\n        const runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let x = 0; x < this.size; x++) {\n          if (this.modules[y][x] == runColor) {\n            runX++;\n            if (runX == 5) result += QrCode.PENALTY_N1;\n            else if (runX > 5) result++;\n          } else {\n            this.finderPenaltyAddHistory(runX, runHistory);\n            if (!runColor) result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runX = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * QrCode.PENALTY_N3;\n      }\n      // Adjacent modules in column having same color, and finder-like patterns\n      for (let x = 0; x < this.size; x++) {\n        let runColor = false;\n        let runY = 0;\n        const runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let y = 0; y < this.size; y++) {\n          if (this.modules[y][x] == runColor) {\n            runY++;\n            if (runY == 5) result += QrCode.PENALTY_N1;\n            else if (runY > 5) result++;\n          } else {\n            this.finderPenaltyAddHistory(runY, runHistory);\n            if (!runColor) result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runY = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * QrCode.PENALTY_N3;\n      }\n\n      // 2*2 blocks of modules having same color\n      for (let y = 0; y < this.size - 1; y++) {\n        for (let x = 0; x < this.size - 1; x++) {\n          const color: boolean = this.modules[y][x];\n          if (color == this.modules[y][x + 1] && color == this.modules[y + 1][x] && color == this.modules[y + 1][x + 1])\n            result += QrCode.PENALTY_N2;\n        }\n      }\n\n      // Balance of dark and light modules\n      let dark: int = 0;\n      for (const row of this.modules) dark = row.reduce((sum, color) => sum + (color ? 1 : 0), dark);\n      const total: int = this.size * this.size; // Note that size is odd, so dark/total != 1/2\n      // Compute the smallest integer k >= 0 such that (45-5k)% <= dark/total <= (55+5k)%\n      const k: int = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n      assert(k >= 0 && k <= 9);\n      result += k * QrCode.PENALTY_N4;\n      assert(result >= 0 && result <= 2568888); // Non-tight upper bound based on default values of PENALTY_N1, ..., N4\n      return result;\n    }\n\n    /*-- Private helper functions --*/\n\n    // Returns an ascending list of positions of alignment patterns for this version number.\n    // Each position is in the range [0,177), and are used on both the x and y axes.\n    // This could be implemented as lookup table of 40 variable-length lists of integers.\n    private getAlignmentPatternPositions(): int[] {\n      if (this.version == 1) return [];\n      else {\n        const numAlign: int = Math.floor(this.version / 7) + 2;\n        const step: int = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n        const result: int[] = [6];\n        for (let pos = this.size - 7; result.length < numAlign; pos -= step) result.splice(1, 0, pos);\n        return result;\n      }\n    }\n\n    // Returns the number of data bits that can be stored in a QR Code of the given version number, after\n    // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.\n    // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.\n    private static getNumRawDataModules(ver: int): int {\n      if (ver < QrCode.MIN_VERSION || ver > QrCode.MAX_VERSION) throw new RangeError('Version number out of range');\n      let result: int = (16 * ver + 128) * ver + 64;\n      if (ver >= 2) {\n        const numAlign: int = Math.floor(ver / 7) + 2;\n        result -= (25 * numAlign - 10) * numAlign - 55;\n        if (ver >= 7) result -= 36;\n      }\n      assert(result >= 208 && result <= 29648);\n      return result;\n    }\n\n    // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any\n    // QR Code of the given version number and error correction level, with remainder bits discarded.\n    // This stateless pure function could be implemented as a (40*4)-cell lookup table.\n    private static getNumDataCodewords(ver: int, ecl: QrCode.Ecc): int {\n      return (\n        Math.floor(QrCode.getNumRawDataModules(ver) / 8) -\n        QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver]\n      );\n    }\n\n    // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be\n    // implemented as a lookup table over all possible parameter values, instead of as an algorithm.\n    private static reedSolomonComputeDivisor(degree: int): byte[] {\n      if (degree < 1 || degree > 255) throw new RangeError('Degree out of range');\n      // Polynomial coefficients are stored from highest to lowest power, excluding the leading term which is always 1.\n      // For example the polynomial x^3 + 255x^2 + 8x + 93 is stored as the uint8 array [255, 8, 93].\n      const result: byte[] = [];\n      for (let i = 0; i < degree - 1; i++) result.push(0);\n      result.push(1); // Start off with the monomial x^0\n\n      // Compute the product polynomial (x - r^0) * (x - r^1) * (x - r^2) * ... * (x - r^{degree-1}),\n      // and drop the highest monomial term which is always 1x^degree.\n      // Note that r = 0x02, which is a generator element of this field GF(2^8/0x11D).\n      let root = 1;\n      for (let i = 0; i < degree; i++) {\n        // Multiply the current product by (x - r^i)\n        for (let j = 0; j < result.length; j++) {\n          result[j] = QrCode.reedSolomonMultiply(result[j], root);\n          if (j + 1 < result.length) result[j] ^= result[j + 1];\n        }\n        root = QrCode.reedSolomonMultiply(root, 0x02);\n      }\n      return result;\n    }\n\n    // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.\n    private static reedSolomonComputeRemainder(data: readonly byte[], divisor: readonly byte[]): byte[] {\n      const result: byte[] = divisor.map(_ => 0);\n      for (const b of data) {\n        // Polynomial division\n        const factor: byte = b ^ (result.shift() as byte);\n        result.push(0);\n        divisor.forEach((coef, i) => (result[i] ^= QrCode.reedSolomonMultiply(coef, factor)));\n      }\n      return result;\n    }\n\n    // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result\n    // are unsigned 8-bit integers. This could be implemented as a lookup table of 256*256 entries of uint8.\n    private static reedSolomonMultiply(x: byte, y: byte): byte {\n      if (x >>> 8 != 0 || y >>> 8 != 0) throw new RangeError('Byte out of range');\n      // Russian peasant multiplication\n      let z: int = 0;\n      for (let i = 7; i >= 0; i--) {\n        z = (z << 1) ^ ((z >>> 7) * 0x11d);\n        z ^= ((y >>> i) & 1) * x;\n      }\n      assert(z >>> 8 == 0);\n      return z as byte;\n    }\n\n    // Can only be called immediately after a light run is added, and\n    // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n    private finderPenaltyCountPatterns(runHistory: readonly int[]): int {\n      const n: int = runHistory[1];\n      assert(n <= this.size * 3);\n      const core: boolean =\n        n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n      return (\n        (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) +\n        (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0)\n      );\n    }\n\n    // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n    private finderPenaltyTerminateAndCount(currentRunColor: boolean, currentRunLength: int, runHistory: int[]): int {\n      if (currentRunColor) {\n        // Terminate dark run\n        this.finderPenaltyAddHistory(currentRunLength, runHistory);\n        currentRunLength = 0;\n      }\n      currentRunLength += this.size; // Add light border to final run\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      return this.finderPenaltyCountPatterns(runHistory);\n    }\n\n    // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n    private finderPenaltyAddHistory(currentRunLength: int, runHistory: int[]): void {\n      if (runHistory[0] == 0) currentRunLength += this.size; // Add light border to initial run\n      runHistory.pop();\n      runHistory.unshift(currentRunLength);\n    }\n\n    /*-- Constants and tables --*/\n\n    // The minimum version number supported in the QR Code Model 2 standard.\n    public static readonly MIN_VERSION: int = 1;\n    // The maximum version number supported in the QR Code Model 2 standard.\n    public static readonly MAX_VERSION: int = 40;\n\n    // For use in getPenaltyScore(), when evaluating which mask is best.\n    private static readonly PENALTY_N1: int = 3;\n    private static readonly PENALTY_N2: int = 3;\n    private static readonly PENALTY_N3: int = 40;\n    private static readonly PENALTY_N4: int = 10;\n\n    private static readonly ECC_CODEWORDS_PER_BLOCK: int[][] = [\n      // Version: (note that index 0 is for padding, and is set to an illegal value)\n      //0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n      [\n        -1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30,\n        30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30\n      ], // Low\n      [\n        -1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28,\n        28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28\n      ], // Medium\n      [\n        -1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30,\n        30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30\n      ], // Quartile\n      [\n        -1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30,\n        30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30\n      ] // High\n    ];\n\n    private static readonly NUM_ERROR_CORRECTION_BLOCKS: int[][] = [\n      // Version: (note that index 0 is for padding, and is set to an illegal value)\n      //0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n      [\n        -1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18,\n        19, 19, 20, 21, 22, 24, 25\n      ], // Low\n      [\n        -1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29,\n        31, 33, 35, 37, 38, 40, 43, 45, 47, 49\n      ], // Medium\n      [\n        -1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40,\n        43, 45, 48, 51, 53, 56, 59, 62, 65, 68\n      ], // Quartile\n      [\n        -1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45,\n        48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81\n      ] // High\n    ];\n  }\n\n  // Appends the given number of low-order bits of the given value\n  // to the given buffer. Requires 0 <= len <= 31 and 0 <= val < 2^len.\n  function appendBits(val: int, len: int, bb: bit[]): void {\n    if (len < 0 || len > 31 || val >>> len != 0) throw new RangeError('Value out of range');\n    for (\n      let i = len - 1;\n      i >= 0;\n      i-- // Append bit by bit\n    )\n      bb.push((val >>> i) & 1);\n  }\n\n  // Returns true iff the i'th bit of x is set to 1.\n  function getBit(x: int, i: int): boolean {\n    return ((x >>> i) & 1) != 0;\n  }\n\n  // Throws an exception if the given condition is false.\n  function assert(cond: boolean): void {\n    if (!cond) throw new Error('Assertion error');\n  }\n\n  /*---- Data segment class ----*/\n\n  /*\n   * A segment of character/binary/control data in a QR Code symbol.\n   * Instances of this class are immutable.\n   * The mid-level way to create a segment is to take the payload data\n   * and call a static factory function such as QrSegment.makeNumeric().\n   * The low-level way to create a segment is to custom-make the bit buffer\n   * and call the QrSegment() constructor with appropriate values.\n   * This segment class imposes no length restrictions, but QR Codes have restrictions.\n   * Even in the most favorable conditions, a QR Code can only hold 7089 characters of data.\n   * Any segment longer than this is meaningless for the purpose of generating QR Codes.\n   */\n  export class QrSegment {\n    /*-- Static factory functions (mid level) --*/\n\n    // Returns a segment representing the given binary data encoded in\n    // byte mode. All input byte arrays are acceptable. Any text string\n    // can be converted to UTF-8 bytes and encoded as a byte mode segment.\n    public static makeBytes(data: readonly byte[]): QrSegment {\n      const bb: bit[] = [];\n      for (const b of data) appendBits(b, 8, bb);\n      return new QrSegment(QrSegment.Mode.BYTE, data.length, bb);\n    }\n\n    // Returns a segment representing the given string of decimal digits encoded in numeric mode.\n    public static makeNumeric(digits: string): QrSegment {\n      if (!QrSegment.isNumeric(digits)) throw new RangeError('String contains non-numeric characters');\n      const bb: bit[] = [];\n      for (let i = 0; i < digits.length; ) {\n        // Consume up to 3 digits per iteration\n        const n: int = Math.min(digits.length - i, 3);\n        appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n        i += n;\n      }\n      return new QrSegment(QrSegment.Mode.NUMERIC, digits.length, bb);\n    }\n\n    // Returns a segment representing the given text string encoded in alphanumeric mode.\n    // The characters allowed are: 0 to 9, A to Z (uppercase only), space,\n    // dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    public static makeAlphanumeric(text: string): QrSegment {\n      if (!QrSegment.isAlphanumeric(text))\n        throw new RangeError('String contains unencodable characters in alphanumeric mode');\n      const bb: bit[] = [];\n      let i: int;\n      for (i = 0; i + 2 <= text.length; i += 2) {\n        // Process groups of 2\n        let temp: int = QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n        temp += QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n        appendBits(temp, 11, bb);\n      }\n      if (i < text.length)\n        // 1 character remaining\n        appendBits(QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n      return new QrSegment(QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n    }\n\n    // Returns a new mutable list of zero or more segments to represent the given Unicode text string.\n    // The result may use various segment modes and switch modes to optimize the length of the bit stream.\n    public static makeSegments(text: string): QrSegment[] {\n      // Select the most efficient segment encoding automatically\n      if (text == '') return [];\n      else if (QrSegment.isNumeric(text)) return [QrSegment.makeNumeric(text)];\n      else if (QrSegment.isAlphanumeric(text)) return [QrSegment.makeAlphanumeric(text)];\n      else return [QrSegment.makeBytes(QrSegment.toUtf8ByteArray(text))];\n    }\n\n    // Returns a segment representing an Extended Channel Interpretation\n    // (ECI) designator with the given assignment value.\n    public static makeEci(assignVal: int): QrSegment {\n      const bb: bit[] = [];\n      if (assignVal < 0) throw new RangeError('ECI assignment value out of range');\n      else if (assignVal < 1 << 7) appendBits(assignVal, 8, bb);\n      else if (assignVal < 1 << 14) {\n        appendBits(0b10, 2, bb);\n        appendBits(assignVal, 14, bb);\n      } else if (assignVal < 1000000) {\n        appendBits(0b110, 3, bb);\n        appendBits(assignVal, 21, bb);\n      } else throw new RangeError('ECI assignment value out of range');\n      return new QrSegment(QrSegment.Mode.ECI, 0, bb);\n    }\n\n    // Tests whether the given string can be encoded as a segment in numeric mode.\n    // A string is encodable iff each character is in the range 0 to 9.\n    public static isNumeric(text: string): boolean {\n      return QrSegment.NUMERIC_REGEX.test(text);\n    }\n\n    // Tests whether the given string can be encoded as a segment in alphanumeric mode.\n    // A string is encodable iff each character is in the following set: 0 to 9, A to Z\n    // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    public static isAlphanumeric(text: string): boolean {\n      return QrSegment.ALPHANUMERIC_REGEX.test(text);\n    }\n\n    /*-- Constructor (low level) and fields --*/\n\n    // Creates a new QR Code segment with the given attributes and data.\n    // The character count (numChars) must agree with the mode and the bit buffer length,\n    // but the constraint isn't checked. The given bit buffer is cloned and stored.\n    public constructor(\n      // The mode indicator of this segment.\n      public readonly mode: QrSegment.Mode,\n\n      // The length of this segment's unencoded data. Measured in characters for\n      // numeric/alphanumeric/kanji mode, bytes for byte mode, and 0 for ECI mode.\n      // Always zero or positive. Not the same as the data's bit length.\n      public readonly numChars: int,\n\n      // The data bits of this segment. Accessed through getData().\n      private readonly bitData: bit[]\n    ) {\n      if (numChars < 0) throw new RangeError('Invalid argument');\n      this.bitData = bitData.slice(); // Make defensive copy\n    }\n\n    /*-- Methods --*/\n\n    // Returns a new copy of the data bits of this segment.\n    public getData(): bit[] {\n      return this.bitData.slice(); // Make defensive copy\n    }\n\n    // (Package-private) Calculates and returns the number of bits needed to encode the given segments at\n    // the given version. The result is infinity if a segment has too many characters to fit its length field.\n    public static getTotalBits(segs: readonly QrSegment[], version: int): number {\n      let result = 0;\n      for (const seg of segs) {\n        const ccbits: int = seg.mode.numCharCountBits(version);\n        if (seg.numChars >= 1 << ccbits) return Infinity; // The segment's length doesn't fit the field's bit width\n        result += 4 + ccbits + seg.bitData.length;\n      }\n      return result;\n    }\n\n    // Returns a new array of bytes representing the given string encoded in UTF-8.\n    private static toUtf8ByteArray(str: string): byte[] {\n      str = encodeURI(str);\n      const result: byte[] = [];\n      for (let i = 0; i < str.length; i++) {\n        if (str.charAt(i) != '%') result.push(str.charCodeAt(i));\n        else {\n          result.push(parseInt(str.substring(i + 1, i + 3), 16));\n          i += 2;\n        }\n      }\n      return result;\n    }\n\n    /*-- Constants --*/\n\n    // Describes precisely all strings that are encodable in numeric mode.\n    private static readonly NUMERIC_REGEX: RegExp = /^[0-9]*$/;\n\n    // Describes precisely all strings that are encodable in alphanumeric mode.\n    private static readonly ALPHANUMERIC_REGEX: RegExp = /^[A-Z0-9 $%*+./:-]*$/;\n\n    // The set of all legal characters in alphanumeric mode,\n    // where each character value maps to the index in the string.\n    private static readonly ALPHANUMERIC_CHARSET: string = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:';\n  }\n}\n\n/*---- Public helper enumeration ----*/\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace qrcodegen.QrCode {\n  type int = number;\n\n  /*\n   * The error correction level in a QR Code symbol. Immutable.\n   */\n  export class Ecc {\n    /*-- Constants --*/\n\n    public static readonly LOW = new Ecc(0, 1); // The QR Code can tolerate about  7% erroneous codewords\n    public static readonly MEDIUM = new Ecc(1, 0); // The QR Code can tolerate about 15% erroneous codewords\n    public static readonly QUARTILE = new Ecc(2, 3); // The QR Code can tolerate about 25% erroneous codewords\n    public static readonly HIGH = new Ecc(3, 2); // The QR Code can tolerate about 30% erroneous codewords\n\n    /*-- Constructor and fields --*/\n\n    private constructor(\n      // In the range 0 to 3 (unsigned 2-bit integer).\n      public readonly ordinal: int,\n      // (Package-private) In the range 0 to 3 (unsigned 2-bit integer).\n      public readonly formatBits: int\n    ) {}\n  }\n}\n\n/*---- Public helper enumeration ----*/\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace qrcodegen.QrSegment {\n  type int = number;\n\n  /*\n   * Describes how a segment's data bits are interpreted. Immutable.\n   */\n  export class Mode {\n    /*-- Constants --*/\n\n    public static readonly NUMERIC = new Mode(0x1, [10, 12, 14]);\n    public static readonly ALPHANUMERIC = new Mode(0x2, [9, 11, 13]);\n    public static readonly BYTE = new Mode(0x4, [8, 16, 16]);\n    public static readonly KANJI = new Mode(0x8, [8, 10, 12]);\n    public static readonly ECI = new Mode(0x7, [0, 0, 0]);\n\n    /*-- Constructor and fields --*/\n\n    private constructor(\n      // The mode indicator bits, which is a uint4 value (range 0 to 15).\n      public readonly modeBits: int,\n      // Number of character count bits for three different version ranges.\n      private readonly numBitsCharCount: [int, int, int]\n    ) {}\n\n    /*-- Method --*/\n\n    // (Package-private) Returns the bit width of the character count field for a segment in\n    // this mode in a QR Code at the given version number. The result is in the range [0, 16].\n    public numCharCountBits(ver: int): int {\n      return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n    }\n  }\n}\n\n// Modification to export for actual use\nexport default qrcodegen;\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport qrcodegen from './qrcodegen';\n\nexport const ERROR_LEVEL_MAP: Record<'L' | 'M' | 'Q' | 'H', qrcodegen.QrCode.Ecc> = {\n  L: qrcodegen.QrCode.Ecc.LOW,\n  M: qrcodegen.QrCode.Ecc.MEDIUM,\n  Q: qrcodegen.QrCode.Ecc.QUARTILE,\n  H: qrcodegen.QrCode.Ecc.HIGH\n} as const;\n\nconst DEFAULT_SIZE = 160;\nconst DEFAULT_SCALE = 10;\nconst DEFAULT_PADDING = 10;\nconst DEFAULT_COLOR = '#000000';\nconst DEFAULT_BACKGROUND_COLOR = '#FFFFFF';\nconst DEFAULT_ICONSIZE = 40;\nconst DEFAULT_LEVEL: keyof typeof ERROR_LEVEL_MAP = 'M';\n\nexport const plotQRCodeData = (\n  value: string,\n  level: keyof typeof ERROR_LEVEL_MAP = DEFAULT_LEVEL\n): qrcodegen.QrCode | null => {\n  if (!value) {\n    return null;\n  }\n  return qrcodegen.QrCode.encodeText(value, ERROR_LEVEL_MAP[level]);\n};\n\nexport function drawCanvas(\n  canvas: HTMLCanvasElement,\n  value: qrcodegen.QrCode | null,\n  size = DEFAULT_SIZE,\n  scale = DEFAULT_SCALE,\n  padding: number | number[] = DEFAULT_PADDING,\n  color = DEFAULT_COLOR,\n  backgroundColor = DEFAULT_BACKGROUND_COLOR,\n  iconSize = DEFAULT_ICONSIZE,\n  icon?: string\n): void {\n  const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;\n  const formattedPadding = formatPadding(padding);\n  canvas.style.width = `${size}px`;\n  canvas.style.height = `${size}px`;\n  if (!value) {\n    ctx.fillStyle = 'rgba(0, 0, 0, 0)';\n    ctx.fillRect(0, 0, canvas.width, canvas.height);\n    return;\n  }\n  canvas.width = value.size * scale + formattedPadding[1] + formattedPadding[3];\n  canvas.height = value.size * scale + formattedPadding[0] + formattedPadding[2];\n  if (!icon) {\n    drawCanvasBackground(ctx, canvas.width, canvas.height, scale, backgroundColor);\n    drawCanvasColor(ctx, value, scale, formattedPadding, backgroundColor, color);\n  } else {\n    const iconImg = new Image();\n    iconImg.src = icon;\n    iconImg.crossOrigin = 'anonymous';\n    iconImg.width = iconSize * (canvas.width / size);\n    iconImg.height = iconSize * (canvas.width / size);\n\n    const onLoad = (): void => {\n      cleanup();\n      drawCanvasBackground(ctx, canvas.width, canvas.height, scale, backgroundColor);\n      drawCanvasColor(ctx, value!, scale, formattedPadding, backgroundColor, color);\n      const iconCoordinate = canvas.width / 2 - (iconSize * (canvas.width / size)) / 2;\n\n      ctx.fillRect(iconCoordinate, iconCoordinate, iconSize * (canvas.width / size), iconSize * (canvas.width / size));\n      ctx.drawImage(\n        iconImg,\n        iconCoordinate,\n        iconCoordinate,\n        iconSize * (canvas.width / size),\n        iconSize * (canvas.width / size)\n      );\n    };\n\n    const onError = (): void => {\n      cleanup();\n      drawCanvasBackground(ctx, canvas.width, canvas.height, scale, backgroundColor);\n      drawCanvasColor(ctx, value, scale, formattedPadding, backgroundColor, color);\n    };\n\n    const cleanup = (): void => {\n      iconImg.removeEventListener('load', onLoad);\n      iconImg.removeEventListener('error', onError);\n    };\n\n    iconImg.addEventListener('load', onLoad);\n    iconImg.addEventListener('error', onError);\n  }\n}\n\nexport function drawCanvasColor(\n  ctx: CanvasRenderingContext2D,\n  value: qrcodegen.QrCode,\n  scale: number,\n  padding: number[],\n  backgroundColor: string,\n  color: string\n): void {\n  for (let y = 0; y < value.size; y++) {\n    for (let x = 0; x < value.size; x++) {\n      ctx.fillStyle = value.getModule(x, y) ? color : backgroundColor;\n      ctx.fillRect(padding[3] + x * scale, padding[0] + y * scale, scale, scale);\n    }\n  }\n}\n\nexport function drawCanvasBackground(\n  ctx: CanvasRenderingContext2D,\n  width: number,\n  height: number,\n  scale: number,\n  backgroundColor: string\n): void {\n  ctx.fillStyle = backgroundColor;\n  ctx.fillRect(0, 0, width * scale, height * scale);\n}\n\nexport function formatPadding(padding: number | number[]): number[] {\n  if (Array.isArray(padding)) {\n    // Build an array of 4 elements and repeat values from padding as necessary to set the value of the array\n    return Array(4)\n      .fill(0)\n      .map((_, index) => padding[index % padding.length]);\n  } else {\n    return [padding, padding, padding, padding];\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { isPlatformBrowser } from '@angular/common';\nimport {\n  AfterViewInit,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  inject,\n  Input,\n  numberAttribute,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  PLATFORM_ID,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild\n} from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NzStringTemplateOutletDirective } from 'ng-zorro-antd/core/outlet';\nimport { NzI18nService, NzQRCodeI18nInterface } from 'ng-zorro-antd/i18n';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzSpinModule } from 'ng-zorro-antd/spin';\n\nimport { drawCanvas, ERROR_LEVEL_MAP, plotQRCodeData } from './qrcode';\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  selector: 'nz-qrcode',\n  exportAs: 'nzQRCode',\n  template: `\n    @if (!!nzStatusRender) {\n      <div class=\"ant-qrcode-mask\">\n        <ng-container *nzStringTemplateOutlet=\"nzStatusRender\">{{ nzStatusRender }}</ng-container>\n      </div>\n    } @else if (nzStatus !== 'active') {\n      <div class=\"ant-qrcode-mask\">\n        @switch (nzStatus) {\n          @case ('loading') {\n            <nz-spin />\n          }\n          @case ('expired') {\n            <div>\n              <p class=\"ant-qrcode-expired\">{{ locale.expired }}</p>\n              <button nz-button nzType=\"link\" (click)=\"reloadQRCode()\">\n                <nz-icon nzType=\"reload\" nzTheme=\"outline\" />\n                <span>{{ locale.refresh }}</span>\n              </button>\n            </div>\n          }\n          @case ('scanned') {\n            <div>\n              <p class=\"ant-qrcode-expired\">{{ locale.scanned }}</p>\n            </div>\n          }\n        }\n      </div>\n    }\n\n    @if (isBrowser) {\n      <canvas #canvas></canvas>\n    }\n  `,\n  host: {\n    class: 'ant-qrcode',\n    '[class.ant-qrcode-border]': `nzBordered`\n  },\n  imports: [NzSpinModule, NzButtonModule, NzIconModule, NzStringTemplateOutletDirective]\n})\nexport class NzQRCodeComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {\n  @ViewChild('canvas', { static: false }) canvas!: ElementRef<HTMLCanvasElement>;\n  @Input() nzValue: string = '';\n  @Input() nzPadding: number | number[] = 0;\n  @Input() nzColor: string = '#000000';\n  @Input() nzBgColor: string = '#FFFFFF';\n  @Input({ transform: numberAttribute }) nzSize: number = 160;\n  @Input() nzIcon: string = '';\n  @Input({ transform: numberAttribute }) nzIconSize: number = 40;\n  @Input({ transform: booleanAttribute }) nzBordered: boolean = true;\n  @Input() nzStatus: 'active' | 'expired' | 'loading' | 'scanned' = 'active';\n  @Input() nzLevel: keyof typeof ERROR_LEVEL_MAP = 'M';\n  @Input() nzStatusRender?: TemplateRef<void> | string | null = null;\n\n  @Output() readonly nzRefresh = new EventEmitter<string>();\n\n  locale!: NzQRCodeI18nInterface;\n  // https://github.com/angular/universal-starter/issues/538#issuecomment-365518693\n  // canvas is not supported by the SSR DOM\n  isBrowser = true;\n  private destroy$ = new Subject<void>();\n  private platformId = inject(PLATFORM_ID);\n\n  constructor(\n    private i18n: NzI18nService,\n    private el: ElementRef,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.isBrowser = isPlatformBrowser(this.platformId);\n    this.cdr.markForCheck();\n  }\n\n  ngOnInit(): void {\n    this.el.nativeElement.style.backgroundColor = this.nzBgColor;\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('QRCode');\n      this.cdr.markForCheck();\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzValue, nzIcon, nzLevel, nzSize, nzIconSize, nzColor, nzPadding, nzBgColor } = changes;\n    if ((nzValue || nzIcon || nzLevel || nzSize || nzIconSize || nzColor || nzPadding || nzBgColor) && this.canvas) {\n      this.drawCanvasQRCode();\n    }\n\n    if (nzBgColor) {\n      this.el.nativeElement.style.backgroundColor = this.nzBgColor;\n    }\n  }\n\n  ngAfterViewInit(): void {\n    this.drawCanvasQRCode();\n  }\n\n  reloadQRCode(): void {\n    this.drawCanvasQRCode();\n    this.nzRefresh.emit('refresh');\n  }\n\n  drawCanvasQRCode(): void {\n    if (this.canvas) {\n      drawCanvas(\n        this.canvas.nativeElement,\n        plotQRCodeData(this.nzValue, this.nzLevel),\n        this.nzSize,\n        10,\n        this.nzPadding,\n        this.nzColor,\n        this.nzBgColor,\n        this.nzIconSize,\n        this.nzIcon\n      );\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzQRCodeComponent } from './qrcode.component';\n\n@NgModule({\n  imports: [NzQRCodeComponent],\n  exports: [NzQRCodeComponent]\n})\nexport class NzQRCodeModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './qrcode.component';\nexport * from './qrcode.module';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["qrcodegen"], "mappings": ";;;;;;;;;;;;;;;AAAA;;;AAGG;AAEH;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AAEH,YAAY;AAEZ;AACA,IAAU,SAAS;AAAnB,CAAA,UAAU,SAAS,EAAA;;AAOjB;;;;;;;;;;;;;;;AAeG;AACH,IAAA,MAAa,MAAM,CAAA;AA4HC,QAAA,OAAA;AAGA,QAAA,oBAAA;;;;;;;AAvHX,QAAA,OAAO,UAAU,CAAC,IAAY,EAAE,GAAe,EAAA;YACpD,MAAM,IAAI,GAAgB,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC;YAChE,OAAO,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC;;;;;;AAOlC,QAAA,OAAO,YAAY,CAAC,IAAqB,EAAE,GAAe,EAAA;YAC/D,MAAM,GAAG,GAAc,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC;YAC1D,OAAO,MAAM,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;;;;;;;;;;;;AAcnC,QAAA,OAAO,cAAc,CAC1B,IAA0B,EAC1B,GAAe,EACf,UAAkB,GAAA,CAAC,EACnB,UAAA,GAAkB,EAAE,EACpB,IAAA,GAAY,CAAC,CAAC,EACd,WAAoB,IAAI,EAAA;AAExB,YAAA,IACE,EAAE,MAAM,CAAC,WAAW,IAAI,UAAU,IAAI,UAAU,IAAI,UAAU,IAAI,UAAU,IAAI,MAAM,CAAC,WAAW,CAAC;gBACnG,IAAI,GAAG,CAAC,CAAC;AACT,gBAAA,IAAI,GAAG,CAAC;AAER,gBAAA,MAAM,IAAI,UAAU,CAAC,eAAe,CAAC;;AAGvC,YAAA,IAAI,OAAY;AAChB,YAAA,IAAI,YAAiB;AACrB,YAAA,KAAK,OAAO,GAAG,UAAU,GAAI,OAAO,EAAE,EAAE;AACtC,gBAAA,MAAM,gBAAgB,GAAQ,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC3E,MAAM,QAAQ,GAAW,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC;AAC9D,gBAAA,IAAI,QAAQ,IAAI,gBAAgB,EAAE;oBAChC,YAAY,GAAG,QAAQ;AACvB,oBAAA,MAAM;;gBAER,IAAI,OAAO,IAAI,UAAU;;AAEvB,oBAAA,MAAM,IAAI,UAAU,CAAC,eAAe,CAAC;;;YAIzC,KAAK,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;;AAE9E,gBAAA,IAAI,QAAQ,IAAI,YAAY,IAAI,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC;oBAAE,GAAG,GAAG,MAAM;;;YAI/F,MAAM,EAAE,GAAU,EAAE;AACpB,YAAA,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;gBACtB,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;AACpC,gBAAA,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;AAChE,gBAAA,KAAK,MAAM,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE;AAAE,oBAAA,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;;AAE3C,YAAA,MAAM,CAAC,EAAE,CAAC,MAAM,IAAI,YAAY,CAAC;;AAGjC,YAAA,MAAM,gBAAgB,GAAQ,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC;AAC1E,YAAA,MAAM,CAAC,EAAE,CAAC,MAAM,IAAI,gBAAgB,CAAC;AACrC,YAAA,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,gBAAgB,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;AAC5D,YAAA,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YAC5C,MAAM,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;;AAG1B,YAAA,KAAK,IAAI,OAAO,GAAG,IAAI,EAAE,EAAE,CAAC,MAAM,GAAG,gBAAgB,EAAE,OAAO,IAAI,IAAI,GAAG,IAAI;AAAE,gBAAA,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC;;YAGzG,MAAM,aAAa,GAAW,EAAE;YAChC,OAAO,aAAa,CAAC,MAAM,GAAG,CAAC,GAAG,EAAE,CAAC,MAAM;AAAE,gBAAA,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;AAClE,YAAA,EAAE,CAAC,OAAO,CAAC,CAAC,CAAM,EAAE,CAAM,MAAM,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;YAG9E,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,CAAC;;;;;AAOtC,QAAA,IAAI;;;;AAKJ,QAAA,IAAI;;;QAIH,OAAO,GAAgB,EAAE;;QAGzB,UAAU,GAAgB,EAAE;;;;;;AAQ7C,QAAA,WAAA;;;QAGkB,OAAY;;QAGZ,oBAAgC,EAEhD,aAA8B,EAE9B,GAAQ,EAAA;YAPQ,IAAO,CAAA,OAAA,GAAP,OAAO;YAGP,IAAoB,CAAA,oBAAA,GAApB,oBAAoB;;YAOpC,IAAI,OAAO,GAAG,MAAM,CAAC,WAAW,IAAI,OAAO,GAAG,MAAM,CAAC,WAAW;AAC9D,gBAAA,MAAM,IAAI,UAAU,CAAC,4BAA4B,CAAC;AACpD,YAAA,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC;AAAE,gBAAA,MAAM,IAAI,UAAU,CAAC,yBAAyB,CAAC;YACxE,IAAI,CAAC,IAAI,GAAG,OAAO,GAAG,CAAC,GAAG,EAAE;;YAG5B,MAAM,GAAG,GAAc,EAAE;AACzB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE;AAAE,gBAAA,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;AACnD,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;AAClC,gBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC;gBAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;;;YAInC,IAAI,CAAC,oBAAoB,EAAE;YAC3B,MAAM,YAAY,GAAW,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC;AACpE,YAAA,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;;AAGhC,YAAA,IAAI,GAAG,IAAI,CAAC,CAAC,EAAE;;gBAEb,IAAI,UAAU,GAAQ,UAAU;AAChC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1B,oBAAA,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AACjB,oBAAA,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;AACtB,oBAAA,MAAM,OAAO,GAAQ,IAAI,CAAC,eAAe,EAAE;AAC3C,oBAAA,IAAI,OAAO,GAAG,UAAU,EAAE;wBACxB,GAAG,GAAG,CAAC;wBACP,UAAU,GAAG,OAAO;;AAEtB,oBAAA,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;;;YAGtB,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;AAC5B,YAAA,IAAI,CAAC,IAAI,GAAG,GAAG;AACf,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACpB,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AAEzB,YAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;;;;;QAQf,SAAS,CAAC,CAAM,EAAE,CAAM,EAAA;AAC7B,YAAA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;QAK1E,UAAU,GAAA;YACf,OAAO,IAAI,CAAC,OAAO;;;;QAMb,oBAAoB,GAAA;;AAE1B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;AAClC,gBAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACxC,gBAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;;AAI1C,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;YACxC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;;AAGxC,YAAA,MAAM,WAAW,GAAU,IAAI,CAAC,4BAA4B,EAAE;AAC9D,YAAA,MAAM,QAAQ,GAAQ,WAAW,CAAC,MAAM;AACxC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;AACjC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;;AAEjC,oBAAA,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACzF,wBAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;;;;AAK/D,YAAA,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YACvB,IAAI,CAAC,WAAW,EAAE;;;;AAKZ,QAAA,cAAc,CAAC,IAAS,EAAA;;AAE9B,YAAA,MAAM,IAAI,GAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,CAAC;YACrE,IAAI,GAAG,GAAQ,IAAI;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;AAAE,gBAAA,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC;AACrE,YAAA,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC;AAC3C,YAAA,MAAM,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC;;YAGxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAAE,gBAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC7C,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC7C,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;AAAE,gBAAA,IAAI,CAAC,iBAAiB,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;;YAG/E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;gBAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACzF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;gBAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC3F,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;;;;QAKzC,WAAW,GAAA;AACjB,YAAA,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC;gBAAE;;AAGtB,YAAA,IAAI,GAAG,GAAQ,IAAI,CAAC,OAAO,CAAC;YAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;AAAE,gBAAA,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,EAAE,IAAI,MAAM,CAAC;AACvE,YAAA,MAAM,IAAI,GAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,IAAI,GAAG,CAAC;AAC7C,YAAA,MAAM,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC;;AAGxB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;gBAC3B,MAAM,KAAK,GAAY,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;AACtC,gBAAA,MAAM,CAAC,GAAQ,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;gBACvC,MAAM,CAAC,GAAQ,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;gBAChC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;gBACnC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;;;;;QAM/B,iBAAiB,CAAC,CAAM,EAAE,CAAM,EAAA;AACtC,YAAA,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE;AAC/B,gBAAA,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE;oBAC/B,MAAM,IAAI,GAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AACvD,oBAAA,MAAM,EAAE,GAAQ,CAAC,GAAG,EAAE;AACtB,oBAAA,MAAM,EAAE,GAAQ,CAAC,GAAG,EAAE;AACtB,oBAAA,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI;AACxD,wBAAA,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;;;;;;QAOtD,oBAAoB,CAAC,CAAM,EAAE,CAAM,EAAA;AACzC,YAAA,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE;gBAC/B,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE;AAC7B,oBAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;;;;;AAM/E,QAAA,iBAAiB,CAAC,CAAM,EAAE,CAAM,EAAE,MAAe,EAAA;YACvD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM;YAC3B,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;;;;;AAOtB,QAAA,mBAAmB,CAAC,IAAqB,EAAA;AAC/C,YAAA,MAAM,GAAG,GAAQ,IAAI,CAAC,OAAO;AAC7B,YAAA,MAAM,GAAG,GAAe,IAAI,CAAC,oBAAoB;YACjD,IAAI,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,CAAC;AAAE,gBAAA,MAAM,IAAI,UAAU,CAAC,kBAAkB,CAAC;;AAGjG,YAAA,MAAM,SAAS,GAAQ,MAAM,CAAC,2BAA2B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC;AAC3E,YAAA,MAAM,WAAW,GAAQ,MAAM,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC;AACzE,YAAA,MAAM,YAAY,GAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC1E,MAAM,cAAc,GAAQ,SAAS,IAAI,YAAY,GAAG,SAAS,CAAC;YAClE,MAAM,aAAa,GAAQ,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,SAAS,CAAC;;YAG/D,MAAM,MAAM,GAAa,EAAE;YAC3B,MAAM,KAAK,GAAW,MAAM,CAAC,yBAAyB,CAAC,WAAW,CAAC;AACnE,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;AACzC,gBAAA,MAAM,GAAG,GAAW,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,aAAa,GAAG,WAAW,IAAI,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACjG,gBAAA,CAAC,IAAI,GAAG,CAAC,MAAM;gBACf,MAAM,GAAG,GAAW,MAAM,CAAC,2BAA2B,CAAC,GAAG,EAAE,KAAK,CAAC;gBAClE,IAAI,CAAC,GAAG,cAAc;AAAE,oBAAA,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;gBACnC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;;;YAI9B,MAAM,MAAM,GAAW,EAAE;AACzB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC,KAAI;;oBAE1B,IAAI,CAAC,IAAI,aAAa,GAAG,WAAW,IAAI,CAAC,IAAI,cAAc;wBAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACpF,iBAAC,CAAC;;AAEJ,YAAA,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,YAAY,CAAC;AACrC,YAAA,OAAO,MAAM;;;;AAKP,QAAA,aAAa,CAAC,IAAqB,EAAA;AACzC,YAAA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC1E,gBAAA,MAAM,IAAI,UAAU,CAAC,kBAAkB,CAAC;AAC1C,YAAA,IAAI,CAAC,GAAQ,CAAC,CAAC;;AAEf,YAAA,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE;;gBAEtD,IAAI,KAAK,IAAI,CAAC;oBAAE,KAAK,GAAG,CAAC;AACzB,gBAAA,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;;AAE3C,oBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1B,wBAAA,MAAM,CAAC,GAAQ,KAAK,GAAG,CAAC,CAAC;AACzB,wBAAA,MAAM,MAAM,GAAY,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9C,wBAAA,MAAM,CAAC,GAAQ,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;AACpD,wBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;4BACjD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACvD,4BAAA,CAAC,EAAE;;;;;;;YAOX,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;;;;;;;AAQtB,QAAA,SAAS,CAAC,IAAS,EAAA;AACzB,YAAA,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC;AAAE,gBAAA,MAAM,IAAI,UAAU,CAAC,yBAAyB,CAAC;AACzE,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;AAClC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;AAClC,oBAAA,IAAI,MAAe;oBACnB,QAAQ,IAAI;AACV,wBAAA,KAAK,CAAC;4BACJ,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;4BACzB;AACF,wBAAA,KAAK,CAAC;AACJ,4BAAA,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;4BACnB;AACF,wBAAA,KAAK,CAAC;AACJ,4BAAA,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;4BACnB;AACF,wBAAA,KAAK,CAAC;4BACJ,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;4BACzB;AACF,wBAAA,KAAK,CAAC;4BACJ,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;4BACzD;AACF,wBAAA,KAAK,CAAC;4BACJ,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;4BAC3C;AACF,wBAAA,KAAK,CAAC;4BACJ,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;4BACjD;AACF,wBAAA,KAAK,CAAC;4BACJ,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;4BACjD;AACF,wBAAA;AACE,4BAAA,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC;;oBAElC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM;AAAE,wBAAA,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;QAO5E,eAAe,GAAA;YACrB,IAAI,MAAM,GAAQ,CAAC;;AAGnB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;gBAClC,IAAI,QAAQ,GAAG,KAAK;gBACpB,IAAI,IAAI,GAAG,CAAC;AACZ,gBAAA,MAAM,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACxC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;AAClC,oBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;AAClC,wBAAA,IAAI,EAAE;wBACN,IAAI,IAAI,IAAI,CAAC;AAAE,4BAAA,MAAM,IAAI,MAAM,CAAC,UAAU;6BACrC,IAAI,IAAI,GAAG,CAAC;AAAE,4BAAA,MAAM,EAAE;;yBACtB;AACL,wBAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC;AAC9C,wBAAA,IAAI,CAAC,QAAQ;4BAAE,MAAM,IAAI,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,UAAU;wBACxF,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC7B,IAAI,GAAG,CAAC;;;AAGZ,gBAAA,MAAM,IAAI,IAAI,CAAC,8BAA8B,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC,GAAG,MAAM,CAAC,UAAU;;;AAG/F,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;gBAClC,IAAI,QAAQ,GAAG,KAAK;gBACpB,IAAI,IAAI,GAAG,CAAC;AACZ,gBAAA,MAAM,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACxC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;AAClC,oBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;AAClC,wBAAA,IAAI,EAAE;wBACN,IAAI,IAAI,IAAI,CAAC;AAAE,4BAAA,MAAM,IAAI,MAAM,CAAC,UAAU;6BACrC,IAAI,IAAI,GAAG,CAAC;AAAE,4BAAA,MAAM,EAAE;;yBACtB;AACL,wBAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC;AAC9C,wBAAA,IAAI,CAAC,QAAQ;4BAAE,MAAM,IAAI,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,UAAU;wBACxF,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC7B,IAAI,GAAG,CAAC;;;AAGZ,gBAAA,MAAM,IAAI,IAAI,CAAC,8BAA8B,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC,GAAG,MAAM,CAAC,UAAU;;;AAI/F,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACtC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBACtC,MAAM,KAAK,GAAY,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,oBAAA,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3G,wBAAA,MAAM,IAAI,MAAM,CAAC,UAAU;;;;YAKjC,IAAI,IAAI,GAAQ,CAAC;AACjB,YAAA,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,OAAO;AAAE,gBAAA,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YAC9F,MAAM,KAAK,GAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;;YAEzC,MAAM,CAAC,GAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;YACtE,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxB,YAAA,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,UAAU;YAC/B,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,OAAO,CAAC,CAAC;AACzC,YAAA,OAAO,MAAM;;;;;;QAQP,4BAA4B,GAAA;AAClC,YAAA,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC;AAAE,gBAAA,OAAO,EAAE;iBAC3B;AACH,gBAAA,MAAM,QAAQ,GAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC;AACtD,gBAAA,MAAM,IAAI,GAAQ,IAAI,CAAC,OAAO,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;AACtG,gBAAA,MAAM,MAAM,GAAU,CAAC,CAAC,CAAC;AACzB,gBAAA,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,QAAQ,EAAE,GAAG,IAAI,IAAI;oBAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AAC7F,gBAAA,OAAO,MAAM;;;;;;QAOT,OAAO,oBAAoB,CAAC,GAAQ,EAAA;YAC1C,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW;AAAE,gBAAA,MAAM,IAAI,UAAU,CAAC,6BAA6B,CAAC;AAC7G,YAAA,IAAI,MAAM,GAAQ,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,EAAE;AAC7C,YAAA,IAAI,GAAG,IAAI,CAAC,EAAE;AACZ,gBAAA,MAAM,QAAQ,GAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;AAC7C,gBAAA,MAAM,IAAI,CAAC,EAAE,GAAG,QAAQ,GAAG,EAAE,IAAI,QAAQ,GAAG,EAAE;gBAC9C,IAAI,GAAG,IAAI,CAAC;oBAAE,MAAM,IAAI,EAAE;;YAE5B,MAAM,CAAC,MAAM,IAAI,GAAG,IAAI,MAAM,IAAI,KAAK,CAAC;AACxC,YAAA,OAAO,MAAM;;;;;AAMP,QAAA,OAAO,mBAAmB,CAAC,GAAQ,EAAE,GAAe,EAAA;AAC1D,YAAA,QACE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAChD,MAAM,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,2BAA2B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC;;;;QAMnG,OAAO,yBAAyB,CAAC,MAAW,EAAA;AAClD,YAAA,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,GAAG;AAAE,gBAAA,MAAM,IAAI,UAAU,CAAC,qBAAqB,CAAC;;;YAG3E,MAAM,MAAM,GAAW,EAAE;AACzB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE;AAAE,gBAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACnD,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;;;YAKf,IAAI,IAAI,GAAG,CAAC;AACZ,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;;AAE/B,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,oBAAA,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;AACvD,oBAAA,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM;wBAAE,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;;gBAEvD,IAAI,GAAG,MAAM,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC;;AAE/C,YAAA,OAAO,MAAM;;;AAIP,QAAA,OAAO,2BAA2B,CAAC,IAAqB,EAAE,OAAwB,EAAA;AACxF,YAAA,MAAM,MAAM,GAAW,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAC1C,YAAA,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;;gBAEpB,MAAM,MAAM,GAAS,CAAC,GAAI,MAAM,CAAC,KAAK,EAAW;AACjD,gBAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;gBACd,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;;AAEvF,YAAA,OAAO,MAAM;;;;AAKP,QAAA,OAAO,mBAAmB,CAAC,CAAO,EAAE,CAAO,EAAA;YACjD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AAAE,gBAAA,MAAM,IAAI,UAAU,CAAC,mBAAmB,CAAC;;YAE3E,IAAI,CAAC,GAAQ,CAAC;AACd,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC3B,gBAAA,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC;AAClC,gBAAA,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;AAE1B,YAAA,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACpB,YAAA,OAAO,CAAS;;;;AAKV,QAAA,0BAA0B,CAAC,UAA0B,EAAA;AAC3D,YAAA,MAAM,CAAC,GAAQ,UAAU,CAAC,CAAC,CAAC;YAC5B,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AAC1B,YAAA,MAAM,IAAI,GACR,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;YACnG,QACE,CAAC,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;iBAC5D,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;;;AAK1D,QAAA,8BAA8B,CAAC,eAAwB,EAAE,gBAAqB,EAAE,UAAiB,EAAA;YACvG,IAAI,eAAe,EAAE;;AAEnB,gBAAA,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,UAAU,CAAC;gBAC1D,gBAAgB,GAAG,CAAC;;AAEtB,YAAA,gBAAgB,IAAI,IAAI,CAAC,IAAI,CAAC;AAC9B,YAAA,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,UAAU,CAAC;AAC1D,YAAA,OAAO,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC;;;QAI5C,uBAAuB,CAAC,gBAAqB,EAAE,UAAiB,EAAA;AACtE,YAAA,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;AAAE,gBAAA,gBAAgB,IAAI,IAAI,CAAC,IAAI,CAAC;YACtD,UAAU,CAAC,GAAG,EAAE;AAChB,YAAA,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC;;;;AAM/B,QAAA,OAAgB,WAAW,GAAQ,CAAC;;AAEpC,QAAA,OAAgB,WAAW,GAAQ,EAAE;;AAGpC,QAAA,OAAgB,UAAU,GAAQ,CAAC;AACnC,QAAA,OAAgB,UAAU,GAAQ,CAAC;AACnC,QAAA,OAAgB,UAAU,GAAQ,EAAE;AACpC,QAAA,OAAgB,UAAU,GAAQ,EAAE;QAEpC,OAAgB,uBAAuB,GAAY;;;AAGzD,YAAA;gBACE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAC7G,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACjD,aAAA;AACD,YAAA;gBACE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAC9G,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACjD,aAAA;AACD,YAAA;gBACE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAC9G,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACjD,aAAA;AACD,YAAA;gBACE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAC9G,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACjD,aAAA;SACF;QAEO,OAAgB,2BAA2B,GAAY;;;AAG7D,YAAA;AACE,gBAAA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAC/G,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACzB,aAAA;AACD,YAAA;AACE,gBAAA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAC5G,gBAAA,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACrC,aAAA;AACD,YAAA;AACE,gBAAA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAC/G,gBAAA,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACrC,aAAA;AACD,YAAA;gBACE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAC5G,gBAAA,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACzC,aAAA;SACF;;AAvoBU,IAAA,SAAA,CAAA,MAAM,SAwoBlB;;;AAID,IAAA,SAAS,UAAU,CAAC,GAAQ,EAAE,GAAQ,EAAE,EAAS,EAAA;AAC/C,QAAA,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;AAAE,YAAA,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC;AACvF,QAAA,KACE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EACf,CAAC,IAAI,CAAC,EACN,CAAC,EAAE;;YAEH,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;;;AAI5B,IAAA,SAAS,MAAM,CAAC,CAAM,EAAE,CAAM,EAAA;QAC5B,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;;;IAI7B,SAAS,MAAM,CAAC,IAAa,EAAA;AAC3B,QAAA,IAAI,CAAC,IAAI;AAAE,YAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC;;;AAK/C;;;;;;;;;;AAUG;AACH,IAAA,MAAa,SAAS,CAAA;AA2FF,QAAA,IAAA;AAKA,QAAA,QAAA;AAGC,QAAA,OAAA;;;;;QA7FZ,OAAO,SAAS,CAAC,IAAqB,EAAA;YAC3C,MAAM,EAAE,GAAU,EAAE;YACpB,KAAK,MAAM,CAAC,IAAI,IAAI;AAAE,gBAAA,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;AAC1C,YAAA,OAAO,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;;;QAIrD,OAAO,WAAW,CAAC,MAAc,EAAA;AACtC,YAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC;AAAE,gBAAA,MAAM,IAAI,UAAU,CAAC,wCAAwC,CAAC;YAChG,MAAM,EAAE,GAAU,EAAE;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAI;;AAEnC,gBAAA,MAAM,CAAC,GAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC7C,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;gBACnE,CAAC,IAAI,CAAC;;AAER,YAAA,OAAO,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC;;;;;QAM1D,OAAO,gBAAgB,CAAC,IAAY,EAAA;AACzC,YAAA,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC;AACjC,gBAAA,MAAM,IAAI,UAAU,CAAC,6DAA6D,CAAC;YACrF,MAAM,EAAE,GAAU,EAAE;AACpB,YAAA,IAAI,CAAM;AACV,YAAA,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;;AAExC,gBAAA,IAAI,IAAI,GAAQ,SAAS,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;AAC3E,gBAAA,IAAI,IAAI,SAAS,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAClE,gBAAA,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC;;AAE1B,YAAA,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM;;AAEjB,gBAAA,UAAU,CAAC,SAAS,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;AAC3E,YAAA,OAAO,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;;;;QAK7D,OAAO,YAAY,CAAC,IAAY,EAAA;;YAErC,IAAI,IAAI,IAAI,EAAE;AAAE,gBAAA,OAAO,EAAE;AACpB,iBAAA,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC;gBAAE,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACnE,iBAAA,IAAI,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC;gBAAE,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;;AAC7E,gBAAA,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;;;;QAK7D,OAAO,OAAO,CAAC,SAAc,EAAA;YAClC,MAAM,EAAE,GAAU,EAAE;YACpB,IAAI,SAAS,GAAG,CAAC;AAAE,gBAAA,MAAM,IAAI,UAAU,CAAC,mCAAmC,CAAC;AACvE,iBAAA,IAAI,SAAS,GAAG,CAAC,IAAI,CAAC;AAAE,gBAAA,UAAU,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;AACpD,iBAAA,IAAI,SAAS,GAAG,CAAC,IAAI,EAAE,EAAE;AAC5B,gBAAA,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;AACvB,gBAAA,UAAU,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,CAAC;;AACxB,iBAAA,IAAI,SAAS,GAAG,OAAO,EAAE;AAC9B,gBAAA,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;AACxB,gBAAA,UAAU,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,CAAC;;;AACxB,gBAAA,MAAM,IAAI,UAAU,CAAC,mCAAmC,CAAC;AAChE,YAAA,OAAO,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;;;;QAK1C,OAAO,SAAS,CAAC,IAAY,EAAA;YAClC,OAAO,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;QAMpC,OAAO,cAAc,CAAC,IAAY,EAAA;YACvC,OAAO,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;AAQhD,QAAA,WAAA;;QAEkB,IAAoB;;;;QAKpB,QAAa;;QAGZ,OAAc,EAAA;YARf,IAAI,CAAA,IAAA,GAAJ,IAAI;YAKJ,IAAQ,CAAA,QAAA,GAAR,QAAQ;YAGP,IAAO,CAAA,OAAA,GAAP,OAAO;YAExB,IAAI,QAAQ,GAAG,CAAC;AAAE,gBAAA,MAAM,IAAI,UAAU,CAAC,kBAAkB,CAAC;YAC1D,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;;;;QAM1B,OAAO,GAAA;YACZ,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;;;;AAKvB,QAAA,OAAO,YAAY,CAAC,IAA0B,EAAE,OAAY,EAAA;YACjE,IAAI,MAAM,GAAG,CAAC;AACd,YAAA,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;gBACtB,MAAM,MAAM,GAAQ,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;AACtD,gBAAA,IAAI,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,MAAM;oBAAE,OAAO,QAAQ,CAAC;gBACjD,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM;;AAE3C,YAAA,OAAO,MAAM;;;QAIP,OAAO,eAAe,CAAC,GAAW,EAAA;AACxC,YAAA,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC;YACpB,MAAM,MAAM,GAAW,EAAE;AACzB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnC,gBAAA,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG;oBAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;qBACnD;oBACH,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;oBACtD,CAAC,IAAI,CAAC;;;AAGV,YAAA,OAAO,MAAM;;;;AAMP,QAAA,OAAgB,aAAa,GAAW,UAAU;;AAGlD,QAAA,OAAgB,kBAAkB,GAAW,sBAAsB;;;AAInE,QAAA,OAAgB,oBAAoB,GAAW,+CAA+C;;AApJ3F,IAAA,SAAA,CAAA,SAAS,YAqJrB;AACH,CAAC,EA11BS,SAAS,KAAT,SAAS,GA01BlB,EAAA,CAAA,CAAA;AAED;AACA;AACA,CAAA,UAAU,SAAS,EAAA;AAAC,IAAA,IAAA,MAAM;AAAN,IAAA,CAAA,UAAA,MAAM,EAAA;AAGxB;;AAEG;AACH,QAAA,MAAa,GAAG,CAAA;AAYI,YAAA,OAAA;AAEA,YAAA,UAAA;;AAXX,YAAA,OAAgB,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpC,YAAA,OAAgB,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvC,YAAA,OAAgB,QAAQ,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC,YAAA,OAAgB,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;AAI5C,YAAA,WAAA;;YAEkB,OAAY;;YAEZ,UAAe,EAAA;gBAFf,IAAO,CAAA,OAAA,GAAP,OAAO;gBAEP,IAAU,CAAA,UAAA,GAAV,UAAU;;;AAdjB,QAAA,MAAA,CAAA,GAAG,MAgBf;AACH,KAAC,EAvBmB,MAAM,GAAN,SAAM,CAAA,MAAA,KAAN,gBAAM,GAuBzB,EAAA,CAAA,CAAA;AAAD,CAAC,EAvBS,SAAS,KAAT,SAAS,GAuBlB,EAAA,CAAA,CAAA;AAED;AACA;AACA,CAAA,UAAU,SAAS,EAAA;AAAC,IAAA,IAAA,SAAS;AAAT,IAAA,CAAA,UAAA,SAAS,EAAA;AAG3B;;AAEG;AACH,QAAA,MAAa,IAAI,CAAA;AAaG,YAAA,QAAA;AAEC,YAAA,gBAAA;;AAZZ,YAAA,OAAgB,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACrD,YAAA,OAAgB,YAAY,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACzD,YAAA,OAAgB,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACjD,YAAA,OAAgB,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAClD,YAAA,OAAgB,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;AAIrD,YAAA,WAAA;;YAEkB,QAAa;;YAEZ,gBAAiC,EAAA;gBAFlC,IAAQ,CAAA,QAAA,GAAR,QAAQ;gBAEP,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;;;;;AAO5B,YAAA,gBAAgB,CAAC,GAAQ,EAAA;AAC9B,gBAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;;;AAvB/C,QAAA,SAAA,CAAA,IAAI,OAyBhB;AACH,KAAC,EAhCmB,SAAS,GAAT,SAAS,CAAA,SAAA,KAAT,mBAAS,GAgC5B,EAAA,CAAA,CAAA;AAAD,CAAC,EAhCS,SAAS,KAAT,SAAS,GAgClB,EAAA,CAAA,CAAA;AAED;AACA,kBAAe,SAAS;;AC77BxB;;;AAGG;AAII,MAAM,eAAe,GAAwD;AAClF,IAAA,CAAC,EAAEA,WAAS,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG;AAC3B,IAAA,CAAC,EAAEA,WAAS,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM;AAC9B,IAAA,CAAC,EAAEA,WAAS,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ;AAChC,IAAA,CAAC,EAAEA,WAAS,CAAC,MAAM,CAAC,GAAG,CAAC;CAChB;AAEV,MAAM,YAAY,GAAG,GAAG;AACxB,MAAM,aAAa,GAAG,EAAE;AACxB,MAAM,eAAe,GAAG,EAAE;AAC1B,MAAM,aAAa,GAAG,SAAS;AAC/B,MAAM,wBAAwB,GAAG,SAAS;AAC1C,MAAM,gBAAgB,GAAG,EAAE;AAC3B,MAAM,aAAa,GAAiC,GAAG;AAEhD,MAAM,cAAc,GAAG,CAC5B,KAAa,EACb,KAAA,GAAsC,aAAa,KACxB;IAC3B,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,OAAO,IAAI;;AAEb,IAAA,OAAOA,WAAS,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC;AACnE,CAAC;AAEe,SAAA,UAAU,CACxB,MAAyB,EACzB,KAA8B,EAC9B,IAAI,GAAG,YAAY,EACnB,KAAK,GAAG,aAAa,EACrB,OAA6B,GAAA,eAAe,EAC5C,KAAK,GAAG,aAAa,EACrB,eAAe,GAAG,wBAAwB,EAC1C,QAAQ,GAAG,gBAAgB,EAC3B,IAAa,EAAA;IAEb,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAA6B;AAC/D,IAAA,MAAM,gBAAgB,GAAG,aAAa,CAAC,OAAO,CAAC;IAC/C,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,CAAG,EAAA,IAAI,IAAI;IAChC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAG,EAAA,IAAI,IAAI;IACjC,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,GAAG,CAAC,SAAS,GAAG,kBAAkB;AAClC,QAAA,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC;QAC/C;;AAEF,IAAA,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,gBAAgB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC;AAC7E,IAAA,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,gBAAgB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC;IAC9E,IAAI,CAAC,IAAI,EAAE;AACT,QAAA,oBAAoB,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,eAAe,CAAC;AAC9E,QAAA,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,gBAAgB,EAAE,eAAe,EAAE,KAAK,CAAC;;SACvE;AACL,QAAA,MAAM,OAAO,GAAG,IAAI,KAAK,EAAE;AAC3B,QAAA,OAAO,CAAC,GAAG,GAAG,IAAI;AAClB,QAAA,OAAO,CAAC,WAAW,GAAG,WAAW;AACjC,QAAA,OAAO,CAAC,KAAK,GAAG,QAAQ,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;AAChD,QAAA,OAAO,CAAC,MAAM,GAAG,QAAQ,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;QAEjD,MAAM,MAAM,GAAG,MAAW;AACxB,YAAA,OAAO,EAAE;AACT,YAAA,oBAAoB,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,eAAe,CAAC;AAC9E,YAAA,eAAe,CAAC,GAAG,EAAE,KAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,eAAe,EAAE,KAAK,CAAC;YAC7E,MAAM,cAAc,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,QAAQ,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;YAEhF,GAAG,CAAC,QAAQ,CAAC,cAAc,EAAE,cAAc,EAAE,QAAQ,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,QAAQ,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AAChH,YAAA,GAAG,CAAC,SAAS,CACX,OAAO,EACP,cAAc,EACd,cAAc,EACd,QAAQ,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,EAChC,QAAQ,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,CACjC;AACH,SAAC;QAED,MAAM,OAAO,GAAG,MAAW;AACzB,YAAA,OAAO,EAAE;AACT,YAAA,oBAAoB,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,eAAe,CAAC;AAC9E,YAAA,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,gBAAgB,EAAE,eAAe,EAAE,KAAK,CAAC;AAC9E,SAAC;QAED,MAAM,OAAO,GAAG,MAAW;AACzB,YAAA,OAAO,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC;AAC3C,YAAA,OAAO,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC;AAC/C,SAAC;AAED,QAAA,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC;AACxC,QAAA,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC;;AAE9C;AAEgB,SAAA,eAAe,CAC7B,GAA6B,EAC7B,KAAuB,EACvB,KAAa,EACb,OAAiB,EACjB,eAAuB,EACvB,KAAa,EAAA;AAEb,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;AACnC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;AACnC,YAAA,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,eAAe;YAC/D,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;;;AAGhF;AAEM,SAAU,oBAAoB,CAClC,GAA6B,EAC7B,KAAa,EACb,MAAc,EACd,KAAa,EACb,eAAuB,EAAA;AAEvB,IAAA,GAAG,CAAC,SAAS,GAAG,eAAe;AAC/B,IAAA,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,MAAM,GAAG,KAAK,CAAC;AACnD;AAEM,SAAU,aAAa,CAAC,OAA0B,EAAA;AACtD,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;;QAE1B,OAAO,KAAK,CAAC,CAAC;aACX,IAAI,CAAC,CAAC;AACN,aAAA,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;;SAChD;QACL,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;;AAE/C;;ACpIA;;;AAGG;MA6EU,iBAAiB,CAAA;AAwBlB,IAAA,IAAA;AACA,IAAA,EAAA;AACA,IAAA,GAAA;AAzB8B,IAAA,MAAM;IACrC,OAAO,GAAW,EAAE;IACpB,SAAS,GAAsB,CAAC;IAChC,OAAO,GAAW,SAAS;IAC3B,SAAS,GAAW,SAAS;IACC,MAAM,GAAW,GAAG;IAClD,MAAM,GAAW,EAAE;IACW,UAAU,GAAW,EAAE;IACtB,UAAU,GAAY,IAAI;IACzD,QAAQ,GAAiD,QAAQ;IACjE,OAAO,GAAiC,GAAG;IAC3C,cAAc,GAAuC,IAAI;AAE/C,IAAA,SAAS,GAAG,IAAI,YAAY,EAAU;AAEzD,IAAA,MAAM;;;IAGN,SAAS,GAAG,IAAI;AACR,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;AAC9B,IAAA,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;AAExC,IAAA,WAAA,CACU,IAAmB,EACnB,EAAc,EACd,GAAsB,EAAA;QAFtB,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAE,CAAA,EAAA,GAAF,EAAE;QACF,IAAG,CAAA,GAAA,GAAH,GAAG;QAEX,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC;AACnD,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;IAGzB,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS;AAC5D,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YACnE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;AAC/C,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,SAAC,CAAC;;AAGJ,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,OAAO;QAC/F,IAAI,CAAC,OAAO,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,IAAI,UAAU,IAAI,OAAO,IAAI,SAAS,IAAI,SAAS,KAAK,IAAI,CAAC,MAAM,EAAE;YAC9G,IAAI,CAAC,gBAAgB,EAAE;;QAGzB,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS;;;IAIhE,eAAe,GAAA;QACb,IAAI,CAAC,gBAAgB,EAAE;;IAGzB,YAAY,GAAA;QACV,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;;IAGhC,gBAAgB,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,UAAU,CACR,IAAI,CAAC,MAAM,CAAC,aAAa,EACzB,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,EAC1C,IAAI,CAAC,MAAM,EACX,EAAE,EACF,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,MAAM,CACZ;;;IAIL,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;uGA9Ef,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,4KAMR,eAAe,CAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAEf,eAAe,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EACf,gBAAgB,CAhD1B,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,yBAAA,EAAA,YAAA,EAAA,EAAA,cAAA,EAAA,YAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAKS,YAAY,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,CAAA,aAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,cAAc,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,QAAA,EAAA,iCAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,QAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,2BAAA,EAAA,QAAA,EAAA,8IAAA,EAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,0NAAE,+BAA+B,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAE1E,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBA3C7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,YAAY;AACnB,wBAAA,2BAA2B,EAAE,CAAY,UAAA;AAC1C,qBAAA;oBACD,OAAO,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,+BAA+B;AACtF,iBAAA;2IAEyC,MAAM,EAAA,CAAA;sBAA7C,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,QAAQ,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBAC7B,OAAO,EAAA,CAAA;sBAAf;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACsC,MAAM,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBAC5B,MAAM,EAAA,CAAA;sBAAd;gBACsC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACG,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBAEkB,SAAS,EAAA,CAAA;sBAA3B;;;AC9FH;;;AAGG;MAUU,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAd,cAAc,EAAA,OAAA,EAAA,CAHf,iBAAiB,CAAA,EAAA,OAAA,EAAA,CACjB,iBAAiB,CAAA,EAAA,CAAA;AAEhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAHf,iBAAiB,CAAA,EAAA,CAAA;;2FAGhB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,iBAAiB,CAAC;oBAC5B,OAAO,EAAE,CAAC,iBAAiB;AAC5B,iBAAA;;;ACZD;;;AAGG;;ACHH;;AAEG;;;;"}