/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */
import { Direction, Directionality } from '@angular/cdk/bidi';
import { ConnectionPositionPair } from '@angular/cdk/overlay';
import { Platform } from '@angular/cdk/platform';
import { AfterViewInit, ChangeDetectorRef, ElementRef, EventEmitter, OnChanges, OnInit, Renderer2, SimpleChanges, TemplateRef } from '@angular/core';
import { ControlValueAccessor } from '@angular/forms';
import { Observable } from 'rxjs';
import { NzConfigKey, NzConfigService } from 'ng-zorro-antd/core/config';
import { NgClassInterface, NzSafeAny, NzSizeLDSType, NzStatus, NzValidateStatus } from 'ng-zorro-antd/core/types';
import { DateHelperService, NzI18nService } from 'ng-zorro-antd/i18n';
import * as i0 from "@angular/core";
import * as i1 from "ng-zorro-antd/space";
export declare class NzTimePickerComponent implements ControlValueAccessor, OnInit, AfterViewInit, OnChanges {
    nzConfigService: NzConfigService;
    protected i18n: NzI18nService;
    private element;
    private renderer;
    private cdr;
    private dateHelper;
    private platform;
    private directionality;
    readonly _nzModuleName: NzConfigKey;
    private _onChange?;
    private _onTouched?;
    private destroy$;
    private isNzDisableFirstChange;
    isInit: boolean;
    focused: boolean;
    inputValue: string;
    value: Date | null;
    preValue: Date | null;
    inputSize?: number;
    i18nPlaceHolder$: Observable<string | undefined>;
    overlayPositions: ConnectionPositionPair[];
    dir: Direction;
    prefixCls: string;
    statusCls: NgClassInterface;
    status: NzValidateStatus;
    hasFeedback: boolean;
    get origin(): ElementRef;
    inputRef: ElementRef<HTMLInputElement>;
    nzId: string | null;
    nzSize: NzSizeLDSType;
    nzStatus: NzStatus;
    nzHourStep: number;
    nzMinuteStep: number;
    nzSecondStep: number;
    nzClearText: string;
    nzNowText: string;
    nzOkText: string;
    nzPopupClassName: string;
    nzPlaceHolder: string;
    nzAddOn?: TemplateRef<void>;
    nzDefaultOpenValue?: Date;
    nzDisabledHours?: () => number[];
    nzDisabledMinutes?: (hour: number) => number[];
    nzDisabledSeconds?: (hour: number, minute: number) => number[];
    nzFormat: string;
    nzOpen: boolean;
    nzUse12Hours: boolean;
    nzSuffixIcon: string | TemplateRef<NzSafeAny>;
    readonly nzOpenChange: EventEmitter<boolean>;
    nzHideDisabledOptions: boolean;
    nzAllowEmpty: boolean;
    nzDisabled: boolean;
    nzAutoFocus: boolean;
    nzBackdrop: boolean;
    nzBorderless: boolean;
    nzInputReadOnly: boolean;
    emitValue(value: Date | null): void;
    setValue(value: Date | null, syncPreValue?: boolean): void;
    open(): void;
    close(): void;
    updateAutoFocus(): void;
    onClickClearBtn(event: MouseEvent): void;
    onClickOutside(event: MouseEvent): void;
    onFocus(value: boolean): void;
    focus(): void;
    blur(): void;
    onKeyupEsc(): void;
    onKeyupEnter(): void;
    onInputChange(str: string): void;
    onPanelValueChange(value: Date): void;
    closePanel(): void;
    setCurrentValueAndClose(): void;
    protected finalSize: import("@angular/core").Signal<NzSizeLDSType>;
    private size;
    private compactSize;
    private nzFormStatusService;
    private nzFormNoStatusService;
    constructor(nzConfigService: NzConfigService, i18n: NzI18nService, element: ElementRef, renderer: Renderer2, cdr: ChangeDetectorRef, dateHelper: DateHelperService, platform: Platform, directionality: Directionality);
    ngOnInit(): void;
    ngOnChanges({ nzUse12Hours, nzFormat, nzDisabled, nzAutoFocus, nzStatus, nzSize }: SimpleChanges): void;
    parseTimeString(str: string): void;
    ngAfterViewInit(): void;
    writeValue(time: Date | null | undefined): void;
    registerOnChange(fn: (time: Date | null) => void): void;
    registerOnTouched(fn: () => void): void;
    setDisabledState(isDisabled: boolean): void;
    private checkTimeValid;
    private setStatusStyles;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTimePickerComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzTimePickerComponent, "nz-time-picker", ["nzTimePicker"], { "nzId": { "alias": "nzId"; "required": false; }; "nzSize": { "alias": "nzSize"; "required": false; }; "nzStatus": { "alias": "nzStatus"; "required": false; }; "nzHourStep": { "alias": "nzHourStep"; "required": false; }; "nzMinuteStep": { "alias": "nzMinuteStep"; "required": false; }; "nzSecondStep": { "alias": "nzSecondStep"; "required": false; }; "nzClearText": { "alias": "nzClearText"; "required": false; }; "nzNowText": { "alias": "nzNowText"; "required": false; }; "nzOkText": { "alias": "nzOkText"; "required": false; }; "nzPopupClassName": { "alias": "nzPopupClassName"; "required": false; }; "nzPlaceHolder": { "alias": "nzPlaceHolder"; "required": false; }; "nzAddOn": { "alias": "nzAddOn"; "required": false; }; "nzDefaultOpenValue": { "alias": "nzDefaultOpenValue"; "required": false; }; "nzDisabledHours": { "alias": "nzDisabledHours"; "required": false; }; "nzDisabledMinutes": { "alias": "nzDisabledMinutes"; "required": false; }; "nzDisabledSeconds": { "alias": "nzDisabledSeconds"; "required": false; }; "nzFormat": { "alias": "nzFormat"; "required": false; }; "nzOpen": { "alias": "nzOpen"; "required": false; }; "nzUse12Hours": { "alias": "nzUse12Hours"; "required": false; }; "nzSuffixIcon": { "alias": "nzSuffixIcon"; "required": false; }; "nzHideDisabledOptions": { "alias": "nzHideDisabledOptions"; "required": false; }; "nzAllowEmpty": { "alias": "nzAllowEmpty"; "required": false; }; "nzDisabled": { "alias": "nzDisabled"; "required": false; }; "nzAutoFocus": { "alias": "nzAutoFocus"; "required": false; }; "nzBackdrop": { "alias": "nzBackdrop"; "required": false; }; "nzBorderless": { "alias": "nzBorderless"; "required": false; }; "nzInputReadOnly": { "alias": "nzInputReadOnly"; "required": false; }; }, { "nzOpenChange": "nzOpenChange"; }, never, never, true, [{ directive: typeof i1.NzSpaceCompactItemDirective; inputs: {}; outputs: {}; }]>;
    static ngAcceptInputType_nzUse12Hours: unknown;
    static ngAcceptInputType_nzHideDisabledOptions: unknown;
    static ngAcceptInputType_nzAllowEmpty: unknown;
    static ngAcceptInputType_nzDisabled: unknown;
    static ngAcceptInputType_nzAutoFocus: unknown;
    static ngAcceptInputType_nzBorderless: unknown;
    static ngAcceptInputType_nzInputReadOnly: unknown;
}
