import { ChangeDetector<PERSON>ef, ElementRef, EventEmitter, NgZone, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { Subject } from 'rxjs';
import { NzConfigKey, NzConfigService } from 'ng-zorro-antd/core/config';
import { NzDestroyService } from 'ng-zorro-antd/core/services';
import { NzTableFilterFn, NzTableFilterList, NzTableFilterValue, NzTableSortFn, NzTableSortOrder } from '../table.types';
import * as i0 from "@angular/core";
export declare class NzThAddOnComponent<T> implements OnChanges, OnInit {
    nzConfigService: NzConfigService;
    private host;
    private cdr;
    private ngZone;
    private destroy$;
    readonly _nzModuleName: NzConfigKey;
    manualClickOrder$: Subject<NzThAddOnComponent<T>>;
    calcOperatorChange$: Subject<void>;
    nzFilterValue: NzTableFilterValue;
    sortOrder: NzTableSortOrder;
    sortDirections: NzTableSortOrder[];
    private sortOrderChange$;
    private isNzShowSortChanged;
    private isNzShowFilterChanged;
    nzColumnKey?: string;
    nzFilterMultiple: boolean;
    nzSortOrder: NzTableSortOrder;
    nzSortPriority: number | boolean;
    nzSortDirections: NzTableSortOrder[];
    nzFilters: NzTableFilterList;
    nzSortFn: NzTableSortFn<T> | boolean | null;
    nzFilterFn: NzTableFilterFn<T> | boolean | null;
    nzShowSort: boolean;
    nzShowFilter: boolean;
    nzCustomFilter: boolean;
    readonly nzCheckedChange: EventEmitter<boolean>;
    readonly nzSortOrderChange: EventEmitter<string | null>;
    readonly nzFilterChange: EventEmitter<any>;
    getNextSortDirection(sortDirections: NzTableSortOrder[], current: NzTableSortOrder): NzTableSortOrder;
    setSortOrder(order: NzTableSortOrder): void;
    clearSortOrder(): void;
    onFilterValueChange(value: NzTableFilterValue): void;
    updateCalcOperator(): void;
    constructor(nzConfigService: NzConfigService, host: ElementRef<HTMLElement>, cdr: ChangeDetectorRef, ngZone: NgZone, destroy$: NzDestroyService);
    ngOnInit(): void;
    ngOnChanges(changes: SimpleChanges): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzThAddOnComponent<any>, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzThAddOnComponent<any>, "th[nzColumnKey], th[nzSortFn], th[nzSortOrder], th[nzFilters], th[nzShowSort], th[nzShowFilter], th[nzCustomFilter]", never, { "nzColumnKey": { "alias": "nzColumnKey"; "required": false; }; "nzFilterMultiple": { "alias": "nzFilterMultiple"; "required": false; }; "nzSortOrder": { "alias": "nzSortOrder"; "required": false; }; "nzSortPriority": { "alias": "nzSortPriority"; "required": false; }; "nzSortDirections": { "alias": "nzSortDirections"; "required": false; }; "nzFilters": { "alias": "nzFilters"; "required": false; }; "nzSortFn": { "alias": "nzSortFn"; "required": false; }; "nzFilterFn": { "alias": "nzFilterFn"; "required": false; }; "nzShowSort": { "alias": "nzShowSort"; "required": false; }; "nzShowFilter": { "alias": "nzShowFilter"; "required": false; }; "nzCustomFilter": { "alias": "nzCustomFilter"; "required": false; }; }, { "nzCheckedChange": "nzCheckedChange"; "nzSortOrderChange": "nzSortOrderChange"; "nzFilterChange": "nzFilterChange"; }, never, ["[nz-th-extra]", "nz-filter-trigger", "*"], true, never>;
    static ngAcceptInputType_nzShowSort: unknown;
    static ngAcceptInputType_nzShowFilter: unknown;
    static ngAcceptInputType_nzCustomFilter: unknown;
}
