{"version": 3, "file": "ng-zorro-antd-select.mjs", "sources": ["../../components/select/option-group.component.ts", "../../components/select/option-item-group.component.ts", "../../components/select/option-item.component.ts", "../../components/select/option-container.component.ts", "../../components/select/option.component.ts", "../../components/select/select-arrow.component.ts", "../../components/select/select-clear.component.ts", "../../components/select/select-item.component.ts", "../../components/select/select-placeholder.component.ts", "../../components/select/select-search.component.ts", "../../components/select/select-top-control.component.ts", "../../components/select/select.component.ts", "../../components/select/select.module.ts", "../../components/select/select.types.ts", "../../components/select/public-api.ts", "../../components/select/ng-zorro-antd-select.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { ChangeDetectionStrategy, Component, Input, OnChanges, TemplateRef, ViewEncapsulation } from '@angular/core';\nimport { Subject } from 'rxjs';\n\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\n@Component({\n  selector: 'nz-option-group',\n  exportAs: 'nzOptionGroup',\n  template: `<ng-content></ng-content>`,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class NzOptionGroupComponent implements OnChanges {\n  @Input() nzLabel: string | number | TemplateRef<NzSafeAny> | null = null;\n  changes = new Subject<void>();\n  ngOnChanges(): void {\n    this.changes.next();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { ChangeDetectionStrategy, Component, Input, TemplateRef, ViewEncapsulation } from '@angular/core';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\n@Component({\n  selector: 'nz-option-item-group',\n  template: ` <ng-container *nzStringTemplateOutlet=\"nzLabel\">{{ nzLabel }}</ng-container> `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  host: {\n    class: 'ant-select-item ant-select-item-group'\n  },\n  imports: [NzOutletModule]\n})\nexport class NzOptionItemGroupComponent {\n  @Input() nzLabel: string | number | TemplateRef<NzSafeAny> | null = null;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnChanges,\n  OnInit,\n  Output,\n  SimpleChanges,\n  TemplateRef,\n  ViewEncapsulation\n} from '@angular/core';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\n@Component({\n  selector: 'nz-option-item',\n  template: `\n    <div class=\"ant-select-item-option-content\">\n      @if (customContent) {\n        <ng-template [ngTemplateOutlet]=\"template\"></ng-template>\n      } @else {\n        {{ label }}\n      }\n    </div>\n    @if (showState && selected) {\n      <div class=\"ant-select-item-option-state\" unselectable=\"on\">\n        @if (!icon) {\n          <nz-icon nzType=\"check\" class=\"ant-select-selected-icon\" />\n        } @else {\n          <ng-template [ngTemplateOutlet]=\"icon\"></ng-template>\n        }\n      </div>\n    }\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  host: {\n    class: 'ant-select-item ant-select-item-option',\n    '[attr.title]': 'title',\n    '[class.ant-select-item-option-grouped]': 'grouped',\n    '[class.ant-select-item-option-selected]': 'selected && !disabled',\n    '[class.ant-select-item-option-disabled]': 'disabled',\n    '[class.ant-select-item-option-active]': 'activated && !disabled'\n  },\n  providers: [NzDestroyService],\n  imports: [NgTemplateOutlet, NzIconModule]\n})\nexport class NzOptionItemComponent implements OnChanges, OnInit {\n  selected = false;\n  activated = false;\n  @Input() grouped = false;\n  @Input({ transform: booleanAttribute }) customContent = false;\n  @Input() template: TemplateRef<NzSafeAny> | null = null;\n  @Input() disabled = false;\n  @Input() showState = false;\n  @Input() title?: string | number | null;\n  @Input() label: string | number | null = null;\n  @Input() value: NzSafeAny | null = null;\n  @Input() activatedValue: NzSafeAny | null = null;\n  @Input() listOfSelectedValue: NzSafeAny[] = [];\n  @Input() icon: TemplateRef<NzSafeAny> | null = null;\n  @Input() compareWith!: (o1: NzSafeAny, o2: NzSafeAny) => boolean;\n  @Output() readonly itemClick = new EventEmitter<NzSafeAny>();\n  @Output() readonly itemHover = new EventEmitter<NzSafeAny>();\n\n  constructor(\n    private elementRef: ElementRef<HTMLElement>,\n    private ngZone: NgZone,\n    private destroy$: NzDestroyService\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { value, activatedValue, listOfSelectedValue } = changes;\n    if (value || listOfSelectedValue) {\n      this.selected = this.listOfSelectedValue.some(v => this.compareWith(v, this.value));\n    }\n    if (value || activatedValue) {\n      this.activated = this.compareWith(this.activatedValue, this.value);\n    }\n  }\n\n  ngOnInit(): void {\n    fromEventOutsideAngular(this.elementRef.nativeElement, 'click')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        if (!this.disabled) {\n          this.ngZone.run(() => this.itemClick.emit(this.value));\n        }\n      });\n\n    fromEventOutsideAngular(this.elementRef.nativeElement, 'mouseenter')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        if (!this.disabled) {\n          this.ngZone.run(() => this.itemHover.emit(this.value));\n        }\n      });\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { OverlayModule } from '@angular/cdk/overlay';\nimport { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport { isPlatformBrowser, NgTemplateOutlet } from '@angular/common';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  Component,\n  EventEmitter,\n  inject,\n  Input,\n  NgZone,\n  OnChanges,\n  Output,\n  PLATFORM_ID,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation\n} from '@angular/core';\n\nimport { NzOverlayModule } from 'ng-zorro-antd/core/overlay';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\nimport { NzEmptyModule } from 'ng-zorro-antd/empty';\n\nimport { NzOptionItemGroupComponent } from './option-item-group.component';\nimport { NzOptionItemComponent } from './option-item.component';\nimport { NzSelectItemInterface, NzSelectModeType } from './select.types';\n\n@Component({\n  selector: 'nz-option-container',\n  exportAs: 'nzOptionContainer',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  template: `\n    <div>\n      @if (listOfContainerItem.length === 0) {\n        <div class=\"ant-select-item-empty\">\n          <nz-embed-empty nzComponentName=\"select\" [specificContent]=\"notFoundContent!\"></nz-embed-empty>\n        </div>\n      }\n      <cdk-virtual-scroll-viewport\n        [class.full-width]=\"!matchWidth\"\n        [itemSize]=\"itemSize\"\n        [maxBufferPx]=\"itemSize * maxItemLength\"\n        [minBufferPx]=\"itemSize * maxItemLength\"\n        (scrolledIndexChange)=\"onScrolledIndexChange($event)\"\n        [style.height.px]=\"listOfContainerItem.length * itemSize\"\n        [style.max-height.px]=\"itemSize * maxItemLength\"\n      >\n        <ng-template\n          cdkVirtualFor\n          [cdkVirtualForOf]=\"listOfContainerItem\"\n          [cdkVirtualForTrackBy]=\"trackValue\"\n          [cdkVirtualForTemplateCacheSize]=\"0\"\n          let-item\n        >\n          @switch (item.type) {\n            @case ('group') {\n              <nz-option-item-group [nzLabel]=\"item.groupLabel ?? null\"></nz-option-item-group>\n            }\n            @case ('item') {\n              <nz-option-item\n                [icon]=\"menuItemSelectedIcon\"\n                [customContent]=\"item.nzCustomContent\"\n                [template]=\"item.template ?? null\"\n                [grouped]=\"!!item.groupLabel\"\n                [disabled]=\"\n                  item.nzDisabled || (isMaxMultipleCountReached && !listOfSelectedValue.includes(item['nzValue']))\n                \"\n                [showState]=\"mode === 'tags' || mode === 'multiple'\"\n                [title]=\"item.nzTitle\"\n                [label]=\"item.nzLabel\"\n                [compareWith]=\"compareWith\"\n                [activatedValue]=\"activatedValue\"\n                [listOfSelectedValue]=\"listOfSelectedValue\"\n                [value]=\"item.nzValue\"\n                (itemHover)=\"onItemHover($event)\"\n                (itemClick)=\"onItemClick($event)\"\n              ></nz-option-item>\n            }\n          }\n        </ng-template>\n      </cdk-virtual-scroll-viewport>\n      <ng-template [ngTemplateOutlet]=\"dropdownRender\"></ng-template>\n    </div>\n  `,\n  host: { class: 'ant-select-dropdown' },\n  imports: [\n    NzEmptyModule,\n    NzOptionItemGroupComponent,\n    NzOptionItemComponent,\n    NgTemplateOutlet,\n    OverlayModule,\n    NzOverlayModule\n  ]\n})\nexport class NzOptionContainerComponent implements OnChanges, AfterViewInit {\n  @Input() notFoundContent: string | TemplateRef<NzSafeAny> | undefined = undefined;\n  @Input() menuItemSelectedIcon: TemplateRef<NzSafeAny> | null = null;\n  @Input() dropdownRender: TemplateRef<NzSafeAny> | null = null;\n  @Input() activatedValue: NzSafeAny | null = null;\n  @Input() listOfSelectedValue: NzSafeAny[] = [];\n  @Input() compareWith!: (o1: NzSafeAny, o2: NzSafeAny) => boolean;\n  @Input() mode: NzSelectModeType = 'default';\n  @Input() matchWidth = true;\n  @Input() itemSize = 32;\n  @Input() maxItemLength = 8;\n  @Input() isMaxMultipleCountReached = false;\n  @Input() listOfContainerItem: NzSelectItemInterface[] = [];\n  @Output() readonly itemClick = new EventEmitter<NzSafeAny>();\n  @Output() readonly scrollToBottom = new EventEmitter<void>();\n  @ViewChild(CdkVirtualScrollViewport, { static: true }) cdkVirtualScrollViewport!: CdkVirtualScrollViewport;\n  private scrolledIndex = 0;\n  private ngZone = inject(NgZone);\n  private platformId = inject(PLATFORM_ID);\n\n  onItemClick(value: NzSafeAny): void {\n    this.itemClick.emit(value);\n  }\n\n  onItemHover(value: NzSafeAny): void {\n    // TODO: keydown.enter won't activate this value\n    this.activatedValue = value;\n  }\n\n  trackValue(_index: number, option: NzSelectItemInterface): NzSafeAny {\n    return option.key;\n  }\n\n  onScrolledIndexChange(index: number): void {\n    this.scrolledIndex = index;\n    if (index === this.listOfContainerItem.length - this.maxItemLength - 1) {\n      this.scrollToBottom.emit();\n    }\n  }\n\n  scrollToActivatedValue(): void {\n    const index = this.listOfContainerItem.findIndex(item => this.compareWith(item.key, this.activatedValue));\n    if (index < this.scrolledIndex || index >= this.scrolledIndex + this.maxItemLength) {\n      this.cdkVirtualScrollViewport.scrollToIndex(index || 0);\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { listOfContainerItem, activatedValue } = changes;\n    if (listOfContainerItem || activatedValue) {\n      this.scrollToActivatedValue();\n    }\n  }\n\n  ngAfterViewInit(): void {\n    if (isPlatformBrowser(this.platformId)) {\n      this.ngZone.runOutsideAngular(() => setTimeout(() => this.scrollToActivatedValue()));\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Input,\n  OnChanges,\n  OnInit,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject\n} from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { startWith, takeUntil } from 'rxjs/operators';\n\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\nimport { NzOptionGroupComponent } from './option-group.component';\n\n@Component({\n  selector: 'nz-option',\n  exportAs: 'nzOption',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [NzDestroyService],\n  template: `\n    <ng-template>\n      <ng-content></ng-content>\n    </ng-template>\n  `\n})\nexport class NzOptionComponent implements OnChanges, OnInit {\n  changes = new Subject<void>();\n  groupLabel?: string | number | TemplateRef<NzSafeAny> | null = null;\n  @ViewChild(TemplateRef, { static: true }) template!: TemplateRef<NzSafeAny>;\n  @Input() nzTitle?: string | number | null;\n  @Input() nzLabel: string | number | null = null;\n  @Input() nzValue: NzSafeAny | null = null;\n  @Input() nzKey?: string | number;\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Input({ transform: booleanAttribute }) nzHide = false;\n  @Input({ transform: booleanAttribute }) nzCustomContent = false;\n\n  private nzOptionGroupComponent = inject(NzOptionGroupComponent, { optional: true });\n\n  constructor(private destroy$: NzDestroyService) {}\n\n  ngOnInit(): void {\n    if (this.nzOptionGroupComponent) {\n      this.nzOptionGroupComponent.changes.pipe(startWith(true), takeUntil(this.destroy$)).subscribe(() => {\n        this.groupLabel = this.nzOptionGroupComponent?.nzLabel;\n      });\n    }\n  }\n\n  ngOnChanges(): void {\n    this.changes.next();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { ChangeDetectionStrategy, Component, Input, TemplateRef, ViewEncapsulation } from '@angular/core';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\nimport { numberAttributeWithInfinityFallback } from 'ng-zorro-antd/core/util';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\n@Component({\n  selector: 'nz-select-arrow',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  template: `\n    @if (isMaxMultipleCountSet) {\n      <span>{{ listOfValue.length }} / {{ nzMaxMultipleCount }}</span>\n    }\n    @if (loading) {\n      <nz-icon nzType=\"loading\" />\n    } @else {\n      @if (showArrow && !suffixIcon) {\n        @if (search) {\n          <nz-icon nzType=\"search\" />\n        } @else {\n          <nz-icon nzType=\"down\" />\n        }\n      } @else {\n        <ng-container *nzStringTemplateOutlet=\"suffixIcon; let suffixIcon\">\n          @if (suffixIcon) {\n            <nz-icon [nzType]=\"suffixIcon\" />\n          }\n        </ng-container>\n      }\n    }\n    <ng-container *nzStringTemplateOutlet=\"feedbackIcon\">{{ feedbackIcon }}</ng-container>\n  `,\n  host: {\n    class: 'ant-select-arrow',\n    '[class.ant-select-arrow-loading]': 'loading'\n  },\n  imports: [NzIconModule, NzOutletModule]\n})\nexport class NzSelectArrowComponent {\n  @Input() listOfValue: NzSafeAny[] = [];\n  @Input() loading = false;\n  @Input() search = false;\n  @Input() showArrow = false;\n  @Input() isMaxMultipleCountSet = false;\n  @Input() suffixIcon: TemplateRef<NzSafeAny> | string | null = null;\n  @Input() feedbackIcon: TemplateRef<NzSafeAny> | string | null = null;\n  @Input({ transform: numberAttributeWithInfinityFallback }) nzMaxMultipleCount = Infinity;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  EventEmitter,\n  Input,\n  Output,\n  TemplateRef,\n  ViewEncapsulation\n} from '@angular/core';\n\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\n@Component({\n  selector: 'nz-select-clear',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  template: `\n    @if (clearIcon) {\n      <ng-template [ngTemplateOutlet]=\"clearIcon\"></ng-template>\n    } @else {\n      <nz-icon nzType=\"close-circle\" nzTheme=\"fill\" class=\"ant-select-close-icon\" />\n    }\n  `,\n  host: {\n    class: 'ant-select-clear',\n    '(click)': 'onClick($event)'\n  },\n  imports: [NgTemplateOutlet, NzIconModule]\n})\nexport class NzSelectClearComponent {\n  @Input() clearIcon: TemplateRef<NzSafeAny> | null = null;\n  @Output() readonly clear = new EventEmitter<MouseEvent>();\n\n  onClick(e: MouseEvent): void {\n    e.preventDefault();\n    e.stopPropagation();\n    this.clear.emit(e);\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  EventEmitter,\n  Input,\n  Output,\n  TemplateRef,\n  ViewEncapsulation\n} from '@angular/core';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\n@Component({\n  selector: 'nz-select-item',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  template: `\n    <ng-container *nzStringTemplateOutlet=\"contentTemplateOutlet; context: templateOutletContext\">\n      @if (deletable) {\n        <div class=\"ant-select-selection-item-content\">{{ label }}</div>\n      } @else {\n        {{ label }}\n      }\n    </ng-container>\n    @if (deletable && !disabled) {\n      <span class=\"ant-select-selection-item-remove\" (click)=\"onDelete($event)\">\n        @if (!removeIcon) {\n          <nz-icon nzType=\"close\" />\n        } @else {\n          <ng-template [ngTemplateOutlet]=\"removeIcon\"></ng-template>\n        }\n      </span>\n    }\n  `,\n  host: {\n    class: 'ant-select-selection-item',\n    '[attr.title]': 'label',\n    '[class.ant-select-selection-item-disabled]': 'disabled'\n  },\n  imports: [NgTemplateOutlet, NzOutletModule, NzIconModule]\n})\nexport class NzSelectItemComponent {\n  @Input() disabled = false;\n  @Input() label: string | number | null | undefined = null;\n  @Input() deletable = false;\n  @Input() removeIcon: TemplateRef<NzSafeAny> | null = null;\n  @Input() contentTemplateOutletContext: NzSafeAny | null = null;\n  @Input() contentTemplateOutlet: string | TemplateRef<NzSafeAny> | null = null;\n  @Output() readonly delete = new EventEmitter<MouseEvent>();\n\n  protected get templateOutletContext(): NzSafeAny {\n    return {\n      $implicit: this.contentTemplateOutletContext,\n      ...this.contentTemplateOutletContext\n    };\n  }\n\n  onDelete(e: MouseEvent): void {\n    e.preventDefault();\n    e.stopPropagation();\n    if (!this.disabled) {\n      this.delete.next(e);\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { ChangeDetectionStrategy, Component, Input, TemplateRef, ViewEncapsulation } from '@angular/core';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\n@Component({\n  selector: 'nz-select-placeholder',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  template: `\n    <ng-container *nzStringTemplateOutlet=\"placeholder\">\n      {{ placeholder }}\n    </ng-container>\n  `,\n  host: { class: 'ant-select-selection-placeholder' },\n  imports: [NzOutletModule]\n})\nexport class NzSelectPlaceholderComponent {\n  @Input() placeholder: TemplateRef<NzSafeAny> | string | null = null;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnChanges,\n  Output,\n  Renderer2,\n  SimpleChanges,\n  ViewChild,\n  ViewEncapsulation\n} from '@angular/core';\nimport { COMPOSITION_BUFFER_MODE, FormsModule } from '@angular/forms';\n\nimport { reqAnimFrame } from 'ng-zorro-antd/core/polyfill';\n\n@Component({\n  selector: 'nz-select-search',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  template: `\n    <input\n      #inputElement\n      [attr.id]=\"nzId\"\n      autocomplete=\"off\"\n      class=\"ant-select-selection-search-input\"\n      [ngModel]=\"value\"\n      [attr.autofocus]=\"autofocus ? 'autofocus' : null\"\n      [disabled]=\"disabled\"\n      [style.opacity]=\"showInput ? null : 0\"\n      (ngModelChange)=\"onValueChange($event)\"\n      (compositionstart)=\"setCompositionState(true)\"\n      (compositionend)=\"setCompositionState(false)\"\n    />\n    @if (mirrorSync) {\n      <span #mirrorElement class=\"ant-select-selection-search-mirror\"></span>\n    }\n  `,\n  host: { class: 'ant-select-selection-search' },\n  providers: [{ provide: COMPOSITION_BUFFER_MODE, useValue: false }],\n  imports: [FormsModule]\n})\nexport class NzSelectSearchComponent implements AfterViewInit, OnChanges {\n  @Input() nzId: string | null = null;\n  @Input() disabled = false;\n  @Input() mirrorSync = false;\n  @Input() showInput = true;\n  @Input() focusTrigger = false;\n  @Input() value = '';\n  @Input() autofocus = false;\n  @Output() readonly valueChange = new EventEmitter<string>();\n  @Output() readonly isComposingChange = new EventEmitter<boolean>();\n  @ViewChild('inputElement', { static: true }) inputElement!: ElementRef;\n  @ViewChild('mirrorElement', { static: false }) mirrorElement?: ElementRef;\n\n  setCompositionState(isComposing: boolean): void {\n    this.isComposingChange.next(isComposing);\n  }\n\n  onValueChange(value: string): void {\n    this.value = value;\n    this.valueChange.next(value);\n    if (this.mirrorSync) {\n      this.syncMirrorWidth();\n    }\n  }\n\n  clearInputValue(): void {\n    const inputDOM = this.inputElement.nativeElement;\n    inputDOM.value = '';\n    this.onValueChange('');\n  }\n\n  syncMirrorWidth(): void {\n    reqAnimFrame(() => {\n      const mirrorDOM = this.mirrorElement!.nativeElement;\n      const hostDOM = this.elementRef.nativeElement;\n      const inputDOM = this.inputElement.nativeElement;\n      this.renderer.removeStyle(hostDOM, 'width');\n      this.renderer.setProperty(mirrorDOM, 'textContent', `${inputDOM.value}\\u00a0`);\n      this.renderer.setStyle(hostDOM, 'width', `${mirrorDOM.scrollWidth}px`);\n    });\n  }\n\n  focus(): void {\n    this.focusMonitor.focusVia(this.inputElement, 'keyboard');\n  }\n\n  blur(): void {\n    this.inputElement.nativeElement.blur();\n  }\n\n  constructor(\n    private elementRef: ElementRef,\n    private renderer: Renderer2,\n    private focusMonitor: FocusMonitor\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const inputDOM = this.inputElement.nativeElement;\n    const { focusTrigger, showInput } = changes;\n\n    if (showInput) {\n      if (this.showInput) {\n        this.renderer.removeAttribute(inputDOM, 'readonly');\n      } else {\n        this.renderer.setAttribute(inputDOM, 'readonly', 'readonly');\n      }\n    }\n\n    // IE11 cannot input value if focused before removing readonly\n    if (focusTrigger && focusTrigger.currentValue === true && focusTrigger.previousValue === false) {\n      inputDOM.focus();\n    }\n  }\n\n  ngAfterViewInit(): void {\n    if (this.mirrorSync) {\n      this.syncMirrorWidth();\n    }\n    if (this.autofocus) {\n      this.focus();\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { BACKSPACE } from '@angular/cdk/keycodes';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnChanges,\n  OnInit,\n  Output,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation,\n  inject,\n  numberAttribute,\n  DestroyRef\n} from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\n\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\n\nimport { NzSelectItemComponent } from './select-item.component';\nimport { NzSelectPlaceholderComponent } from './select-placeholder.component';\nimport { NzSelectSearchComponent } from './select-search.component';\nimport { NzSelectItemInterface, NzSelectModeType, NzSelectTopControlItemType } from './select.types';\n\n@Component({\n  selector: 'nz-select-top-control',\n  exportAs: 'nzSelectTopControl',\n  preserveWhitespaces: false,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  template: `\n    <!--single mode-->\n    @switch (mode) {\n      @case ('default') {\n        <nz-select-search\n          [nzId]=\"nzId\"\n          [disabled]=\"disabled\"\n          [value]=\"inputValue!\"\n          [showInput]=\"showSearch\"\n          [mirrorSync]=\"false\"\n          [autofocus]=\"autofocus\"\n          [focusTrigger]=\"open\"\n          (isComposingChange)=\"isComposingChange($event)\"\n          (valueChange)=\"onInputValueChange($event)\"\n        ></nz-select-search>\n        @if (isShowSingleLabel) {\n          <nz-select-item\n            [deletable]=\"false\"\n            [disabled]=\"false\"\n            [removeIcon]=\"removeIcon\"\n            [label]=\"listOfTopItem[0].nzLabel\"\n            [contentTemplateOutlet]=\"customTemplate\"\n            [contentTemplateOutletContext]=\"listOfTopItem[0]\"\n          ></nz-select-item>\n        }\n      }\n      @default {\n        <!--multiple or tags mode-->\n        @for (item of listOfSlicedItem; track item.nzValue) {\n          <nz-select-item\n            [removeIcon]=\"removeIcon\"\n            [label]=\"item.nzLabel\"\n            [disabled]=\"item.nzDisabled || disabled\"\n            [contentTemplateOutlet]=\"item.contentTemplateOutlet\"\n            [deletable]=\"true\"\n            [contentTemplateOutletContext]=\"item.contentTemplateOutletContext\"\n            (delete)=\"onDeleteItem(item.contentTemplateOutletContext)\"\n          ></nz-select-item>\n        }\n        <nz-select-search\n          [nzId]=\"nzId\"\n          [disabled]=\"disabled\"\n          [value]=\"inputValue!\"\n          [autofocus]=\"autofocus\"\n          [showInput]=\"true\"\n          [mirrorSync]=\"true\"\n          [focusTrigger]=\"open\"\n          (isComposingChange)=\"isComposingChange($event)\"\n          (valueChange)=\"onInputValueChange($event)\"\n        ></nz-select-search>\n      }\n    }\n    @if (isShowPlaceholder) {\n      <nz-select-placeholder [placeholder]=\"placeHolder\"></nz-select-placeholder>\n    }\n  `,\n  host: { class: 'ant-select-selector' },\n  imports: [NzSelectSearchComponent, NzSelectItemComponent, NzSelectPlaceholderComponent]\n})\nexport class NzSelectTopControlComponent implements OnChanges, OnInit {\n  @Input() nzId: string | null = null;\n  @Input() showSearch = false;\n  @Input() placeHolder: string | TemplateRef<NzSafeAny> | null = null;\n  @Input() open = false;\n  @Input({ transform: numberAttribute }) maxTagCount: number = Infinity;\n  @Input() autofocus = false;\n  @Input() disabled = false;\n  @Input() mode: NzSelectModeType = 'default';\n  @Input() customTemplate: TemplateRef<{ $implicit: NzSelectItemInterface }> | null = null;\n  @Input() maxTagPlaceholder: TemplateRef<{ $implicit: NzSafeAny[] }> | null = null;\n  @Input() removeIcon: TemplateRef<NzSafeAny> | null = null;\n  @Input() listOfTopItem: NzSelectItemInterface[] = [];\n  @Input() tokenSeparators: string[] = [];\n  @Output() readonly tokenize = new EventEmitter<string[]>();\n  @Output() readonly inputValueChange = new EventEmitter<string>();\n  @Output() readonly deleteItem = new EventEmitter<NzSelectItemInterface>();\n  @ViewChild(NzSelectSearchComponent) nzSelectSearchComponent!: NzSelectSearchComponent;\n  listOfSlicedItem: NzSelectTopControlItemType[] = [];\n  isShowPlaceholder = true;\n  isShowSingleLabel = false;\n  isComposing = false;\n  inputValue: string | null = null;\n\n  updateTemplateVariable(): void {\n    const isSelectedValueEmpty = this.listOfTopItem.length === 0;\n    this.isShowPlaceholder = isSelectedValueEmpty && !this.isComposing && !this.inputValue;\n    this.isShowSingleLabel = !isSelectedValueEmpty && !this.isComposing && !this.inputValue;\n  }\n\n  isComposingChange(isComposing: boolean): void {\n    this.isComposing = isComposing;\n    this.updateTemplateVariable();\n  }\n\n  onInputValueChange(value: string): void {\n    if (value !== this.inputValue) {\n      this.inputValue = value;\n      this.updateTemplateVariable();\n      this.inputValueChange.emit(value);\n      this.tokenSeparate(value, this.tokenSeparators);\n    }\n  }\n\n  tokenSeparate(inputValue: string, tokenSeparators: string[]): void {\n    const includesSeparators = (str: string, separators: string[]): boolean => {\n      // eslint-disable-next-line @typescript-eslint/prefer-for-of\n      for (let i = 0; i < separators.length; ++i) {\n        if (str.lastIndexOf(separators[i]) > 0) {\n          return true;\n        }\n      }\n      return false;\n    };\n    const splitBySeparators = (str: string, separators: string[]): string[] => {\n      const reg = new RegExp(`[${separators.join()}]`);\n      const array = str.split(reg).filter(token => token);\n      return [...new Set(array)];\n    };\n    if (\n      inputValue &&\n      inputValue.length &&\n      tokenSeparators.length &&\n      this.mode !== 'default' &&\n      includesSeparators(inputValue, tokenSeparators)\n    ) {\n      const listOfLabel = splitBySeparators(inputValue, tokenSeparators);\n      this.tokenize.next(listOfLabel);\n    }\n  }\n\n  clearInputValue(): void {\n    if (this.nzSelectSearchComponent) {\n      this.nzSelectSearchComponent.clearInputValue();\n    }\n  }\n\n  focus(): void {\n    if (this.nzSelectSearchComponent) {\n      this.nzSelectSearchComponent.focus();\n    }\n  }\n\n  blur(): void {\n    if (this.nzSelectSearchComponent) {\n      this.nzSelectSearchComponent.blur();\n    }\n  }\n\n  onDeleteItem(item: NzSelectItemInterface): void {\n    if (!this.disabled && !item.nzDisabled) {\n      this.deleteItem.next(item);\n    }\n  }\n\n  private destroyRef = inject(DestroyRef);\n  private elementRef = inject(ElementRef<HTMLElement>);\n  private ngZone = inject(NgZone);\n  noAnimation = inject(NzNoAnimationDirective, { host: true, optional: true });\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { listOfTopItem, maxTagCount, customTemplate, maxTagPlaceholder } = changes;\n    if (listOfTopItem) {\n      this.updateTemplateVariable();\n    }\n    if (listOfTopItem || maxTagCount || customTemplate || maxTagPlaceholder) {\n      const listOfSlicedItem: NzSelectTopControlItemType[] = this.listOfTopItem.slice(0, this.maxTagCount).map(o => ({\n        nzLabel: o.nzLabel,\n        nzValue: o.nzValue,\n        nzDisabled: o.nzDisabled,\n        contentTemplateOutlet: this.customTemplate,\n        contentTemplateOutletContext: o\n      }));\n      if (this.listOfTopItem.length > this.maxTagCount) {\n        const exceededLabel = `+ ${this.listOfTopItem.length - this.maxTagCount} ...`;\n        const listOfSelectedValue = this.listOfTopItem.map(item => item.nzValue);\n        const exceededItem = {\n          nzLabel: exceededLabel,\n          nzValue: '$$__nz_exceeded_item',\n          nzDisabled: true,\n          contentTemplateOutlet: this.maxTagPlaceholder,\n          contentTemplateOutletContext: listOfSelectedValue.slice(this.maxTagCount)\n        };\n        listOfSlicedItem.push(exceededItem);\n      }\n      this.listOfSlicedItem = listOfSlicedItem;\n    }\n  }\n\n  ngOnInit(): void {\n    fromEventOutsideAngular<MouseEvent>(this.elementRef.nativeElement, 'click')\n      .pipe(takeUntilDestroyed(this.destroyRef))\n      .subscribe(event => {\n        // `HTMLElement.focus()` is a native DOM API which doesn't require Angular to run change detection.\n        if (event.target !== this.nzSelectSearchComponent.inputElement.nativeElement) {\n          this.nzSelectSearchComponent.focus();\n        }\n      });\n\n    fromEventOutsideAngular<KeyboardEvent>(this.elementRef.nativeElement, 'keydown')\n      .pipe(takeUntilDestroyed(this.destroyRef))\n      .subscribe(event => {\n        if (event.target instanceof HTMLInputElement) {\n          const inputValue = event.target.value;\n\n          if (event.keyCode === BACKSPACE && this.mode !== 'default' && !inputValue && this.listOfTopItem.length > 0) {\n            event.preventDefault();\n            // Run change detection only if the user has pressed the `Backspace` key and the following condition is met.\n            this.ngZone.run(() => this.onDeleteItem(this.listOfTopItem[this.listOfTopItem.length - 1]));\n          }\n        }\n      });\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { DOWN_ARROW, ENTER, ESCAPE, SPACE, TAB, UP_ARROW } from '@angular/cdk/keycodes';\nimport {\n  CdkConnectedOverlay,\n  CdkOverlayOrigin,\n  ConnectedOverlayPositionChange,\n  ConnectionPositionPair\n} from '@angular/cdk/overlay';\nimport { Platform, _getEventTarget } from '@angular/cdk/platform';\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  ElementRef,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  QueryList,\n  Renderer2,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  computed,\n  forwardRef,\n  inject,\n  signal\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { BehaviorSubject, combineLatest, merge, of as observableOf } from 'rxjs';\nimport { distinctUntilChanged, map, startWith, switchMap, takeUntil, withLatestFrom } from 'rxjs/operators';\n\nimport { slideMotion } from 'ng-zorro-antd/core/animation';\nimport { NzConfigKey, NzConfigService, WithConfig } from 'ng-zorro-antd/core/config';\nimport { NzFormItemFeedbackIconComponent, NzFormNoStatusService, NzFormStatusService } from 'ng-zorro-antd/core/form';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport { NzOverlayModule, POSITION_MAP, POSITION_TYPE, getPlacementName } from 'ng-zorro-antd/core/overlay';\nimport { cancelRequestAnimationFrame, reqAnimFrame } from 'ng-zorro-antd/core/polyfill';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport {\n  NgClassInterface,\n  NzSafeAny,\n  NzSizeLDSType,\n  NzStatus,\n  NzValidateStatus,\n  OnChangeType,\n  OnTouchedType\n} from 'ng-zorro-antd/core/types';\nimport {\n  fromEventOutsideAngular,\n  getStatusClassNames,\n  isNotNil,\n  numberAttributeWithInfinityFallback\n} from 'ng-zorro-antd/core/util';\nimport { NZ_SPACE_COMPACT_ITEM_TYPE, NZ_SPACE_COMPACT_SIZE, NzSpaceCompactItemDirective } from 'ng-zorro-antd/space';\n\nimport { NzOptionContainerComponent } from './option-container.component';\nimport { NzOptionGroupComponent } from './option-group.component';\nimport { NzOptionComponent } from './option.component';\nimport { NzSelectArrowComponent } from './select-arrow.component';\nimport { NzSelectClearComponent } from './select-clear.component';\nimport { NzSelectTopControlComponent } from './select-top-control.component';\nimport {\n  NzFilterOptionType,\n  NzSelectItemInterface,\n  NzSelectModeType,\n  NzSelectOptionInterface,\n  NzSelectPlacementType\n} from './select.types';\n\nconst defaultFilterOption: NzFilterOptionType = (searchValue: string, item: NzSelectItemInterface): boolean => {\n  if (item && item.nzLabel) {\n    return item.nzLabel.toString().toLowerCase().indexOf(searchValue.toLowerCase()) > -1;\n  } else {\n    return false;\n  }\n};\n\nconst NZ_CONFIG_MODULE_NAME: NzConfigKey = 'select';\n\nexport type NzSelectSizeType = NzSizeLDSType;\n\n@Component({\n  selector: 'nz-select',\n  exportAs: 'nzSelect',\n  preserveWhitespaces: false,\n  providers: [\n    NzDestroyService,\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => NzSelectComponent),\n      multi: true\n    },\n    { provide: NZ_SPACE_COMPACT_ITEM_TYPE, useValue: 'select' }\n  ],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  animations: [slideMotion],\n  template: `\n    <nz-select-top-control\n      cdkOverlayOrigin\n      #origin=\"cdkOverlayOrigin\"\n      [nzId]=\"nzId\"\n      [open]=\"nzOpen\"\n      [disabled]=\"nzDisabled\"\n      [mode]=\"nzMode\"\n      [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n      [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n      [maxTagPlaceholder]=\"nzMaxTagPlaceholder\"\n      [removeIcon]=\"nzRemoveIcon\"\n      [placeHolder]=\"nzPlaceHolder\"\n      [maxTagCount]=\"nzMaxTagCount\"\n      [customTemplate]=\"nzCustomTemplate\"\n      [tokenSeparators]=\"nzTokenSeparators\"\n      [showSearch]=\"nzShowSearch\"\n      [autofocus]=\"nzAutoFocus\"\n      [listOfTopItem]=\"listOfTopItem\"\n      (inputValueChange)=\"onInputValueChange($event)\"\n      (tokenize)=\"onTokenSeparate($event)\"\n      (deleteItem)=\"onItemDelete($event)\"\n      (keydown)=\"onKeyDown($event)\"\n    ></nz-select-top-control>\n    @if (nzShowArrow || (hasFeedback && !!status) || isMaxMultipleCountSet) {\n      <nz-select-arrow\n        [showArrow]=\"nzShowArrow\"\n        [loading]=\"nzLoading\"\n        [search]=\"nzOpen && nzShowSearch\"\n        [suffixIcon]=\"nzSuffixIcon\"\n        [feedbackIcon]=\"feedbackIconTpl\"\n        [nzMaxMultipleCount]=\"nzMaxMultipleCount\"\n        [listOfValue]=\"listOfValue\"\n        [isMaxMultipleCountSet]=\"isMaxMultipleCountSet\"\n      >\n        <ng-template #feedbackIconTpl>\n          @if (hasFeedback && !!status) {\n            <nz-form-item-feedback-icon [status]=\"status\"></nz-form-item-feedback-icon>\n          }\n        </ng-template>\n      </nz-select-arrow>\n    }\n\n    @if (nzAllowClear && !nzDisabled && listOfValue.length) {\n      <nz-select-clear [clearIcon]=\"nzClearIcon\" (clear)=\"onClearSelection()\"></nz-select-clear>\n    }\n    <ng-template\n      cdkConnectedOverlay\n      nzConnectedOverlay\n      [cdkConnectedOverlayHasBackdrop]=\"nzBackdrop\"\n      [cdkConnectedOverlayMinWidth]=\"$any(nzDropdownMatchSelectWidth ? null : triggerWidth)\"\n      [cdkConnectedOverlayWidth]=\"$any(nzDropdownMatchSelectWidth ? triggerWidth : null)\"\n      [cdkConnectedOverlayOrigin]=\"origin\"\n      [cdkConnectedOverlayTransformOriginOn]=\"'.ant-select-dropdown'\"\n      [cdkConnectedOverlayPanelClass]=\"nzDropdownClassName!\"\n      [cdkConnectedOverlayOpen]=\"nzOpen\"\n      [cdkConnectedOverlayPositions]=\"positions\"\n      (overlayOutsideClick)=\"onClickOutside($event)\"\n      (detach)=\"setOpenState(false)\"\n      (positionChange)=\"onPositionChange($event)\"\n    >\n      <nz-option-container\n        [style]=\"nzDropdownStyle\"\n        [itemSize]=\"nzOptionHeightPx\"\n        [maxItemLength]=\"nzOptionOverflowSize\"\n        [matchWidth]=\"nzDropdownMatchSelectWidth\"\n        [class.ant-select-dropdown-placement-bottomLeft]=\"dropdownPosition === 'bottomLeft'\"\n        [class.ant-select-dropdown-placement-topLeft]=\"dropdownPosition === 'topLeft'\"\n        [class.ant-select-dropdown-placement-bottomRight]=\"dropdownPosition === 'bottomRight'\"\n        [class.ant-select-dropdown-placement-topRight]=\"dropdownPosition === 'topRight'\"\n        [@slideMotion]=\"'enter'\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        [listOfContainerItem]=\"listOfContainerItem\"\n        [menuItemSelectedIcon]=\"nzMenuItemSelectedIcon\"\n        [notFoundContent]=\"nzNotFoundContent\"\n        [activatedValue]=\"activatedValue\"\n        [listOfSelectedValue]=\"listOfValue\"\n        [dropdownRender]=\"nzDropdownRender\"\n        [compareWith]=\"compareWith\"\n        [mode]=\"nzMode\"\n        [isMaxMultipleCountReached]=\"isMaxMultipleCountReached\"\n        (keydown)=\"onKeyDown($event)\"\n        (itemClick)=\"onItemClick($event)\"\n        (scrollToBottom)=\"nzScrollToBottom.emit()\"\n      ></nz-option-container>\n    </ng-template>\n  `,\n  host: {\n    class: 'ant-select',\n    '[class.ant-select-in-form-item]': '!!nzFormStatusService',\n    '[class.ant-select-lg]': 'finalSize() === \"large\"',\n    '[class.ant-select-sm]': 'finalSize() === \"small\"',\n    '[class.ant-select-show-arrow]': `nzShowArrow`,\n    '[class.ant-select-disabled]': 'nzDisabled',\n    '[class.ant-select-show-search]': `(nzShowSearch || nzMode !== 'default') && !nzDisabled`,\n    '[class.ant-select-allow-clear]': 'nzAllowClear',\n    '[class.ant-select-borderless]': 'nzBorderless',\n    '[class.ant-select-open]': 'nzOpen',\n    '[class.ant-select-focused]': 'nzOpen || focused',\n    '[class.ant-select-single]': `nzMode === 'default'`,\n    '[class.ant-select-multiple]': `nzMode !== 'default'`,\n    '[class.ant-select-rtl]': `dir === 'rtl'`\n  },\n  hostDirectives: [NzSpaceCompactItemDirective],\n  imports: [\n    NzSelectTopControlComponent,\n    CdkOverlayOrigin,\n    NzNoAnimationDirective,\n    NzSelectArrowComponent,\n    NzFormItemFeedbackIconComponent,\n    NzSelectClearComponent,\n    CdkConnectedOverlay,\n    NzOverlayModule,\n    NzOptionContainerComponent\n  ]\n})\nexport class NzSelectComponent implements ControlValueAccessor, OnInit, AfterContentInit, OnChanges, OnDestroy {\n  readonly _nzModuleName: NzConfigKey = NZ_CONFIG_MODULE_NAME;\n\n  @Input() nzId: string | null = null;\n  @Input() nzSize: NzSelectSizeType = 'default';\n  @Input() nzStatus: NzStatus = '';\n  @Input() @WithConfig() nzOptionHeightPx = 32;\n  @Input() nzOptionOverflowSize = 8;\n  @Input() nzDropdownClassName: string[] | string | null = null;\n  @Input() nzDropdownMatchSelectWidth = true;\n  @Input() nzDropdownStyle: Record<string, string> | null = null;\n  @Input() nzNotFoundContent: string | TemplateRef<NzSafeAny> | undefined = undefined;\n  @Input() nzPlaceHolder: string | TemplateRef<NzSafeAny> | null = null;\n  @Input() nzPlacement: NzSelectPlacementType | null = null;\n  @Input() nzMaxTagCount = Infinity;\n  @Input() nzDropdownRender: TemplateRef<NzSafeAny> | null = null;\n  @Input() nzCustomTemplate: TemplateRef<{ $implicit: NzSelectItemInterface }> | null = null;\n  @Input()\n  @WithConfig()\n  nzSuffixIcon: TemplateRef<NzSafeAny> | string | null = null;\n  @Input() nzClearIcon: TemplateRef<NzSafeAny> | null = null;\n  @Input() nzRemoveIcon: TemplateRef<NzSafeAny> | null = null;\n  @Input() nzMenuItemSelectedIcon: TemplateRef<NzSafeAny> | null = null;\n  @Input() nzTokenSeparators: string[] = [];\n  @Input() nzMaxTagPlaceholder: TemplateRef<{ $implicit: NzSafeAny[] }> | null = null;\n  @Input({ transform: numberAttributeWithInfinityFallback }) nzMaxMultipleCount = Infinity;\n  @Input() nzMode: NzSelectModeType = 'default';\n  @Input() nzFilterOption: NzFilterOptionType = defaultFilterOption;\n  @Input() compareWith: (o1: NzSafeAny, o2: NzSafeAny) => boolean = (o1: NzSafeAny, o2: NzSafeAny) => o1 === o2;\n  @Input({ transform: booleanAttribute }) nzAllowClear = false;\n  @Input({ transform: booleanAttribute }) @WithConfig() nzBorderless = false;\n  @Input({ transform: booleanAttribute }) nzShowSearch = false;\n  @Input({ transform: booleanAttribute }) nzLoading = false;\n  @Input({ transform: booleanAttribute }) nzAutoFocus = false;\n  @Input({ transform: booleanAttribute }) nzAutoClearSearchValue = true;\n  @Input({ transform: booleanAttribute }) nzServerSearch = false;\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Input({ transform: booleanAttribute }) nzOpen = false;\n  @Input({ transform: booleanAttribute }) nzSelectOnTab = false;\n  @Input({ transform: booleanAttribute }) @WithConfig() nzBackdrop = false;\n  @Input() nzOptions: NzSelectOptionInterface[] = [];\n\n  @Input({ transform: booleanAttribute })\n  set nzShowArrow(value: boolean) {\n    this._nzShowArrow = value;\n  }\n  get nzShowArrow(): boolean {\n    return this._nzShowArrow === undefined ? this.nzMode === 'default' : this._nzShowArrow;\n  }\n\n  get isMultiple(): boolean {\n    return this.nzMode === 'multiple' || this.nzMode === 'tags';\n  }\n\n  get isMaxMultipleCountSet(): boolean {\n    return this.isMultiple && this.nzMaxMultipleCount !== Infinity;\n  }\n\n  get isMaxMultipleCountReached(): boolean {\n    return this.nzMaxMultipleCount !== Infinity && this.listOfValue.length === this.nzMaxMultipleCount;\n  }\n\n  @Output() readonly nzOnSearch = new EventEmitter<string>();\n  @Output() readonly nzScrollToBottom = new EventEmitter<void>();\n  @Output() readonly nzOpenChange = new EventEmitter<boolean>();\n  @Output() readonly nzBlur = new EventEmitter<void>();\n  @Output() readonly nzFocus = new EventEmitter<void>();\n  @ViewChild(CdkOverlayOrigin, { static: true, read: ElementRef }) originElement!: ElementRef;\n  @ViewChild(CdkConnectedOverlay, { static: true }) cdkConnectedOverlay!: CdkConnectedOverlay;\n  @ViewChild(NzSelectTopControlComponent, { static: true }) nzSelectTopControlComponent!: NzSelectTopControlComponent;\n  @ContentChildren(NzOptionComponent, { descendants: true }) listOfNzOptionComponent!: QueryList<NzOptionComponent>;\n  @ContentChildren(NzOptionGroupComponent, { descendants: true })\n  listOfNzOptionGroupComponent!: QueryList<NzOptionGroupComponent>;\n  @ViewChild(NzOptionGroupComponent, { static: true, read: ElementRef }) nzOptionGroupComponentElement!: ElementRef;\n  @ViewChild(NzSelectTopControlComponent, { static: true, read: ElementRef })\n  nzSelectTopControlComponentElement!: ElementRef;\n\n  protected finalSize = computed(() => {\n    if (this.compactSize) {\n      return this.compactSize();\n    }\n    return this.size();\n  });\n\n  private size = signal<NzSizeLDSType>(this.nzSize);\n  private compactSize = inject(NZ_SPACE_COMPACT_SIZE, { optional: true });\n  private listOfValue$ = new BehaviorSubject<NzSafeAny[]>([]);\n  private listOfTemplateItem$ = new BehaviorSubject<NzSelectItemInterface[]>([]);\n  private listOfTagAndTemplateItem: NzSelectItemInterface[] = [];\n  private searchValue: string = '';\n  private isReactiveDriven = false;\n  private value: NzSafeAny | NzSafeAny[];\n  private _nzShowArrow: boolean | undefined;\n  private requestId: number = -1;\n  private isNzDisableFirstChange: boolean = true;\n\n  onChange: OnChangeType = () => {};\n  onTouched: OnTouchedType = () => {};\n  dropdownPosition: NzSelectPlacementType = 'bottomLeft';\n  triggerWidth: number | null = null;\n  listOfContainerItem: NzSelectItemInterface[] = [];\n  listOfTopItem: NzSelectItemInterface[] = [];\n  activatedValue: NzSafeAny | null = null;\n  listOfValue: NzSafeAny[] = [];\n  focused = false;\n  dir: Direction = 'ltr';\n  positions: ConnectionPositionPair[] = [];\n\n  // status\n  prefixCls: string = 'ant-select';\n  statusCls: NgClassInterface = {};\n  status: NzValidateStatus = '';\n  hasFeedback: boolean = false;\n\n  generateTagItem(value: string): NzSelectItemInterface {\n    return {\n      nzValue: value,\n      nzLabel: value,\n      type: 'item'\n    };\n  }\n\n  onItemClick(value: NzSafeAny): void {\n    this.activatedValue = value;\n    if (this.nzMode === 'default') {\n      if (this.listOfValue.length === 0 || !this.compareWith(this.listOfValue[0], value)) {\n        this.updateListOfValue([value]);\n      }\n      this.setOpenState(false);\n    } else {\n      const targetIndex = this.listOfValue.findIndex(o => this.compareWith(o, value));\n      if (targetIndex !== -1) {\n        const listOfValueAfterRemoved = this.listOfValue.filter((_, i) => i !== targetIndex);\n        this.updateListOfValue(listOfValueAfterRemoved);\n      } else if (this.listOfValue.length < this.nzMaxMultipleCount) {\n        const listOfValueAfterAdded = [...this.listOfValue, value];\n        this.updateListOfValue(listOfValueAfterAdded);\n      }\n      this.focus();\n      if (this.nzAutoClearSearchValue) {\n        this.clearInput();\n      }\n    }\n  }\n\n  onItemDelete(item: NzSelectItemInterface): void {\n    const listOfSelectedValue = this.listOfValue.filter(v => !this.compareWith(v, item.nzValue));\n    this.updateListOfValue(listOfSelectedValue);\n    this.clearInput();\n  }\n\n  updateListOfContainerItem(): void {\n    let listOfContainerItem = this.listOfTagAndTemplateItem\n      .filter(item => !item.nzHide)\n      .filter(item => {\n        if (!this.nzServerSearch && this.searchValue) {\n          return this.nzFilterOption(this.searchValue, item);\n        } else {\n          return true;\n        }\n      });\n    if (this.nzMode === 'tags' && this.searchValue) {\n      const matchedItem = this.listOfTagAndTemplateItem.find(item => item.nzLabel === this.searchValue);\n      if (!matchedItem) {\n        const tagItem = this.generateTagItem(this.searchValue);\n        listOfContainerItem = [tagItem, ...listOfContainerItem];\n        this.activatedValue = tagItem.nzValue;\n      } else {\n        this.activatedValue = matchedItem.nzValue;\n      }\n    }\n    const activatedItem =\n      listOfContainerItem.find(item => item.nzLabel === this.searchValue) ||\n      listOfContainerItem.find(item => this.compareWith(item.nzValue, this.activatedValue)) ||\n      listOfContainerItem.find(item => this.compareWith(item.nzValue, this.listOfValue[0])) ||\n      listOfContainerItem[0];\n    this.activatedValue = (activatedItem && activatedItem.nzValue) || null;\n    let listOfGroupLabel: Array<string | number | TemplateRef<NzSafeAny> | null> = [];\n    if (this.isReactiveDriven) {\n      listOfGroupLabel = [...new Set(this.nzOptions.filter(o => o.groupLabel).map(o => o.groupLabel!))];\n    } else {\n      if (this.listOfNzOptionGroupComponent) {\n        listOfGroupLabel = this.listOfNzOptionGroupComponent.map(o => o.nzLabel);\n      }\n    }\n    /** insert group item **/\n    listOfGroupLabel.forEach(label => {\n      const index = listOfContainerItem.findIndex(item => label === item.groupLabel);\n      if (index > -1) {\n        const groupItem = { groupLabel: label, type: 'group', key: label } as NzSelectItemInterface;\n        listOfContainerItem.splice(index, 0, groupItem);\n      }\n    });\n    this.listOfContainerItem = [...listOfContainerItem];\n    this.updateCdkConnectedOverlayPositions();\n  }\n\n  clearInput(): void {\n    this.nzSelectTopControlComponent.clearInputValue();\n  }\n\n  updateListOfValue(listOfValue: NzSafeAny[]): void {\n    const covertListToModel = (list: NzSafeAny[], mode: NzSelectModeType): NzSafeAny[] | NzSafeAny => {\n      if (mode === 'default') {\n        if (list.length > 0) {\n          return list[0];\n        } else {\n          return null;\n        }\n      } else {\n        return list;\n      }\n    };\n    const model = covertListToModel(listOfValue, this.nzMode);\n    if (this.value !== model) {\n      this.listOfValue = listOfValue;\n      this.listOfValue$.next(listOfValue);\n      this.value = model;\n      this.onChange(this.value);\n    }\n  }\n\n  onTokenSeparate(listOfLabel: string[]): void {\n    const listOfMatchedValue = this.listOfTagAndTemplateItem\n      .filter(item => listOfLabel.findIndex(label => label === item.nzLabel) !== -1)\n      .map(item => item.nzValue)\n      .filter(item => this.listOfValue.findIndex(v => this.compareWith(v, item)) === -1);\n    /**\n     * Limit the number of selected item to nzMaxMultipleCount\n     */\n    const limitWithinMaxCount = <T>(value: T[]): T[] =>\n      this.isMaxMultipleCountSet ? value.slice(0, this.nzMaxMultipleCount) : value;\n\n    if (this.nzMode === 'multiple') {\n      const updateValue = limitWithinMaxCount([...this.listOfValue, ...listOfMatchedValue]);\n      this.updateListOfValue(updateValue);\n    } else if (this.nzMode === 'tags') {\n      const listOfUnMatchedLabel = listOfLabel.filter(\n        label => this.listOfTagAndTemplateItem.findIndex(item => item.nzLabel === label) === -1\n      );\n      const updateValue = limitWithinMaxCount([...this.listOfValue, ...listOfMatchedValue, ...listOfUnMatchedLabel]);\n      this.updateListOfValue(updateValue);\n    }\n    this.clearInput();\n  }\n\n  onKeyDown(e: KeyboardEvent): void {\n    if (this.nzDisabled) {\n      return;\n    }\n    const listOfFilteredOptionNotDisabled = this.listOfContainerItem\n      .filter(item => item.type === 'item')\n      .filter(item => !item.nzDisabled);\n    const activatedIndex = listOfFilteredOptionNotDisabled.findIndex(item =>\n      this.compareWith(item.nzValue, this.activatedValue)\n    );\n    switch (e.keyCode) {\n      case UP_ARROW:\n        e.preventDefault();\n        if (this.nzOpen && listOfFilteredOptionNotDisabled.length > 0) {\n          const preIndex = activatedIndex > 0 ? activatedIndex - 1 : listOfFilteredOptionNotDisabled.length - 1;\n          this.activatedValue = listOfFilteredOptionNotDisabled[preIndex].nzValue;\n        }\n        break;\n      case DOWN_ARROW:\n        e.preventDefault();\n        if (this.nzOpen && listOfFilteredOptionNotDisabled.length > 0) {\n          const nextIndex = activatedIndex < listOfFilteredOptionNotDisabled.length - 1 ? activatedIndex + 1 : 0;\n          this.activatedValue = listOfFilteredOptionNotDisabled[nextIndex].nzValue;\n        } else {\n          this.setOpenState(true);\n        }\n        break;\n      case ENTER:\n        e.preventDefault();\n        if (this.nzOpen) {\n          if (isNotNil(this.activatedValue) && activatedIndex !== -1) {\n            this.onItemClick(this.activatedValue);\n          }\n        } else {\n          this.setOpenState(true);\n        }\n        break;\n      case SPACE:\n        if (!this.nzOpen) {\n          this.setOpenState(true);\n          e.preventDefault();\n        }\n        break;\n      case TAB:\n        if (this.nzSelectOnTab) {\n          if (this.nzOpen) {\n            e.preventDefault();\n            if (isNotNil(this.activatedValue)) {\n              this.onItemClick(this.activatedValue);\n            }\n          }\n        } else {\n          this.setOpenState(false);\n        }\n        break;\n      case ESCAPE:\n        /**\n         * Skip the ESCAPE processing, it will be handled in {@link onOverlayKeyDown}.\n         */\n        break;\n      default:\n        if (!this.nzOpen) {\n          this.setOpenState(true);\n        }\n    }\n  }\n\n  setOpenState(value: boolean): void {\n    if (this.nzOpen !== value) {\n      this.nzOpen = value;\n      this.nzOpenChange.emit(value);\n      this.onOpenChange();\n      this.cdr.markForCheck();\n    }\n  }\n\n  onOpenChange(): void {\n    this.updateCdkConnectedOverlayStatus();\n    if (this.nzAutoClearSearchValue) {\n      this.clearInput();\n    }\n  }\n\n  onInputValueChange(value: string): void {\n    this.searchValue = value;\n    this.updateListOfContainerItem();\n    this.nzOnSearch.emit(value);\n    this.updateCdkConnectedOverlayPositions();\n  }\n\n  onClearSelection(): void {\n    this.updateListOfValue([]);\n  }\n\n  onClickOutside(event: MouseEvent): void {\n    const target = _getEventTarget(event);\n    if (!this.host.nativeElement.contains(target as Node)) {\n      this.setOpenState(false);\n    }\n  }\n\n  focus(): void {\n    this.nzSelectTopControlComponent.focus();\n  }\n\n  blur(): void {\n    this.nzSelectTopControlComponent.blur();\n  }\n\n  onPositionChange(position: ConnectedOverlayPositionChange): void {\n    const placement = getPlacementName(position);\n    this.dropdownPosition = placement as NzSelectPlacementType;\n  }\n\n  updateCdkConnectedOverlayStatus(): void {\n    if (this.platform.isBrowser && this.originElement.nativeElement) {\n      const triggerWidth = this.triggerWidth;\n      cancelRequestAnimationFrame(this.requestId);\n      this.requestId = reqAnimFrame(() => {\n        // Blink triggers style and layout pipelines anytime the `getBoundingClientRect()` is called, which may cause a\n        // frame drop. That's why it's scheduled through the `requestAnimationFrame` to unload the composite thread.\n        this.triggerWidth = this.originElement.nativeElement.getBoundingClientRect().width;\n        if (triggerWidth !== this.triggerWidth) {\n          // The `requestAnimationFrame` will trigger change detection, but we're inside an `OnPush` component which won't have\n          // the `ChecksEnabled` state. Calling `markForCheck()` will allow Angular to run the change detection from the root component\n          // down to the `nz-select`. But we'll trigger only local change detection if the `triggerWidth` has been changed.\n          this.cdr.detectChanges();\n        }\n      });\n    }\n  }\n\n  updateCdkConnectedOverlayPositions(): void {\n    reqAnimFrame(() => {\n      this.cdkConnectedOverlay?.overlayRef?.updatePosition();\n    });\n  }\n\n  noAnimation = inject(NzNoAnimationDirective, { host: true, optional: true });\n  protected nzFormStatusService = inject(NzFormStatusService, { optional: true });\n  private nzFormNoStatusService = inject(NzFormNoStatusService, { optional: true });\n\n  constructor(\n    private ngZone: NgZone,\n    private destroy$: NzDestroyService,\n    public nzConfigService: NzConfigService,\n    private cdr: ChangeDetectorRef,\n    private host: ElementRef<HTMLElement>,\n    private renderer: Renderer2,\n    private platform: Platform,\n    private focusMonitor: FocusMonitor,\n    private directionality: Directionality\n  ) {}\n\n  writeValue(modelValue: NzSafeAny | NzSafeAny[]): void {\n    /** https://github.com/angular/angular/issues/14988 **/\n    if (this.value !== modelValue) {\n      this.value = modelValue;\n      const covertModelToList = (model: NzSafeAny[] | NzSafeAny, mode: NzSelectModeType): NzSafeAny[] => {\n        if (model === null || model === undefined) {\n          return [];\n        } else if (mode === 'default') {\n          return [model];\n        } else {\n          return model;\n        }\n      };\n      const listOfValue = covertModelToList(modelValue, this.nzMode);\n      this.listOfValue = listOfValue;\n      this.listOfValue$.next(listOfValue);\n      this.cdr.markForCheck();\n    }\n  }\n\n  registerOnChange(fn: OnChangeType): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: OnTouchedType): void {\n    this.onTouched = fn;\n  }\n\n  setDisabledState(disabled: boolean): void {\n    this.nzDisabled = (this.isNzDisableFirstChange && this.nzDisabled) || disabled;\n    this.isNzDisableFirstChange = false;\n    if (this.nzDisabled) {\n      this.setOpenState(false);\n    }\n    this.cdr.markForCheck();\n  }\n\n  ngOnChanges({ nzOpen, nzDisabled, nzOptions, nzStatus, nzPlacement, nzSize }: SimpleChanges): void {\n    if (nzOpen) {\n      this.onOpenChange();\n    }\n    if (nzDisabled && this.nzDisabled) {\n      this.setOpenState(false);\n    }\n    if (nzOptions) {\n      this.isReactiveDriven = true;\n      const listOfOptions = this.nzOptions || [];\n      const listOfTransformedItem = listOfOptions.map(item => {\n        return {\n          template: item.label instanceof TemplateRef ? item.label : null,\n          nzTitle: this.getTitle(item.title, item.label),\n          nzLabel: typeof item.label === 'string' || typeof item.label === 'number' ? item.label : null,\n          nzValue: item.value,\n          nzDisabled: item.disabled || false,\n          nzHide: item.hide || false,\n          nzCustomContent: item.label instanceof TemplateRef,\n          groupLabel: item.groupLabel || null,\n          type: 'item',\n          key: item.key === undefined ? item.value : item.key\n        };\n      });\n      this.listOfTemplateItem$.next(listOfTransformedItem);\n    }\n    if (nzStatus) {\n      this.setStatusStyles(this.nzStatus, this.hasFeedback);\n    }\n    if (nzPlacement) {\n      const { currentValue } = nzPlacement;\n      this.dropdownPosition = currentValue as NzSelectPlacementType;\n      const listOfPlacement = ['bottomLeft', 'topLeft', 'bottomRight', 'topRight'];\n      if (currentValue && listOfPlacement.includes(currentValue)) {\n        this.positions = [POSITION_MAP[currentValue as POSITION_TYPE]];\n      } else {\n        this.positions = listOfPlacement.map(e => POSITION_MAP[e as POSITION_TYPE]);\n      }\n    }\n    if (nzSize) {\n      this.size.set(nzSize.currentValue);\n    }\n  }\n\n  ngOnInit(): void {\n    this.nzFormStatusService?.formStatusChanges\n      .pipe(\n        distinctUntilChanged((pre, cur) => {\n          return pre.status === cur.status && pre.hasFeedback === cur.hasFeedback;\n        }),\n        withLatestFrom(this.nzFormNoStatusService ? this.nzFormNoStatusService.noFormStatus : observableOf(false)),\n        map(([{ status, hasFeedback }, noStatus]) => ({ status: noStatus ? '' : status, hasFeedback })),\n        takeUntil(this.destroy$)\n      )\n      .subscribe(({ status, hasFeedback }) => {\n        this.setStatusStyles(status, hasFeedback);\n      });\n\n    this.focusMonitor\n      .monitor(this.host, true)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(focusOrigin => {\n        if (!focusOrigin) {\n          this.focused = false;\n          this.cdr.markForCheck();\n          this.nzBlur.emit();\n          Promise.resolve().then(() => {\n            this.onTouched();\n          });\n        } else {\n          this.focused = true;\n          this.cdr.markForCheck();\n          this.nzFocus.emit();\n        }\n      });\n    combineLatest([this.listOfValue$, this.listOfTemplateItem$])\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(([listOfSelectedValue, listOfTemplateItem]) => {\n        const listOfTagItem = listOfSelectedValue\n          .filter(() => this.nzMode === 'tags')\n          .filter(value => listOfTemplateItem.findIndex(o => this.compareWith(o.nzValue, value)) === -1)\n          .map(\n            value => this.listOfTopItem.find(o => this.compareWith(o.nzValue, value)) || this.generateTagItem(value)\n          );\n        this.listOfTagAndTemplateItem = [...listOfTemplateItem, ...listOfTagItem];\n        this.listOfTopItem = this.listOfValue\n          .map(\n            v =>\n              [...this.listOfTagAndTemplateItem, ...this.listOfTopItem].find(item => this.compareWith(v, item.nzValue))!\n          )\n          .filter(item => !!item);\n        this.updateListOfContainerItem();\n      });\n\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n\n    this.nzConfigService\n      .getConfigChangeEventForComponent('select')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.size.set(this.nzSize);\n        this.cdr.markForCheck();\n      });\n\n    this.dir = this.directionality.value;\n\n    fromEventOutsideAngular(this.host.nativeElement, 'click')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        if ((this.nzOpen && this.nzShowSearch) || this.nzDisabled) {\n          return;\n        }\n\n        this.ngZone.run(() => this.setOpenState(!this.nzOpen));\n      });\n\n    // Caretaker note: we could've added this listener within the template `(overlayKeydown)=\"...\"`,\n    // but with this approach, it'll run change detection on each keyboard click, and also it'll run\n    // `markForCheck()` internally, which means the whole component tree (starting from the root and\n    // going down to the select component) will be re-checked and updated (if needed).\n    // This is safe to do that manually since `setOpenState()` calls `markForCheck()` if needed.\n    this.cdkConnectedOverlay.overlayKeydown.pipe(takeUntil(this.destroy$)).subscribe(event => {\n      if (event.keyCode === ESCAPE) {\n        this.setOpenState(false);\n      }\n    });\n  }\n\n  ngAfterContentInit(): void {\n    if (!this.isReactiveDriven) {\n      merge(this.listOfNzOptionGroupComponent.changes, this.listOfNzOptionComponent.changes)\n        .pipe(\n          startWith(true),\n          switchMap(() =>\n            merge(\n              ...[\n                this.listOfNzOptionComponent.changes,\n                this.listOfNzOptionGroupComponent.changes,\n                ...this.listOfNzOptionComponent.map(option => option.changes),\n                ...this.listOfNzOptionGroupComponent.map(option => option.changes)\n              ]\n            ).pipe(startWith(true))\n          ),\n          takeUntil(this.destroy$)\n        )\n        .subscribe(() => {\n          const listOfOptionInterface = this.listOfNzOptionComponent.toArray().map(item => {\n            const { template, nzLabel, nzValue, nzKey, nzDisabled, nzHide, nzCustomContent, groupLabel } = item;\n            return {\n              template,\n              nzLabel,\n              nzValue,\n              nzDisabled,\n              nzHide,\n              nzCustomContent,\n              groupLabel,\n              nzTitle: this.getTitle(item.nzTitle, item.nzLabel),\n              type: 'item',\n              key: nzKey === undefined ? nzValue : nzKey\n            };\n          });\n          this.listOfTemplateItem$.next(listOfOptionInterface);\n          this.cdr.markForCheck();\n        });\n    }\n  }\n\n  ngOnDestroy(): void {\n    cancelRequestAnimationFrame(this.requestId);\n    this.focusMonitor.stopMonitoring(this.host);\n  }\n\n  private setStatusStyles(status: NzValidateStatus, hasFeedback: boolean): void {\n    this.status = status;\n    this.hasFeedback = hasFeedback;\n    this.cdr.markForCheck();\n    // render status if nzStatus is set\n    this.statusCls = getStatusClassNames(this.prefixCls, status, hasFeedback);\n    Object.keys(this.statusCls).forEach(status => {\n      if (this.statusCls[status]) {\n        this.renderer.addClass(this.host.nativeElement, status);\n      } else {\n        this.renderer.removeClass(this.host.nativeElement, status);\n      }\n    });\n  }\n\n  private getTitle(title: NzSelectOptionInterface['title'], label: NzSelectOptionInterface['label']): string {\n    let rawTitle: string = undefined!;\n    if (title === undefined) {\n      if (typeof label === 'string' || typeof label === 'number') {\n        rawTitle = label.toString();\n      }\n    } else if (typeof title === 'string' || typeof title === 'number') {\n      rawTitle = title.toString();\n    }\n\n    return rawTitle;\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzOptionContainerComponent } from './option-container.component';\nimport { NzOptionGroupComponent } from './option-group.component';\nimport { NzOptionItemGroupComponent } from './option-item-group.component';\nimport { NzOptionItemComponent } from './option-item.component';\nimport { NzOptionComponent } from './option.component';\nimport { NzSelectArrowComponent } from './select-arrow.component';\nimport { NzSelectClearComponent } from './select-clear.component';\nimport { NzSelectItemComponent } from './select-item.component';\nimport { NzSelectPlaceholderComponent } from './select-placeholder.component';\nimport { NzSelectSearchComponent } from './select-search.component';\nimport { NzSelectTopControlComponent } from './select-top-control.component';\nimport { NzSelectComponent } from './select.component';\n\n@NgModule({\n  imports: [\n    NzOptionComponent,\n    NzSelectComponent,\n    NzOptionContainerComponent,\n    NzOptionGroupComponent,\n    NzOptionItemComponent,\n    NzSelectTopControlComponent,\n    NzSelectSearchComponent,\n    NzSelectItemComponent,\n    NzSelectClearComponent,\n    NzSelectArrowComponent,\n    NzSelectPlaceholderComponent,\n    NzOptionItemGroupComponent\n  ],\n  exports: [\n    NzOptionComponent,\n    NzSelectComponent,\n    NzOptionGroupComponent,\n    NzSelectArrowComponent,\n    NzSelectClearComponent,\n    NzSelectItemComponent,\n    NzSelectPlaceholderComponent,\n    NzSelectSearchComponent\n  ]\n})\nexport class NzSelectModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { TemplateRef } from '@angular/core';\n\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\nexport type NzSelectModeType = 'default' | 'multiple' | 'tags';\nexport interface NzSelectItemInterface {\n  template?: TemplateRef<NzSafeAny> | null;\n  nzLabel: string | number | null;\n  nzValue: NzSafeAny | null;\n  nzTitle?: string | number | null;\n  nzDisabled?: boolean;\n  nzHide?: boolean;\n  nzCustomContent?: boolean;\n  groupLabel?: string | number | TemplateRef<NzSafeAny> | null;\n  type?: string;\n  key?: NzSafeAny;\n}\n\nexport interface NzSelectOptionInterface {\n  label: string | number | null | TemplateRef<NzSafeAny>;\n  value: NzSafeAny | null;\n  title?: string | number | null;\n  disabled?: boolean;\n  hide?: boolean;\n  groupLabel?: string | number | TemplateRef<NzSafeAny> | null;\n  key?: string | number;\n}\n\nexport type NzSelectTopControlItemType = Partial<NzSelectItemInterface> & {\n  contentTemplateOutlet: TemplateRef<NzSafeAny> | null;\n  contentTemplateOutletContext: NzSafeAny;\n};\n\nexport type NzFilterOptionType = (input: string, option: NzSelectItemInterface) => boolean;\n\nexport type NzSelectPlacementType = 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './option-group.component';\nexport * from './option-container.component';\nexport * from './option.component';\nexport * from './select.component';\nexport * from './select.module';\nexport * from './option-item.component';\nexport * from './option-item-group.component';\nexport * from './select-top-control.component';\nexport * from './select-search.component';\nexport * from './select-item.component';\nexport * from './select-clear.component';\nexport * from './select-arrow.component';\nexport * from './select-placeholder.component';\nexport * from './select.types';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1", "i2", "observableOf", "i4"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;MAcU,sBAAsB,CAAA;IACxB,OAAO,GAAoD,IAAI;AACxE,IAAA,OAAO,GAAG,IAAI,OAAO,EAAQ;IAC7B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;;uGAJV,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAtB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,sBAAsB,6JAJvB,CAA2B,yBAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAI1B,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAPlC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,CAA2B,yBAAA,CAAA;oBACrC,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC;AAC1C,iBAAA;8BAEU,OAAO,EAAA,CAAA;sBAAf;;;AClBH;;;AAGG;MAiBU,0BAA0B,CAAA;IAC5B,OAAO,GAAoD,IAAI;uGAD7D,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA1B,0BAA0B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,uCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAR3B,CAAgF,8EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAMhF,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEb,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBAVtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE,CAAgF,8EAAA,CAAA;oBAC1F,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE;AACR,qBAAA;oBACD,OAAO,EAAE,CAAC,cAAc;AACzB,iBAAA;8BAEU,OAAO,EAAA,CAAA;sBAAf;;;ACrBH;;;AAGG;MA0DU,qBAAqB,CAAA;AAmBtB,IAAA,UAAA;AACA,IAAA,MAAA;AACA,IAAA,QAAA;IApBV,QAAQ,GAAG,KAAK;IAChB,SAAS,GAAG,KAAK;IACR,OAAO,GAAG,KAAK;IACgB,aAAa,GAAG,KAAK;IACpD,QAAQ,GAAkC,IAAI;IAC9C,QAAQ,GAAG,KAAK;IAChB,SAAS,GAAG,KAAK;AACjB,IAAA,KAAK;IACL,KAAK,GAA2B,IAAI;IACpC,KAAK,GAAqB,IAAI;IAC9B,cAAc,GAAqB,IAAI;IACvC,mBAAmB,GAAgB,EAAE;IACrC,IAAI,GAAkC,IAAI;AAC1C,IAAA,WAAW;AACD,IAAA,SAAS,GAAG,IAAI,YAAY,EAAa;AACzC,IAAA,SAAS,GAAG,IAAI,YAAY,EAAa;AAE5D,IAAA,WAAA,CACU,UAAmC,EACnC,MAAc,EACd,QAA0B,EAAA;QAF1B,IAAU,CAAA,UAAA,GAAV,UAAU;QACV,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAQ,CAAA,QAAA,GAAR,QAAQ;;AAGlB,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,mBAAmB,EAAE,GAAG,OAAO;AAC9D,QAAA,IAAI,KAAK,IAAI,mBAAmB,EAAE;YAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;;AAErF,QAAA,IAAI,KAAK,IAAI,cAAc,EAAE;AAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC;;;IAItE,QAAQ,GAAA;QACN,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,OAAO;AAC3D,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC7B,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;AAE1D,SAAC,CAAC;QAEJ,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,YAAY;AAChE,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC7B,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;AAE1D,SAAC,CAAC;;uGAjDK,qBAAqB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,qBAAqB,kIAIZ,gBAAgB,CAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,OAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,IAAA,EAAA,MAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,YAAA,EAAA,OAAA,EAAA,sCAAA,EAAA,SAAA,EAAA,uCAAA,EAAA,uBAAA,EAAA,uCAAA,EAAA,UAAA,EAAA,qCAAA,EAAA,wBAAA,EAAA,EAAA,cAAA,EAAA,wCAAA,EAAA,EAAA,SAAA,EAPzB,CAAC,gBAAgB,CAAC,EA5BnB,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;GAiBT,EAYS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,mJAAE,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAC,IAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE7B,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAjCjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;AAiBT,EAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,wCAAwC;AAC/C,wBAAA,cAAc,EAAE,OAAO;AACvB,wBAAA,wCAAwC,EAAE,SAAS;AACnD,wBAAA,yCAAyC,EAAE,uBAAuB;AAClE,wBAAA,yCAAyC,EAAE,UAAU;AACrD,wBAAA,uCAAuC,EAAE;AAC1C,qBAAA;oBACD,SAAS,EAAE,CAAC,gBAAgB,CAAC;AAC7B,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,YAAY;AACzC,iBAAA;qIAIU,OAAO,EAAA,CAAA;sBAAf;gBACuC,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,mBAAmB,EAAA,CAAA;sBAA3B;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACkB,SAAS,EAAA,CAAA;sBAA3B;gBACkB,SAAS,EAAA,CAAA;sBAA3B;;;AC7EH;;;AAGG;MAmGU,0BAA0B,CAAA;IAC5B,eAAe,GAAgD,SAAS;IACxE,oBAAoB,GAAkC,IAAI;IAC1D,cAAc,GAAkC,IAAI;IACpD,cAAc,GAAqB,IAAI;IACvC,mBAAmB,GAAgB,EAAE;AACrC,IAAA,WAAW;IACX,IAAI,GAAqB,SAAS;IAClC,UAAU,GAAG,IAAI;IACjB,QAAQ,GAAG,EAAE;IACb,aAAa,GAAG,CAAC;IACjB,yBAAyB,GAAG,KAAK;IACjC,mBAAmB,GAA4B,EAAE;AACvC,IAAA,SAAS,GAAG,IAAI,YAAY,EAAa;AACzC,IAAA,cAAc,GAAG,IAAI,YAAY,EAAQ;AACL,IAAA,wBAAwB;IACvE,aAAa,GAAG,CAAC;AACjB,IAAA,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AACvB,IAAA,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;AAExC,IAAA,WAAW,CAAC,KAAgB,EAAA;AAC1B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;;AAG5B,IAAA,WAAW,CAAC,KAAgB,EAAA;;AAE1B,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK;;IAG7B,UAAU,CAAC,MAAc,EAAE,MAA6B,EAAA;QACtD,OAAO,MAAM,CAAC,GAAG;;AAGnB,IAAA,qBAAqB,CAAC,KAAa,EAAA;AACjC,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK;AAC1B,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;AACtE,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;;;IAI9B,sBAAsB,GAAA;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AACzG,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,IAAI,KAAK,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE;YAClF,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,CAAC;;;AAI3D,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,EAAE,mBAAmB,EAAE,cAAc,EAAE,GAAG,OAAO;AACvD,QAAA,IAAI,mBAAmB,IAAI,cAAc,EAAE;YACzC,IAAI,CAAC,sBAAsB,EAAE;;;IAIjC,eAAe,GAAA;AACb,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACtC,YAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,UAAU,CAAC,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;;;uGAxD7E,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA1B,0BAA0B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,IAAA,EAAA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,aAAA,EAAA,eAAA,EAAA,yBAAA,EAAA,2BAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,0BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAe1B,wBAAwB,EA9EzB,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoDT,EAGC,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,aAAa,EACb,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAD,IAAA,CAAA,qBAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,0BAA0B,EAC1B,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,qBAAqB,sQACrB,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAChB,aAAa,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,yBAAA,EAAA,QAAA,EAAA,uCAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,aAAA,EAAA,aAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,kCAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,sBAAA,EAAA,uBAAA,EAAA,gCAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,wBAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,MAAA,EAAA,CAAA,aAAA,EAAA,YAAA,CAAA,EAAA,OAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EACb,eAAe,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAGN,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBArEtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,QAAQ,EAAE,mBAAmB;oBAC7B,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE,EAAE,KAAK,EAAE,qBAAqB,EAAE;AACtC,oBAAA,OAAO,EAAE;wBACP,aAAa;wBACb,0BAA0B;wBAC1B,qBAAqB;wBACrB,gBAAgB;wBAChB,aAAa;wBACb;AACD;AACF,iBAAA;8BAEU,eAAe,EAAA,CAAA;sBAAvB;gBACQ,oBAAoB,EAAA,CAAA;sBAA5B;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,mBAAmB,EAAA,CAAA;sBAA3B;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,yBAAyB,EAAA,CAAA;sBAAjC;gBACQ,mBAAmB,EAAA,CAAA;sBAA3B;gBACkB,SAAS,EAAA,CAAA;sBAA3B;gBACkB,cAAc,EAAA,CAAA;sBAAhC;gBACsD,wBAAwB,EAAA,CAAA;sBAA9E,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,wBAAwB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;;;ACrHvD;;;AAGG;MAkCU,iBAAiB,CAAA;AAcR,IAAA,QAAA;AAbpB,IAAA,OAAO,GAAG,IAAI,OAAO,EAAQ;IAC7B,UAAU,GAAqD,IAAI;AACzB,IAAA,QAAQ;AACzC,IAAA,OAAO;IACP,OAAO,GAA2B,IAAI;IACtC,OAAO,GAAqB,IAAI;AAChC,IAAA,KAAK;IAC0B,UAAU,GAAG,KAAK;IAClB,MAAM,GAAG,KAAK;IACd,eAAe,GAAG,KAAK;IAEvD,sBAAsB,GAAG,MAAM,CAAC,sBAAsB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAEnF,IAAA,WAAA,CAAoB,QAA0B,EAAA;QAA1B,IAAQ,CAAA,QAAA,GAAR,QAAQ;;IAE5B,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;gBACjG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,sBAAsB,EAAE,OAAO;AACxD,aAAC,CAAC;;;IAIN,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;;uGAzBV,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,EAQR,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAChB,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,gBAAgB,CAChB,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,gBAAgB,CAjBzB,EAAA,EAAA,SAAA,EAAA,CAAC,gBAAgB,CAAC,EAUlB,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,WAAW,EATZ,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;AAIT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEU,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAZ7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,QAAQ,EAAE,UAAU;oBACpB,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,SAAS,EAAE,CAAC,gBAAgB,CAAC;AAC7B,oBAAA,QAAQ,EAAE;;;;AAIT,EAAA;AACF,iBAAA;uFAI2C,QAAQ,EAAA,CAAA;sBAAjD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBAC/B,OAAO,EAAA,CAAA;sBAAf;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACuC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;;;AC/CxC;;;AAGG;MA0CU,sBAAsB,CAAA;IACxB,WAAW,GAAgB,EAAE;IAC7B,OAAO,GAAG,KAAK;IACf,MAAM,GAAG,KAAK;IACd,SAAS,GAAG,KAAK;IACjB,qBAAqB,GAAG,KAAK;IAC7B,UAAU,GAA2C,IAAI;IACzD,YAAY,GAA2C,IAAI;IACT,kBAAkB,GAAG,QAAQ;uGAR7E,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAtB,sBAAsB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,SAAA,EAAA,WAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAQb,mCAAmC,CArC7C,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gCAAA,EAAA,SAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;GAsBT,EAKS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,2NAAE,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAC,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE3B,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAjClC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;oBAC3B,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;AAsBT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,kBAAkB;AACzB,wBAAA,kCAAkC,EAAE;AACrC,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,cAAc;AACvC,iBAAA;8BAEU,WAAW,EAAA,CAAA;sBAAnB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,qBAAqB,EAAA,CAAA;sBAA7B;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBAC0D,kBAAkB,EAAA,CAAA;sBAA5E,KAAK;uBAAC,EAAE,SAAS,EAAE,mCAAmC,EAAE;;;ACrD3D;;;AAGG;MAiCU,sBAAsB,CAAA;IACxB,SAAS,GAAkC,IAAI;AACrC,IAAA,KAAK,GAAG,IAAI,YAAY,EAAc;AAEzD,IAAA,OAAO,CAAC,CAAa,EAAA;QACnB,CAAC,CAAC,cAAc,EAAE;QAClB,CAAC,CAAC,eAAe,EAAE;AACnB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;;uGAPT,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAtB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,sBAAsB,EAbvB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;GAMT,EAKS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,mJAAE,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAD,IAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE7B,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAjBlC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;oBAC3B,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE;;;;;;AAMT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,kBAAkB;AACzB,wBAAA,SAAS,EAAE;AACZ,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,YAAY;AACzC,iBAAA;8BAEU,SAAS,EAAA,CAAA;sBAAjB;gBACkB,KAAK,EAAA,CAAA;sBAAvB;;;ACtCH;;;AAGG;MA8CU,qBAAqB,CAAA;IACvB,QAAQ,GAAG,KAAK;IAChB,KAAK,GAAuC,IAAI;IAChD,SAAS,GAAG,KAAK;IACjB,UAAU,GAAkC,IAAI;IAChD,4BAA4B,GAAqB,IAAI;IACrD,qBAAqB,GAA2C,IAAI;AAC1D,IAAA,MAAM,GAAG,IAAI,YAAY,EAAc;AAE1D,IAAA,IAAc,qBAAqB,GAAA;QACjC,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,4BAA4B;YAC5C,GAAG,IAAI,CAAC;SACT;;AAGH,IAAA,QAAQ,CAAC,CAAa,EAAA;QACpB,CAAC,CAAC,cAAc,EAAE;QAClB,CAAC,CAAC,eAAe,EAAE;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;;uGApBZ,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,qBAAqB,EAzBtB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAA,EAAA,UAAA,EAAA,YAAA,EAAA,4BAAA,EAAA,8BAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,YAAA,EAAA,OAAA,EAAA,0CAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,2BAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;AAiBT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAMS,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAE,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAE,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAC,IAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE7C,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBA7BjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;oBAC1B,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;AAiBT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,2BAA2B;AAClC,wBAAA,cAAc,EAAE,OAAO;AACvB,wBAAA,4CAA4C,EAAE;AAC/C,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,cAAc,EAAE,YAAY;AACzD,iBAAA;8BAEU,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,4BAA4B,EAAA,CAAA;sBAApC;gBACQ,qBAAqB,EAAA,CAAA;sBAA7B;gBACkB,MAAM,EAAA,CAAA;sBAAxB;;;ACxDH;;;AAGG;MAmBU,4BAA4B,CAAA;IAC9B,WAAW,GAA2C,IAAI;uGADxD,4BAA4B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAA5B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,4BAA4B,EAR7B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,kCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;AAIT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAES,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEb,4BAA4B,EAAA,UAAA,EAAA,CAAA;kBAZxC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;oBACjC,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE;;;;AAIT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE;oBACnD,OAAO,EAAE,CAAC,cAAc;AACzB,iBAAA;8BAEU,WAAW,EAAA,CAAA;sBAAnB;;;MC2BU,uBAAuB,CAAA;AAmDxB,IAAA,UAAA;AACA,IAAA,QAAA;AACA,IAAA,YAAA;IApDD,IAAI,GAAkB,IAAI;IAC1B,QAAQ,GAAG,KAAK;IAChB,UAAU,GAAG,KAAK;IAClB,SAAS,GAAG,IAAI;IAChB,YAAY,GAAG,KAAK;IACpB,KAAK,GAAG,EAAE;IACV,SAAS,GAAG,KAAK;AACP,IAAA,WAAW,GAAG,IAAI,YAAY,EAAU;AACxC,IAAA,iBAAiB,GAAG,IAAI,YAAY,EAAW;AACrB,IAAA,YAAY;AACV,IAAA,aAAa;AAE5D,IAAA,mBAAmB,CAAC,WAAoB,EAAA;AACtC,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC;;AAG1C,IAAA,aAAa,CAAC,KAAa,EAAA;AACzB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;AAC5B,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,eAAe,EAAE;;;IAI1B,eAAe,GAAA;AACb,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa;AAChD,QAAA,QAAQ,CAAC,KAAK,GAAG,EAAE;AACnB,QAAA,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;;IAGxB,eAAe,GAAA;QACb,YAAY,CAAC,MAAK;AAChB,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAc,CAAC,aAAa;AACnD,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;AAC7C,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa;YAChD,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC;AAC3C,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,EAAE,aAAa,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAA,MAAA,CAAQ,CAAC;AAC9E,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,SAAS,CAAC,WAAW,CAAA,EAAA,CAAI,CAAC;AACxE,SAAC,CAAC;;IAGJ,KAAK,GAAA;QACH,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC;;IAG3D,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,EAAE;;AAGxC,IAAA,WAAA,CACU,UAAsB,EACtB,QAAmB,EACnB,YAA0B,EAAA;QAF1B,IAAU,CAAA,UAAA,GAAV,UAAU;QACV,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAY,CAAA,YAAA,GAAZ,YAAY;;AAGtB,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa;AAChD,QAAA,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG,OAAO;QAE3C,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,UAAU,CAAC;;iBAC9C;gBACL,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC;;;;AAKhE,QAAA,IAAI,YAAY,IAAI,YAAY,CAAC,YAAY,KAAK,IAAI,IAAI,YAAY,CAAC,aAAa,KAAK,KAAK,EAAE;YAC9F,QAAQ,CAAC,KAAK,EAAE;;;IAIpB,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,eAAe,EAAE;;AAExB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,KAAK,EAAE;;;uGA/EL,uBAAuB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAAD,IAAA,CAAA,YAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,uBAAuB,EAHvB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,6BAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAnBxD,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,cAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,eAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;AAiBT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAGS,WAAW,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAC,IAAA,CAAA,oBAAA,EAAA,QAAA,EAAA,8MAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAA,IAAA,CAAA,eAAA,EAAA,QAAA,EAAA,2CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAA,IAAA,CAAA,OAAA,EAAA,QAAA,EAAA,qDAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,SAAA,EAAA,gBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEV,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBA1BnC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;oBAC5B,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;AAiBT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE,EAAE,KAAK,EAAE,6BAA6B,EAAE;oBAC9C,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;oBAClE,OAAO,EAAE,CAAC,WAAW;AACtB,iBAAA;oIAEU,IAAI,EAAA,CAAA;sBAAZ;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACkB,WAAW,EAAA,CAAA;sBAA7B;gBACkB,iBAAiB,EAAA,CAAA;sBAAnC;gBAC4C,YAAY,EAAA,CAAA;sBAAxD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACI,aAAa,EAAA,CAAA;sBAA3D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,eAAe,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;;;AC7D/C;;;AAGG;MAiGU,2BAA2B,CAAA;IAC7B,IAAI,GAAkB,IAAI;IAC1B,UAAU,GAAG,KAAK;IAClB,WAAW,GAA2C,IAAI;IAC1D,IAAI,GAAG,KAAK;IACkB,WAAW,GAAW,QAAQ;IAC5D,SAAS,GAAG,KAAK;IACjB,QAAQ,GAAG,KAAK;IAChB,IAAI,GAAqB,SAAS;IAClC,cAAc,GAA6D,IAAI;IAC/E,iBAAiB,GAAmD,IAAI;IACxE,UAAU,GAAkC,IAAI;IAChD,aAAa,GAA4B,EAAE;IAC3C,eAAe,GAAa,EAAE;AACpB,IAAA,QAAQ,GAAG,IAAI,YAAY,EAAY;AACvC,IAAA,gBAAgB,GAAG,IAAI,YAAY,EAAU;AAC7C,IAAA,UAAU,GAAG,IAAI,YAAY,EAAyB;AACrC,IAAA,uBAAuB;IAC3D,gBAAgB,GAAiC,EAAE;IACnD,iBAAiB,GAAG,IAAI;IACxB,iBAAiB,GAAG,KAAK;IACzB,WAAW,GAAG,KAAK;IACnB,UAAU,GAAkB,IAAI;IAEhC,sBAAsB,GAAA;QACpB,MAAM,oBAAoB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC;AAC5D,QAAA,IAAI,CAAC,iBAAiB,GAAG,oBAAoB,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU;AACtF,QAAA,IAAI,CAAC,iBAAiB,GAAG,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU;;AAGzF,IAAA,iBAAiB,CAAC,WAAoB,EAAA;AACpC,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW;QAC9B,IAAI,CAAC,sBAAsB,EAAE;;AAG/B,IAAA,kBAAkB,CAAC,KAAa,EAAA;AAC9B,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU,EAAE;AAC7B,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK;YACvB,IAAI,CAAC,sBAAsB,EAAE;AAC7B,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC;YACjC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC;;;IAInD,aAAa,CAAC,UAAkB,EAAE,eAAyB,EAAA;AACzD,QAAA,MAAM,kBAAkB,GAAG,CAAC,GAAW,EAAE,UAAoB,KAAa;;AAExE,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AAC1C,gBAAA,IAAI,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AACtC,oBAAA,OAAO,IAAI;;;AAGf,YAAA,OAAO,KAAK;AACd,SAAC;AACD,QAAA,MAAM,iBAAiB,GAAG,CAAC,GAAW,EAAE,UAAoB,KAAc;AACxE,YAAA,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,CAAA,CAAA,EAAI,UAAU,CAAC,IAAI,EAAE,CAAG,CAAA,CAAA,CAAC;AAChD,YAAA,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC;YACnD,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;AAC5B,SAAC;AACD,QAAA,IACE,UAAU;AACV,YAAA,UAAU,CAAC,MAAM;AACjB,YAAA,eAAe,CAAC,MAAM;YACtB,IAAI,CAAC,IAAI,KAAK,SAAS;AACvB,YAAA,kBAAkB,CAAC,UAAU,EAAE,eAAe,CAAC,EAC/C;YACA,MAAM,WAAW,GAAG,iBAAiB,CAAC,UAAU,EAAE,eAAe,CAAC;AAClE,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC;;;IAInC,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAChC,YAAA,IAAI,CAAC,uBAAuB,CAAC,eAAe,EAAE;;;IAIlD,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAChC,YAAA,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE;;;IAIxC,IAAI,GAAA;AACF,QAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAChC,YAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE;;;AAIvC,IAAA,YAAY,CAAC,IAA2B,EAAA;QACtC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACtC,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;;;AAItB,IAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AAC/B,IAAA,UAAU,GAAG,MAAM,EAAC,UAAuB,EAAC;AAC5C,IAAA,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC/B,IAAA,WAAW,GAAG,MAAM,CAAC,sBAAsB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAE5E,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,cAAc,EAAE,iBAAiB,EAAE,GAAG,OAAO;QACjF,IAAI,aAAa,EAAE;YACjB,IAAI,CAAC,sBAAsB,EAAE;;QAE/B,IAAI,aAAa,IAAI,WAAW,IAAI,cAAc,IAAI,iBAAiB,EAAE;YACvE,MAAM,gBAAgB,GAAiC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK;gBAC7G,OAAO,EAAE,CAAC,CAAC,OAAO;gBAClB,OAAO,EAAE,CAAC,CAAC,OAAO;gBAClB,UAAU,EAAE,CAAC,CAAC,UAAU;gBACxB,qBAAqB,EAAE,IAAI,CAAC,cAAc;AAC1C,gBAAA,4BAA4B,EAAE;AAC/B,aAAA,CAAC,CAAC;YACH,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE;AAChD,gBAAA,MAAM,aAAa,GAAG,CAAK,EAAA,EAAA,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,MAAM;AAC7E,gBAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC;AACxE,gBAAA,MAAM,YAAY,GAAG;AACnB,oBAAA,OAAO,EAAE,aAAa;AACtB,oBAAA,OAAO,EAAE,sBAAsB;AAC/B,oBAAA,UAAU,EAAE,IAAI;oBAChB,qBAAqB,EAAE,IAAI,CAAC,iBAAiB;oBAC7C,4BAA4B,EAAE,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;iBACzE;AACD,gBAAA,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC;;AAErC,YAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;;;IAI5C,QAAQ,GAAA;QACN,uBAAuB,CAAa,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,OAAO;AACvE,aAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC;aACxC,SAAS,CAAC,KAAK,IAAG;;AAEjB,YAAA,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,aAAa,EAAE;AAC5E,gBAAA,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE;;AAExC,SAAC,CAAC;QAEJ,uBAAuB,CAAgB,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,SAAS;AAC5E,aAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC;aACxC,SAAS,CAAC,KAAK,IAAG;AACjB,YAAA,IAAI,KAAK,CAAC,MAAM,YAAY,gBAAgB,EAAE;AAC5C,gBAAA,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK;gBAErC,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC1G,KAAK,CAAC,cAAc,EAAE;;oBAEtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;;AAGjG,SAAC,CAAC;;uGAvJK,2BAA2B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAA3B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,2BAA2B,EAKlB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,IAAA,EAAA,MAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,eAAe,CAYxB,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,IAAA,EAAA,MAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,aAAA,EAAA,eAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,yBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,uBAAuB,EA5ExB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAES,uBAAuB,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,cAAA,EAAA,OAAA,EAAA,WAAA,CAAA,EAAA,OAAA,EAAA,CAAA,aAAA,EAAA,mBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,qBAAqB,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,OAAA,EAAA,WAAA,EAAA,YAAA,EAAA,8BAAA,EAAA,uBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,4BAA4B,EAAA,QAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,CAAA,aAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE3E,2BAA2B,EAAA,UAAA,EAAA,CAAA;kBAjEvC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,mBAAmB,EAAE,KAAK;oBAC1B,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE,EAAE,KAAK,EAAE,qBAAqB,EAAE;AACtC,oBAAA,OAAO,EAAE,CAAC,uBAAuB,EAAE,qBAAqB,EAAE,4BAA4B;AACvF,iBAAA;8BAEU,IAAI,EAAA,CAAA;sBAAZ;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACsC,WAAW,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBAC5B,SAAS,EAAA,CAAA;sBAAjB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACkB,QAAQ,EAAA,CAAA;sBAA1B;gBACkB,gBAAgB,EAAA,CAAA;sBAAlC;gBACkB,UAAU,EAAA,CAAA;sBAA5B;gBACmC,uBAAuB,EAAA,CAAA;sBAA1D,SAAS;uBAAC,uBAAuB;;;AClCpC,MAAM,mBAAmB,GAAuB,CAAC,WAAmB,EAAE,IAA2B,KAAa;AAC5G,IAAA,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC;;SAC/E;AACL,QAAA,OAAO,KAAK;;AAEhB,CAAC;AAED,MAAM,qBAAqB,GAAgB,QAAQ;IAyItC,iBAAiB,GAAA,CAAA,MAAA;;;;;;;;;;;;;iBAAjB,iBAAiB,CAAA;;;AAMlB,YAAA,4BAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAYrB,YAAA,wBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAY4B,YAAA,wBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AASZ,YAAA,sBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;YAjC9B,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,4BAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,kBAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,gBAAgB,EAAhB,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,gBAAgB,GAAM,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,8BAAA,EAAA,mCAAA,CAAA;YAa7C,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,wBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,cAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,cAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,YAAY,EAAZ,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,YAAY,GAAgD,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,0BAAA,EAAA,+BAAA,CAAA;YAWN,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,wBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,cAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,cAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,YAAY,EAAZ,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,YAAY,GAAS,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,0BAAA,EAAA,+BAAA,CAAA;YASrB,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,YAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,UAAU,EAAV,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,UAAU,GAAS,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,wBAAA,EAAA,6BAAA,CAAA;;;QA8V/D,MAAM;QACN,QAAQ;QACT,eAAe;QACd,GAAG;QACH,IAAI;QACJ,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,cAAc;QA5Yf,aAAa,GAAgB,qBAAqB;QAElD,IAAI,GAAkB,IAAI;QAC1B,MAAM,GAAqB,SAAS;QACpC,QAAQ,GAAa,EAAE;QACT,gBAAgB,GAAA,iBAAA,CAAA,IAAA,EAAA,8BAAA,EAAG,EAAE,CAAC;QACpC,oBAAoB,IAAA,iBAAA,CAAA,IAAA,EAAA,mCAAA,CAAA,EAAG,CAAC;QACxB,mBAAmB,GAA6B,IAAI;QACpD,0BAA0B,GAAG,IAAI;QACjC,eAAe,GAAkC,IAAI;QACrD,iBAAiB,GAAgD,SAAS;QAC1E,aAAa,GAA2C,IAAI;QAC5D,WAAW,GAAiC,IAAI;QAChD,aAAa,GAAG,QAAQ;QACxB,gBAAgB,GAAkC,IAAI;QACtD,gBAAgB,GAA6D,IAAI;QAG1F,YAAY,GAAA,iBAAA,CAAA,IAAA,EAAA,0BAAA,EAA2C,IAAI,CAAC;QACnD,WAAW,IAAA,iBAAA,CAAA,IAAA,EAAA,+BAAA,CAAA,EAAkC,IAAI;QACjD,YAAY,GAAkC,IAAI;QAClD,sBAAsB,GAAkC,IAAI;QAC5D,iBAAiB,GAAa,EAAE;QAChC,mBAAmB,GAAmD,IAAI;QACxB,kBAAkB,GAAG,QAAQ;QAC/E,MAAM,GAAqB,SAAS;QACpC,cAAc,GAAuB,mBAAmB;QACxD,WAAW,GAA8C,CAAC,EAAa,EAAE,EAAa,KAAK,EAAE,KAAK,EAAE;QACrE,YAAY,GAAG,KAAK;QACN,YAAY,GAAA,iBAAA,CAAA,IAAA,EAAA,0BAAA,EAAG,KAAK,CAAC;QACnC,YAAY,IAAA,iBAAA,CAAA,IAAA,EAAA,+BAAA,CAAA,EAAG,KAAK;QACpB,SAAS,GAAG,KAAK;QACjB,WAAW,GAAG,KAAK;QACnB,sBAAsB,GAAG,IAAI;QAC7B,cAAc,GAAG,KAAK;QACtB,UAAU,GAAG,KAAK;QAClB,MAAM,GAAG,KAAK;QACd,aAAa,GAAG,KAAK;QACP,UAAU,GAAA,iBAAA,CAAA,IAAA,EAAA,wBAAA,EAAG,KAAK,CAAC;QAChE,SAAS,IAAA,iBAAA,CAAA,IAAA,EAAA,6BAAA,CAAA,EAA8B,EAAE;QAElD,IACI,WAAW,CAAC,KAAc,EAAA;AAC5B,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK;;AAE3B,QAAA,IAAI,WAAW,GAAA;AACb,YAAA,OAAO,IAAI,CAAC,YAAY,KAAK,SAAS,GAAG,IAAI,CAAC,MAAM,KAAK,SAAS,GAAG,IAAI,CAAC,YAAY;;AAGxF,QAAA,IAAI,UAAU,GAAA;YACZ,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM;;AAG7D,QAAA,IAAI,qBAAqB,GAAA;YACvB,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,kBAAkB,KAAK,QAAQ;;AAGhE,QAAA,IAAI,yBAAyB,GAAA;AAC3B,YAAA,OAAO,IAAI,CAAC,kBAAkB,KAAK,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC,kBAAkB;;AAGjF,QAAA,UAAU,GAAG,IAAI,YAAY,EAAU;AACvC,QAAA,gBAAgB,GAAG,IAAI,YAAY,EAAQ;AAC3C,QAAA,YAAY,GAAG,IAAI,YAAY,EAAW;AAC1C,QAAA,MAAM,GAAG,IAAI,YAAY,EAAQ;AACjC,QAAA,OAAO,GAAG,IAAI,YAAY,EAAQ;AACY,QAAA,aAAa;AAC5B,QAAA,mBAAmB;AACX,QAAA,2BAA2B;AAC1B,QAAA,uBAAuB;AAElF,QAAA,4BAA4B;AAC2C,QAAA,6BAA6B;AAEpG,QAAA,kCAAkC;AAExB,QAAA,SAAS,GAAG,QAAQ,CAAC,MAAK;AAClC,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,OAAO,IAAI,CAAC,WAAW,EAAE;;AAE3B,YAAA,OAAO,IAAI,CAAC,IAAI,EAAE;AACpB,SAAC,CAAC;AAEM,QAAA,IAAI,GAAG,MAAM,CAAgB,IAAI,CAAC,MAAM,CAAC;QACzC,WAAW,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAC/D,QAAA,YAAY,GAAG,IAAI,eAAe,CAAc,EAAE,CAAC;AACnD,QAAA,mBAAmB,GAAG,IAAI,eAAe,CAA0B,EAAE,CAAC;QACtE,wBAAwB,GAA4B,EAAE;QACtD,WAAW,GAAW,EAAE;QACxB,gBAAgB,GAAG,KAAK;AACxB,QAAA,KAAK;AACL,QAAA,YAAY;QACZ,SAAS,GAAW,CAAC,CAAC;QACtB,sBAAsB,GAAY,IAAI;AAE9C,QAAA,QAAQ,GAAiB,MAAK,GAAG;AACjC,QAAA,SAAS,GAAkB,MAAK,GAAG;QACnC,gBAAgB,GAA0B,YAAY;QACtD,YAAY,GAAkB,IAAI;QAClC,mBAAmB,GAA4B,EAAE;QACjD,aAAa,GAA4B,EAAE;QAC3C,cAAc,GAAqB,IAAI;QACvC,WAAW,GAAgB,EAAE;QAC7B,OAAO,GAAG,KAAK;QACf,GAAG,GAAc,KAAK;QACtB,SAAS,GAA6B,EAAE;;QAGxC,SAAS,GAAW,YAAY;QAChC,SAAS,GAAqB,EAAE;QAChC,MAAM,GAAqB,EAAE;QAC7B,WAAW,GAAY,KAAK;AAE5B,QAAA,eAAe,CAAC,KAAa,EAAA;YAC3B,OAAO;AACL,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,IAAI,EAAE;aACP;;AAGH,QAAA,WAAW,CAAC,KAAgB,EAAA;AAC1B,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC3B,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;gBAC7B,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE;AAClF,oBAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,CAAC;;AAEjC,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;iBACnB;gBACL,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC/E,gBAAA,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;AACtB,oBAAA,MAAM,uBAAuB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,WAAW,CAAC;AACpF,oBAAA,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC;;qBAC1C,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE;oBAC5D,MAAM,qBAAqB,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC;AAC1D,oBAAA,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC;;gBAE/C,IAAI,CAAC,KAAK,EAAE;AACZ,gBAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;oBAC/B,IAAI,CAAC,UAAU,EAAE;;;;AAKvB,QAAA,YAAY,CAAC,IAA2B,EAAA;YACtC,MAAM,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5F,YAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC;YAC3C,IAAI,CAAC,UAAU,EAAE;;QAGnB,yBAAyB,GAAA;AACvB,YAAA,IAAI,mBAAmB,GAAG,IAAI,CAAC;iBAC5B,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;iBAC3B,MAAM,CAAC,IAAI,IAAG;gBACb,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,WAAW,EAAE;oBAC5C,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;;qBAC7C;AACL,oBAAA,OAAO,IAAI;;AAEf,aAAC,CAAC;YACJ,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;gBAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC;gBACjG,IAAI,CAAC,WAAW,EAAE;oBAChB,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC;AACtD,oBAAA,mBAAmB,GAAG,CAAC,OAAO,EAAE,GAAG,mBAAmB,CAAC;AACvD,oBAAA,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,OAAO;;qBAChC;AACL,oBAAA,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,OAAO;;;AAG7C,YAAA,MAAM,aAAa,GACjB,mBAAmB,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC;AACnE,gBAAA,mBAAmB,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBACrF,mBAAmB,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrF,mBAAmB,CAAC,CAAC,CAAC;AACxB,YAAA,IAAI,CAAC,cAAc,GAAG,CAAC,aAAa,IAAI,aAAa,CAAC,OAAO,KAAK,IAAI;YACtE,IAAI,gBAAgB,GAA2D,EAAE;AACjF,YAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,gBAAA,gBAAgB,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,UAAW,CAAC,CAAC,CAAC;;iBAC5F;AACL,gBAAA,IAAI,IAAI,CAAC,4BAA4B,EAAE;AACrC,oBAAA,gBAAgB,GAAG,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;;;;AAI5E,YAAA,gBAAgB,CAAC,OAAO,CAAC,KAAK,IAAG;AAC/B,gBAAA,MAAM,KAAK,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC;AAC9E,gBAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACd,oBAAA,MAAM,SAAS,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAA2B;oBAC3F,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC;;AAEnD,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,mBAAmB,GAAG,CAAC,GAAG,mBAAmB,CAAC;YACnD,IAAI,CAAC,kCAAkC,EAAE;;QAG3C,UAAU,GAAA;AACR,YAAA,IAAI,CAAC,2BAA2B,CAAC,eAAe,EAAE;;AAGpD,QAAA,iBAAiB,CAAC,WAAwB,EAAA;AACxC,YAAA,MAAM,iBAAiB,GAAG,CAAC,IAAiB,EAAE,IAAsB,KAA6B;AAC/F,gBAAA,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,oBAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACnB,wBAAA,OAAO,IAAI,CAAC,CAAC,CAAC;;yBACT;AACL,wBAAA,OAAO,IAAI;;;qBAER;AACL,oBAAA,OAAO,IAAI;;AAEf,aAAC;YACD,MAAM,KAAK,GAAG,iBAAiB,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC;AACzD,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;AACxB,gBAAA,IAAI,CAAC,WAAW,GAAG,WAAW;AAC9B,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,gBAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;;AAI7B,QAAA,eAAe,CAAC,WAAqB,EAAA;AACnC,YAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC;iBAC7B,MAAM,CAAC,IAAI,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;iBAC5E,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO;AACxB,iBAAA,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACpF;;AAEG;YACH,MAAM,mBAAmB,GAAG,CAAI,KAAU,KACxC,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,KAAK;AAE9E,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE;AAC9B,gBAAA,MAAM,WAAW,GAAG,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,kBAAkB,CAAC,CAAC;AACrF,gBAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;;AAC9B,iBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE;AACjC,gBAAA,MAAM,oBAAoB,GAAG,WAAW,CAAC,MAAM,CAC7C,KAAK,IAAI,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CACxF;AACD,gBAAA,MAAM,WAAW,GAAG,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,kBAAkB,EAAE,GAAG,oBAAoB,CAAC,CAAC;AAC9G,gBAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;;YAErC,IAAI,CAAC,UAAU,EAAE;;AAGnB,QAAA,SAAS,CAAC,CAAgB,EAAA;AACxB,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB;;AAEF,YAAA,MAAM,+BAA+B,GAAG,IAAI,CAAC;iBAC1C,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;iBACnC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACnC,MAAM,cAAc,GAAG,+BAA+B,CAAC,SAAS,CAAC,IAAI,IACnE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CACpD;AACD,YAAA,QAAQ,CAAC,CAAC,OAAO;AACf,gBAAA,KAAK,QAAQ;oBACX,CAAC,CAAC,cAAc,EAAE;oBAClB,IAAI,IAAI,CAAC,MAAM,IAAI,+BAA+B,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7D,wBAAA,MAAM,QAAQ,GAAG,cAAc,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,+BAA+B,CAAC,MAAM,GAAG,CAAC;wBACrG,IAAI,CAAC,cAAc,GAAG,+BAA+B,CAAC,QAAQ,CAAC,CAAC,OAAO;;oBAEzE;AACF,gBAAA,KAAK,UAAU;oBACb,CAAC,CAAC,cAAc,EAAE;oBAClB,IAAI,IAAI,CAAC,MAAM,IAAI,+BAA+B,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7D,wBAAA,MAAM,SAAS,GAAG,cAAc,GAAG,+BAA+B,CAAC,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,CAAC;wBACtG,IAAI,CAAC,cAAc,GAAG,+BAA+B,CAAC,SAAS,CAAC,CAAC,OAAO;;yBACnE;AACL,wBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;;oBAEzB;AACF,gBAAA,KAAK,KAAK;oBACR,CAAC,CAAC,cAAc,EAAE;AAClB,oBAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,wBAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE;AAC1D,4BAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC;;;yBAElC;AACL,wBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;;oBAEzB;AACF,gBAAA,KAAK,KAAK;AACR,oBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,wBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;wBACvB,CAAC,CAAC,cAAc,EAAE;;oBAEpB;AACF,gBAAA,KAAK,GAAG;AACN,oBAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,wBAAA,IAAI,IAAI,CAAC,MAAM,EAAE;4BACf,CAAC,CAAC,cAAc,EAAE;AAClB,4BAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;AACjC,gCAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC;;;;yBAGpC;AACL,wBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;oBAE1B;AACF,gBAAA,KAAK,MAAM;AACT;;AAEG;oBACH;AACF,gBAAA;AACE,oBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,wBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;;;;AAK/B,QAAA,YAAY,CAAC,KAAc,EAAA;AACzB,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;AACzB,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC7B,IAAI,CAAC,YAAY,EAAE;AACnB,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;;QAI3B,YAAY,GAAA;YACV,IAAI,CAAC,+BAA+B,EAAE;AACtC,YAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC/B,IAAI,CAAC,UAAU,EAAE;;;AAIrB,QAAA,kBAAkB,CAAC,KAAa,EAAA;AAC9B,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;YACxB,IAAI,CAAC,yBAAyB,EAAE;AAChC,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;YAC3B,IAAI,CAAC,kCAAkC,EAAE;;QAG3C,gBAAgB,GAAA;AACd,YAAA,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;;AAG5B,QAAA,cAAc,CAAC,KAAiB,EAAA;AAC9B,YAAA,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC;AACrC,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAc,CAAC,EAAE;AACrD,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;;QAI5B,KAAK,GAAA;AACH,YAAA,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE;;QAG1C,IAAI,GAAA;AACF,YAAA,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE;;AAGzC,QAAA,gBAAgB,CAAC,QAAwC,EAAA;AACvD,YAAA,MAAM,SAAS,GAAG,gBAAgB,CAAC,QAAQ,CAAC;AAC5C,YAAA,IAAI,CAAC,gBAAgB,GAAG,SAAkC;;QAG5D,+BAA+B,GAAA;AAC7B,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;AAC/D,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY;AACtC,gBAAA,2BAA2B,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3C,gBAAA,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,MAAK;;;AAGjC,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,KAAK;AAClF,oBAAA,IAAI,YAAY,KAAK,IAAI,CAAC,YAAY,EAAE;;;;AAItC,wBAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;;AAE5B,iBAAC,CAAC;;;QAIN,kCAAkC,GAAA;YAChC,YAAY,CAAC,MAAK;AAChB,gBAAA,IAAI,CAAC,mBAAmB,EAAE,UAAU,EAAE,cAAc,EAAE;AACxD,aAAC,CAAC;;AAGJ,QAAA,WAAW,GAAG,MAAM,CAAC,sBAAsB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;QAClE,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;QACvE,qBAAqB,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAEjF,QAAA,WAAA,CACU,MAAc,EACd,QAA0B,EAC3B,eAAgC,EAC/B,GAAsB,EACtB,IAA6B,EAC7B,QAAmB,EACnB,QAAkB,EAClB,YAA0B,EAC1B,cAA8B,EAAA;YAR9B,IAAM,CAAA,MAAA,GAAN,MAAM;YACN,IAAQ,CAAA,QAAA,GAAR,QAAQ;YACT,IAAe,CAAA,eAAA,GAAf,eAAe;YACd,IAAG,CAAA,GAAA,GAAH,GAAG;YACH,IAAI,CAAA,IAAA,GAAJ,IAAI;YACJ,IAAQ,CAAA,QAAA,GAAR,QAAQ;YACR,IAAQ,CAAA,QAAA,GAAR,QAAQ;YACR,IAAY,CAAA,YAAA,GAAZ,YAAY;YACZ,IAAc,CAAA,cAAA,GAAd,cAAc;;AAGxB,QAAA,UAAU,CAAC,UAAmC,EAAA;;AAE5C,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,UAAU,EAAE;AAC7B,gBAAA,IAAI,CAAC,KAAK,GAAG,UAAU;AACvB,gBAAA,MAAM,iBAAiB,GAAG,CAAC,KAA8B,EAAE,IAAsB,KAAiB;oBAChG,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;AACzC,wBAAA,OAAO,EAAE;;AACJ,yBAAA,IAAI,IAAI,KAAK,SAAS,EAAE;wBAC7B,OAAO,CAAC,KAAK,CAAC;;yBACT;AACL,wBAAA,OAAO,KAAK;;AAEhB,iBAAC;gBACD,MAAM,WAAW,GAAG,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC;AAC9D,gBAAA,IAAI,CAAC,WAAW,GAAG,WAAW;AAC9B,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;;AAI3B,QAAA,gBAAgB,CAAC,EAAgB,EAAA;AAC/B,YAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAGpB,QAAA,iBAAiB,CAAC,EAAiB,EAAA;AACjC,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB,QAAA,gBAAgB,CAAC,QAAiB,EAAA;AAChC,YAAA,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ;AAC9E,YAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;AACnC,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;AAE1B,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,QAAA,WAAW,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAiB,EAAA;YACzF,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,YAAY,EAAE;;AAErB,YAAA,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE;AACjC,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;YAE1B,IAAI,SAAS,EAAE;AACb,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;AAC5B,gBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE;gBAC1C,MAAM,qBAAqB,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,IAAG;oBACrD,OAAO;AACL,wBAAA,QAAQ,EAAE,IAAI,CAAC,KAAK,YAAY,WAAW,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI;AAC/D,wBAAA,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;wBAC9C,OAAO,EAAE,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI;wBAC7F,OAAO,EAAE,IAAI,CAAC,KAAK;AACnB,wBAAA,UAAU,EAAE,IAAI,CAAC,QAAQ,IAAI,KAAK;AAClC,wBAAA,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK;AAC1B,wBAAA,eAAe,EAAE,IAAI,CAAC,KAAK,YAAY,WAAW;AAClD,wBAAA,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI;AACnC,wBAAA,IAAI,EAAE,MAAM;AACZ,wBAAA,GAAG,EAAE,IAAI,CAAC,GAAG,KAAK,SAAS,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;qBACjD;AACH,iBAAC,CAAC;AACF,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC;;YAEtD,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC;;YAEvD,IAAI,WAAW,EAAE;AACf,gBAAA,MAAM,EAAE,YAAY,EAAE,GAAG,WAAW;AACpC,gBAAA,IAAI,CAAC,gBAAgB,GAAG,YAAqC;gBAC7D,MAAM,eAAe,GAAG,CAAC,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,CAAC;gBAC5E,IAAI,YAAY,IAAI,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;oBAC1D,IAAI,CAAC,SAAS,GAAG,CAAC,YAAY,CAAC,YAA6B,CAAC,CAAC;;qBACzD;AACL,oBAAA,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,IAAI,YAAY,CAAC,CAAkB,CAAC,CAAC;;;YAG/E,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC;;;QAItC,QAAQ,GAAA;YACN,IAAI,CAAC,mBAAmB,EAAE;iBACvB,IAAI,CACH,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,KAAI;AAChC,gBAAA,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,KAAK,GAAG,CAAC,WAAW;AACzE,aAAC,CAAC,EACF,cAAc,CAAC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,GAAGC,EAAY,CAAC,KAAK,CAAC,CAAC,EAC1G,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,GAAG,EAAE,GAAG,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC,EAC/F,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAEzB,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,KAAI;AACrC,gBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,CAAC;AAC3C,aAAC,CAAC;AAEJ,YAAA,IAAI,CAAC;AACF,iBAAA,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI;AACvB,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,WAAW,IAAG;gBACvB,IAAI,CAAC,WAAW,EAAE;AAChB,oBAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACpB,oBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACvB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AAClB,oBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;wBAC1B,IAAI,CAAC,SAAS,EAAE;AAClB,qBAAC,CAAC;;qBACG;AACL,oBAAA,IAAI,CAAC,OAAO,GAAG,IAAI;AACnB,oBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACvB,oBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;;AAEvB,aAAC,CAAC;YACJ,aAAa,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC;AACxD,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,CAAC,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,KAAI;gBACvD,MAAM,aAAa,GAAG;qBACnB,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,KAAK,MAAM;qBACnC,MAAM,CAAC,KAAK,IAAI,kBAAkB,CAAC,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;AAC5F,qBAAA,GAAG,CACF,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CACzG;gBACH,IAAI,CAAC,wBAAwB,GAAG,CAAC,GAAG,kBAAkB,EAAE,GAAG,aAAa,CAAC;AACzE,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AACvB,qBAAA,GAAG,CACF,CAAC,IACC,CAAC,GAAG,IAAI,CAAC,wBAAwB,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAE;qBAE7G,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC;gBACzB,IAAI,CAAC,yBAAyB,EAAE;AAClC,aAAC,CAAC;YAEJ,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,gBAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,gBAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,aAAC,CAAC;AAEF,YAAA,IAAI,CAAC;iBACF,gCAAgC,CAAC,QAAQ;AACzC,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,MAAK;gBACd,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;AAC1B,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;YAEJ,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;YAEpC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO;AACrD,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,MAAK;AACd,gBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,UAAU,EAAE;oBACzD;;AAGF,gBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACxD,aAAC,CAAC;;;;;;AAOJ,YAAA,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AACvF,gBAAA,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;AAC5B,oBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;AAE5B,aAAC,CAAC;;QAGJ,kBAAkB,GAAA;AAChB,YAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC1B,gBAAA,KAAK,CAAC,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,IAAI,CAAC,uBAAuB,CAAC,OAAO;AAClF,qBAAA,IAAI,CACH,SAAS,CAAC,IAAI,CAAC,EACf,SAAS,CAAC,MACR,KAAK,CACH,GAAG;oBACD,IAAI,CAAC,uBAAuB,CAAC,OAAO;oBACpC,IAAI,CAAC,4BAA4B,CAAC,OAAO;AACzC,oBAAA,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC;AAC7D,oBAAA,GAAG,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO;AAClE,iBAAA,CACF,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CACxB,EACD,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;qBAEzB,SAAS,CAAC,MAAK;AACd,oBAAA,MAAM,qBAAqB,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,IAAG;AAC9E,wBAAA,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,UAAU,EAAE,GAAG,IAAI;wBACnG,OAAO;4BACL,QAAQ;4BACR,OAAO;4BACP,OAAO;4BACP,UAAU;4BACV,MAAM;4BACN,eAAe;4BACf,UAAU;AACV,4BAAA,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC;AAClD,4BAAA,IAAI,EAAE,MAAM;4BACZ,GAAG,EAAE,KAAK,KAAK,SAAS,GAAG,OAAO,GAAG;yBACtC;AACH,qBAAC,CAAC;AACF,oBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC;AACpD,oBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,iBAAC,CAAC;;;QAIR,WAAW,GAAA;AACT,YAAA,2BAA2B,CAAC,IAAI,CAAC,SAAS,CAAC;YAC3C,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;;QAGrC,eAAe,CAAC,MAAwB,EAAE,WAAoB,EAAA;AACpE,YAAA,IAAI,CAAC,MAAM,GAAG,MAAM;AACpB,YAAA,IAAI,CAAC,WAAW,GAAG,WAAW;AAC9B,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAEvB,YAAA,IAAI,CAAC,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,CAAC;AACzE,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,IAAG;AAC3C,gBAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AAC1B,oBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC;;qBAClD;AACL,oBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC;;AAE9D,aAAC,CAAC;;QAGI,QAAQ,CAAC,KAAuC,EAAE,KAAuC,EAAA;YAC/F,IAAI,QAAQ,GAAW,SAAU;AACjC,YAAA,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC1D,oBAAA,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE;;;iBAExB,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACjE,gBAAA,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE;;AAG7B,YAAA,OAAO,QAAQ;;2GA7nBN,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAAF,IAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAAE,IAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;+FAAjB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,0BAAA,EAAA,4BAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,WAAA,EAAA,aAAA,EAAA,aAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,WAAA,EAAA,aAAA,EAAA,YAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,wBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,kBAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAyBR,mCAAmC,CAAA,EAAA,MAAA,EAAA,QAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAInC,gBAAgB,CAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAChB,gBAAgB,CAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAChB,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAChB,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAChB,gBAAgB,CAAA,EAAA,sBAAA,EAAA,CAAA,wBAAA,EAAA,wBAAA,EAChB,gBAAgB,CAChB,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAChB,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,gBAAgB,CAChB,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAGhB,EAAA,SAAA,EAAA,WAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,CA3KzB,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,+BAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,2BAAA,EAAA,qBAAA,EAAA,2BAAA,EAAA,6BAAA,EAAA,aAAA,EAAA,2BAAA,EAAA,YAAA,EAAA,8BAAA,EAAA,uDAAA,EAAA,8BAAA,EAAA,cAAA,EAAA,6BAAA,EAAA,cAAA,EAAA,uBAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,mBAAA,EAAA,yBAAA,EAAA,sBAAA,EAAA,2BAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,eAAA,EAAA,EAAA,cAAA,EAAA,YAAA,EAAA,EAAA,SAAA,EAAA;gBACT,gBAAgB;AAChB,gBAAA;AACE,oBAAA,OAAO,EAAE,iBAAiB;AAC1B,oBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,iBAAiB,CAAC;AAChD,oBAAA,KAAK,EAAE;AACR,iBAAA;AACD,gBAAA,EAAE,OAAO,EAAE,0BAA0B,EAAE,QAAQ,EAAE,QAAQ;AAC1D,aAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,yBAAA,EAAA,SAAA,EA+LgB,iBAAiB,EACjB,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,8BAAA,EAAA,SAAA,EAAA,sBAAsB,+FAJ5B,gBAAgB,EAAA,WAAA,EAAA,IAAA,EAAA,IAAA,EAAwB,UAAU,EAClD,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,mBAAmB,4GACnB,2BAA2B,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,+BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAI3B,sBAAsB,EAAwB,WAAA,EAAA,IAAA,EAAA,IAAA,EAAA,UAAU,gGACxD,2BAA2B,EAAA,WAAA,EAAA,IAAA,EAAA,IAAA,EAAwB,UAAU,EA/L9D,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,cAAA,EAAA,CAAA,EAAA,SAAA,EAAA,EAAA,CAAA,2BAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuFT,EAmBC,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,2BAA2B,6VAC3B,gBAAgB,EAAA,QAAA,EAAA,4DAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAChB,sBAAsB,EACtB,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,sBAAsB,oMACtB,+BAA+B,EAAA,QAAA,EAAA,4BAAA,EAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAC/B,sBAAsB,EACtB,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,OAAA,EAAA,CAAA,OAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,mBAAmB,2+BACnB,eAAe,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,2BAAA,EAAA,QAAA,EAAA,2CAAA,EAAA,MAAA,EAAA,CAAA,sBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACf,0BAA0B,EAnHhB,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,sBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,qBAAA,EAAA,aAAA,EAAA,MAAA,EAAA,YAAA,EAAA,UAAA,EAAA,eAAA,EAAA,2BAAA,EAAA,qBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,EAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA,CAAC,WAAW,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;;2FAsHd,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBArI7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,SAAS,EAAE;wBACT,gBAAgB;AAChB,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,uBAAuB,CAAC;AAChD,4BAAA,KAAK,EAAE;AACR,yBAAA;AACD,wBAAA,EAAE,OAAO,EAAE,0BAA0B,EAAE,QAAQ,EAAE,QAAQ;AAC1D,qBAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,UAAU,EAAE,CAAC,WAAW,CAAC;AACzB,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuFT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,YAAY;AACnB,wBAAA,iCAAiC,EAAE,uBAAuB;AAC1D,wBAAA,uBAAuB,EAAE,yBAAyB;AAClD,wBAAA,uBAAuB,EAAE,yBAAyB;AAClD,wBAAA,+BAA+B,EAAE,CAAa,WAAA,CAAA;AAC9C,wBAAA,6BAA6B,EAAE,YAAY;AAC3C,wBAAA,gCAAgC,EAAE,CAAuD,qDAAA,CAAA;AACzF,wBAAA,gCAAgC,EAAE,cAAc;AAChD,wBAAA,+BAA+B,EAAE,cAAc;AAC/C,wBAAA,yBAAyB,EAAE,QAAQ;AACnC,wBAAA,4BAA4B,EAAE,mBAAmB;AACjD,wBAAA,2BAA2B,EAAE,CAAsB,oBAAA,CAAA;AACnD,wBAAA,6BAA6B,EAAE,CAAsB,oBAAA,CAAA;AACrD,wBAAA,wBAAwB,EAAE,CAAe,aAAA;AAC1C,qBAAA;oBACD,cAAc,EAAE,CAAC,2BAA2B,CAAC;AAC7C,oBAAA,OAAO,EAAE;wBACP,2BAA2B;wBAC3B,gBAAgB;wBAChB,sBAAsB;wBACtB,sBAAsB;wBACtB,+BAA+B;wBAC/B,sBAAsB;wBACtB,mBAAmB;wBACnB,eAAe;wBACf;AACD;AACF,iBAAA;8SAIU,IAAI,EAAA,CAAA;sBAAZ;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACsB,gBAAgB,EAAA,CAAA;sBAAtC;gBACQ,oBAAoB,EAAA,CAAA;sBAA5B;gBACQ,mBAAmB,EAAA,CAAA;sBAA3B;gBACQ,0BAA0B,EAAA,CAAA;sBAAlC;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,gBAAgB,EAAA,CAAA;sBAAxB;gBACQ,gBAAgB,EAAA,CAAA;sBAAxB;gBAGD,YAAY,EAAA,CAAA;sBAFX;gBAGQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,sBAAsB,EAAA,CAAA;sBAA9B;gBACQ,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,mBAAmB,EAAA,CAAA;sBAA3B;gBAC0D,kBAAkB,EAAA,CAAA;sBAA5E,KAAK;uBAAC,EAAE,SAAS,EAAE,mCAAmC,EAAE;gBAChD,MAAM,EAAA,CAAA;sBAAd;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACuC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACgB,YAAY,EAAA,CAAA;sBAAjE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,sBAAsB,EAAA,CAAA;sBAA7D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACgB,UAAU,EAAA,CAAA;sBAA/D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,SAAS,EAAA,CAAA;sBAAjB;gBAGG,WAAW,EAAA,CAAA;sBADd,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAoBnB,UAAU,EAAA,CAAA;sBAA5B;gBACkB,gBAAgB,EAAA,CAAA;sBAAlC;gBACkB,YAAY,EAAA,CAAA;sBAA9B;gBACkB,MAAM,EAAA,CAAA;sBAAxB;gBACkB,OAAO,EAAA,CAAA;sBAAzB;gBACgE,aAAa,EAAA,CAAA;sBAA7E,SAAS;uBAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE;gBACb,mBAAmB,EAAA,CAAA;sBAApE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,mBAAmB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACU,2BAA2B,EAAA,CAAA;sBAApF,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,2BAA2B,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACG,uBAAuB,EAAA,CAAA;sBAAjF,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;gBAEzD,4BAA4B,EAAA,CAAA;sBAD3B,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,sBAAsB,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;gBAES,6BAA6B,EAAA,CAAA;sBAAnG,SAAS;uBAAC,sBAAsB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE;gBAErE,kCAAkC,EAAA,CAAA;sBADjC,SAAS;uBAAC,2BAA2B,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE;;;AC9S5E;;;AAGG;MA2CU,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAxBvB,iBAAiB;YACjB,iBAAiB;YACjB,0BAA0B;YAC1B,sBAAsB;YACtB,qBAAqB;YACrB,2BAA2B;YAC3B,uBAAuB;YACvB,qBAAqB;YACrB,sBAAsB;YACtB,sBAAsB;YACtB,4BAA4B;AAC5B,YAAA,0BAA0B,aAG1B,iBAAiB;YACjB,iBAAiB;YACjB,sBAAsB;YACtB,sBAAsB;YACtB,sBAAsB;YACtB,qBAAqB;YACrB,4BAA4B;YAC5B,uBAAuB,CAAA,EAAA,CAAA;AAGd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAvBvB,iBAAiB;YACjB,0BAA0B;YAE1B,qBAAqB;YACrB,2BAA2B;YAC3B,uBAAuB;YACvB,qBAAqB;YACrB,sBAAsB;YACtB,sBAAsB;YACtB,4BAA4B;YAC5B,0BAA0B,CAAA,EAAA,CAAA;;2FAajB,cAAc,EAAA,UAAA,EAAA,CAAA;kBA1B1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,iBAAiB;wBACjB,iBAAiB;wBACjB,0BAA0B;wBAC1B,sBAAsB;wBACtB,qBAAqB;wBACrB,2BAA2B;wBAC3B,uBAAuB;wBACvB,qBAAqB;wBACrB,sBAAsB;wBACtB,sBAAsB;wBACtB,4BAA4B;wBAC5B;AACD,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,iBAAiB;wBACjB,iBAAiB;wBACjB,sBAAsB;wBACtB,sBAAsB;wBACtB,sBAAsB;wBACtB,qBAAqB;wBACrB,4BAA4B;wBAC5B;AACD;AACF,iBAAA;;;AC7CD;;;AAGG;;ACHH;;;AAGG;;ACHH;;AAEG;;;;"}