import * as i0 from "@angular/core";
import * as i1 from "./tree.component";
import * as i2 from "./tree-node.component";
import * as i3 from "./tree-indent.component";
import * as i4 from "./tree-node-switcher.component";
import * as i5 from "./tree-node-checkbox.component";
import * as i6 from "./tree-node-title.component";
import * as i7 from "./tree-drop-indicator.component";
export declare class NzTreeModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTreeModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<NzTreeModule, never, [typeof i1.NzTreeComponent, typeof i2.NzTreeNodeBuiltinComponent, typeof i3.NzTreeIndentComponent, typeof i4.NzTreeNodeSwitcherComponent, typeof i5.NzTreeNodeBuiltinCheckboxComponent, typeof i6.NzTreeNodeTitleComponent, typeof i7.NzTreeDropIndicatorComponent], [typeof i1.NzTreeComponent, typeof i2.NzTreeNodeBuiltinComponent, typeof i3.NzTreeIndentComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<NzTreeModule>;
}
