import { TemplateRef } from '@angular/core';
import * as i0 from "@angular/core";
export declare class NzTabBodyComponent {
    content: TemplateRef<void> | null;
    active: boolean;
    animated: boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTabBodyComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzTabBodyComponent, "[nz-tab-body]", ["nzTabBody"], { "content": { "alias": "content"; "required": false; }; "active": { "alias": "active"; "required": false; }; "animated": { "alias": "animated"; "required": false; }; }, {}, never, never, true, never>;
}
