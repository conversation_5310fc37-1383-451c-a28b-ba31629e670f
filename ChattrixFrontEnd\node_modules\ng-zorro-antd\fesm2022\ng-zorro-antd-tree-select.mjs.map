{"version": 3, "file": "ng-zorro-antd-tree-select.mjs", "sources": ["../../components/tree-select/tree-select.service.ts", "../../components/tree-select/tree-select.component.ts", "../../components/tree-select/tree-select.module.ts", "../../components/tree-select/public-api.ts", "../../components/tree-select/ng-zorro-antd-tree-select.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Injectable } from '@angular/core';\n\nimport { NzTreeBaseService } from 'ng-zorro-antd/core/tree';\n\n@Injectable()\nexport class NzTreeSelectService extends NzTreeBaseService {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { BACKSPACE, ESCAPE, TAB } from '@angular/cdk/keycodes';\nimport {\n  CdkConnectedOverlay,\n  CdkOverlayOrigin,\n  ConnectedOverlayPositionChange,\n  ConnectionPositionPair\n} from '@angular/cdk/overlay';\nimport { _getEventTarget } from '@angular/cdk/platform';\nimport { SlicePipe } from '@angular/common';\nimport {\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  Renderer2,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  booleanAttribute,\n  computed,\n  forwardRef,\n  inject,\n  numberAttribute,\n  signal\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject, combineLatest, merge, of as observableOf } from 'rxjs';\nimport { distinctUntilChanged, filter, map, startWith, takeUntil, tap, withLatestFrom } from 'rxjs/operators';\n\nimport { slideMotion } from 'ng-zorro-antd/core/animation';\nimport { NzConfigKey, NzConfigService, WithConfig } from 'ng-zorro-antd/core/config';\nimport { NzFormItemFeedbackIconComponent, NzFormNoStatusService, NzFormStatusService } from 'ng-zorro-antd/core/form';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport { NzOverlayModule, POSITION_MAP } from 'ng-zorro-antd/core/overlay';\nimport { reqAnimFrame } from 'ng-zorro-antd/core/polyfill';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport {\n  NzFormatEmitEvent,\n  NzTreeBase,\n  NzTreeHigherOrderServiceToken,\n  NzTreeNode,\n  NzTreeNodeOptions\n} from 'ng-zorro-antd/core/tree';\nimport {\n  NgClassInterface,\n  NgStyleInterface,\n  NzSizeLDSType,\n  NzStatus,\n  NzValidateStatus,\n  OnChangeType,\n  OnTouchedType\n} from 'ng-zorro-antd/core/types';\nimport { getStatusClassNames, isNotNil } from 'ng-zorro-antd/core/util';\nimport { NzEmptyModule } from 'ng-zorro-antd/empty';\nimport { NzSelectModule, NzSelectSearchComponent } from 'ng-zorro-antd/select';\nimport { NZ_SPACE_COMPACT_ITEM_TYPE, NZ_SPACE_COMPACT_SIZE, NzSpaceCompactItemDirective } from 'ng-zorro-antd/space';\nimport { NzTreeComponent, NzTreeModule } from 'ng-zorro-antd/tree';\n\nimport { NzTreeSelectService } from './tree-select.service';\n\nexport type NzPlacementType = 'bottomLeft' | 'bottomRight' | 'topLeft' | 'topRight' | '';\nconst NZ_CONFIG_MODULE_NAME: NzConfigKey = 'treeSelect';\nconst TREE_SELECT_DEFAULT_CLASS = 'ant-select-dropdown ant-select-tree-dropdown';\nconst listOfPositions = [\n  POSITION_MAP.bottomLeft,\n  POSITION_MAP.bottomRight,\n  POSITION_MAP.topRight,\n  POSITION_MAP.topLeft\n];\n\n@Component({\n  selector: 'nz-tree-select',\n  exportAs: 'nzTreeSelect',\n  imports: [\n    NzOverlayModule,\n    CdkConnectedOverlay,\n    NzNoAnimationDirective,\n    NzTreeModule,\n    NzEmptyModule,\n    CdkOverlayOrigin,\n    SlicePipe,\n    NzSelectModule,\n    NzFormItemFeedbackIconComponent\n  ],\n  animations: [slideMotion],\n  template: `\n    <ng-template\n      cdkConnectedOverlay\n      nzConnectedOverlay\n      [cdkConnectedOverlayHasBackdrop]=\"nzBackdrop\"\n      [cdkConnectedOverlayOrigin]=\"cdkOverlayOrigin\"\n      [cdkConnectedOverlayPositions]=\"nzPlacement ? positions : []\"\n      [cdkConnectedOverlayOpen]=\"nzOpen\"\n      [cdkConnectedOverlayTransformOriginOn]=\"'.ant-select-tree-dropdown'\"\n      [cdkConnectedOverlayMinWidth]=\"$any(nzDropdownMatchSelectWidth ? null : triggerWidth)\"\n      [cdkConnectedOverlayWidth]=\"$any(nzDropdownMatchSelectWidth ? triggerWidth : null)\"\n      (overlayOutsideClick)=\"onClickOutside($event)\"\n      (detach)=\"closeDropDown()\"\n      (positionChange)=\"onPositionChange($event)\"\n    >\n      <div\n        [@slideMotion]=\"'enter'\"\n        [class]=\"dropdownClassName\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        [class.ant-select-dropdown-placement-bottomLeft]=\"dropdownPosition === 'bottom'\"\n        [class.ant-select-dropdown-placement-topLeft]=\"dropdownPosition === 'top'\"\n        [class.ant-tree-select-dropdown-rtl]=\"dir === 'rtl'\"\n        [dir]=\"dir\"\n        [style]=\"nzDropdownStyle\"\n      >\n        <nz-tree\n          #treeRef\n          [hidden]=\"isNotFound\"\n          nzNoAnimation\n          nzSelectMode\n          nzBlockNode\n          [nzData]=\"nzNodes\"\n          [nzMultiple]=\"nzMultiple\"\n          [nzSearchValue]=\"inputValue\"\n          [nzHideUnMatched]=\"nzHideUnMatched\"\n          [nzShowIcon]=\"nzShowIcon\"\n          [nzCheckable]=\"nzCheckable\"\n          [nzAsyncData]=\"nzAsyncData\"\n          [nzShowExpand]=\"nzShowExpand\"\n          [nzShowLine]=\"nzShowLine\"\n          [nzExpandedIcon]=\"nzExpandedIcon\"\n          [nzExpandAll]=\"nzDefaultExpandAll\"\n          [nzExpandedKeys]=\"expandedKeys\"\n          [nzCheckedKeys]=\"nzCheckable ? value : []\"\n          [nzSelectedKeys]=\"!nzCheckable ? value : []\"\n          [nzTreeTemplate]=\"treeTemplate\"\n          [nzCheckStrictly]=\"nzCheckStrictly\"\n          [nzVirtualItemSize]=\"nzVirtualItemSize\"\n          [nzVirtualMaxBufferPx]=\"nzVirtualMaxBufferPx\"\n          [nzVirtualMinBufferPx]=\"nzVirtualMinBufferPx\"\n          [nzVirtualHeight]=\"nzVirtualHeight\"\n          (nzExpandChange)=\"onExpandedKeysChange($event)\"\n          (nzClick)=\"nzTreeClick.emit($event)\"\n          (nzCheckedKeysChange)=\"updateSelectedNodes()\"\n          (nzSelectedKeysChange)=\"updateSelectedNodes()\"\n          (nzCheckboxChange)=\"nzTreeCheckboxChange.emit($event)\"\n          (nzSearchValueChange)=\"setSearchValues($event)\"\n        ></nz-tree>\n        @if (nzNodes.length === 0 || isNotFound) {\n          <span class=\"ant-select-not-found\">\n            <nz-embed-empty [nzComponentName]=\"'tree-select'\" [specificContent]=\"nzNotFoundContent\"></nz-embed-empty>\n          </span>\n        }\n      </div>\n    </ng-template>\n\n    <div cdkOverlayOrigin class=\"ant-select-selector\">\n      @if (isMultiple) {\n        @for (node of selectedNodes | slice: 0 : nzMaxTagCount; track node.key) {\n          <nz-select-item\n            [deletable]=\"true\"\n            [disabled]=\"node.isDisabled || nzDisabled\"\n            [label]=\"nzDisplayWith(node)\"\n            (delete)=\"removeSelected(node, true)\"\n          ></nz-select-item>\n        }\n        @if (selectedNodes.length > nzMaxTagCount) {\n          <nz-select-item\n            [contentTemplateOutlet]=\"nzMaxTagPlaceholder\"\n            [contentTemplateOutletContext]=\"selectedNodes | slice: nzMaxTagCount\"\n            [deletable]=\"false\"\n            [disabled]=\"false\"\n            [label]=\"'+ ' + (selectedNodes.length - nzMaxTagCount) + ' ...'\"\n          ></nz-select-item>\n        }\n      }\n\n      <nz-select-search\n        [nzId]=\"nzId\"\n        [showInput]=\"nzShowSearch\"\n        (keydown)=\"onKeyDownInput($event)\"\n        (isComposingChange)=\"isComposingChange($event)\"\n        (valueChange)=\"setInputValue($event)\"\n        [value]=\"inputValue\"\n        [mirrorSync]=\"isMultiple\"\n        [disabled]=\"nzDisabled\"\n        [focusTrigger]=\"nzOpen\"\n      ></nz-select-search>\n\n      @if (nzPlaceHolder && selectedNodes.length === 0) {\n        <nz-select-placeholder\n          [placeholder]=\"nzPlaceHolder\"\n          [style.display]=\"placeHolderDisplay\"\n        ></nz-select-placeholder>\n      }\n\n      @if (!isMultiple && selectedNodes.length === 1 && !isComposing && inputValue === '') {\n        <nz-select-item\n          [deletable]=\"false\"\n          [disabled]=\"false\"\n          [label]=\"nzDisplayWith(selectedNodes[0])\"\n        ></nz-select-item>\n      }\n\n      @if (!isMultiple) {\n        <nz-select-arrow></nz-select-arrow>\n      }\n      @if (!isMultiple || (hasFeedback && !!status)) {\n        <nz-select-arrow [showArrow]=\"!isMultiple\" [feedbackIcon]=\"feedbackIconTpl\">\n          <ng-template #feedbackIconTpl>\n            @if (hasFeedback && !!status) {\n              <nz-form-item-feedback-icon [status]=\"status\"></nz-form-item-feedback-icon>\n            }\n          </ng-template>\n        </nz-select-arrow>\n      }\n      @if (nzAllowClear && !nzDisabled && selectedNodes.length) {\n        <nz-select-clear (clear)=\"onClearSelection()\"></nz-select-clear>\n      }\n    </div>\n  `,\n  providers: [\n    NzDestroyService,\n    NzTreeSelectService,\n    { provide: NZ_SPACE_COMPACT_ITEM_TYPE, useValue: 'select' },\n    {\n      provide: NzTreeHigherOrderServiceToken,\n      useExisting: NzTreeSelectService\n    },\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => NzTreeSelectComponent),\n      multi: true\n    }\n  ],\n  host: {\n    class: 'ant-select ant-tree-select',\n    '[class.ant-select-in-form-item]': '!!nzFormStatusService',\n    '[class.ant-select-rtl]': 'dir===\"rtl\"',\n    '[class.ant-select-lg]': 'finalSize() === \"large\"',\n    '[class.ant-select-sm]': 'finalSize() === \"small\"',\n    '[class.ant-select-disabled]': 'nzDisabled',\n    '[class.ant-select-single]': '!isMultiple',\n    '[class.ant-select-show-arrow]': '!isMultiple',\n    '[class.ant-select-show-search]': '!isMultiple',\n    '[class.ant-select-multiple]': 'isMultiple',\n    '[class.ant-select-allow-clear]': 'nzAllowClear',\n    '[class.ant-select-open]': 'nzOpen',\n    '[class.ant-select-focused]': 'nzOpen || focused',\n    '(click)': 'trigger()',\n    '(keydown)': 'onKeydown($event)'\n  },\n  hostDirectives: [NzSpaceCompactItemDirective]\n})\nexport class NzTreeSelectComponent extends NzTreeBase implements ControlValueAccessor, OnInit, OnDestroy, OnChanges {\n  readonly _nzModuleName: NzConfigKey = NZ_CONFIG_MODULE_NAME;\n\n  @Input() nzId: string | null = null;\n  @Input({ transform: booleanAttribute }) nzAllowClear: boolean = true;\n  @Input({ transform: booleanAttribute }) nzShowExpand: boolean = true;\n  @Input({ transform: booleanAttribute }) nzShowLine: boolean = false;\n  @Input({ transform: booleanAttribute }) @WithConfig() nzDropdownMatchSelectWidth: boolean = true;\n  @Input({ transform: booleanAttribute }) nzCheckable: boolean = false;\n  @Input({ transform: booleanAttribute }) @WithConfig() nzHideUnMatched: boolean = false;\n  @Input({ transform: booleanAttribute }) @WithConfig() nzShowIcon: boolean = false;\n  @Input({ transform: booleanAttribute }) nzShowSearch: boolean = false;\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Input({ transform: booleanAttribute }) nzAsyncData = false;\n  @Input({ transform: booleanAttribute }) nzMultiple = false;\n  @Input({ transform: booleanAttribute }) nzDefaultExpandAll = false;\n  @Input({ transform: booleanAttribute }) nzCheckStrictly = false;\n  @Input() nzVirtualItemSize = 28;\n  @Input() nzVirtualMaxBufferPx = 500;\n  @Input() nzVirtualMinBufferPx = 28;\n  @Input() nzVirtualHeight: string | null = null;\n  @Input() nzExpandedIcon?: TemplateRef<{ $implicit: NzTreeNode; origin: NzTreeNodeOptions }>;\n  @Input() nzNotFoundContent?: string | TemplateRef<void>;\n  @Input() nzNodes: NzTreeNodeOptions[] | NzTreeNode[] = [];\n  @Input() nzOpen = false;\n  @Input() @WithConfig() nzSize: NzSizeLDSType = 'default';\n  @Input() nzPlaceHolder = '';\n  @Input() nzDropdownStyle: NgStyleInterface | null = null;\n  @Input() nzDropdownClassName?: string;\n  @Input() @WithConfig() nzBackdrop = false;\n  @Input() nzStatus: NzStatus = '';\n  @Input() nzPlacement: NzPlacementType = '';\n  @Input()\n  set nzExpandedKeys(value: string[]) {\n    this.expandedKeys = value;\n  }\n  get nzExpandedKeys(): string[] {\n    return this.expandedKeys;\n  }\n\n  @Input() nzDisplayWith: (node: NzTreeNode) => string | undefined = (node: NzTreeNode) => node.title;\n  @Input({ transform: numberAttribute }) nzMaxTagCount!: number;\n  @Input() nzMaxTagPlaceholder: TemplateRef<{ $implicit: NzTreeNode[] }> | null = null;\n  @Output() readonly nzOpenChange = new EventEmitter<boolean>();\n  @Output() readonly nzCleared = new EventEmitter<void>();\n  @Output() readonly nzRemoved = new EventEmitter<NzTreeNode>();\n  @Output() readonly nzExpandChange = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzTreeClick = new EventEmitter<NzFormatEmitEvent>();\n  @Output() readonly nzTreeCheckboxChange = new EventEmitter<NzFormatEmitEvent>();\n\n  @ViewChild(NzSelectSearchComponent, { static: false }) nzSelectSearchComponent!: NzSelectSearchComponent;\n  @ViewChild('treeRef', { static: false }) treeRef!: NzTreeComponent;\n  @ViewChild(CdkOverlayOrigin, { static: true }) cdkOverlayOrigin!: CdkOverlayOrigin;\n  @ViewChild(CdkConnectedOverlay, { static: false }) cdkConnectedOverlay!: CdkConnectedOverlay;\n\n  @Input() nzTreeTemplate!: TemplateRef<{ $implicit: NzTreeNode; origin: NzTreeNodeOptions }>;\n  @ContentChild('nzTreeTemplate', { static: true }) nzTreeTemplateChild!: TemplateRef<{\n    $implicit: NzTreeNode;\n    origin: NzTreeNodeOptions;\n  }>;\n  get treeTemplate(): TemplateRef<{ $implicit: NzTreeNode; origin: NzTreeNodeOptions }> {\n    return this.nzTreeTemplate || this.nzTreeTemplateChild;\n  }\n\n  prefixCls: string = 'ant-select';\n  statusCls: NgClassInterface = {};\n  status: NzValidateStatus = '';\n  hasFeedback: boolean = false;\n\n  dropdownClassName = TREE_SELECT_DEFAULT_CLASS;\n  triggerWidth?: number;\n  isComposing = false;\n  isDestroy = true;\n  isNotFound = false;\n  focused = false;\n  inputValue = '';\n  dropdownPosition: 'top' | 'center' | 'bottom' = 'bottom';\n  selectedNodes: NzTreeNode[] = [];\n  expandedKeys: string[] = [];\n  value: string[] = [];\n  dir: Direction = 'ltr';\n  positions: ConnectionPositionPair[] = [];\n\n  protected finalSize = computed(() => {\n    if (this.compactSize) {\n      return this.compactSize();\n    }\n    return this.size();\n  });\n\n  private size = signal<NzSizeLDSType>(this.nzSize);\n  private compactSize = inject(NZ_SPACE_COMPACT_SIZE, { optional: true });\n  private destroy$ = inject(NzDestroyService);\n  private isNzDisableFirstChange: boolean = true;\n  private isComposingChange$ = new Subject<boolean>();\n  private searchValueChange$ = new Subject<string>();\n\n  onChange: OnChangeType = _value => {};\n  onTouched: OnTouchedType = () => {};\n\n  get placeHolderDisplay(): string {\n    return this.inputValue || this.isComposing || this.selectedNodes.length ? 'none' : 'block';\n  }\n\n  get isMultiple(): boolean {\n    return this.nzMultiple || this.nzCheckable;\n  }\n\n  noAnimation = inject(NzNoAnimationDirective, { host: true, optional: true });\n  nzFormStatusService = inject(NzFormStatusService, { optional: true });\n  private nzFormNoStatusService = inject(NzFormNoStatusService, { optional: true });\n\n  constructor(\n    nzTreeService: NzTreeSelectService,\n    public nzConfigService: NzConfigService,\n    private renderer: Renderer2,\n    private cdr: ChangeDetectorRef,\n    private elementRef: ElementRef,\n    private directionality: Directionality,\n    private focusMonitor: FocusMonitor\n  ) {\n    super(nzTreeService);\n  }\n\n  ngOnInit(): void {\n    this.size.set(this.nzSize);\n    this.nzConfigService\n      .getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.size.set(this.nzSize);\n        this.cdr.markForCheck();\n      });\n\n    this.nzFormStatusService?.formStatusChanges\n      .pipe(\n        distinctUntilChanged((pre, cur) => {\n          return pre.status === cur.status && pre.hasFeedback === cur.hasFeedback;\n        }),\n        withLatestFrom(this.nzFormNoStatusService ? this.nzFormNoStatusService.noFormStatus : observableOf(false)),\n        map(([{ status, hasFeedback }, noStatus]) => ({ status: noStatus ? '' : status, hasFeedback })),\n        takeUntil(this.destroy$)\n      )\n      .subscribe(({ status, hasFeedback }) => {\n        this.setStatusStyles(status, hasFeedback);\n      });\n\n    this.isDestroy = false;\n    this.subscribeSelectionChange();\n\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n\n    this.focusMonitor\n      .monitor(this.elementRef, true)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(focusOrigin => {\n        if (!focusOrigin) {\n          this.focused = false;\n          this.cdr.markForCheck();\n          Promise.resolve().then(() => {\n            this.onTouched();\n          });\n        } else {\n          this.focused = true;\n          this.cdr.markForCheck();\n        }\n      });\n\n    // setInputValue method executed earlier than isComposingChange\n    combineLatest([this.searchValueChange$, this.isComposingChange$.pipe(startWith(false))])\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(([searchValue, isComposing]) => {\n        this.isComposing = isComposing;\n        if (!isComposing) {\n          this.inputValue = searchValue;\n          this.updatePosition();\n        }\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.isDestroy = true;\n    this.closeDropDown();\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  isComposingChange(isComposing: boolean): void {\n    this.isComposingChange$.next(isComposing);\n  }\n\n  setDisabledState(isDisabled: boolean): void {\n    this.nzDisabled = (this.isNzDisableFirstChange && this.nzDisabled) || isDisabled;\n    this.closeDropDown();\n    this.isNzDisableFirstChange = false;\n  }\n\n  private setStatusStyles(status: NzValidateStatus, hasFeedback: boolean): void {\n    // set inner status\n    this.status = status;\n    this.hasFeedback = hasFeedback;\n    this.cdr.markForCheck();\n    // render status if nzStatus is set\n    this.statusCls = getStatusClassNames(this.prefixCls, status, hasFeedback);\n    Object.keys(this.statusCls).forEach(status => {\n      if (this.statusCls[status]) {\n        this.renderer.addClass(this.elementRef.nativeElement, status);\n      } else {\n        this.renderer.removeClass(this.elementRef.nativeElement, status);\n      }\n    });\n  }\n\n  ngOnChanges({ nzNodes, nzDropdownClassName, nzStatus, nzPlacement, nzSize }: SimpleChanges): void {\n    if (nzNodes) {\n      this.updateSelectedNodes(true);\n    }\n    if (nzDropdownClassName) {\n      const className = this.nzDropdownClassName && this.nzDropdownClassName.trim();\n      this.dropdownClassName = className ? `${TREE_SELECT_DEFAULT_CLASS} ${className}` : TREE_SELECT_DEFAULT_CLASS;\n    }\n    if (nzStatus) {\n      this.setStatusStyles(this.nzStatus, this.hasFeedback);\n    }\n\n    if (nzPlacement && this.nzPlacement) {\n      if (POSITION_MAP[this.nzPlacement]) {\n        this.positions = [POSITION_MAP[this.nzPlacement]];\n      }\n    }\n    if (nzSize) {\n      this.size.set(nzSize.currentValue);\n    }\n  }\n\n  writeValue(value: string[] | string): void {\n    if (isNotNil(value)) {\n      if (this.isMultiple && Array.isArray(value)) {\n        this.value = value;\n      } else {\n        this.value = [value as string];\n      }\n      // need clear selected nodes when user set value before updating\n      this.clearSelectedNodes();\n      this.updateSelectedNodes(true);\n    } else {\n      this.value = [];\n      this.clearSelectedNodes();\n      this.selectedNodes = [];\n    }\n    this.cdr.markForCheck();\n  }\n\n  registerOnChange(fn: (_: string[] | string | null) => void): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this.onTouched = fn;\n  }\n\n  onKeydown(event: KeyboardEvent): void {\n    if (this.nzDisabled) {\n      return;\n    }\n    switch (event.keyCode) {\n      case ESCAPE:\n        /**\n         * Skip the ESCAPE processing, it will be handled in {@link onOverlayKeyDown}.\n         */\n        break;\n      case TAB:\n        this.closeDropDown();\n        break;\n      default:\n        if (!this.nzOpen) {\n          this.openDropdown();\n        }\n    }\n  }\n\n  trigger(): void {\n    if (this.nzDisabled || (!this.nzDisabled && this.nzOpen)) {\n      this.closeDropDown();\n    } else {\n      this.openDropdown();\n    }\n  }\n\n  openDropdown(): void {\n    if (!this.nzDisabled) {\n      this.nzOpen = true;\n      this.nzOpenChange.emit(this.nzOpen);\n      this.updateCdkConnectedOverlayStatus();\n      if (this.nzShowSearch || this.isMultiple) {\n        this.focusOnInput();\n      }\n    }\n  }\n\n  closeDropDown(): void {\n    Promise.resolve().then(() => {\n      this.onTouched();\n    });\n    this.nzOpen = false;\n    this.inputValue = '';\n    this.isNotFound = false;\n    this.nzOpenChange.emit(this.nzOpen);\n    this.cdr.markForCheck();\n  }\n\n  onKeyDownInput(e: KeyboardEvent): void {\n    const keyCode = e.keyCode;\n    const eventTarget = e.target as HTMLInputElement;\n    if (this.isMultiple && !eventTarget.value && keyCode === BACKSPACE) {\n      e.preventDefault();\n      if (this.selectedNodes.length) {\n        const removeNode = this.selectedNodes[this.selectedNodes.length - 1];\n        if (removeNode && !removeNode.isDisabled) {\n          this.removeSelected(removeNode);\n        }\n      }\n    }\n  }\n\n  onExpandedKeysChange(value: NzFormatEmitEvent): void {\n    this.nzExpandChange.emit(value);\n    this.expandedKeys = [...value.keys!];\n  }\n\n  setInputValue(value: string): void {\n    this.searchValueChange$.next(value);\n  }\n\n  removeSelected(node: NzTreeNode, emit: boolean = true): void {\n    node.isSelected = false;\n    node.isChecked = false;\n    if (this.nzCheckable) {\n      this.nzTreeService.conduct(node, this.nzCheckStrictly);\n    } else {\n      this.nzTreeService.setSelectedNodeList(node, this.nzMultiple);\n    }\n\n    if (emit) {\n      this.nzRemoved.emit(node);\n    }\n  }\n\n  focusOnInput(): void {\n    if (this.nzSelectSearchComponent) {\n      this.nzSelectSearchComponent.focus();\n    }\n  }\n\n  subscribeSelectionChange(): void {\n    merge(\n      this.nzTreeClick.pipe(\n        tap((event: NzFormatEmitEvent) => {\n          const node = event.node!;\n          if (this.nzCheckable && !node.isDisabled && !node.isDisableCheckbox) {\n            node.isChecked = !node.isChecked;\n            node.isHalfChecked = false;\n            if (!this.nzCheckStrictly) {\n              this.nzTreeService.conduct(node);\n            }\n          }\n          if (this.nzCheckable) {\n            node.isSelected = false;\n          }\n        }),\n        filter((event: NzFormatEmitEvent) => {\n          const node = event.node!;\n          return this.nzCheckable ? !node.isDisabled && !node.isDisableCheckbox : !node.isDisabled && node.isSelectable;\n        })\n      ),\n      this.nzCheckable ? this.nzTreeCheckboxChange.asObservable() : observableOf(),\n      this.nzCleared,\n      this.nzRemoved\n    )\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.updateSelectedNodes();\n        const value = this.selectedNodes.map(node => node.key!);\n        this.value = [...value];\n        if (this.nzShowSearch || this.isMultiple) {\n          this.inputValue = '';\n          this.isNotFound = false;\n        }\n        if (this.isMultiple) {\n          this.onChange(value);\n          this.focusOnInput();\n          this.updatePosition();\n        } else {\n          this.closeDropDown();\n          this.onChange(value.length ? value[0] : null);\n        }\n      });\n  }\n\n  updateSelectedNodes(init: boolean = false): void {\n    if (init) {\n      const nodes = this.coerceTreeNodes(this.nzNodes);\n      this.nzTreeService.isMultiple = this.isMultiple;\n      this.nzTreeService.isCheckStrictly = this.nzCheckStrictly;\n      this.nzTreeService.initTree(nodes);\n      if (this.nzCheckable) {\n        this.nzTreeService.conductCheck(this.value, this.nzCheckStrictly);\n      } else {\n        this.nzTreeService.conductSelectedKeys(this.value, this.isMultiple);\n      }\n    }\n\n    this.selectedNodes = [...(this.nzCheckable ? this.getCheckedNodeList() : this.getSelectedNodeList())].sort(\n      (a, b) => {\n        const indexA = this.value.indexOf(a.key);\n        const indexB = this.value.indexOf(b.key);\n        if (indexA !== -1 && indexB !== -1) {\n          return indexA - indexB;\n        }\n        if (indexA !== -1) {\n          return -1;\n        }\n        if (indexB !== -1) {\n          return 1;\n        }\n        return 0;\n      }\n    );\n  }\n\n  updatePosition(): void {\n    reqAnimFrame(() => {\n      this.cdkConnectedOverlay?.overlayRef?.updatePosition();\n    });\n  }\n\n  onPositionChange(position: ConnectedOverlayPositionChange): void {\n    this.dropdownPosition = position.connectionPair.originY;\n  }\n\n  onClearSelection(): void {\n    this.selectedNodes.forEach(node => {\n      this.removeSelected(node, false);\n    });\n    this.nzCleared.emit();\n  }\n\n  onClickOutside(event: MouseEvent): void {\n    const target = _getEventTarget(event);\n    if (!this.elementRef.nativeElement.contains(target)) {\n      this.closeDropDown();\n    }\n  }\n\n  setSearchValues($event: NzFormatEmitEvent): void {\n    Promise.resolve().then(() => {\n      this.isNotFound = (this.nzShowSearch || this.isMultiple) && !!this.inputValue && $event.matchedKeys!.length === 0;\n    });\n  }\n\n  updateCdkConnectedOverlayStatus(): void {\n    if (!this.nzPlacement || !listOfPositions.includes(POSITION_MAP[this.nzPlacement])) {\n      this.triggerWidth = this.cdkOverlayOrigin.elementRef.nativeElement.getBoundingClientRect().width;\n    }\n  }\n\n  clearSelectedNodes(): void {\n    this.selectedNodes.forEach(node => {\n      this.removeSelected(node, false);\n    });\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzTreeSelectComponent } from './tree-select.component';\n\n@NgModule({\n  imports: [NzTreeSelectComponent],\n  exports: [NzTreeSelectComponent]\n})\nexport class NzTreeSelectModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './tree-select.component';\nexport * from './tree-select.module';\nexport * from './tree-select.service';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["observableOf", "i1.NzTreeSelectService"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AAOG,MAAO,mBAAoB,SAAQ,iBAAiB,CAAA;uGAA7C,mBAAmB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAAnB,mBAAmB,EAAA,CAAA;;2FAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAD/B;;;ACiED,MAAM,qBAAqB,GAAgB,YAAY;AACvD,MAAM,yBAAyB,GAAG,8CAA8C;AAChF,MAAM,eAAe,GAAG;AACtB,IAAA,YAAY,CAAC,UAAU;AACvB,IAAA,YAAY,CAAC,WAAW;AACxB,IAAA,YAAY,CAAC,QAAQ;AACrB,IAAA,YAAY,CAAC;CACd;IAsLY,qBAAqB,GAAA,CAAA,MAAA;sBAAS,UAAU;;;;;;;;;;;;;;;;AAAxC,IAAA,OAAA,MAAA,qBAAsB,SAAQ,WAAU,CAAA;;;AAOV,YAAA,sCAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAEZ,YAAA,2BAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,sBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAe3C,YAAA,kBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAIZ,YAAA,sBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;YAtBgC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,sCAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,4BAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,4BAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,0BAA0B,EAA1B,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,0BAA0B,GAAiB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,wCAAA,EAAA,6CAAA,CAAA;YAE3C,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,2BAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,iBAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,eAAe,EAAf,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,eAAe,GAAkB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,6BAAA,EAAA,kCAAA,CAAA;YACjC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,YAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,UAAU,EAAV,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,UAAU,GAAkB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,wBAAA,EAAA,6BAAA,CAAA;YAe3D,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,kBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,QAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,QAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,MAAM,EAAN,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,MAAM,GAA4B,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,oBAAA,EAAA,yBAAA,CAAA;YAIlC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,YAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,UAAU,EAAV,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,UAAU,GAAS,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,wBAAA,EAAA,6BAAA,CAAA;;;QAqFjC,eAAe;QACd,QAAQ;QACR,GAAG;QACH,UAAU;QACV,cAAc;QACd,YAAY;QAtHb,aAAa,GAAgB,qBAAqB;QAElD,IAAI,GAAkB,IAAI;QACK,YAAY,GAAY,IAAI;QAC5B,YAAY,GAAY,IAAI;QAC5B,UAAU,GAAY,KAAK;QACb,0BAA0B,GAAA,iBAAA,CAAA,IAAA,EAAA,wCAAA,EAAY,IAAI,CAAC;QACzD,WAAW,IAAA,iBAAA,CAAA,IAAA,EAAA,6CAAA,CAAA,EAAY,KAAK;QACd,eAAe,GAAA,iBAAA,CAAA,IAAA,EAAA,6BAAA,EAAY,KAAK,CAAC;QACjC,UAAU,IAAA,iBAAA,CAAA,IAAA,EAAA,kCAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,wBAAA,EAAY,KAAK,CAAC;QAC1C,YAAY,IAAA,iBAAA,CAAA,IAAA,EAAA,6BAAA,CAAA,EAAY,KAAK;QAC7B,UAAU,GAAG,KAAK;QAClB,WAAW,GAAG,KAAK;QACnB,UAAU,GAAG,KAAK;QAClB,kBAAkB,GAAG,KAAK;QAC1B,eAAe,GAAG,KAAK;QACtD,iBAAiB,GAAG,EAAE;QACtB,oBAAoB,GAAG,GAAG;QAC1B,oBAAoB,GAAG,EAAE;QACzB,eAAe,GAAkB,IAAI;AACrC,QAAA,cAAc;AACd,QAAA,iBAAiB;QACjB,OAAO,GAAuC,EAAE;QAChD,MAAM,GAAG,KAAK;QACA,MAAM,GAAA,iBAAA,CAAA,IAAA,EAAA,oBAAA,EAAkB,SAAS,CAAC;QAChD,aAAa,IAAA,iBAAA,CAAA,IAAA,EAAA,yBAAA,CAAA,EAAG,EAAE;QAClB,eAAe,GAA4B,IAAI;AAC/C,QAAA,mBAAmB;QACL,UAAU,GAAA,iBAAA,CAAA,IAAA,EAAA,wBAAA,EAAG,KAAK,CAAC;QACjC,QAAQ,IAAA,iBAAA,CAAA,IAAA,EAAA,6BAAA,CAAA,EAAa,EAAE;QACvB,WAAW,GAAoB,EAAE;QAC1C,IACI,cAAc,CAAC,KAAe,EAAA;AAChC,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK;;AAE3B,QAAA,IAAI,cAAc,GAAA;YAChB,OAAO,IAAI,CAAC,YAAY;;QAGjB,aAAa,GAA6C,CAAC,IAAgB,KAAK,IAAI,CAAC,KAAK;AAC5D,QAAA,aAAa;QAC3C,mBAAmB,GAAoD,IAAI;AACjE,QAAA,YAAY,GAAG,IAAI,YAAY,EAAW;AAC1C,QAAA,SAAS,GAAG,IAAI,YAAY,EAAQ;AACpC,QAAA,SAAS,GAAG,IAAI,YAAY,EAAc;AAC1C,QAAA,cAAc,GAAG,IAAI,YAAY,EAAqB;AACtD,QAAA,WAAW,GAAG,IAAI,YAAY,EAAqB;AACnD,QAAA,oBAAoB,GAAG,IAAI,YAAY,EAAqB;AAExB,QAAA,uBAAuB;AACrC,QAAA,OAAO;AACD,QAAA,gBAAgB;AACZ,QAAA,mBAAmB;AAE7D,QAAA,cAAc;AAC2B,QAAA,mBAAmB;AAIrE,QAAA,IAAI,YAAY,GAAA;AACd,YAAA,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,mBAAmB;;QAGxD,SAAS,GAAW,YAAY;QAChC,SAAS,GAAqB,EAAE;QAChC,MAAM,GAAqB,EAAE;QAC7B,WAAW,GAAY,KAAK;QAE5B,iBAAiB,GAAG,yBAAyB;AAC7C,QAAA,YAAY;QACZ,WAAW,GAAG,KAAK;QACnB,SAAS,GAAG,IAAI;QAChB,UAAU,GAAG,KAAK;QAClB,OAAO,GAAG,KAAK;QACf,UAAU,GAAG,EAAE;QACf,gBAAgB,GAAgC,QAAQ;QACxD,aAAa,GAAiB,EAAE;QAChC,YAAY,GAAa,EAAE;QAC3B,KAAK,GAAa,EAAE;QACpB,GAAG,GAAc,KAAK;QACtB,SAAS,GAA6B,EAAE;AAE9B,QAAA,SAAS,GAAG,QAAQ,CAAC,MAAK;AAClC,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,OAAO,IAAI,CAAC,WAAW,EAAE;;AAE3B,YAAA,OAAO,IAAI,CAAC,IAAI,EAAE;AACpB,SAAC,CAAC;AAEM,QAAA,IAAI,GAAG,MAAM,CAAgB,IAAI,CAAC,MAAM,CAAC;QACzC,WAAW,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAC/D,QAAA,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC;QACnC,sBAAsB,GAAY,IAAI;AACtC,QAAA,kBAAkB,GAAG,IAAI,OAAO,EAAW;AAC3C,QAAA,kBAAkB,GAAG,IAAI,OAAO,EAAU;AAElD,QAAA,QAAQ,GAAiB,MAAM,IAAG,GAAG;AACrC,QAAA,SAAS,GAAkB,MAAK,GAAG;AAEnC,QAAA,IAAI,kBAAkB,GAAA;YACpB,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO;;AAG5F,QAAA,IAAI,UAAU,GAAA;AACZ,YAAA,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW;;AAG5C,QAAA,WAAW,GAAG,MAAM,CAAC,sBAAsB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;QAC5E,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;QAC7D,qBAAqB,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAEjF,QAAA,WAAA,CACE,aAAkC,EAC3B,eAAgC,EAC/B,QAAmB,EACnB,GAAsB,EACtB,UAAsB,EACtB,cAA8B,EAC9B,YAA0B,EAAA;YAElC,KAAK,CAAC,aAAa,CAAC;YAPb,IAAe,CAAA,eAAA,GAAf,eAAe;YACd,IAAQ,CAAA,QAAA,GAAR,QAAQ;YACR,IAAG,CAAA,GAAA,GAAH,GAAG;YACH,IAAU,CAAA,UAAA,GAAV,UAAU;YACV,IAAc,CAAA,cAAA,GAAd,cAAc;YACd,IAAY,CAAA,YAAA,GAAZ,YAAY;;QAKtB,QAAQ,GAAA;YACN,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;AAC1B,YAAA,IAAI,CAAC;iBACF,gCAAgC,CAAC,qBAAqB;AACtD,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,MAAK;gBACd,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;AAC1B,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;YAEJ,IAAI,CAAC,mBAAmB,EAAE;iBACvB,IAAI,CACH,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,KAAI;AAChC,gBAAA,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,KAAK,GAAG,CAAC,WAAW;AACzE,aAAC,CAAC,EACF,cAAc,CAAC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,GAAGA,EAAY,CAAC,KAAK,CAAC,CAAC,EAC1G,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,GAAG,EAAE,GAAG,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC,EAC/F,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAEzB,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,KAAI;AACrC,gBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,CAAC;AAC3C,aAAC,CAAC;AAEJ,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;YACtB,IAAI,CAAC,wBAAwB,EAAE;YAE/B,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,gBAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,gBAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,aAAC,CAAC;YACF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;AAEpC,YAAA,IAAI,CAAC;AACF,iBAAA,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI;AAC7B,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,WAAW,IAAG;gBACvB,IAAI,CAAC,WAAW,EAAE;AAChB,oBAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACpB,oBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACvB,oBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;wBAC1B,IAAI,CAAC,SAAS,EAAE;AAClB,qBAAC,CAAC;;qBACG;AACL,oBAAA,IAAI,CAAC,OAAO,GAAG,IAAI;AACnB,oBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAE3B,aAAC,CAAC;;AAGJ,YAAA,aAAa,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AACpF,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,KAAI;AACxC,gBAAA,IAAI,CAAC,WAAW,GAAG,WAAW;gBAC9B,IAAI,CAAC,WAAW,EAAE;AAChB,oBAAA,IAAI,CAAC,UAAU,GAAG,WAAW;oBAC7B,IAAI,CAAC,cAAc,EAAE;;AAEzB,aAAC,CAAC;;QAGN,WAAW,GAAA;AACT,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;YACrB,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;AAG1B,QAAA,iBAAiB,CAAC,WAAoB,EAAA;AACpC,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC;;AAG3C,QAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,YAAA,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU;YAChF,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;;QAG7B,eAAe,CAAC,MAAwB,EAAE,WAAoB,EAAA;;AAEpE,YAAA,IAAI,CAAC,MAAM,GAAG,MAAM;AACpB,YAAA,IAAI,CAAC,WAAW,GAAG,WAAW;AAC9B,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAEvB,YAAA,IAAI,CAAC,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,CAAC;AACzE,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,IAAG;AAC3C,gBAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AAC1B,oBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,MAAM,CAAC;;qBACxD;AACL,oBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,MAAM,CAAC;;AAEpE,aAAC,CAAC;;QAGJ,WAAW,CAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAiB,EAAA;YACxF,IAAI,OAAO,EAAE;AACX,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;;YAEhC,IAAI,mBAAmB,EAAE;AACvB,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE;AAC7E,gBAAA,IAAI,CAAC,iBAAiB,GAAG,SAAS,GAAG,CAAA,EAAG,yBAAyB,CAAA,CAAA,EAAI,SAAS,CAAE,CAAA,GAAG,yBAAyB;;YAE9G,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC;;AAGvD,YAAA,IAAI,WAAW,IAAI,IAAI,CAAC,WAAW,EAAE;AACnC,gBAAA,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;oBAClC,IAAI,CAAC,SAAS,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;;;YAGrD,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC;;;AAItC,QAAA,UAAU,CAAC,KAAwB,EAAA;AACjC,YAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACnB,IAAI,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC3C,oBAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;qBACb;AACL,oBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,KAAe,CAAC;;;gBAGhC,IAAI,CAAC,kBAAkB,EAAE;AACzB,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;;iBACzB;AACL,gBAAA,IAAI,CAAC,KAAK,GAAG,EAAE;gBACf,IAAI,CAAC,kBAAkB,EAAE;AACzB,gBAAA,IAAI,CAAC,aAAa,GAAG,EAAE;;AAEzB,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,QAAA,gBAAgB,CAAC,EAAyC,EAAA;AACxD,YAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAGpB,QAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB,QAAA,SAAS,CAAC,KAAoB,EAAA;AAC5B,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB;;AAEF,YAAA,QAAQ,KAAK,CAAC,OAAO;AACnB,gBAAA,KAAK,MAAM;AACT;;AAEG;oBACH;AACF,gBAAA,KAAK,GAAG;oBACN,IAAI,CAAC,aAAa,EAAE;oBACpB;AACF,gBAAA;AACE,oBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;wBAChB,IAAI,CAAC,YAAY,EAAE;;;;QAK3B,OAAO,GAAA;AACL,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;gBACxD,IAAI,CAAC,aAAa,EAAE;;iBACf;gBACL,IAAI,CAAC,YAAY,EAAE;;;QAIvB,YAAY,GAAA;AACV,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI;gBAClB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;gBACnC,IAAI,CAAC,+BAA+B,EAAE;gBACtC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,EAAE;oBACxC,IAAI,CAAC,YAAY,EAAE;;;;QAKzB,aAAa,GAAA;AACX,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;gBAC1B,IAAI,CAAC,SAAS,EAAE;AAClB,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,YAAA,IAAI,CAAC,UAAU,GAAG,EAAE;AACpB,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK;YACvB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AACnC,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,QAAA,cAAc,CAAC,CAAgB,EAAA;AAC7B,YAAA,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO;AACzB,YAAA,MAAM,WAAW,GAAG,CAAC,CAAC,MAA0B;AAChD,YAAA,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,OAAO,KAAK,SAAS,EAAE;gBAClE,CAAC,CAAC,cAAc,EAAE;AAClB,gBAAA,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;AAC7B,oBAAA,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;AACpE,oBAAA,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;AACxC,wBAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;;;;;AAMvC,QAAA,oBAAoB,CAAC,KAAwB,EAAA;AAC3C,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;YAC/B,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,KAAK,CAAC,IAAK,CAAC;;AAGtC,QAAA,aAAa,CAAC,KAAa,EAAA;AACzB,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC;;AAGrC,QAAA,cAAc,CAAC,IAAgB,EAAE,IAAA,GAAgB,IAAI,EAAA;AACnD,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK;AACvB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC;;iBACjD;gBACL,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC;;YAG/D,IAAI,IAAI,EAAE;AACR,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;;;QAI7B,YAAY,GAAA;AACV,YAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAChC,gBAAA,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE;;;QAIxC,wBAAwB,GAAA;AACtB,YAAA,KAAK,CACH,IAAI,CAAC,WAAW,CAAC,IAAI,CACnB,GAAG,CAAC,CAAC,KAAwB,KAAI;AAC/B,gBAAA,MAAM,IAAI,GAAG,KAAK,CAAC,IAAK;AACxB,gBAAA,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;AACnE,oBAAA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS;AAChC,oBAAA,IAAI,CAAC,aAAa,GAAG,KAAK;AAC1B,oBAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AACzB,wBAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC;;;AAGpC,gBAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,oBAAA,IAAI,CAAC,UAAU,GAAG,KAAK;;AAE3B,aAAC,CAAC,EACF,MAAM,CAAC,CAAC,KAAwB,KAAI;AAClC,gBAAA,MAAM,IAAI,GAAG,KAAK,CAAC,IAAK;gBACxB,OAAO,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY;AAC/G,aAAC,CAAC,CACH,EACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,GAAGA,EAAY,EAAE,EAC5E,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS;AAEb,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,MAAK;gBACd,IAAI,CAAC,mBAAmB,EAAE;AAC1B,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,GAAI,CAAC;AACvD,gBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;gBACvB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,EAAE;AACxC,oBAAA,IAAI,CAAC,UAAU,GAAG,EAAE;AACpB,oBAAA,IAAI,CAAC,UAAU,GAAG,KAAK;;AAEzB,gBAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,oBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;oBACpB,IAAI,CAAC,YAAY,EAAE;oBACnB,IAAI,CAAC,cAAc,EAAE;;qBAChB;oBACL,IAAI,CAAC,aAAa,EAAE;AACpB,oBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;;AAEjD,aAAC,CAAC;;QAGN,mBAAmB,CAAC,OAAgB,KAAK,EAAA;YACvC,IAAI,IAAI,EAAE;gBACR,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC;gBAChD,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;gBAC/C,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe;AACzD,gBAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC;AAClC,gBAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,oBAAA,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC;;qBAC5D;AACL,oBAAA,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC;;;AAIvE,YAAA,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,IAAI,CACxG,CAAC,CAAC,EAAE,CAAC,KAAI;AACP,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;AACxC,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;gBACxC,IAAI,MAAM,KAAK,CAAC,CAAC,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE;oBAClC,OAAO,MAAM,GAAG,MAAM;;AAExB,gBAAA,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE;oBACjB,OAAO,CAAC,CAAC;;AAEX,gBAAA,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE;AACjB,oBAAA,OAAO,CAAC;;AAEV,gBAAA,OAAO,CAAC;AACV,aAAC,CACF;;QAGH,cAAc,GAAA;YACZ,YAAY,CAAC,MAAK;AAChB,gBAAA,IAAI,CAAC,mBAAmB,EAAE,UAAU,EAAE,cAAc,EAAE;AACxD,aAAC,CAAC;;AAGJ,QAAA,gBAAgB,CAAC,QAAwC,EAAA;YACvD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,cAAc,CAAC,OAAO;;QAGzD,gBAAgB,GAAA;AACd,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,IAAG;AAChC,gBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC;AAClC,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;;AAGvB,QAAA,cAAc,CAAC,KAAiB,EAAA;AAC9B,YAAA,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC;AACrC,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACnD,IAAI,CAAC,aAAa,EAAE;;;AAIxB,QAAA,eAAe,CAAC,MAAyB,EAAA;AACvC,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;gBAC1B,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,WAAY,CAAC,MAAM,KAAK,CAAC;AACnH,aAAC,CAAC;;QAGJ,+BAA+B,GAAA;AAC7B,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE;AAClF,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,KAAK;;;QAIpG,kBAAkB,GAAA;AAChB,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,IAAG;AAChC,gBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC;AAClC,aAAC,CAAC;;2GAzdO,qBAAqB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,mBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAArB,QAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,qBAAqB,EAIZ,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAChB,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAChB,EAAA,0BAAA,EAAA,CAAA,4BAAA,EAAA,4BAAA,EAAA,gBAAgB,CAChB,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,CAChB,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAChB,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAChB,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAChB,EAAA,kBAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAAA,gBAAgB,CAChB,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,gBAAgB,CAyBhB,EAAA,iBAAA,EAAA,mBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,aAAA,EAAA,eAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,eAAe,CA1ExB,EAAA,mBAAA,EAAA,qBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,WAAA,EAAA,SAAA,EAAA,mBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,+BAAA,EAAA,uBAAA,EAAA,sBAAA,EAAA,eAAA,EAAA,qBAAA,EAAA,2BAAA,EAAA,qBAAA,EAAA,2BAAA,EAAA,2BAAA,EAAA,YAAA,EAAA,yBAAA,EAAA,aAAA,EAAA,6BAAA,EAAA,aAAA,EAAA,8BAAA,EAAA,aAAA,EAAA,2BAAA,EAAA,YAAA,EAAA,8BAAA,EAAA,cAAA,EAAA,uBAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,4BAAA,EAAA,EAAA,SAAA,EAAA;gBACT,gBAAgB;gBAChB,mBAAmB;AACnB,gBAAA,EAAE,OAAO,EAAE,0BAA0B,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAC3D,gBAAA;AACE,oBAAA,OAAO,EAAE,6BAA6B;AACtC,oBAAA,WAAW,EAAE;AACd,iBAAA;AACD,gBAAA;AACE,oBAAA,OAAO,EAAE,iBAAiB;AAC1B,oBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,qBAAqB,CAAC;AACpD,oBAAA,KAAK,EAAE;AACR;AACF,aAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,yBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAsEU,uBAAuB,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAEvB,gBAAgB,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAChB,mBAAmB,EA1NpB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,cAAA,EAAA,CAAA,EAAA,SAAA,EAAA,EAAA,CAAA,2BAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmIT,EA9IC,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,eAAe,uNACf,mBAAmB,EAAA,QAAA,EAAA,qEAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,8BAAA,EAAA,qCAAA,EAAA,4BAAA,EAAA,4BAAA,EAAA,0BAAA,EAAA,2BAAA,EAAA,6BAAA,EAAA,8BAAA,EAAA,kCAAA,EAAA,+BAAA,EAAA,mCAAA,EAAA,mCAAA,EAAA,yBAAA,EAAA,iCAAA,EAAA,sCAAA,EAAA,gCAAA,EAAA,iCAAA,EAAA,uCAAA,EAAA,kCAAA,EAAA,yBAAA,EAAA,wCAAA,CAAA,EAAA,OAAA,EAAA,CAAA,eAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,qBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACnB,sBAAsB,EACtB,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,k0BACZ,aAAa,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,qBAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACb,gBAAgB,EAChB,QAAA,EAAA,4DAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,SAAS,6CACT,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,sBAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,aAAA,EAAA,SAAA,EAAA,QAAA,EAAA,WAAA,EAAA,uBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,oBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,sBAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,OAAA,EAAA,CAAA,OAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,qBAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,OAAA,EAAA,WAAA,EAAA,YAAA,EAAA,8BAAA,EAAA,uBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,4BAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,uBAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,cAAA,EAAA,OAAA,EAAA,WAAA,CAAA,EAAA,OAAA,EAAA,CAAA,aAAA,EAAA,mBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACd,+BAA+B,EAErB,QAAA,EAAA,4BAAA,EAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA,CAAC,WAAW,CAAC,EAAA,CAAA;;;2FAsKd,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBApLjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,mBAAmB;wBACnB,sBAAsB;wBACtB,YAAY;wBACZ,aAAa;wBACb,gBAAgB;wBAChB,SAAS;wBACT,cAAc;wBACd;AACD,qBAAA;oBACD,UAAU,EAAE,CAAC,WAAW,CAAC;AACzB,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIT,EAAA,CAAA;AACD,oBAAA,SAAS,EAAE;wBACT,gBAAgB;wBAChB,mBAAmB;AACnB,wBAAA,EAAE,OAAO,EAAE,0BAA0B,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAC3D,wBAAA;AACE,4BAAA,OAAO,EAAE,6BAA6B;AACtC,4BAAA,WAAW,EAAE;AACd,yBAAA;AACD,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,2BAA2B,CAAC;AACpD,4BAAA,KAAK,EAAE;AACR;AACF,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,4BAA4B;AACnC,wBAAA,iCAAiC,EAAE,uBAAuB;AAC1D,wBAAA,wBAAwB,EAAE,aAAa;AACvC,wBAAA,uBAAuB,EAAE,yBAAyB;AAClD,wBAAA,uBAAuB,EAAE,yBAAyB;AAClD,wBAAA,6BAA6B,EAAE,YAAY;AAC3C,wBAAA,2BAA2B,EAAE,aAAa;AAC1C,wBAAA,+BAA+B,EAAE,aAAa;AAC9C,wBAAA,gCAAgC,EAAE,aAAa;AAC/C,wBAAA,6BAA6B,EAAE,YAAY;AAC3C,wBAAA,gCAAgC,EAAE,cAAc;AAChD,wBAAA,yBAAyB,EAAE,QAAQ;AACnC,wBAAA,4BAA4B,EAAE,mBAAmB;AACjD,wBAAA,SAAS,EAAE,WAAW;AACtB,wBAAA,WAAW,EAAE;AACd,qBAAA;oBACD,cAAc,EAAE,CAAC,2BAA2B;AAC7C,iBAAA;4PAIU,IAAI,EAAA,CAAA;sBAAZ;gBACuC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACgB,0BAA0B,EAAA,CAAA;sBAA/E,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACgB,eAAe,EAAA,CAAA;sBAApE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACgB,UAAU,EAAA,CAAA;sBAA/D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,kBAAkB,EAAA,CAAA;sBAAzD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,oBAAoB,EAAA,CAAA;sBAA5B;gBACQ,oBAAoB,EAAA,CAAA;sBAA5B;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACsB,MAAM,EAAA,CAAA;sBAA5B;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,mBAAmB,EAAA,CAAA;sBAA3B;gBACsB,UAAU,EAAA,CAAA;sBAAhC;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBAEG,cAAc,EAAA,CAAA;sBADjB;gBAQQ,aAAa,EAAA,CAAA;sBAArB;gBACsC,aAAa,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBAC5B,mBAAmB,EAAA,CAAA;sBAA3B;gBACkB,YAAY,EAAA,CAAA;sBAA9B;gBACkB,SAAS,EAAA,CAAA;sBAA3B;gBACkB,SAAS,EAAA,CAAA;sBAA3B;gBACkB,cAAc,EAAA,CAAA;sBAAhC;gBACkB,WAAW,EAAA,CAAA;sBAA7B;gBACkB,oBAAoB,EAAA,CAAA;sBAAtC;gBAEsD,uBAAuB,EAAA,CAAA;sBAA7E,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,uBAAuB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACZ,OAAO,EAAA,CAAA;sBAA/C,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACQ,gBAAgB,EAAA,CAAA;sBAA9D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,gBAAgB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACM,mBAAmB,EAAA,CAAA;sBAArE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,mBAAmB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBAExC,cAAc,EAAA,CAAA;sBAAtB;gBACiD,mBAAmB,EAAA,CAAA;sBAApE,YAAY;AAAC,gBAAA,IAAA,EAAA,CAAA,gBAAgB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;;;AC/TlD;;;AAGG;MAUU,kBAAkB,CAAA;uGAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAlB,kBAAkB,EAAA,OAAA,EAAA,CAHnB,qBAAqB,CAAA,EAAA,OAAA,EAAA,CACrB,qBAAqB,CAAA,EAAA,CAAA;AAEpB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,YAHnB,qBAAqB,CAAA,EAAA,CAAA;;2FAGpB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAJ9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,qBAAqB,CAAC;oBAChC,OAAO,EAAE,CAAC,qBAAqB;AAChC,iBAAA;;;ACZD;;;AAGG;;ACHH;;AAEG;;;;"}