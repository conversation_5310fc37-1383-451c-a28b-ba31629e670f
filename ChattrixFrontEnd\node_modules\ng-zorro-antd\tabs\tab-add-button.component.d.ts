/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */
import { ElementRef, TemplateRef } from '@angular/core';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import * as i0 from "@angular/core";
export declare class NzTabAddButtonComponent {
    private elementRef;
    addIcon: string | TemplateRef<NzSafeAny>;
    private readonly element;
    constructor(elementRef: ElementRef<HTMLElement>);
    getElementWidth(): number;
    getElementHeight(): number;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTabAddButtonComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzTabAddButtonComponent, "nz-tab-add-button, button[nz-tab-add-button]", never, { "addIcon": { "alias": "addIcon"; "required": false; }; }, {}, never, never, true, never>;
}
