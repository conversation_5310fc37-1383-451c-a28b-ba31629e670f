{"version": 3, "file": "ng-zorro-antd-spin.mjs", "sources": ["../../components/spin/spin.component.ts", "../../components/spin/spin.module.ts", "../../components/spin/public-api.ts", "../../components/spin/ng-zorro-antd-spin.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  ChangeDetectorRef,\n  Component,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  SimpleChanges,\n  TemplateRef,\n  ViewEncapsulation,\n  booleanAttribute,\n  numberAttribute\n} from '@angular/core';\nimport { BehaviorSubject, ReplaySubject, Subject, timer } from 'rxjs';\nimport { debounce, distinctUntilChanged, startWith, switchMap, takeUntil } from 'rxjs/operators';\n\nimport { NzConfigKey, NzConfigService, WithConfig } from 'ng-zorro-antd/core/config';\nimport { NzSafeAny, NzSizeLDSType } from 'ng-zorro-antd/core/types';\n\nconst NZ_CONFIG_MODULE_NAME: NzConfigKey = 'spin';\n\n@Component({\n  selector: 'nz-spin',\n  exportAs: 'nzSpin',\n  preserveWhitespaces: false,\n  encapsulation: ViewEncapsulation.None,\n  template: `\n    <ng-template #defaultTemplate>\n      <span class=\"ant-spin-dot ant-spin-dot-spin\">\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n      </span>\n    </ng-template>\n    @if (isLoading) {\n      <div>\n        <div\n          class=\"ant-spin\"\n          [class.ant-spin-rtl]=\"dir === 'rtl'\"\n          [class.ant-spin-spinning]=\"isLoading\"\n          [class.ant-spin-lg]=\"nzSize === 'large'\"\n          [class.ant-spin-sm]=\"nzSize === 'small'\"\n          [class.ant-spin-show-text]=\"nzTip\"\n        >\n          <ng-template [ngTemplateOutlet]=\"nzIndicator || defaultTemplate\"></ng-template>\n          @if (nzTip) {\n            <div class=\"ant-spin-text\">{{ nzTip }}</div>\n          }\n        </div>\n      </div>\n    }\n    @if (!nzSimple) {\n      <div class=\"ant-spin-container\" [class.ant-spin-blur]=\"isLoading\">\n        <ng-content></ng-content>\n      </div>\n    }\n  `,\n  host: {\n    '[class.ant-spin-nested-loading]': '!nzSimple'\n  },\n  imports: [NgTemplateOutlet]\n})\nexport class NzSpinComponent implements OnChanges, OnDestroy, OnInit {\n  readonly _nzModuleName: NzConfigKey = NZ_CONFIG_MODULE_NAME;\n\n  @Input() @WithConfig() nzIndicator: TemplateRef<NzSafeAny> | null = null;\n  @Input() nzSize: NzSizeLDSType = 'default';\n  @Input() nzTip: string | null = null;\n  @Input({ transform: numberAttribute }) nzDelay = 0;\n  @Input({ transform: booleanAttribute }) nzSimple = false;\n  @Input({ transform: booleanAttribute }) nzSpinning = true;\n  private destroy$ = new Subject<void>();\n  private spinning$ = new BehaviorSubject(this.nzSpinning);\n  private delay$ = new ReplaySubject<number>(1);\n  isLoading = false;\n  dir: Direction = 'ltr';\n\n  constructor(\n    public nzConfigService: NzConfigService,\n    private cdr: ChangeDetectorRef,\n    private directionality: Directionality\n  ) {}\n\n  ngOnInit(): void {\n    const loading$ = this.delay$.pipe(\n      startWith(this.nzDelay),\n      distinctUntilChanged(),\n      switchMap(delay => {\n        if (delay === 0) {\n          return this.spinning$;\n        }\n\n        return this.spinning$.pipe(debounce(spinning => timer(spinning ? delay : 0)));\n      }),\n      takeUntil(this.destroy$)\n    );\n    loading$.subscribe(loading => {\n      this.isLoading = loading;\n      this.cdr.markForCheck();\n    });\n    this.nzConfigService\n      .getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => this.cdr.markForCheck());\n\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n\n    this.dir = this.directionality.value;\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzSpinning, nzDelay } = changes;\n    if (nzSpinning) {\n      this.spinning$.next(this.nzSpinning);\n    }\n    if (nzDelay) {\n      this.delay$.next(this.nzDelay);\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzSpinComponent } from './spin.component';\n\n@NgModule({\n  imports: [NzSpinComponent],\n  exports: [NzSpinComponent]\n})\nexport class NzSpinModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './spin.component';\nexport * from './spin.module';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": [], "mappings": ";;;;;;;;;;AA0BA,MAAM,qBAAqB,GAAgB,MAAM;IA4CpC,eAAe,GAAA,CAAA,MAAA;;;;iBAAf,eAAe,CAAA;;;AAGhB,YAAA,uBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;YAAC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,uBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,aAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,aAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,WAAW,EAAX,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,WAAW,GAAuC,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,yBAAA,EAAA,8BAAA,CAAA;;;QAahE,eAAe;QACd,GAAG;QACH,cAAc;QAjBf,aAAa,GAAgB,qBAAqB;QAEpC,WAAW,GAAA,iBAAA,CAAA,IAAA,EAAA,yBAAA,EAAkC,IAAI,CAAC;QAChE,MAAM,IAAA,iBAAA,CAAA,IAAA,EAAA,8BAAA,CAAA,EAAkB,SAAS;QACjC,KAAK,GAAkB,IAAI;QACG,OAAO,GAAG,CAAC;QACV,QAAQ,GAAG,KAAK;QAChB,UAAU,GAAG,IAAI;AACjD,QAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;QAC9B,SAAS,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC;AAChD,QAAA,MAAM,GAAG,IAAI,aAAa,CAAS,CAAC,CAAC;QAC7C,SAAS,GAAG,KAAK;QACjB,GAAG,GAAc,KAAK;AAEtB,QAAA,WAAA,CACS,eAAgC,EAC/B,GAAsB,EACtB,cAA8B,EAAA;YAF/B,IAAe,CAAA,eAAA,GAAf,eAAe;YACd,IAAG,CAAA,GAAA,GAAH,GAAG;YACH,IAAc,CAAA,cAAA,GAAd,cAAc;;QAGxB,QAAQ,GAAA;YACN,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAC/B,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,EACvB,oBAAoB,EAAE,EACtB,SAAS,CAAC,KAAK,IAAG;AAChB,gBAAA,IAAI,KAAK,KAAK,CAAC,EAAE;oBACf,OAAO,IAAI,CAAC,SAAS;;gBAGvB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;aAC9E,CAAC,EACF,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CACzB;AACD,YAAA,QAAQ,CAAC,SAAS,CAAC,OAAO,IAAG;AAC3B,gBAAA,IAAI,CAAC,SAAS,GAAG,OAAO;AACxB,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;AACF,YAAA,IAAI,CAAC;iBACF,gCAAgC,CAAC,qBAAqB;AACtD,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC7B,SAAS,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;YAE3C,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,gBAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,gBAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,aAAC,CAAC;YAEF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;;AAGtC,QAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,YAAA,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,OAAO;YACvC,IAAI,UAAU,EAAE;gBACd,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;;YAEtC,IAAI,OAAO,EAAE;gBACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;;;QAIlC,WAAW,GAAA;AACT,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;2GA/Df,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAf,QAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,mJAMN,eAAe,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EACf,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAChB,gBAAgB,CA7C1B,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,+BAAA,EAAA,WAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAIS,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;;2FAEf,eAAe,EAAA,UAAA,EAAA,CAAA;kBA1C3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,QAAQ,EAAE,QAAQ;AAClB,oBAAA,mBAAmB,EAAE,KAAK;oBAC1B,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,iCAAiC,EAAE;AACpC,qBAAA;oBACD,OAAO,EAAE,CAAC,gBAAgB;AAC3B,iBAAA;iJAIwB,WAAW,EAAA,CAAA;sBAAjC;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACsC,OAAO,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACG,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;;;AC9ExC;;;AAGG;MAUU,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAZ,YAAY,EAAA,OAAA,EAAA,CAHb,eAAe,CAAA,EAAA,OAAA,EAAA,CACf,eAAe,CAAA,EAAA,CAAA;wGAEd,YAAY,EAAA,CAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,CAAC;oBAC1B,OAAO,EAAE,CAAC,eAAe;AAC1B,iBAAA;;;ACZD;;;AAGG;;ACHH;;AAEG;;;;"}