{"version": 3, "file": "ng-zorro-antd-popover.mjs", "sources": ["../../components/popover/popover.ts", "../../components/popover/popover.module.ts", "../../components/popover/public-api.ts", "../../components/popover/ng-zorro-antd-popover.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { OverlayModule } from '@angular/cdk/overlay';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Input,\n  Output,\n  ViewEncapsulation,\n  booleanAttribute\n} from '@angular/core';\n\nimport { zoomBigMotion } from 'ng-zorro-antd/core/animation';\nimport { NzConfigKey, WithConfig } from 'ng-zorro-antd/core/config';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzOverlayModule } from 'ng-zorro-antd/core/overlay';\nimport { NgStyleInterface, NzTSType } from 'ng-zorro-antd/core/types';\nimport {\n  NzToolTipComponent,\n  NzTooltipBaseDirective,\n  NzTooltipTrigger,\n  PropertyMapping,\n  isTooltipEmpty\n} from 'ng-zorro-antd/tooltip';\n\nconst NZ_CONFIG_MODULE_NAME: NzConfigKey = 'popover';\n\n@Directive({\n  selector: '[nz-popover]',\n  exportAs: 'nzPopover',\n  host: {\n    '[class.ant-popover-open]': 'visible'\n  }\n})\nexport class NzPopoverDirective extends NzTooltipBaseDirective {\n  readonly _nzModuleName: NzConfigKey = NZ_CONFIG_MODULE_NAME;\n\n  /* eslint-disable @angular-eslint/no-input-rename, @angular-eslint/no-output-rename */\n  @Input({ alias: 'nzPopoverArrowPointAtCenter', transform: booleanAttribute }) override arrowPointAtCenter?: boolean;\n  @Input('nzPopoverTitle') override title?: NzTSType;\n  @Input('nzPopoverContent') override content?: NzTSType;\n  @Input('nz-popover') override directiveTitle?: NzTSType | null;\n  @Input('nzPopoverTrigger') override trigger?: NzTooltipTrigger = 'hover';\n  @Input('nzPopoverPlacement') override placement?: string | string[] = 'top';\n  @Input('nzPopoverOrigin') override origin?: ElementRef<HTMLElement>;\n  @Input('nzPopoverVisible') override visible?: boolean;\n  @Input('nzPopoverMouseEnterDelay') override mouseEnterDelay?: number;\n  @Input('nzPopoverMouseLeaveDelay') override mouseLeaveDelay?: number;\n  @Input('nzPopoverOverlayClassName') override overlayClassName?: string;\n  @Input('nzPopoverOverlayStyle') override overlayStyle?: NgStyleInterface;\n  @Input('nzPopoverOverlayClickable') override overlayClickable?: boolean;\n\n  override directiveContent?: NzTSType | null = null;\n\n  @Input() @WithConfig() nzPopoverBackdrop?: boolean = false;\n\n  @Output('nzPopoverVisibleChange') override readonly visibleChange = new EventEmitter<boolean>();\n\n  protected override getProxyPropertyMap(): PropertyMapping {\n    return {\n      nzPopoverBackdrop: ['nzBackdrop', () => this.nzPopoverBackdrop],\n      ...super.getProxyPropertyMap()\n    };\n  }\n\n  constructor() {\n    super(NzPopoverComponent);\n  }\n}\n\n@Component({\n  selector: 'nz-popover',\n  exportAs: 'nzPopoverComponent',\n  animations: [zoomBigMotion],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  template: `\n    <ng-template\n      #overlay=\"cdkConnectedOverlay\"\n      cdkConnectedOverlay\n      nzConnectedOverlay\n      [cdkConnectedOverlayHasBackdrop]=\"hasBackdrop\"\n      [cdkConnectedOverlayOrigin]=\"origin\"\n      [cdkConnectedOverlayPositions]=\"_positions\"\n      [cdkConnectedOverlayOpen]=\"_visible\"\n      [cdkConnectedOverlayPush]=\"cdkConnectedOverlayPush\"\n      [nzArrowPointAtCenter]=\"nzArrowPointAtCenter\"\n      (overlayOutsideClick)=\"onClickOutside($event)\"\n      (detach)=\"hide()\"\n      (positionChange)=\"onPositionChange($event)\"\n    >\n      <div\n        class=\"ant-popover\"\n        [class.ant-popover-rtl]=\"dir === 'rtl'\"\n        [class]=\"_classMap\"\n        [style]=\"nzOverlayStyle\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        [@zoomBigMotion]=\"'active'\"\n      >\n        <div class=\"ant-popover-content\">\n          <div class=\"ant-popover-arrow\">\n            <span class=\"ant-popover-arrow-content\"></span>\n          </div>\n          <div class=\"ant-popover-inner\" role=\"tooltip\">\n            <div>\n              @if (nzTitle) {\n                <div class=\"ant-popover-title\">\n                  <ng-container *nzStringTemplateOutlet=\"nzTitle\">{{ nzTitle }}</ng-container>\n                </div>\n              }\n              <div class=\"ant-popover-inner-content\">\n                <ng-container *nzStringTemplateOutlet=\"nzContent\">{{ nzContent }}</ng-container>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </ng-template>\n  `,\n  imports: [OverlayModule, NzOverlayModule, NzNoAnimationDirective, NzOutletModule]\n})\nexport class NzPopoverComponent extends NzToolTipComponent {\n  override _prefix = 'ant-popover';\n\n  get hasBackdrop(): boolean {\n    return this.nzTrigger === 'click' ? this.nzBackdrop : false;\n  }\n\n  protected override isEmpty(): boolean {\n    return isTooltipEmpty(this.nzTitle) && isTooltipEmpty(this.nzContent);\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzPopoverComponent, NzPopoverDirective } from './popover';\n\n@NgModule({\n  imports: [NzPopoverDirective, NzPopoverComponent],\n  exports: [NzPopoverDirective, NzPopoverComponent]\n})\nexport class NzPopoverModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './popover';\nexport * from './popover.module';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAgCA,MAAM,qBAAqB,GAAgB,SAAS;IASvC,kBAAkB,GAAA,CAAA,MAAA;sBAAS,sBAAsB;;;;AAAjD,IAAA,OAAA,MAAA,kBAAmB,SAAQ,WAAsB,CAAA;;;AAoBlD,YAAA,6BAAA,GAAA,CAAA,UAAU,EAAE,CAAA;YAAC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,6BAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,mBAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,iBAAiB,EAAjB,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,iBAAiB,GAAmB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,+BAAA,EAAA,oCAAA,CAAA;;;QAnBlD,aAAa,GAAgB,qBAAqB;;AAG4B,QAAA,kBAAkB;AACvE,QAAA,KAAK;AACH,QAAA,OAAO;AACb,QAAA,cAAc;QACR,OAAO,GAAsB,OAAO;QAClC,SAAS,GAAuB,KAAK;AACxC,QAAA,MAAM;AACL,QAAA,OAAO;AACC,QAAA,eAAe;AACf,QAAA,eAAe;AACd,QAAA,gBAAgB;AACpB,QAAA,YAAY;AACR,QAAA,gBAAgB;QAEpD,gBAAgB,GAAqB,IAAI;QAE3B,iBAAiB,GAAA,iBAAA,CAAA,IAAA,EAAA,+BAAA,EAAa,KAAK,CAAC;AAEP,QAAA,aAAa,IAAG,iBAAA,CAAA,IAAA,EAAA,oCAAA,CAAA,EAAA,IAAI,YAAY,EAAW;QAE5E,mBAAmB,GAAA;YACpC,OAAO;gBACL,iBAAiB,EAAE,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/D,GAAG,KAAK,CAAC,mBAAmB;aAC7B;;AAGH,QAAA,WAAA,GAAA;YACE,KAAK,CAAC,kBAAkB,CAAC;;2GAhChB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAlB,QAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,oIAI6B,gBAAgB,CAAA,EAAA,KAAA,EAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,EAAA,OAAA,EAAA,CAAA,kBAAA,EAAA,SAAA,CAAA,EAAA,cAAA,EAAA,CAAA,YAAA,EAAA,gBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,kBAAA,EAAA,SAAA,CAAA,EAAA,SAAA,EAAA,CAAA,oBAAA,EAAA,WAAA,CAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,kBAAA,EAAA,SAAA,CAAA,EAAA,eAAA,EAAA,CAAA,0BAAA,EAAA,iBAAA,CAAA,EAAA,eAAA,EAAA,CAAA,0BAAA,EAAA,iBAAA,CAAA,EAAA,gBAAA,EAAA,CAAA,2BAAA,EAAA,kBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,uBAAA,EAAA,cAAA,CAAA,EAAA,gBAAA,EAAA,CAAA,2BAAA,EAAA,kBAAA,CAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,aAAA,EAAA,wBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,wBAAA,EAAA,SAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;;2FAJ/D,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAP9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,IAAI,EAAE;AACJ,wBAAA,0BAA0B,EAAE;AAC7B;AACF,iBAAA;wDAKwF,kBAAkB,EAAA,CAAA;sBAAxG,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAE,KAAK,EAAE,6BAA6B,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC1C,KAAK,EAAA,CAAA;sBAAtC,KAAK;uBAAC,gBAAgB;gBACa,OAAO,EAAA,CAAA;sBAA1C,KAAK;uBAAC,kBAAkB;gBACK,cAAc,EAAA,CAAA;sBAA3C,KAAK;uBAAC,YAAY;gBACiB,OAAO,EAAA,CAAA;sBAA1C,KAAK;uBAAC,kBAAkB;gBACa,SAAS,EAAA,CAAA;sBAA9C,KAAK;uBAAC,oBAAoB;gBACQ,MAAM,EAAA,CAAA;sBAAxC,KAAK;uBAAC,iBAAiB;gBACY,OAAO,EAAA,CAAA;sBAA1C,KAAK;uBAAC,kBAAkB;gBACmB,eAAe,EAAA,CAAA;sBAA1D,KAAK;uBAAC,0BAA0B;gBACW,eAAe,EAAA,CAAA;sBAA1D,KAAK;uBAAC,0BAA0B;gBACY,gBAAgB,EAAA,CAAA;sBAA5D,KAAK;uBAAC,2BAA2B;gBACO,YAAY,EAAA,CAAA;sBAApD,KAAK;uBAAC,uBAAuB;gBACe,gBAAgB,EAAA,CAAA;sBAA5D,KAAK;uBAAC,2BAA2B;gBAIX,iBAAiB,EAAA,CAAA;sBAAvC;gBAEmD,aAAa,EAAA,CAAA;sBAAhE,MAAM;uBAAC,wBAAwB;;AAmE5B,MAAO,kBAAmB,SAAQ,kBAAkB,CAAA;IAC/C,OAAO,GAAG,aAAa;AAEhC,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,SAAS,KAAK,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK;;IAG1C,OAAO,GAAA;AACxB,QAAA,OAAO,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC;;uGAR5D,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,EA9CnB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2CT,EACS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,aAAa,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,mBAAA,EAAA,QAAA,EAAA,qEAAA,EAAA,MAAA,EAAA,CAAA,2BAAA,EAAA,8BAAA,EAAA,qCAAA,EAAA,4BAAA,EAAA,4BAAA,EAAA,0BAAA,EAAA,2BAAA,EAAA,6BAAA,EAAA,8BAAA,EAAA,kCAAA,EAAA,+BAAA,EAAA,mCAAA,EAAA,mCAAA,EAAA,yBAAA,EAAA,iCAAA,EAAA,sCAAA,EAAA,gCAAA,EAAA,iCAAA,EAAA,uCAAA,EAAA,kCAAA,EAAA,yBAAA,EAAA,wCAAA,CAAA,EAAA,OAAA,EAAA,CAAA,eAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,qBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,eAAe,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,2BAAA,EAAA,QAAA,EAAA,2CAAA,EAAA,MAAA,EAAA,CAAA,sBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,sBAAsB,mHAAE,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAhDpE,CAAC,aAAa,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAkDhB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBArD9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,QAAQ,EAAE,oBAAoB;oBAC9B,UAAU,EAAE,CAAC,aAAa,CAAC;oBAC3B,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CT,EAAA,CAAA;oBACD,OAAO,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,sBAAsB,EAAE,cAAc;AACjF,iBAAA;;;ACjID;;;AAGG;MAUU,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAHhB,kBAAkB,EAAE,kBAAkB,CACtC,EAAA,OAAA,EAAA,CAAA,kBAAkB,EAAE,kBAAkB,CAAA,EAAA,CAAA;AAErC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAHI,kBAAkB,CAAA,EAAA,CAAA;;2FAGrC,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;AACjD,oBAAA,OAAO,EAAE,CAAC,kBAAkB,EAAE,kBAAkB;AACjD,iBAAA;;;ACZD;;;AAGG;;ACHH;;AAEG;;;;"}