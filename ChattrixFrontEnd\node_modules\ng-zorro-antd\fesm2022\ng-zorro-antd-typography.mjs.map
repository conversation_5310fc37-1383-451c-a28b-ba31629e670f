{"version": 3, "file": "ng-zorro-antd-typography.mjs", "sources": ["../../components/typography/text-copy.component.ts", "../../components/typography/text-edit.component.ts", "../../components/typography/typography.component.ts", "../../components/typography/typography.module.ts", "../../components/typography/public-api.ts", "../../components/typography/ng-zorro-antd-typography.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Clipboard } from '@angular/cdk/clipboard';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  inject,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  SimpleChanges,\n  ViewEncapsulation\n} from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzTransButtonModule } from 'ng-zorro-antd/core/trans-button';\nimport { NzTSType } from 'ng-zorro-antd/core/types';\nimport { NzI18nService, NzTextI18nInterface } from 'ng-zorro-antd/i18n';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzToolTipModule } from 'ng-zorro-antd/tooltip';\n\n@Component({\n  selector: 'nz-text-copy',\n  exportAs: 'nzTextCopy',\n  template: `\n    <button\n      type=\"button\"\n      nz-tooltip\n      nz-trans-button\n      [nzTooltipTitle]=\"copied ? copedTooltip : copyTooltip\"\n      class=\"ant-typography-copy\"\n      [class.ant-typography-copy-success]=\"copied\"\n      (click)=\"onClick()\"\n    >\n      <ng-container *nzStringTemplateOutlet=\"copied ? copedIcon : copyIcon; let icon\">\n        <nz-icon [nzType]=\"icon\" />\n      </ng-container>\n    </button>\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  imports: [NzToolTipModule, NzTransButtonModule, NzIconModule, NzOutletModule]\n})\nexport class NzTextCopyComponent implements OnInit, OnDestroy, OnChanges {\n  copied = false;\n  copyId?: ReturnType<typeof setTimeout>;\n  locale!: NzTextI18nInterface;\n  nativeElement = inject(ElementRef).nativeElement;\n  copyTooltip: NzTSType | null = null;\n  copedTooltip: NzTSType | null = null;\n  copyIcon: NzTSType = 'copy';\n  copedIcon: NzTSType = 'check';\n  private destroy$ = new Subject<boolean>();\n\n  @Input() text!: string;\n  @Input() tooltips?: [NzTSType, NzTSType] | null;\n  @Input() icons: [NzTSType, NzTSType] = ['copy', 'check'];\n\n  @Output() readonly textCopy = new EventEmitter<string>();\n\n  constructor(\n    private cdr: ChangeDetectorRef,\n    private clipboard: Clipboard,\n    private i18n: NzI18nService\n  ) {}\n\n  ngOnInit(): void {\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Text');\n      this.updateTooltips();\n      this.cdr.markForCheck();\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { tooltips, icons } = changes;\n    if (tooltips) {\n      this.updateTooltips();\n    }\n    if (icons) {\n      this.updateIcons();\n    }\n  }\n\n  ngOnDestroy(): void {\n    clearTimeout(this.copyId);\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n\n  onClick(): void {\n    if (this.copied) {\n      return;\n    }\n    this.copied = true;\n    this.cdr.detectChanges();\n    const text = this.text;\n    this.textCopy.emit(text);\n    this.clipboard.copy(text);\n    this.onCopied();\n  }\n\n  onCopied(): void {\n    clearTimeout(this.copyId);\n    this.copyId = setTimeout(() => {\n      this.copied = false;\n      this.cdr.detectChanges();\n    }, 3000);\n  }\n\n  private updateTooltips(): void {\n    if (this.tooltips === null) {\n      this.copedTooltip = null;\n      this.copyTooltip = null;\n    } else if (Array.isArray(this.tooltips)) {\n      const [copyTooltip, copedTooltip] = this.tooltips;\n      this.copyTooltip = copyTooltip || this.locale?.copy;\n      this.copedTooltip = copedTooltip || this.locale?.copied;\n    } else {\n      this.copyTooltip = this.locale?.copy;\n      this.copedTooltip = this.locale?.copied;\n    }\n    this.cdr.markForCheck();\n  }\n\n  private updateIcons(): void {\n    const [copyIcon, copedIcon] = this.icons;\n    this.copyIcon = copyIcon;\n    this.copedIcon = copedIcon;\n    this.cdr.markForCheck();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { ENTER, ESCAPE } from '@angular/cdk/keycodes';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Injector,\n  Input,\n  NgZone,\n  OnInit,\n  Output,\n  ViewChild,\n  ViewEncapsulation,\n  afterNextRender,\n  inject\n} from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { first, switchMap, takeUntil } from 'rxjs/operators';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { NzTransButtonModule } from 'ng-zorro-antd/core/trans-button';\nimport { NzTSType } from 'ng-zorro-antd/core/types';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\nimport { NzI18nService, NzTextI18nInterface } from 'ng-zorro-antd/i18n';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzAutosizeDirective, NzInputModule } from 'ng-zorro-antd/input';\nimport { NzToolTipModule } from 'ng-zorro-antd/tooltip';\n\n@Component({\n  selector: 'nz-text-edit',\n  exportAs: 'nzTextEdit',\n  template: `\n    @if (editing) {\n      <textarea #textarea nz-input nzAutosize (blur)=\"confirm()\"></textarea>\n      <button nz-trans-button class=\"ant-typography-edit-content-confirm\" (click)=\"confirm()\">\n        <nz-icon nzType=\"enter\" />\n      </button>\n    } @else {\n      <button\n        nz-tooltip\n        nz-trans-button\n        class=\"ant-typography-edit\"\n        [nzTooltipTitle]=\"tooltip === null ? null : tooltip || locale?.edit\"\n        (click)=\"onClick()\"\n      >\n        <ng-container *nzStringTemplateOutlet=\"icon; let icon\">\n          <nz-icon [nzType]=\"icon\" />\n        </ng-container>\n      </button>\n    }\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  providers: [NzDestroyService],\n  imports: [NzInputModule, NzTransButtonModule, NzIconModule, NzToolTipModule, NzOutletModule]\n})\nexport class NzTextEditComponent implements OnInit {\n  editing = false;\n  locale!: NzTextI18nInterface;\n\n  @Input() text?: string;\n  @Input() icon: NzTSType = 'edit';\n  @Input() tooltip?: null | NzTSType;\n  @Output() readonly startEditing = new EventEmitter<void>();\n  @Output() readonly endEditing = new EventEmitter<string>(true);\n  @ViewChild('textarea', { static: false })\n  set textarea(textarea: ElementRef<HTMLTextAreaElement> | undefined) {\n    this.textarea$.next(textarea);\n  }\n  @ViewChild(NzAutosizeDirective, { static: false }) autosizeDirective!: NzAutosizeDirective;\n\n  beforeText?: string;\n  currentText?: string;\n  nativeElement: HTMLElement = inject(ElementRef).nativeElement;\n\n  // We could've saved the textarea within some private property (e.g. `_textarea`) and have a getter,\n  // but having subject makes the code more reactive and cancellable (e.g. event listeners will be\n  // automatically removed and re-added through the `switchMap` below).\n  private textarea$ = new BehaviorSubject<ElementRef<HTMLTextAreaElement> | null | undefined>(null);\n\n  private injector = inject(Injector);\n\n  constructor(\n    private ngZone: NgZone,\n    private cdr: ChangeDetectorRef,\n    private i18n: NzI18nService,\n    private destroy$: NzDestroyService\n  ) {}\n\n  ngOnInit(): void {\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Text');\n      this.cdr.markForCheck();\n    });\n\n    this.textarea$\n      .pipe(\n        switchMap(textarea => fromEventOutsideAngular<KeyboardEvent>(textarea?.nativeElement, 'keydown')),\n        takeUntil(this.destroy$)\n      )\n      .subscribe(event => {\n        // Caretaker note: adding modifier at the end (for instance `(keydown.esc)`) will tell Angular to add\n        // an event listener through the `KeyEventsPlugin`, which always runs `ngZone.runGuarded()` internally.\n        // We're interested only in escape and enter keyboard buttons, otherwise Angular will run change detection\n        // on any `keydown` event.\n        if (event.keyCode !== ESCAPE && event.keyCode !== ENTER) {\n          return;\n        }\n\n        this.ngZone.run(() => {\n          if (event.keyCode === ESCAPE) {\n            this.onCancel();\n          } else {\n            this.onEnter(event);\n          }\n          this.cdr.markForCheck();\n        });\n      });\n\n    this.textarea$\n      .pipe(\n        switchMap(textarea => fromEventOutsideAngular<KeyboardEvent>(textarea?.nativeElement, 'input')),\n        takeUntil(this.destroy$)\n      )\n      .subscribe(event => {\n        this.currentText = (event.target as HTMLTextAreaElement).value;\n      });\n  }\n\n  onClick(): void {\n    this.beforeText = this.text;\n    this.currentText = this.beforeText;\n    this.editing = true;\n    this.startEditing.emit();\n    this.focusAndSetValue();\n  }\n\n  confirm(): void {\n    this.editing = false;\n    this.endEditing.emit(this.currentText);\n  }\n\n  onEnter(event: Event): void {\n    event.stopPropagation();\n    event.preventDefault();\n    this.confirm();\n  }\n\n  onCancel(): void {\n    this.currentText = this.beforeText;\n    this.confirm();\n  }\n\n  focusAndSetValue(): void {\n    const { injector } = this;\n\n    afterNextRender(\n      () => {\n        this.textarea$\n          .pipe(\n            // It may still not be available, so we need to wait until view queries\n            // are executed during the change detection. It's safer to wait until\n            // the query runs and the textarea is set on the behavior subject.\n            first((textarea): textarea is ElementRef<HTMLTextAreaElement> => textarea != null),\n            takeUntil(this.destroy$)\n          )\n          .subscribe(textarea => {\n            textarea.nativeElement.focus();\n            textarea.nativeElement.value = this.currentText || '';\n            this.autosizeDirective.resizeToFitContent();\n            this.cdr.markForCheck();\n          });\n      },\n      { injector }\n    );\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { Platform } from '@angular/cdk/platform';\nimport { DOCUMENT, NgTemplateOutlet } from '@angular/common';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EmbeddedViewRef,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  Renderer2,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject,\n  numberAttribute\n} from '@angular/core';\nimport { Subject, Subscription } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzConfigKey, NzConfigService, WithConfig } from 'ng-zorro-antd/core/config';\nimport { cancelRequestAnimationFrame, reqAnimFrame } from 'ng-zorro-antd/core/polyfill';\nimport { NzResizeService } from 'ng-zorro-antd/core/services';\nimport { NzTSType } from 'ng-zorro-antd/core/types';\nimport { isStyleSupport, measure } from 'ng-zorro-antd/core/util';\nimport { NzI18nService, NzTextI18nInterface } from 'ng-zorro-antd/i18n';\n\nimport { NzTextCopyComponent } from './text-copy.component';\nimport { NzTextEditComponent } from './text-edit.component';\n\nconst NZ_CONFIG_MODULE_NAME: NzConfigKey = 'typography';\nconst EXPAND_ELEMENT_CLASSNAME = 'ant-typography-expand';\n\n@Component({\n  selector: `\n  nz-typography,\n  [nz-typography],\n  p[nz-paragraph],\n  span[nz-text],\n  h1[nz-title], h2[nz-title], h3[nz-title], h4[nz-title]\n  `,\n  exportAs: 'nzTypography',\n  template: `\n    <ng-template #contentTemplate let-content=\"content\">\n      @if (!content) {\n        <ng-content></ng-content>\n      }\n      {{ content }}\n    </ng-template>\n    @if (!editing) {\n      @if (\n        expanded ||\n        (!hasOperationsWithEllipsis && nzEllipsisRows === 1 && !hasEllipsisObservers) ||\n        canCssEllipsis ||\n        (nzSuffix && expanded)\n      ) {\n        <ng-template\n          [ngTemplateOutlet]=\"contentTemplate\"\n          [ngTemplateOutletContext]=\"{ content: nzContent }\"\n        ></ng-template>\n        @if (nzSuffix) {\n          {{ nzSuffix }}\n        }\n      } @else {\n        <span #ellipsisContainer></span>\n        @if (isEllipsis) {\n          {{ ellipsisStr }}\n        }\n        @if (nzSuffix) {\n          {{ nzSuffix }}\n        }\n        @if (nzExpandable && isEllipsis) {\n          <a #expandable class=\"ant-typography-expand\" (click)=\"onExpand()\">\n            {{ locale?.expand }}\n          </a>\n        }\n      }\n    }\n\n    @if (nzEditable) {\n      <nz-text-edit\n        [text]=\"nzContent\"\n        [icon]=\"nzEditIcon\"\n        [tooltip]=\"nzEditTooltip\"\n        (endEditing)=\"onEndEditing($event)\"\n        (startEditing)=\"onStartEditing()\"\n      ></nz-text-edit>\n    }\n\n    @if (nzCopyable && !editing) {\n      <nz-text-copy\n        [text]=\"copyText\"\n        [tooltips]=\"nzCopyTooltips\"\n        [icons]=\"nzCopyIcons\"\n        (textCopy)=\"onTextCopy($event)\"\n      ></nz-text-copy>\n    }\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  host: {\n    '[class.ant-typography]': '!editing',\n    '[class.ant-typography-rtl]': 'dir === \"rtl\"',\n    '[class.ant-typography-edit-content]': 'editing',\n    '[class.ant-typography-secondary]': 'nzType === \"secondary\"',\n    '[class.ant-typography-warning]': 'nzType === \"warning\"',\n    '[class.ant-typography-danger]': 'nzType === \"danger\"',\n    '[class.ant-typography-success]': 'nzType === \"success\"',\n    '[class.ant-typography-disabled]': 'nzDisabled',\n    '[class.ant-typography-ellipsis]': 'nzEllipsis && !expanded',\n    '[class.ant-typography-single-line]': 'nzEllipsis && nzEllipsisRows === 1',\n    '[class.ant-typography-ellipsis-single-line]': 'canCssEllipsis && nzEllipsisRows === 1',\n    '[class.ant-typography-ellipsis-multiple-line]': 'canCssEllipsis && nzEllipsisRows > 1',\n    '[style.-webkit-line-clamp]': '(canCssEllipsis && nzEllipsisRows > 1) ? nzEllipsisRows : null'\n  },\n  imports: [NgTemplateOutlet, NzTextEditComponent, NzTextCopyComponent]\n})\nexport class NzTypographyComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {\n  readonly _nzModuleName: NzConfigKey = NZ_CONFIG_MODULE_NAME;\n\n  @Input({ transform: booleanAttribute }) nzCopyable = false;\n  @Input({ transform: booleanAttribute }) nzEditable = false;\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Input({ transform: booleanAttribute }) nzExpandable = false;\n  @Input({ transform: booleanAttribute }) nzEllipsis = false;\n  @Input() @WithConfig() nzCopyTooltips?: [NzTSType, NzTSType] | null = undefined;\n  @Input() @WithConfig() nzCopyIcons: [NzTSType, NzTSType] = ['copy', 'check'];\n  @Input() @WithConfig() nzEditTooltip?: null | NzTSType = undefined;\n  @Input() @WithConfig() nzEditIcon: NzTSType = 'edit';\n  @Input() nzContent?: string;\n  @Input({ transform: numberAttribute }) @WithConfig() nzEllipsisRows: number = 1;\n  @Input() nzType: 'secondary' | 'warning' | 'danger' | 'success' | undefined;\n  @Input() nzCopyText: string | undefined;\n  @Input() nzSuffix: string | undefined;\n  @Output() readonly nzContentChange = new EventEmitter<string>();\n  @Output() readonly nzCopy = new EventEmitter<string>();\n  @Output() readonly nzExpandChange = new EventEmitter<void>();\n  // This is not a two-way binding output with {@link nzEllipsis}\n  @Output() readonly nzOnEllipsis = new EventEmitter<boolean>();\n\n  @ViewChild(NzTextEditComponent, { static: false }) textEditRef?: NzTextEditComponent;\n  @ViewChild(NzTextCopyComponent, { static: false }) textCopyRef?: NzTextCopyComponent;\n  @ViewChild('ellipsisContainer', { static: false }) ellipsisContainer?: ElementRef<HTMLSpanElement>;\n  @ViewChild('expandable', { static: false }) expandableBtn?: ElementRef<HTMLSpanElement>;\n  @ViewChild('contentTemplate', { static: false }) contentTemplate?: TemplateRef<{ content: string }>;\n\n  locale!: NzTextI18nInterface;\n  private document: Document = inject(DOCUMENT);\n  expandableBtnElementCache: HTMLElement | null = null;\n  editing = false;\n  ellipsisText: string | undefined;\n  cssEllipsis: boolean = false;\n  isEllipsis: boolean = true;\n  expanded: boolean = false;\n  ellipsisStr = '...';\n  dir: Direction = 'ltr';\n\n  get hasEllipsisObservers(): boolean {\n    return this.nzOnEllipsis.observers.length > 0;\n  }\n\n  get canCssEllipsis(): boolean {\n    return this.nzEllipsis && this.cssEllipsis && !this.expanded && !this.hasEllipsisObservers;\n  }\n\n  get hasOperationsWithEllipsis(): boolean {\n    return (this.nzCopyable || this.nzEditable || this.nzExpandable) && this.nzEllipsis;\n  }\n\n  private viewInit = false;\n  private rfaId: number = -1;\n  private destroy$ = new Subject<boolean>();\n  private windowResizeSubscription = Subscription.EMPTY;\n  get copyText(): string {\n    return (typeof this.nzCopyText === 'string' ? this.nzCopyText : this.nzContent)!;\n  }\n\n  constructor(\n    public nzConfigService: NzConfigService,\n    private host: ElementRef<HTMLElement>,\n    private cdr: ChangeDetectorRef,\n    private viewContainerRef: ViewContainerRef,\n    private renderer: Renderer2,\n    private platform: Platform,\n    private i18n: NzI18nService,\n    private resizeService: NzResizeService,\n    private directionality: Directionality\n  ) {}\n\n  onTextCopy(text: string): void {\n    this.nzCopy.emit(text);\n  }\n\n  onStartEditing(): void {\n    this.editing = true;\n  }\n\n  onEndEditing(text: string): void {\n    this.editing = false;\n    this.nzContentChange.emit(text);\n    if (this.nzContent === text) {\n      this.renderOnNextFrame();\n    }\n    this.cdr.markForCheck();\n  }\n\n  onExpand(): void {\n    this.isEllipsis = false;\n    this.expanded = true;\n    this.nzExpandChange.emit();\n    this.nzOnEllipsis.emit(false);\n  }\n\n  canUseCSSEllipsis(): boolean {\n    if (this.nzEditable || this.nzCopyable || this.nzExpandable || this.nzSuffix) {\n      return false;\n    }\n    // make sure {@link nzOnEllipsis} works, will force use JS to calculations\n    if (this.hasEllipsisObservers) {\n      return false;\n    }\n    if (this.nzEllipsisRows === 1) {\n      return isStyleSupport('textOverflow');\n    } else {\n      return isStyleSupport('webkitLineClamp');\n    }\n  }\n\n  renderOnNextFrame(): void {\n    cancelRequestAnimationFrame(this.rfaId);\n    if (!this.viewInit || !this.nzEllipsis || this.nzEllipsisRows < 0 || this.expanded || !this.platform.isBrowser) {\n      return;\n    }\n    this.rfaId = reqAnimFrame(() => {\n      this.syncEllipsis();\n    });\n  }\n\n  getOriginContentViewRef(): { viewRef: EmbeddedViewRef<{ content: string }>; removeView(): void } {\n    const viewRef = this.viewContainerRef.createEmbeddedView<{ content: string }>(this.contentTemplate!, {\n      content: this.nzContent!\n    });\n    viewRef.detectChanges();\n    return {\n      viewRef,\n      removeView: () => {\n        this.viewContainerRef.remove(this.viewContainerRef.indexOf(viewRef));\n      }\n    };\n  }\n\n  syncEllipsis(): void {\n    if (this.cssEllipsis) {\n      return;\n    }\n    const { viewRef, removeView } = this.getOriginContentViewRef();\n    const fixedNodes = [this.textCopyRef, this.textEditRef]\n      .filter(e => e && e.nativeElement)\n      .map(e => e!.nativeElement);\n    const expandableBtnElement = this.getExpandableBtnElement();\n    if (expandableBtnElement) {\n      fixedNodes.push(expandableBtnElement);\n    }\n    const { contentNodes, text, ellipsis } = measure(\n      this.host.nativeElement,\n      this.nzEllipsisRows,\n      viewRef.rootNodes,\n      fixedNodes,\n      this.ellipsisStr,\n      this.nzSuffix\n    );\n\n    removeView();\n\n    this.ellipsisText = text;\n    if (ellipsis !== this.isEllipsis) {\n      this.isEllipsis = ellipsis;\n      this.nzOnEllipsis.emit(ellipsis);\n    }\n    const ellipsisContainerNativeElement = this.ellipsisContainer!.nativeElement;\n    while (ellipsisContainerNativeElement.firstChild) {\n      this.renderer.removeChild(ellipsisContainerNativeElement, ellipsisContainerNativeElement.firstChild);\n    }\n    contentNodes.forEach(n => {\n      this.renderer.appendChild(ellipsisContainerNativeElement, n.cloneNode(true));\n    });\n    this.cdr.markForCheck();\n  }\n\n  // Need to create the element for calculation size before view init\n  private getExpandableBtnElement(): HTMLElement | null {\n    if (this.nzExpandable) {\n      const expandText = this.locale ? this.locale.expand : '';\n      const cache = this.expandableBtnElementCache;\n      if (!cache || cache.innerText === expandText) {\n        const el = this.document.createElement('a');\n        el.className = EXPAND_ELEMENT_CLASSNAME;\n        el.innerText = expandText;\n        this.expandableBtnElementCache = el;\n      }\n      return this.expandableBtnElementCache;\n    } else {\n      this.expandableBtnElementCache = null;\n      return null;\n    }\n  }\n\n  private renderAndSubscribeWindowResize(): void {\n    if (this.platform.isBrowser) {\n      this.windowResizeSubscription.unsubscribe();\n      this.cssEllipsis = this.canUseCSSEllipsis();\n      this.renderOnNextFrame();\n      this.windowResizeSubscription = this.resizeService\n        .subscribe()\n        .pipe(takeUntil(this.destroy$))\n        .subscribe(() => this.renderOnNextFrame());\n    }\n  }\n\n  ngOnInit(): void {\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Text');\n      this.cdr.markForCheck();\n    });\n\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n\n    this.dir = this.directionality.value;\n  }\n\n  ngAfterViewInit(): void {\n    this.viewInit = true;\n    this.renderAndSubscribeWindowResize();\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzCopyable, nzEditable, nzExpandable, nzEllipsis, nzContent, nzEllipsisRows, nzSuffix } = changes;\n    if (nzCopyable || nzEditable || nzExpandable || nzEllipsis || nzContent || nzEllipsisRows || nzSuffix) {\n      if (this.nzEllipsis) {\n        if (this.expanded) {\n          this.windowResizeSubscription.unsubscribe();\n        } else {\n          this.renderAndSubscribeWindowResize();\n        }\n      }\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n    this.expandableBtnElementCache = null;\n    this.windowResizeSubscription.unsubscribe();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzTextCopyComponent } from './text-copy.component';\nimport { NzTextEditComponent } from './text-edit.component';\nimport { NzTypographyComponent } from './typography.component';\n\n@NgModule({\n  imports: [NzTypographyComponent, NzTextCopyComponent, NzTextEditComponent],\n  exports: [NzTypographyComponent, NzTextCopyComponent, NzTextEditComponent]\n})\nexport class NzTypographyModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport { NzTypographyModule } from './typography.module';\nexport { NzTypographyComponent } from './typography.component';\nexport { NzTextCopyComponent } from './text-copy.component';\nexport { NzTextEditComponent } from './text-edit.component';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1", "i2", "i7", "i3", "i4", "i5"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;MAsDa,mBAAmB,CAAA;AAkBpB,IAAA,GAAA;AACA,IAAA,SAAA;AACA,IAAA,IAAA;IAnBV,MAAM,GAAG,KAAK;AACd,IAAA,MAAM;AACN,IAAA,MAAM;AACN,IAAA,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,aAAa;IAChD,WAAW,GAAoB,IAAI;IACnC,YAAY,GAAoB,IAAI;IACpC,QAAQ,GAAa,MAAM;IAC3B,SAAS,GAAa,OAAO;AACrB,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAW;AAEhC,IAAA,IAAI;AACJ,IAAA,QAAQ;AACR,IAAA,KAAK,GAAyB,CAAC,MAAM,EAAE,OAAO,CAAC;AAErC,IAAA,QAAQ,GAAG,IAAI,YAAY,EAAU;AAExD,IAAA,WAAA,CACU,GAAsB,EACtB,SAAoB,EACpB,IAAmB,EAAA;QAFnB,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAI,CAAA,IAAA,GAAJ,IAAI;;IAGd,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YACnE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YAC7C,IAAI,CAAC,cAAc,EAAE;AACrB,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,SAAC,CAAC;;AAGJ,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,OAAO;QACnC,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,cAAc,EAAE;;QAEvB,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,WAAW,EAAE;;;IAItB,WAAW,GAAA;AACT,QAAA,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;AACzB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;IAG1B,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf;;AAEF,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI;AAClB,QAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AACxB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI;AACtB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,QAAQ,EAAE;;IAGjB,QAAQ,GAAA;AACN,QAAA,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;AACzB,QAAA,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,MAAK;AAC5B,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;SACzB,EAAE,IAAI,CAAC;;IAGF,cAAc,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;AAC1B,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;aAClB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACvC,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,IAAI,CAAC,QAAQ;YACjD,IAAI,CAAC,WAAW,GAAG,WAAW,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI;YACnD,IAAI,CAAC,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC,MAAM,EAAE,MAAM;;aAClD;YACL,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI;YACpC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM;;AAEzC,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;IAGjB,WAAW,GAAA;QACjB,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK;AACxC,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;AACxB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS;AAC1B,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;uGAtFd,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,EApBpB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;AAcT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAIS,eAAe,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,kBAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,uBAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,iBAAA,EAAA,kBAAA,EAAA,0BAAA,EAAA,0BAAA,EAAA,2BAAA,EAAA,uBAAA,EAAA,6BAAA,EAAA,yBAAA,EAAA,gBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,mBAAmB,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,sBAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,yNAAE,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEjE,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAvB/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;AAcT,EAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,mBAAmB,EAAE,KAAK;oBAC1B,OAAO,EAAE,CAAC,eAAe,EAAE,mBAAmB,EAAE,YAAY,EAAE,cAAc;AAC7E,iBAAA;0IAYU,IAAI,EAAA,CAAA;sBAAZ;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBAEkB,QAAQ,EAAA,CAAA;sBAA1B;;;ACrEH;;;AAGG;MA6DU,mBAAmB,CAAA;AA2BpB,IAAA,MAAA;AACA,IAAA,GAAA;AACA,IAAA,IAAA;AACA,IAAA,QAAA;IA7BV,OAAO,GAAG,KAAK;AACf,IAAA,MAAM;AAEG,IAAA,IAAI;IACJ,IAAI,GAAa,MAAM;AACvB,IAAA,OAAO;AACG,IAAA,YAAY,GAAG,IAAI,YAAY,EAAQ;AACvC,IAAA,UAAU,GAAG,IAAI,YAAY,CAAS,IAAI,CAAC;IAC9D,IACI,QAAQ,CAAC,QAAqD,EAAA;AAChE,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAEoB,IAAA,iBAAiB;AAEpE,IAAA,UAAU;AACV,IAAA,WAAW;AACX,IAAA,aAAa,GAAgB,MAAM,CAAC,UAAU,CAAC,CAAC,aAAa;;;;AAKrD,IAAA,SAAS,GAAG,IAAI,eAAe,CAAqD,IAAI,CAAC;AAEzF,IAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AAEnC,IAAA,WAAA,CACU,MAAc,EACd,GAAsB,EACtB,IAAmB,EACnB,QAA0B,EAAA;QAH1B,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAQ,CAAA,QAAA,GAAR,QAAQ;;IAGlB,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YACnE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;AAC7C,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC;aACF,IAAI,CACH,SAAS,CAAC,QAAQ,IAAI,uBAAuB,CAAgB,QAAQ,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC,EACjG,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAEzB,SAAS,CAAC,KAAK,IAAG;;;;;AAKjB,YAAA,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,EAAE;gBACvD;;AAGF,YAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAK;AACnB,gBAAA,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;oBAC5B,IAAI,CAAC,QAAQ,EAAE;;qBACV;AACL,oBAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;;AAErB,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;AACJ,SAAC,CAAC;AAEJ,QAAA,IAAI,CAAC;aACF,IAAI,CACH,SAAS,CAAC,QAAQ,IAAI,uBAAuB,CAAgB,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC,EAC/F,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAEzB,SAAS,CAAC,KAAK,IAAG;YACjB,IAAI,CAAC,WAAW,GAAI,KAAK,CAAC,MAA8B,CAAC,KAAK;AAChE,SAAC,CAAC;;IAGN,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI;AAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU;AAClC,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI;AACnB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;QACxB,IAAI,CAAC,gBAAgB,EAAE;;IAGzB,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;;AAGxC,IAAA,OAAO,CAAC,KAAY,EAAA;QAClB,KAAK,CAAC,eAAe,EAAE;QACvB,KAAK,CAAC,cAAc,EAAE;QACtB,IAAI,CAAC,OAAO,EAAE;;IAGhB,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU;QAClC,IAAI,CAAC,OAAO,EAAE;;IAGhB,gBAAgB,GAAA;AACd,QAAA,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI;QAEzB,eAAe,CACb,MAAK;AACH,YAAA,IAAI,CAAC;iBACF,IAAI;;;;AAIH,YAAA,KAAK,CAAC,CAAC,QAAQ,KAAkD,QAAQ,IAAI,IAAI,CAAC,EAClF,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAEzB,SAAS,CAAC,QAAQ,IAAG;AACpB,gBAAA,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE;gBAC9B,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE;AACrD,gBAAA,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAAE;AAC3C,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;AACN,SAAC,EACD,EAAE,QAAQ,EAAE,CACb;;uGAtHQ,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,4LAHnB,CAAC,gBAAgB,CAAC,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAgBlB,mBAAmB,EAvCpB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;GAmBT,EAKS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,aAAa,iXAAE,mBAAmB,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,sBAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAE,YAAY,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,eAAe,wfAAE,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAC,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEhF,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBA7B/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;AAmBT,EAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,mBAAmB,EAAE,KAAK;oBAC1B,SAAS,EAAE,CAAC,gBAAgB,CAAC;oBAC7B,OAAO,EAAE,CAAC,aAAa,EAAE,mBAAmB,EAAE,YAAY,EAAE,eAAe,EAAE,cAAc;AAC5F,iBAAA;wKAKU,IAAI,EAAA,CAAA;sBAAZ;gBACQ,IAAI,EAAA,CAAA;sBAAZ;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACkB,YAAY,EAAA,CAAA;sBAA9B;gBACkB,UAAU,EAAA,CAAA;sBAA5B;gBAEG,QAAQ,EAAA,CAAA;sBADX,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBAIW,iBAAiB,EAAA,CAAA;sBAAnE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,mBAAmB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;;;ACjCnD,MAAM,qBAAqB,GAAgB,YAAY;AACvD,MAAM,wBAAwB,GAAG,uBAAuB;IAuF3C,qBAAqB,GAAA,CAAA,MAAA;;;;;;;;;;;;;;;;iBAArB,qBAAqB,CAAA;;;AAQtB,YAAA,0BAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,uBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,yBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,sBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AAEkB,YAAA,0BAAA,GAAA,CAAA,UAAU,EAAE,CAAA;YAL7B,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,0BAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,gBAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,cAAc,EAAd,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,cAAc,GAA2C,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,4BAAA,EAAA,iCAAA,CAAA;YACzD,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,uBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,aAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,aAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,WAAW,EAAX,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,WAAW,GAA2C,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,yBAAA,EAAA,8BAAA,CAAA;YACtD,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,yBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,eAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,eAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,aAAa,EAAb,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,aAAa,GAA+B,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,2BAAA,EAAA,gCAAA,CAAA;YAC5C,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,YAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,UAAU,EAAV,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,UAAU,GAAoB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,wBAAA,EAAA,6BAAA,CAAA;YAEA,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,0BAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,gBAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,cAAc,EAAd,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,cAAc,GAAa,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,4BAAA,EAAA,iCAAA,CAAA;;;QAgDvE,eAAe;QACd,IAAI;QACJ,GAAG;QACH,gBAAgB;QAChB,QAAQ;QACR,QAAQ;QACR,IAAI;QACJ,aAAa;QACb,cAAc;QApEf,aAAa,GAAgB,qBAAqB;QAEnB,UAAU,GAAG,KAAK;QAClB,UAAU,GAAG,KAAK;QAClB,UAAU,GAAG,KAAK;QAClB,YAAY,GAAG,KAAK;QACpB,UAAU,GAAG,KAAK;QACnC,cAAc,GAAA,iBAAA,CAAA,IAAA,EAAA,4BAAA,EAAiC,SAAS,CAAC;AACzD,QAAA,WAAW,mHAAyB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACtD,aAAa,IAAA,iBAAA,CAAA,IAAA,EAAA,8BAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,2BAAA,EAAqB,SAAS,CAAC;QAC5C,UAAU,IAAA,iBAAA,CAAA,IAAA,EAAA,gCAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,wBAAA,EAAa,MAAM,CAAC;AAC5C,QAAA,SAAS,GAAU,iBAAA,CAAA,IAAA,EAAA,6BAAA,CAAA;QACyB,cAAc,GAAA,iBAAA,CAAA,IAAA,EAAA,4BAAA,EAAW,CAAC,CAAC;AACvE,QAAA,MAAM,GAA6D,iBAAA,CAAA,IAAA,EAAA,iCAAA,CAAA;AACnE,QAAA,UAAU;AACV,QAAA,QAAQ;AACE,QAAA,eAAe,GAAG,IAAI,YAAY,EAAU;AAC5C,QAAA,MAAM,GAAG,IAAI,YAAY,EAAU;AACnC,QAAA,cAAc,GAAG,IAAI,YAAY,EAAQ;;AAEzC,QAAA,YAAY,GAAG,IAAI,YAAY,EAAW;AAEV,QAAA,WAAW;AACX,QAAA,WAAW;AACX,QAAA,iBAAiB;AACxB,QAAA,aAAa;AACR,QAAA,eAAe;AAEhE,QAAA,MAAM;AACE,QAAA,QAAQ,GAAa,MAAM,CAAC,QAAQ,CAAC;QAC7C,yBAAyB,GAAuB,IAAI;QACpD,OAAO,GAAG,KAAK;AACf,QAAA,YAAY;QACZ,WAAW,GAAY,KAAK;QAC5B,UAAU,GAAY,IAAI;QAC1B,QAAQ,GAAY,KAAK;QACzB,WAAW,GAAG,KAAK;QACnB,GAAG,GAAc,KAAK;AAEtB,QAAA,IAAI,oBAAoB,GAAA;YACtB,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;;AAG/C,QAAA,IAAI,cAAc,GAAA;AAChB,YAAA,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,oBAAoB;;AAG5F,QAAA,IAAI,yBAAyB,GAAA;AAC3B,YAAA,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,UAAU;;QAG7E,QAAQ,GAAG,KAAK;QAChB,KAAK,GAAW,CAAC,CAAC;AAClB,QAAA,QAAQ,GAAG,IAAI,OAAO,EAAW;AACjC,QAAA,wBAAwB,GAAG,YAAY,CAAC,KAAK;AACrD,QAAA,IAAI,QAAQ,GAAA;YACV,QAAQ,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS;;AAGhF,QAAA,WAAA,CACS,eAAgC,EAC/B,IAA6B,EAC7B,GAAsB,EACtB,gBAAkC,EAClC,QAAmB,EACnB,QAAkB,EAClB,IAAmB,EACnB,aAA8B,EAC9B,cAA8B,EAAA;YAR/B,IAAe,CAAA,eAAA,GAAf,eAAe;YACd,IAAI,CAAA,IAAA,GAAJ,IAAI;YACJ,IAAG,CAAA,GAAA,GAAH,GAAG;YACH,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;YAChB,IAAQ,CAAA,QAAA,GAAR,QAAQ;YACR,IAAQ,CAAA,QAAA,GAAR,QAAQ;YACR,IAAI,CAAA,IAAA,GAAJ,IAAI;YACJ,IAAa,CAAA,aAAA,GAAb,aAAa;YACb,IAAc,CAAA,cAAA,GAAd,cAAc;;AAGxB,QAAA,UAAU,CAAC,IAAY,EAAA;AACrB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;;QAGxB,cAAc,GAAA;AACZ,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;AAGrB,QAAA,YAAY,CAAC,IAAY,EAAA;AACvB,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACpB,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;AAC/B,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;gBAC3B,IAAI,CAAC,iBAAiB,EAAE;;AAE1B,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;QAGzB,QAAQ,GAAA;AACN,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK;AACvB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;AACpB,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;AAC1B,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;;QAG/B,iBAAiB,GAAA;AACf,YAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC5E,gBAAA,OAAO,KAAK;;;AAGd,YAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,gBAAA,OAAO,KAAK;;AAEd,YAAA,IAAI,IAAI,CAAC,cAAc,KAAK,CAAC,EAAE;AAC7B,gBAAA,OAAO,cAAc,CAAC,cAAc,CAAC;;iBAChC;AACL,gBAAA,OAAO,cAAc,CAAC,iBAAiB,CAAC;;;QAI5C,iBAAiB,GAAA;AACf,YAAA,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC;YACvC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;gBAC9G;;AAEF,YAAA,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,MAAK;gBAC7B,IAAI,CAAC,YAAY,EAAE;AACrB,aAAC,CAAC;;QAGJ,uBAAuB,GAAA;YACrB,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAsB,IAAI,CAAC,eAAgB,EAAE;gBACnG,OAAO,EAAE,IAAI,CAAC;AACf,aAAA,CAAC;YACF,OAAO,CAAC,aAAa,EAAE;YACvB,OAAO;gBACL,OAAO;gBACP,UAAU,EAAE,MAAK;AACf,oBAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;;aAEvE;;QAGH,YAAY,GAAA;AACV,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB;;YAEF,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,uBAAuB,EAAE;YAC9D,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW;iBACnD,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,aAAa;iBAChC,GAAG,CAAC,CAAC,IAAI,CAAE,CAAC,aAAa,CAAC;AAC7B,YAAA,MAAM,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,EAAE;YAC3D,IAAI,oBAAoB,EAAE;AACxB,gBAAA,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC;;AAEvC,YAAA,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAC9C,IAAI,CAAC,IAAI,CAAC,aAAa,EACvB,IAAI,CAAC,cAAc,EACnB,OAAO,CAAC,SAAS,EACjB,UAAU,EACV,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,QAAQ,CACd;AAED,YAAA,UAAU,EAAE;AAEZ,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,YAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,EAAE;AAChC,gBAAA,IAAI,CAAC,UAAU,GAAG,QAAQ;AAC1B,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAElC,YAAA,MAAM,8BAA8B,GAAG,IAAI,CAAC,iBAAkB,CAAC,aAAa;AAC5E,YAAA,OAAO,8BAA8B,CAAC,UAAU,EAAE;gBAChD,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,8BAA8B,EAAE,8BAA8B,CAAC,UAAU,CAAC;;AAEtG,YAAA,YAAY,CAAC,OAAO,CAAC,CAAC,IAAG;AACvB,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,8BAA8B,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC9E,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;;QAIjB,uBAAuB,GAAA;AAC7B,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE;AACxD,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,yBAAyB;gBAC5C,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,KAAK,UAAU,EAAE;oBAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC;AAC3C,oBAAA,EAAE,CAAC,SAAS,GAAG,wBAAwB;AACvC,oBAAA,EAAE,CAAC,SAAS,GAAG,UAAU;AACzB,oBAAA,IAAI,CAAC,yBAAyB,GAAG,EAAE;;gBAErC,OAAO,IAAI,CAAC,yBAAyB;;iBAChC;AACL,gBAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI;AACrC,gBAAA,OAAO,IAAI;;;QAIP,8BAA8B,GAAA;AACpC,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;AAC3B,gBAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE;AAC3C,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE;gBAC3C,IAAI,CAAC,iBAAiB,EAAE;AACxB,gBAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;AAClC,qBAAA,SAAS;AACT,qBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;qBAC7B,SAAS,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;;;QAIhD,QAAQ,GAAA;AACN,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;gBACnE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;AAC7C,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,gBAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,gBAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,aAAC,CAAC;YAEF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;;QAGtC,eAAe,GAAA;AACb,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;YACpB,IAAI,CAAC,8BAA8B,EAAE;;AAGvC,QAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,YAAA,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,QAAQ,EAAE,GAAG,OAAO;AACzG,YAAA,IAAI,UAAU,IAAI,UAAU,IAAI,YAAY,IAAI,UAAU,IAAI,SAAS,IAAI,cAAc,IAAI,QAAQ,EAAE;AACrG,gBAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,oBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,wBAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE;;yBACtC;wBACL,IAAI,CAAC,8BAA8B,EAAE;;;;;QAM7C,WAAW,GAAA;AACT,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;AACxB,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI;AACrC,YAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE;;2GA9OlC,qBAAqB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAF,IAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAAE,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAArB,QAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,qBAAqB,iOAGZ,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAChB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAChB,gBAAgB,CAChB,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,wMAMhB,eAAe,CAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,sBAAA,EAAA,UAAA,EAAA,0BAAA,EAAA,iBAAA,EAAA,mCAAA,EAAA,SAAA,EAAA,gCAAA,EAAA,0BAAA,EAAA,8BAAA,EAAA,wBAAA,EAAA,6BAAA,EAAA,uBAAA,EAAA,8BAAA,EAAA,wBAAA,EAAA,+BAAA,EAAA,YAAA,EAAA,+BAAA,EAAA,yBAAA,EAAA,kCAAA,EAAA,oCAAA,EAAA,2CAAA,EAAA,wCAAA,EAAA,6CAAA,EAAA,sCAAA,EAAA,0BAAA,EAAA,gEAAA,EAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAUxB,mBAAmB,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EACnB,mBAAmB,EApGpB,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAmBS,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,mBAAmB,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,cAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,mBAAmB,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,OAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;;2FAEzD,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBArFjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE;;;;;;AAMT,EAAA,CAAA;AACD,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDT,EAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,wBAAwB,EAAE,UAAU;AACpC,wBAAA,4BAA4B,EAAE,eAAe;AAC7C,wBAAA,qCAAqC,EAAE,SAAS;AAChD,wBAAA,kCAAkC,EAAE,wBAAwB;AAC5D,wBAAA,gCAAgC,EAAE,sBAAsB;AACxD,wBAAA,+BAA+B,EAAE,qBAAqB;AACtD,wBAAA,gCAAgC,EAAE,sBAAsB;AACxD,wBAAA,iCAAiC,EAAE,YAAY;AAC/C,wBAAA,iCAAiC,EAAE,yBAAyB;AAC5D,wBAAA,oCAAoC,EAAE,oCAAoC;AAC1E,wBAAA,6CAA6C,EAAE,wCAAwC;AACvF,wBAAA,+CAA+C,EAAE,sCAAsC;AACvF,wBAAA,4BAA4B,EAAE;AAC/B,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,mBAAmB;AACrE,iBAAA;0TAIyC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACf,cAAc,EAAA,CAAA;sBAApC;gBACsB,WAAW,EAAA,CAAA;sBAAjC;gBACsB,aAAa,EAAA,CAAA;sBAAnC;gBACsB,UAAU,EAAA,CAAA;sBAAhC;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACoD,cAAc,EAAA,CAAA;sBAAlE,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBAC5B,MAAM,EAAA,CAAA;sBAAd;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACkB,eAAe,EAAA,CAAA;sBAAjC;gBACkB,MAAM,EAAA,CAAA;sBAAxB;gBACkB,cAAc,EAAA,CAAA;sBAAhC;gBAEkB,YAAY,EAAA,CAAA;sBAA9B;gBAEkD,WAAW,EAAA,CAAA;sBAA7D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,mBAAmB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACE,WAAW,EAAA,CAAA;sBAA7D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,mBAAmB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACE,iBAAiB,EAAA,CAAA;sBAAnE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,mBAAmB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACL,aAAa,EAAA,CAAA;sBAAxD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,YAAY,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACO,eAAe,EAAA,CAAA;sBAA/D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;;;AC/JjD;;;AAGG;MAYU,kBAAkB,CAAA;uGAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAlB,kBAAkB,EAAA,OAAA,EAAA,CAHnB,qBAAqB,EAAE,mBAAmB,EAAE,mBAAmB,CAAA,EAAA,OAAA,EAAA,CAC/D,qBAAqB,EAAE,mBAAmB,EAAE,mBAAmB,CAAA,EAAA,CAAA;AAE9D,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,EAHnB,OAAA,EAAA,CAAA,qBAAqB,EAAE,mBAAmB,EAAE,mBAAmB,CAAA,EAAA,CAAA;;2FAG9D,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAJ9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,qBAAqB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC;AAC1E,oBAAA,OAAO,EAAE,CAAC,qBAAqB,EAAE,mBAAmB,EAAE,mBAAmB;AAC1E,iBAAA;;;ACdD;;;AAGG;;ACHH;;AAEG;;;;"}