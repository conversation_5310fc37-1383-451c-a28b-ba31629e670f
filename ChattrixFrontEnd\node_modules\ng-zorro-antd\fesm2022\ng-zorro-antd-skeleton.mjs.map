{"version": 3, "file": "ng-zorro-antd-skeleton.mjs", "sources": ["../../components/skeleton/skeleton-element.component.ts", "../../components/skeleton/skeleton.component.ts", "../../components/skeleton/skeleton.module.ts", "../../components/skeleton/skeleton.type.ts", "../../components/skeleton/public-api.ts", "../../components/skeleton/ng-zorro-antd-skeleton.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Directive,\n  Input,\n  OnChanges,\n  SimpleChanges,\n  booleanAttribute\n} from '@angular/core';\n\nimport {\n  NzSkeletonAvatarShape,\n  NzSkeletonAvatarSize,\n  NzSkeletonButtonShape,\n  NzSkeletonButtonSize,\n  NzSkeletonInputSize\n} from './skeleton.type';\n\n@Directive({\n  selector: 'nz-skeleton-element',\n  host: {\n    class: 'ant-skeleton ant-skeleton-element',\n    '[class.ant-skeleton-active]': 'nzActive',\n    '[class.ant-skeleton-block]': 'nzBlock'\n  }\n})\nexport class NzSkeletonElementDirective {\n  @Input({ transform: booleanAttribute }) nzActive: boolean = false;\n  @Input() nzType!: 'button' | 'input' | 'avatar' | 'image';\n  @Input({ transform: booleanAttribute }) nzBlock: boolean = false;\n}\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  selector: 'nz-skeleton-element[nzType=\"button\"]',\n  template: `\n    <span\n      class=\"ant-skeleton-button\"\n      [class.ant-skeleton-button-square]=\"nzShape === 'square'\"\n      [class.ant-skeleton-button-round]=\"nzShape === 'round'\"\n      [class.ant-skeleton-button-circle]=\"nzShape === 'circle'\"\n      [class.ant-skeleton-button-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-button-sm]=\"nzSize === 'small'\"\n    ></span>\n  `\n})\nexport class NzSkeletonElementButtonComponent {\n  @Input() nzShape: NzSkeletonButtonShape = 'default';\n  @Input() nzSize: NzSkeletonButtonSize = 'default';\n}\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  selector: 'nz-skeleton-element[nzType=\"avatar\"]',\n  template: `\n    <span\n      class=\"ant-skeleton-avatar\"\n      [class.ant-skeleton-avatar-square]=\"nzShape === 'square'\"\n      [class.ant-skeleton-avatar-circle]=\"nzShape === 'circle'\"\n      [class.ant-skeleton-avatar-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-avatar-sm]=\"nzSize === 'small'\"\n      [style]=\"styleMap\"\n    ></span>\n  `\n})\nexport class NzSkeletonElementAvatarComponent implements OnChanges {\n  @Input() nzShape: NzSkeletonAvatarShape = 'circle';\n  @Input() nzSize: NzSkeletonAvatarSize = 'default';\n\n  styleMap = {};\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes.nzSize && typeof this.nzSize === 'number') {\n      const sideLength = `${this.nzSize}px`;\n      this.styleMap = { width: sideLength, height: sideLength, 'line-height': sideLength };\n    } else {\n      this.styleMap = {};\n    }\n  }\n}\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  selector: 'nz-skeleton-element[nzType=\"input\"]',\n  template: `\n    <span\n      class=\"ant-skeleton-input\"\n      [class.ant-skeleton-input-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-input-sm]=\"nzSize === 'small'\"\n    ></span>\n  `\n})\nexport class NzSkeletonElementInputComponent {\n  @Input() nzSize: NzSkeletonInputSize = 'default';\n}\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  selector: 'nz-skeleton-element[nzType=\"image\"]',\n  template: `\n    <span class=\"ant-skeleton-image\">\n      <svg class=\"ant-skeleton-image-svg\" viewBox=\"0 0 1098 1024\" xmlns=\"http://www.w3.org/2000/svg\">\n        <path\n          d=\"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z\"\n          class=\"ant-skeleton-image-path\"\n        />\n      </svg>\n    </span>\n  `\n})\nexport class NzSkeletonElementImageComponent {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Input,\n  OnChanges,\n  OnInit,\n  SimpleChanges,\n  ViewEncapsulation\n} from '@angular/core';\n\nimport { toCssPixel } from 'ng-zorro-antd/core/util';\n\nimport { NzSkeletonElementAvatarComponent, NzSkeletonElementDirective } from './skeleton-element.component';\nimport {\n  NzSkeletonAvatar,\n  NzSkeletonAvatarShape,\n  NzSkeletonAvatarSize,\n  NzSkeletonParagraph,\n  NzSkeletonTitle\n} from './skeleton.type';\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  selector: 'nz-skeleton',\n  exportAs: 'nzSkeleton',\n  host: {\n    class: 'ant-skeleton',\n    '[class.ant-skeleton-with-avatar]': '!!nzAvatar',\n    '[class.ant-skeleton-active]': 'nzActive',\n    '[class.ant-skeleton-round]': '!!nzRound'\n  },\n  template: `\n    @if (nzLoading) {\n      @if (!!nzAvatar) {\n        <div class=\"ant-skeleton-header\">\n          <nz-skeleton-element\n            nzType=\"avatar\"\n            [nzSize]=\"avatar.size || 'default'\"\n            [nzShape]=\"avatar.shape || 'circle'\"\n          ></nz-skeleton-element>\n        </div>\n      }\n      <div class=\"ant-skeleton-content\">\n        @if (!!nzTitle) {\n          <h3 class=\"ant-skeleton-title\" [style.width]=\"toCSSUnit(title.width)\"></h3>\n        }\n        @if (!!nzParagraph) {\n          <ul class=\"ant-skeleton-paragraph\">\n            @for (row of rowsList; track row; let i = $index) {\n              <li [style.width]=\"toCSSUnit(widthList[i])\"></li>\n            }\n          </ul>\n        }\n      </div>\n    } @else {\n      <ng-content></ng-content>\n    }\n  `,\n  imports: [NzSkeletonElementDirective, NzSkeletonElementAvatarComponent]\n})\nexport class NzSkeletonComponent implements OnInit, OnChanges {\n  @Input() nzActive = false;\n  @Input() nzLoading = true;\n  @Input() nzRound = false;\n  @Input() nzTitle: NzSkeletonTitle | boolean = true;\n  @Input() nzAvatar: NzSkeletonAvatar | boolean = false;\n  @Input() nzParagraph: NzSkeletonParagraph | boolean = true;\n\n  title!: NzSkeletonTitle;\n  avatar!: NzSkeletonAvatar;\n  paragraph!: NzSkeletonParagraph;\n  rowsList: number[] = [];\n  widthList: Array<number | string> = [];\n\n  constructor(private cdr: ChangeDetectorRef) {}\n\n  toCSSUnit(value: number | string = ''): string {\n    return toCssPixel(value);\n  }\n\n  private getTitleProps(): NzSkeletonTitle {\n    const hasAvatar = !!this.nzAvatar;\n    const hasParagraph = !!this.nzParagraph;\n    let width = '';\n    if (!hasAvatar && hasParagraph) {\n      width = '38%';\n    } else if (hasAvatar && hasParagraph) {\n      width = '50%';\n    }\n    return { width, ...this.getProps(this.nzTitle) };\n  }\n\n  private getAvatarProps(): NzSkeletonAvatar {\n    const shape: NzSkeletonAvatarShape = !!this.nzTitle && !this.nzParagraph ? 'square' : 'circle';\n    const size: NzSkeletonAvatarSize = 'large';\n    return { shape, size, ...this.getProps(this.nzAvatar) };\n  }\n\n  private getParagraphProps(): NzSkeletonParagraph {\n    const hasAvatar = !!this.nzAvatar;\n    const hasTitle = !!this.nzTitle;\n    const basicProps: NzSkeletonParagraph = {};\n    // Width\n    if (!hasAvatar || !hasTitle) {\n      basicProps.width = '61%';\n    }\n    // Rows\n    if (!hasAvatar && hasTitle) {\n      basicProps.rows = 3;\n    } else {\n      basicProps.rows = 2;\n    }\n    return { ...basicProps, ...this.getProps(this.nzParagraph) };\n  }\n\n  private getProps<T>(prop: T | boolean | undefined): T | {} {\n    return prop && typeof prop === 'object' ? prop : {};\n  }\n\n  private getWidthList(): Array<number | string> {\n    const { width, rows } = this.paragraph;\n    let widthList: Array<string | number> = [];\n    if (width && Array.isArray(width)) {\n      widthList = width;\n    } else if (width && !Array.isArray(width)) {\n      widthList = [];\n      widthList[rows! - 1] = width;\n    }\n    return widthList;\n  }\n\n  private updateProps(): void {\n    this.title = this.getTitleProps();\n    this.avatar = this.getAvatarProps();\n    this.paragraph = this.getParagraphProps();\n    this.rowsList = [...Array(this.paragraph.rows)];\n    this.widthList = this.getWidthList();\n    this.cdr.markForCheck();\n  }\n\n  ngOnInit(): void {\n    this.updateProps();\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes.nzTitle || changes.nzAvatar || changes.nzParagraph) {\n      this.updateProps();\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport {\n  NzSkeletonElementAvatarComponent,\n  NzSkeletonElementButtonComponent,\n  NzSkeletonElementDirective,\n  NzSkeletonElementImageComponent,\n  NzSkeletonElementInputComponent\n} from './skeleton-element.component';\nimport { NzSkeletonComponent } from './skeleton.component';\n\n@NgModule({\n  imports: [\n    NzSkeletonElementDirective,\n    NzSkeletonComponent,\n    NzSkeletonElementButtonComponent,\n    NzSkeletonElementAvatarComponent,\n    NzSkeletonElementImageComponent,\n    NzSkeletonElementInputComponent\n  ],\n  exports: [\n    NzSkeletonElementDirective,\n    NzSkeletonComponent,\n    NzSkeletonElementButtonComponent,\n    NzSkeletonElementAvatarComponent,\n    NzSkeletonElementImageComponent,\n    NzSkeletonElementInputComponent\n  ]\n})\nexport class NzSkeletonModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport type NzSkeletonParagraphWidth = number | string | Array<number | string>;\nexport type NzSkeletonButtonShape = 'square' | 'circle' | 'round' | 'default';\nexport type NzSkeletonAvatarShape = 'square' | 'circle';\nexport type NzSkeletonInputSize = 'large' | 'small' | 'default';\nexport type NzSkeletonButtonSize = NzSkeletonInputSize;\nexport type NzSkeletonAvatarSize = NzSkeletonInputSize | number;\n\nexport interface NzSkeletonAvatar {\n  size?: NzSkeletonAvatarSize;\n  shape?: NzSkeletonAvatarShape;\n}\n\nexport interface NzSkeletonTitle {\n  width?: number | string;\n}\n\nexport interface NzSkeletonParagraph {\n  rows?: number;\n  width?: NzSkeletonParagraphWidth;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './skeleton.component';\nexport * from './skeleton.module';\nexport * from './skeleton.type';\nexport * from './skeleton-element.component';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": [], "mappings": ";;;;AAAA;;;AAGG;MA4BU,0BAA0B,CAAA;IACG,QAAQ,GAAY,KAAK;AACxD,IAAA,MAAM;IACyB,OAAO,GAAY,KAAK;uGAHrD,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA1B,0BAA0B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EACjB,gBAAgB,CAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAEhB,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,2BAAA,EAAA,UAAA,EAAA,0BAAA,EAAA,SAAA,EAAA,EAAA,cAAA,EAAA,mCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAHzB,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBARtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,mCAAmC;AAC1C,wBAAA,6BAA6B,EAAE,UAAU;AACzC,wBAAA,4BAA4B,EAAE;AAC/B;AACF,iBAAA;8BAEyC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,MAAM,EAAA,CAAA;sBAAd;gBACuC,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;;MAiB3B,gCAAgC,CAAA;IAClC,OAAO,GAA0B,SAAS;IAC1C,MAAM,GAAyB,SAAS;uGAFtC,gCAAgC,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gCAAgC,EAXjC,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wCAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;AAST,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAEU,gCAAgC,EAAA,UAAA,EAAA,CAAA;kBAd5C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE,sCAAsC;AAChD,oBAAA,QAAQ,EAAE;;;;;;;;;AAST,EAAA;AACF,iBAAA;8BAEU,OAAO,EAAA,CAAA;sBAAf;gBACQ,MAAM,EAAA,CAAA;sBAAd;;MAiBU,gCAAgC,CAAA;IAClC,OAAO,GAA0B,QAAQ;IACzC,MAAM,GAAyB,SAAS;IAEjD,QAAQ,GAAG,EAAE;AAEb,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;AACrD,YAAA,MAAM,UAAU,GAAG,CAAA,EAAG,IAAI,CAAC,MAAM,IAAI;AACrC,YAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,EAAE;;aAC/E;AACL,YAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;;uGAXX,gCAAgC,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gCAAgC,EAXjC,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wCAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;AAST,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAEU,gCAAgC,EAAA,UAAA,EAAA,CAAA;kBAd5C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE,sCAAsC;AAChD,oBAAA,QAAQ,EAAE;;;;;;;;;AAST,EAAA;AACF,iBAAA;8BAEU,OAAO,EAAA,CAAA;sBAAf;gBACQ,MAAM,EAAA,CAAA;sBAAd;;MAyBU,+BAA+B,CAAA;IACjC,MAAM,GAAwB,SAAS;uGADrC,+BAA+B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAA/B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,+BAA+B,EARhC,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uCAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;AAMT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAEU,+BAA+B,EAAA,UAAA,EAAA,CAAA;kBAX3C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE,qCAAqC;AAC/C,oBAAA,QAAQ,EAAE;;;;;;AAMT,EAAA;AACF,iBAAA;8BAEU,MAAM,EAAA,CAAA;sBAAd;;MAiBU,+BAA+B,CAAA;uGAA/B,+BAA+B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAA/B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,+BAA+B,EAXhC,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uCAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;AAST,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAEU,+BAA+B,EAAA,UAAA,EAAA,CAAA;kBAd3C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE,qCAAqC;AAC/C,oBAAA,QAAQ,EAAE;;;;;;;;;AAST,EAAA;AACF,iBAAA;;;AClHD;;;AAGG;MAgEU,mBAAmB,CAAA;AAcV,IAAA,GAAA;IAbX,QAAQ,GAAG,KAAK;IAChB,SAAS,GAAG,IAAI;IAChB,OAAO,GAAG,KAAK;IACf,OAAO,GAA8B,IAAI;IACzC,QAAQ,GAA+B,KAAK;IAC5C,WAAW,GAAkC,IAAI;AAE1D,IAAA,KAAK;AACL,IAAA,MAAM;AACN,IAAA,SAAS;IACT,QAAQ,GAAa,EAAE;IACvB,SAAS,GAA2B,EAAE;AAEtC,IAAA,WAAA,CAAoB,GAAsB,EAAA;QAAtB,IAAG,CAAA,GAAA,GAAH,GAAG;;IAEvB,SAAS,CAAC,QAAyB,EAAE,EAAA;AACnC,QAAA,OAAO,UAAU,CAAC,KAAK,CAAC;;IAGlB,aAAa,GAAA;AACnB,QAAA,MAAM,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ;AACjC,QAAA,MAAM,YAAY,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW;QACvC,IAAI,KAAK,GAAG,EAAE;AACd,QAAA,IAAI,CAAC,SAAS,IAAI,YAAY,EAAE;YAC9B,KAAK,GAAG,KAAK;;AACR,aAAA,IAAI,SAAS,IAAI,YAAY,EAAE;YACpC,KAAK,GAAG,KAAK;;AAEf,QAAA,OAAO,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;;IAG1C,cAAc,GAAA;QACpB,MAAM,KAAK,GAA0B,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,GAAG,QAAQ;QAC9F,MAAM,IAAI,GAAyB,OAAO;AAC1C,QAAA,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;;IAGjD,iBAAiB,GAAA;AACvB,QAAA,MAAM,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ;AACjC,QAAA,MAAM,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO;QAC/B,MAAM,UAAU,GAAwB,EAAE;;AAE1C,QAAA,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE;AAC3B,YAAA,UAAU,CAAC,KAAK,GAAG,KAAK;;;AAG1B,QAAA,IAAI,CAAC,SAAS,IAAI,QAAQ,EAAE;AAC1B,YAAA,UAAU,CAAC,IAAI,GAAG,CAAC;;aACd;AACL,YAAA,UAAU,CAAC,IAAI,GAAG,CAAC;;AAErB,QAAA,OAAO,EAAE,GAAG,UAAU,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;;AAGtD,IAAA,QAAQ,CAAI,IAA6B,EAAA;AAC/C,QAAA,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,EAAE;;IAG7C,YAAY,GAAA;QAClB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS;QACtC,IAAI,SAAS,GAA2B,EAAE;QAC1C,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACjC,SAAS,GAAG,KAAK;;aACZ,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACzC,SAAS,GAAG,EAAE;AACd,YAAA,SAAS,CAAC,IAAK,GAAG,CAAC,CAAC,GAAG,KAAK;;AAE9B,QAAA,OAAO,SAAS;;IAGV,WAAW,GAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE;AACjC,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE;AACnC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE;AACzC,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC/C,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE;AACpC,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;IAGzB,QAAQ,GAAA;QACN,IAAI,CAAC,WAAW,EAAE;;AAGpB,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,WAAW,EAAE;YAC9D,IAAI,CAAC,WAAW,EAAE;;;uGAtFX,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,EA7BpB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gCAAA,EAAA,YAAA,EAAA,2BAAA,EAAA,UAAA,EAAA,0BAAA,EAAA,WAAA,EAAA,EAAA,cAAA,EAAA,cAAA,EAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BT,EACS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,0BAA0B,2GAAE,gCAAgC,EAAA,QAAA,EAAA,wCAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,QAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE3D,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAxC/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,cAAc;AACrB,wBAAA,kCAAkC,EAAE,YAAY;AAChD,wBAAA,6BAA6B,EAAE,UAAU;AACzC,wBAAA,4BAA4B,EAAE;AAC/B,qBAAA;AACD,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BT,EAAA,CAAA;AACD,oBAAA,OAAO,EAAE,CAAC,0BAA0B,EAAE,gCAAgC;AACvE,iBAAA;sFAEU,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,WAAW,EAAA,CAAA;sBAAnB;;;ACzEH;;;AAGG;MA+BU,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAhBzB,0BAA0B;YAC1B,mBAAmB;YACnB,gCAAgC;YAChC,gCAAgC;YAChC,+BAA+B;AAC/B,YAAA,+BAA+B,aAG/B,0BAA0B;YAC1B,mBAAmB;YACnB,gCAAgC;YAChC,gCAAgC;YAChC,+BAA+B;YAC/B,+BAA+B,CAAA,EAAA,CAAA;wGAGtB,gBAAgB,EAAA,CAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAlB5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,0BAA0B;wBAC1B,mBAAmB;wBACnB,gCAAgC;wBAChC,gCAAgC;wBAChC,+BAA+B;wBAC/B;AACD,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,0BAA0B;wBAC1B,mBAAmB;wBACnB,gCAAgC;wBAChC,gCAAgC;wBAChC,+BAA+B;wBAC/B;AACD;AACF,iBAAA;;;ACjCD;;;AAGG;;ACHH;;;AAGG;;ACHH;;AAEG;;;;"}