@import '../../style/themes/index';
@import '../../style/mixins/index';
@import '../../checkbox/style/mixin';
@import './customize';
@import './status';

@transfer-prefix-cls: ~'@{ant-prefix}-transfer';

@transfer-header-vertical-padding: ceil(
  ((@transfer-header-height - 1px - @font-size-base * @line-height-base) / 2)
);

.@{transfer-prefix-cls} {
  .reset-component();

  position: relative;
  display: flex;
  align-items: stretch;

  &-disabled {
    .@{transfer-prefix-cls}-list {
      background: @transfer-disabled-bg;
    }
  }

  &-list {
    display: flex;
    flex-direction: column;
    width: 180px;
    height: @transfer-list-height;
    border: @border-width-base @border-style-base @border-color-base;
    border-radius: @border-radius-base;

    &-with-pagination {
      width: 250px;
      height: auto;
    }

    &-search {
      .anticon-search {
        color: @disabled-color;
      }
    }

    &-header {
      display: flex;
      flex: none;
      align-items: center;
      height: @transfer-header-height;
      // border-top is on the transfer dom. We should minus 1px for this
      padding: (@transfer-header-vertical-padding - 1px) @control-padding-horizontal
        @transfer-header-vertical-padding;
      color: @text-color;
      background: @component-background;
      border-bottom: @border-width-base @border-style-base @border-color-split;
      border-radius: @border-radius-base @border-radius-base 0 0;

      > *:not(:last-child) {
        margin-right: 4px;
      }

      > * {
        flex: none;
      }

      &-title {
        flex: auto;
        overflow: hidden;
        white-space: nowrap;
        text-align: right;
        text-overflow: ellipsis;
      }

      &-dropdown {
        font-size: 10px;
        transform: translateY(10%);
        cursor: pointer;

        &[disabled] {
          cursor: not-allowed;
        }
      }
    }

    &-body {
      display: flex;
      flex: auto;
      flex-direction: column;
      overflow: hidden;
      font-size: @font-size-base;

      &-search-wrapper {
        position: relative;
        flex: none;
        padding: @padding-sm;
      }
    }

    &-content {
      flex: auto;
      margin: 0;
      padding: 0;
      overflow: auto;
      list-style: none;

      &-item {
        display: flex;
        align-items: center;
        min-height: @transfer-item-height;
        padding: @transfer-item-padding-vertical @control-padding-horizontal;
        line-height: @transfer-item-height - 2 * @transfer-item-padding-vertical;
        transition: all 0.3s;

        > *:not(:last-child) {
          margin-right: 8px;
        }

        > * {
          flex: none;
        }

        &-text {
          flex: auto;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        &-remove {
          position: relative;
          color: @border-color-base;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            color: @link-hover-color;
          }

          &::after {
            position: absolute;
            top: -@transfer-item-padding-vertical;
            right: -50%;
            bottom: -@transfer-item-padding-vertical;
            left: -50%;
            content: '';
          }
        }
      }

      &-item:not(&-item-disabled) {
        &:hover {
          background-color: @transfer-item-hover-bg;
          cursor: pointer;
        }

        &.@{transfer-prefix-cls}-list-content-item-checked:hover {
          background-color: @transfer-item-selected-hover-bg;
        }
      }

      // Do not change hover style when `oneWay` mode
      &-show-remove &-item:not(&-item-disabled):hover {
        background: transparent;
        cursor: default;
      }

      &-item-checked {
        background-color: @item-active-bg;
      }

      &-item-disabled {
        color: @btn-disable-color;
        cursor: not-allowed;
      }
    }

    &-pagination {
      padding: @padding-xs 0;
      text-align: right;
      border-top: @border-width-base @border-style-base @border-color-split;
    }

    &-body-not-found {
      flex: none;
      width: 100%;
      margin: auto 0;
      color: @disabled-color;
      text-align: center;
    }

    &-footer {
      border-top: @border-width-base @border-style-base @border-color-split;
    }
  }

  &-operation {
    display: flex;
    flex: none;
    flex-direction: column;
    align-self: center;
    margin: 0 8px;
    vertical-align: middle;

    .@{ant-prefix}-btn {
      display: block;

      &:first-child {
        margin-bottom: 4px;
      }

      .@{iconfont-css-prefix} {
        font-size: 12px;
      }
    }
  }

  .@{ant-prefix}-empty-image {
    max-height: (@transfer-header-height / 2) - 22;
  }
}

@import './rtl';
