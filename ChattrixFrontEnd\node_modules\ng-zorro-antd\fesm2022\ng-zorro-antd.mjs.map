{"version": 3, "file": "ng-zorro-antd.mjs", "sources": ["../../components/public_api.ts", "../../components/ng-zorro-antd.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n// https://github.com/ng-packagr/ng-packagr/issues/1655\nexport default void 0;\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": "AAAA;;;AAGG;AAEH;AACA,iBAAe,KAAK,CAAC;;ACNrB;;AAEG"}