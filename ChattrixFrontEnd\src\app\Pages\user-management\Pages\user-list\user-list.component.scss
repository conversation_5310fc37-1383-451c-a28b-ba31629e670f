/* User Management Container */
.user-management-container {
  padding: var(--spacing-lg);
  background: var(
    --bg-main-content
  ); /* Uses CSS variable for theme consistency */
  min-height: 100vh;
}

/* Integrated Header Section */
.integrated-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md)
    var(--spacing-lg);
  border-bottom: 1px solid var(--border-secondary);
  gap: var(--spacing-md);
}

.header-content {
  flex: 1;
}

.page-title {
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.add-user-btn {
  height: 44px;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-transform: none;
  background: #000000 !important; /* Black background */
  color: #ffffff !important; /* White text */
  border: 2px solid #ffffff !important; /* White thick border */

  mat-icon {
    margin-right: var(--spacing-xs);
    color: #ffffff !important;
  }

  &:hover {
    background: #333333 !important; /* Slightly lighter on hover */
    border-color: #ffffff !important;
  }
}

/* Table Header with Integrated Filters */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-card);
}

.table-header-left {
  display: flex;
  align-items: center;
}

.table-view-controls {
  display: flex;
  gap: var(--spacing-xs);

  .view-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    font-size: 0.875rem;
    text-transform: none;
    min-width: auto;

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    &.active {
      background: var(--bg-secondary);
      color: var(--text-primary);
    }

    &:hover:not(.active) {
      background: var(--bg-hover);
      color: var(--text-primary);
    }
  }
}

.table-header-right {
  display: flex;
  align-items: center;
}

.filters-container {
  display: flex;
  flex-direction: row;
  gap: var(--spacing-md);
  align-items: center;
}

/* ng-zorro Search Field */
.nz-search-field {
  width: 240px;

  nz-input-search {
    display: block !important;
    width: 100% !important;
    height: 44px !important;
  }
}

/* Search Input Styling with Higher Specificity */
:host ::ng-deep .nz-search-field .search-input .ant-input-search {
  .ant-input {
    height: 44px !important;
    border: 1px solid var(--border-primary) !important;
    border-radius: var(--radius-sm) !important;
    background: var(--bg-card) !important;
    color: var(--text-primary) !important;
    font-size: 0.875rem !important;
    padding: 0 var(--spacing-md) !important;
    transition: all 0.2s ease !important;

    &::placeholder {
      color: var(--text-muted) !important;
    }

    &:focus,
    &:focus-within {
      border-color: var(--accent-green) !important;
      box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2) !important;
    }

    &:hover:not(:focus) {
      border-color: var(--border-secondary) !important;
    }
  }

  .ant-input-search-button {
    height: 44px !important;
    border: 1px solid var(--border-primary) !important;
    border-left: none !important;
    background: var(--bg-secondary) !important;
    color: var(--text-muted) !important;
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0 !important;

    &:hover {
      background: var(--bg-hover) !important;
      color: var(--text-primary) !important;
    }
  }
}

/* ng-zorro Filter Fields */
.nz-filter-field {
  width: 120px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);

  nz-select {
    display: block !important;
    width: 100% !important;
    height: 44px !important;
  }
}

.filter-label {
  color: var(--text-secondary);
  font-size: 0.75rem;
  font-weight: 500;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Select Field Styling with Higher Specificity */
:host ::ng-deep .nz-filter-field .filter-select .ant-select {
  height: 44px !important;

  .ant-select-selector {
    height: 44px !important;
    border: 1px solid var(--border-primary) !important;
    border-radius: var(--radius-sm) !important;
    background: var(--bg-card) !important;
    color: var(--text-primary) !important;
    font-size: 0.875rem !important;
    padding: 0 var(--spacing-md) !important;
    transition: all 0.2s ease !important;

    .ant-select-selection-search-input {
      height: 42px !important;
      color: var(--text-primary) !important;
    }

    .ant-select-selection-placeholder {
      color: var(--text-muted) !important;
      line-height: 42px !important;
    }

    .ant-select-selection-item {
      color: var(--text-primary) !important;
      line-height: 42px !important;
    }
  }

  &:hover .ant-select-selector {
    border-color: var(--border-secondary) !important;
  }

  &.ant-select-focused .ant-select-selector {
    border-color: var(--accent-green) !important;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2) !important;
  }

  .ant-select-arrow {
    
    color: var(--text-muted) !important;
  }

  .ant-select-clear {
    color: var(--text-muted) !important;
    background: var(--bg-card) !important;

    &:hover {
      color: var(--text-primary) !important;
    }
  }
}

/* ng-zorro Reset Button */
:host ::ng-deep .reset-btn {
  height: 44px;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: none;
  padding: 0 var(--spacing-md);

  &.ant-btn {
    background: #000000 !important;
    color: #ffffff !important;
    border: 2px solid #ffffff !important;

    &:hover,
    &:focus {
      background: #333333 !important;
      color: #ffffff !important;
      border-color: #ffffff !important;
    }

    &:active {
      background: #555555 !important;
      color: #ffffff !important;
      border-color: #ffffff !important;
    }
  }
}

/* ng-zorro Global Dropdown Styling with Higher Specificity */
:host ::ng-deep .ant-select-dropdown {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: var(--radius-md) !important;
  box-shadow: var(--shadow-lg) !important;
  z-index: 1050 !important;

  .ant-select-item {
    color: var(--text-primary) !important;
    font-size: 0.875rem !important;
    padding: var(--spacing-xs) var(--spacing-md) !important;

    &:hover {
      background: var(--bg-hover) !important;
    }

    &.ant-select-item-option-selected {
      background: var(--accent-green) !important;
      color: white !important;
    }

    &.ant-select-item-option-active {
      background: var(--bg-secondary) !important;
    }
  }
}

/* Global ng-zorro dropdown styling (outside component scope) */
.ant-select-dropdown {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: var(--radius-md) !important;
  box-shadow: var(--shadow-lg) !important;
  z-index: 1050 !important;

  .ant-select-item {
    color: var(--text-primary) !important;
    font-size: 0.875rem !important;
    padding: var(--spacing-xs) var(--spacing-md) !important;

    &:hover {
      background: var(--bg-hover) !important;
    }

    &.ant-select-item-option-selected {
      background: var(--accent-green) !important;
      color: white !important;
    }

    &.ant-select-item-option-active {
      background: var(--bg-secondary) !important;
    }
  }
}

/* Table Section */
.table-section {
  margin-bottom: var(--spacing-lg);
}

.table-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  gap: var(--spacing-md);
}

.loading-text {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

.table-container {
  overflow-x: auto;
}

.users-table {
  width: 100%;
  background: var(--bg-card);

  .mat-mdc-header-cell {
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
    border-bottom: 1px solid var(--border-primary);
  }

  .mat-mdc-cell {
    border-bottom: 1px solid var(--border-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
  }

  .mat-mdc-row {
    &:hover {
      background: var(--bg-hover);
    }
  }
}

/* User Info Column */
.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--border-primary);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.user-avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #000000; /* Black background for minimalist design */
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1rem;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
  color: var(--text-primary);
}

.user-email {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Roles Column */
.roles-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.role-chip {
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--radius-sm);

  &.role-admin {
    background: #e3f2fd;
    color: #1976d2;
  }

  &.role-super-admin {
    background: #fce4ec;
    color: #c2185b;
  }

  &.role-user {
    background: #f3e5f5;
    color: #7b1fa2;
  }
}

/* Status Column */
.status-active {
  background: #e8f5e8;
  color: #2e7d32;

  mat-icon {
    color: #2e7d32;
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-xs);
  }
}

.status-inactive {
  background: #ffebee;
  color: #d32f2f;

  mat-icon {
    color: #d32f2f;
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-xs);
  }
}

/* Created Date */
.created-date {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Actions Column */
.actions-container {
  display: flex;
  gap: var(--spacing-xs);
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-sm);

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  &.view-btn {
    color: var(--accent-green);

    &:hover {
      background: rgba(76, 175, 80, 0.1);
    }
  }

  &.edit-btn {
    color: #ff9800;

    &:hover {
      background: rgba(255, 152, 0, 0.1);
    }
  }

  &.delete-btn {
    color: red;

    &:hover {
      background: rgba(244, 67, 54, 0.1);
    }
  }
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.empty-state h3 {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0 0 var(--spacing-sm) 0;
}

.empty-state p {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

/* Pagination Styling */
.mat-mdc-paginator {
  background: var(--bg-card) !important;
  border-top: 1px solid var(--border-primary);
  color: var(--text-primary);
  font-size: 0.875rem;

  /* Pagination container */
  .mat-mdc-paginator-container {
    padding: var(--spacing-md) var(--spacing-lg);
    min-height: 56px;
    justify-content: space-between;
    align-items: center;
  }

  /* Range label styling */
  .mat-mdc-paginator-range-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0 var(--spacing-md);
  }

  /* Page size selector */
  .mat-mdc-paginator-page-size {
    align-items: center;

    .mat-mdc-paginator-page-size-label {
      color: var(--text-secondary);
      font-size: 0.875rem;
      margin-right: var(--spacing-sm);
    }

    .mat-mdc-select {
      color: var(--text-primary);
      font-size: 0.875rem;

      .mat-mdc-select-trigger {
        color: var(--text-primary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-sm);
        padding: var(--spacing-xs) var(--spacing-sm);
        background: var(--bg-secondary);

        &:hover {
          background: var(--bg-hover);
          border-color: var(--border-secondary);
        }
      }

      .mat-mdc-select-arrow {
        color: var(--text-secondary);
      }
    }
  }

  /* Navigation buttons */
  .mat-mdc-paginator-navigation-previous,
  .mat-mdc-paginator-navigation-next,
  .mat-mdc-paginator-navigation-first,
  .mat-mdc-paginator-navigation-last {
    color: white !important;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    width: 36px;
    height: 36px;
    margin: 0 var(--spacing-xs);

    &:hover:not([disabled]) {
      background: var(--bg-hover);
      color: white !important;
      border-color: var(--border-secondary);
    }

    &[disabled] {
      color: var(--text-muted);
      background: var(--bg-tertiary);
      border-color: var(--border-primary);
      opacity: 0.6;
      cursor: not-allowed;
    }

    .mat-mdc-button-touch-target {
      width: 36px;
      height: 36px;
    }

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      color: white !important;
    }
  }

  /* Actions container */
  .mat-mdc-paginator-range-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }
}

/* Pagination select dropdown styling */
.mat-mdc-select-panel {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);

  .mat-mdc-option {
    color: var(--text-primary);
    font-size: 0.875rem;

    &:hover {
      background: var(--bg-hover) !important;
    }

    &.mat-mdc-option-active {
      background: var(--bg-secondary) !important;
      color: var(--text-primary);
    }

    &.mdc-list-item--selected {
      background: var(--accent-green) !important;
      color: white;
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-management-container {
    padding: var(--spacing-md);
  }

  .integrated-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .header-actions {
    justify-content: stretch;
  }

  .add-user-btn {
    width: 100%;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .table-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  // .table-header-left,
  // .table-header-right {
  //   width: 100%;
  // }

  .table-view-controls {
    justify-content: center;
  }

  .filters-container {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .nz-search-field,
  .nz-filter-field {
    width: 100%;
  }

  .users-table {
    font-size: 0.75rem;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .user-avatar,
  .user-avatar-placeholder {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
  }

  .actions-container {
    flex-direction: column;
    gap: 2px;
  }

  .action-btn {
    width: 32px;
    height: 32px;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }

  /* Mobile pagination styling */
  .mat-mdc-paginator {
    .mat-mdc-paginator-container {
      padding: var(--spacing-sm) var(--spacing-md);
      min-height: 48px;
      flex-wrap: wrap;
      gap: var(--spacing-xs);
    }

    .mat-mdc-paginator-page-size {
      order: 1;
      width: 100%;
      justify-content: center;
      margin-bottom: var(--spacing-xs);

      .mat-mdc-paginator-page-size-label {
        font-size: 0.75rem;
      }
    }

    .mat-mdc-paginator-range-label {
      order: 2;
      font-size: 0.75rem;
      text-align: center;
      flex: 1;
    }

    .mat-mdc-paginator-range-actions {
      order: 3;
      justify-content: center;

      .mat-mdc-paginator-navigation-previous,
      .mat-mdc-paginator-navigation-next,
      .mat-mdc-paginator-navigation-first,
      .mat-mdc-paginator-navigation-last {
        width: 32px;
        height: 32px;
        margin: 0 2px;

        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
          color: white !important;
        }
      }
    }
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .user-management-container {
    background: var(--bg-main-content); /* Light grayish-white background */
  }

  .table-card {
    background: #ffffff; /* White cards */
    border-color: #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .table-header {
    background: #ffffff;
    border-bottom-color: #e0e0e0;
  }

  .table-view-controls .view-btn {
    color: #666666;

    &.active {
      background: #f5f5f5;
      color: #333333;
    }

    &:hover:not(.active) {
      background: #f9f9f9;
      color: #333333;
    }
  }

  /* Reset button styling handled by global .reset-btn class */

  /* ng-zorro form field styling for light theme */
  :host ::ng-deep .nz-search-field .search-input .ant-input-search {
    .ant-input {
      background: #ffffff !important;
      border-color: #e0e0e0 !important;
      color: #333333 !important;

      &::placeholder {
        color: #999999 !important;
      }

      &:focus,
      &:focus-within {
        border-color: #10b981 !important;
        box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2) !important;
      }

      &:hover:not(:focus) {
        border-color: #d0d0d0 !important;
      }
    }

    .ant-input-search-button {
      background: #f5f5f5 !important;
      border-color: #e0e0e0 !important;
      color: #999999 !important;

      &:hover {
        background: #f0f0f0 !important;
        color: #333333 !important;
      }
    }
  }

  .filter-label {
    color: #666666;
  }

  :host ::ng-deep .nz-filter-field .filter-select .ant-select {
    .ant-select-selector {
      background: #ffffff !important;
      border-color: #e0e0e0 !important;
      color: #333333 !important;

      .ant-select-selection-placeholder {
        color: #999999 !important;
      }

      .ant-select-selection-item {
        color: #333333 !important;
      }
    }

    &:hover .ant-select-selector {
      border-color: #d0d0d0 !important;
    }

    &.ant-select-focused .ant-select-selector {
      border-color: #10b981 !important;
      box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2) !important;
    }

    .ant-select-arrow {
      color: #999999 !important;
    }

    .ant-select-clear {
      color: #999999 !important;
      background: #ffffff !important;

      &:hover {
        color: #333333 !important;
      }
    }
  }

  /* Light theme dropdown styling */
  .ant-select-dropdown {
    background: #ffffff !important;
    border-color: #e0e0e0 !important;

    .ant-select-item {
      color: #333333 !important;

      &:hover {
        background: #f0f0f0 !important;
      }

      &.ant-select-item-option-selected {
        background: #10b981 !important;
        color: white !important;
      }

      &.ant-select-item-option-active {
        background: #f5f5f5 !important;
      }
    }
  }

  .page-title {
    color: #333333;
  }

  .page-subtitle {
    color: #666666;
  }

  .users-table {
    .mat-mdc-header-cell {
      background: #f5f5f5;
      color: #333333;
      border-bottom-color: #e0e0e0;
    }

    .mat-mdc-cell {
      border-bottom-color: #f0f0f0;
      color: #333333;
    }

    .mat-mdc-row:hover {
      background: #f9f9f9;
    }
  }

  .user-name {
    color: #333333;
  }

  .user-email {
    color: #666666;
  }

  .created-date {
    color: #666666;
  }

  .loading-text {
    color: #666666;
  }

  .empty-icon {
    color: #999999;
  }

  .empty-state h3 {
    color: #333333;
  }

  .empty-state p {
    color: #666666;
  }

  /* Pagination styling for light theme */
  .mat-mdc-paginator {
    background: #ffffff !important;
    border-top-color: #e0e0e0;
    color: #333333;

    .mat-mdc-paginator-range-label {
      color: #666666;
    }

    .mat-mdc-paginator-page-size-label {
      color: #666666;
    }

    .mat-mdc-select {
      color: #333333;

      .mat-mdc-select-trigger {
        color: #333333;
        border-color: #e0e0e0;
        background: #f5f5f5;

        &:hover {
          background: #f0f0f0;
          border-color: #d0d0d0;
        }
      }

      .mat-mdc-select-arrow {
        color: #666666;
      }
    }

    .mat-mdc-paginator-navigation-previous,
    .mat-mdc-paginator-navigation-next,
    .mat-mdc-paginator-navigation-first,
    .mat-mdc-paginator-navigation-last {
      color: white !important;
      border-color: #e0e0e0;
      background: #f5f5f5;

      &:hover:not([disabled]) {
        background: #f0f0f0;
        color: white !important;
        border-color: #d0d0d0;
      }

      &[disabled] {
        color: #999999;
        background: #f9f9f9;
        border-color: #e0e0e0;
      }
    }
  }

  /* Pagination select dropdown for light theme */
  .mat-mdc-select-panel {
    background: #ffffff !important;
    border-color: #e0e0e0;

    .mat-mdc-option {
      color: #333333;

      &:hover {
        background: #f0f0f0 !important;
      }

      &.mat-mdc-option-active {
        background: #f5f5f5 !important;
        color: #333333;
      }

      &.mdc-list-item--selected {
        background: #10b981 !important;
        color: white;
      }
    }
  }
}

/* Dark Theme Overrides */
:host-context(.dark-theme) {
  .table-card {
    background: #2a2a2a; /* Greyish background for dark mode */
    border-color: #404040;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .integrated-header {
    background: #2a2a2a; /* Match card background */
    border-bottom-color: #404040;
  }

  .page-title {
    color: #ffffff;
  }

  .page-subtitle {
    color: #b0b0b0;
  }

  /* Add user button styling handled by global .add-user-btn class */

  .table-header {
    background: #2a2a2a; /* Match card background */
    border-bottom-color: #404040;
  }

  .table-view-controls .view-btn {
    color: #b0b0b0;

    &.active {
      background: #404040;
      color: #ffffff;
    }

    &:hover:not(.active) {
      background: #353535;
      color: #ffffff;
    }
  }

  /* Reset button styling handled by global .reset-btn class */

  /* ng-zorro form field styling for dark theme */
  :host ::ng-deep .nz-search-field .search-input .ant-input-search {
    .ant-input {
      background: #2a2a2a !important;
      border-color: #404040 !important;
      color: #ffffff !important;

      &::placeholder {
        color: #b0b0b0 !important;
      }

      &:focus,
      &:focus-within {
        border-color: #10b981 !important;
        box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2) !important;
      }

      &:hover:not(:focus) {
        border-color: #505050 !important;
      }
    }

    .ant-input-search-button {
      background: #404040 !important;
      border-color: #404040 !important;
      color: #b0b0b0 !important;

      &:hover {
        background: #505050 !important;
        color: #ffffff !important;
      }
    }
  }

  .filter-label {
    color: #b0b0b0;
  }

  :host ::ng-deep .nz-filter-field .filter-select .ant-select {
    .ant-select-selector {
      background: #2a2a2a !important;
      border-color: #404040 !important;
      color: #ffffff !important;

      .ant-select-selection-placeholder {
        color: #b0b0b0 !important;
      }

      .ant-select-selection-item {
        color: #ffffff !important;
      }
    }

    &:hover .ant-select-selector {
      border-color: #505050 !important;
    }

    &.ant-select-focused .ant-select-selector {
      border-color: #10b981 !important;
      box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2) !important;
    }

    .ant-select-arrow {
      color: #b0b0b0 !important;
    }

    .ant-select-clear {
      color: #b0b0b0 !important;
      background: #2a2a2a !important;

      &:hover {
        color: #ffffff !important;
      }
    }
  }

  /* Dark theme dropdown styling */
  .ant-select-dropdown {
    background: #2a2a2a !important;
    border-color: #404040 !important;

    .ant-select-item {
      color: #ffffff !important;

      &:hover {
        background: #404040 !important;
      }

      &.ant-select-item-option-selected {
        background: #10b981 !important;
        color: white !important;
      }

      &.ant-select-item-option-active {
        background: #404040 !important;
      }
    }
  }

  /* Table styling handled by global .users-table class */

  /* Pagination styling for dark theme */
  .mat-mdc-paginator {
    background: #2a2a2a !important; /* Match card background */
    border-top-color: #404040;
    color: #ffffff;

    /* Container styling */
    .mat-mdc-paginator-container {
      background: #2a2a2a;
    }

    /* Range label (e.g., "1 - 5 of 5") */
    .mat-mdc-paginator-range-label {
      color: #ffffff !important; /* Make more visible */
      font-weight: 500;
    }

    /* Page size label (e.g., "Items per page:") */
    .mat-mdc-paginator-page-size-label {
      color: #ffffff !important; /* Make more visible */
      font-weight: 500;
    }

    .mat-mdc-select {
      color: #ffffff;

      .mat-mdc-select-value {
        color: #ffffff;
      }

      .mat-mdc-select-trigger {
        color: #ffffff;
        border-color: #404040;
        background: #404040;

        &:hover {
          background: #505050;
          border-color: #505050;
        }
      }

      .mat-mdc-select-arrow {
        color: #b0b0b0;
      }
    }

    /* Page size form field styling */
    .mat-mdc-paginator-page-size .mat-mdc-form-field {
      .mat-mdc-form-field-outline {
        .mdc-notched-outline__leading,
        .mdc-notched-outline__notch,
        .mdc-notched-outline__trailing {
          border-color: #404040;
        }
      }

      &.mat-focused .mat-mdc-form-field-outline {
        .mdc-notched-outline__leading,
        .mdc-notched-outline__notch,
        .mdc-notched-outline__trailing {
          border-color: #10b981;
          border-width: 2px;
        }
      }

      .mat-mdc-form-field-label {
        color: #b0b0b0;
      }
    }

    .mat-mdc-paginator-navigation-previous,
    .mat-mdc-paginator-navigation-next,
    .mat-mdc-paginator-navigation-first,
    .mat-mdc-paginator-navigation-last {
      color: #ffffff !important;
      border-color: #404040;
      background: #404040;

      &:hover:not([disabled]) {
        background: #505050;
        border-color: #505050;
        color: #ffffff !important;
      }

      &[disabled] {
        color: #666666 !important;
        background: #353535;
        border-color: #404040;
        opacity: 0.6;
        cursor: not-allowed;
      }

      .mat-mdc-button-touch-target {
        width: 36px;
        height: 36px;
      }

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
        color: #ffffff !important;
      }
    }
  }

  /* Pagination select dropdown for dark theme */
  .mat-mdc-select-panel {
    background: #2a2a2a !important;
    border-color: #404040;

    .mat-mdc-option {
      color: #ffffff;
      background: #2a2a2a;

      &:hover {
        background: #404040 !important;
        color: #ffffff;
      }

      &.mat-mdc-option-active {
        background: #404040 !important;
        color: #ffffff;
      }

      &.mdc-list-item--selected {
        background: #10b981 !important;
        color: white;
      }
    }
  }
}
