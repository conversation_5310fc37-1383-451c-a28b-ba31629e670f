import * as i0 from "@angular/core";
import * as i1 from "./element-patch.directive";
export declare class NzElementPatchModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<NzElementPatchModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<NzElementPatchModule, never, [typeof i1.NzElementPatchDirective], [typeof i1.NzElementPatchDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<NzElementPatchModule>;
}
