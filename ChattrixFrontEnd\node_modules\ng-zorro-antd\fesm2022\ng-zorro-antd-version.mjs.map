{"version": 3, "file": "ng-zorro-antd-version.mjs", "sources": ["../../components/version/version.ts", "../../components/version/public-api.ts", "../../components/version/ng-zorro-antd-version.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Version } from '@angular/core';\n\nexport const VERSION = new Version('19.3.1');\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './version';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": [], "mappings": ";;AAAA;;;AAGG;MAIU,OAAO,GAAG,IAAI,OAAO,CAAC,QAAQ;;ACP3C;;;AAGG;;ACHH;;AAEG;;;;"}