@import '../../style/themes/index';
@import '../../style/mixins/index';

@card-prefix-cls: ~'@{ant-prefix}-card';
@card-hoverable-hover-border: transparent;
@card-action-icon-size: 16px;

@gradient-min: fade(@card-skeleton-bg, 20%);
@gradient-max: fade(@card-skeleton-bg, 40%);

.@{card-prefix-cls} {
  .reset-component();

  position: relative;
  background: @card-background;
  border-radius: @card-radius;

  &-rtl {
    direction: rtl;
  }

  &-hoverable {
    cursor: pointer;
    transition: box-shadow 0.3s, border-color 0.3s;

    &:hover {
      border-color: @card-hoverable-hover-border;
      box-shadow: @card-shadow;
    }
  }

  &-bordered {
    border: @border-width-base @border-style-base @border-color-split;
  }

  &-head {
    min-height: @card-head-height;
    margin-bottom: -1px; // Fix card grid overflow bug: https://gw.alipayobjects.com/zos/rmsportal/XonYxBikwpgbqIQBeuhk.png
    padding: 0 @card-padding-base;
    color: @card-head-color;
    font-weight: 500;
    font-size: @card-head-font-size;
    background: @card-head-background;
    border-bottom: @border-width-base @border-style-base @border-color-split;
    border-radius: @card-radius @card-radius 0 0;
    .clearfix();

    &-wrapper {
      display: flex;
      align-items: center;
    }

    &-title {
      display: inline-block;
      flex: 1;
      padding: @card-head-padding 0;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      > .@{ant-prefix}-typography,
      > .@{ant-prefix}-typography-edit-content {
        left: 0;
        margin-top: 0;
        margin-bottom: 0;
      }
    }

    .@{ant-prefix}-tabs-top {
      clear: both;
      margin-bottom: @card-head-tabs-margin-bottom;
      color: @text-color;
      font-weight: normal;
      font-size: @font-size-base;

      &-bar {
        border-bottom: @border-width-base @border-style-base @border-color-split;
      }
    }
  }

  &-extra {
    // https://stackoverflow.com/a/22429853/3040605
    margin-left: auto;
    padding: @card-head-padding 0;
    color: @card-head-extra-color;
    font-weight: normal;
    font-size: @font-size-base;

    .@{card-prefix-cls}-rtl & {
      margin-right: auto;
      margin-left: 0;
    }
  }

  &-body {
    padding: @card-padding-base;
    .clearfix();
  }

  &-contain-grid &-body {
    display: flex;
    flex-wrap: wrap;
  }

  &-contain-grid:not(&-loading) &-body {
    margin: -1px 0 0 -1px;
    padding: 0;
  }

  &-grid {
    width: 33.33%;
    padding: @card-padding-base;
    border: 0;
    border-radius: 0;
    box-shadow: 1px 0 0 0 @border-color-split, 0 1px 0 0 @border-color-split,
      1px 1px 0 0 @border-color-split, 1px 0 0 0 @border-color-split inset,
      0 1px 0 0 @border-color-split inset;
    transition: all 0.3s;

    &-hoverable {
      &:hover {
        position: relative;
        z-index: 1;
        box-shadow: @card-shadow;
      }
    }
  }

  &-contain-tabs > &-head &-head-title {
    min-height: @card-head-height - @card-head-padding;
    padding-bottom: 0;
  }

  &-contain-tabs > &-head &-extra {
    padding-bottom: 0;
  }

  &-bordered &-cover {
    margin-top: -1px;
    margin-right: -1px;
    margin-left: -1px;
  }

  &-cover {
    > * {
      display: block;
      width: 100%;
    }

    img {
      border-radius: @card-radius @card-radius 0 0;
    }
  }

  &-actions {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
    background: @card-actions-background;
    border-top: @border-width-base @border-style-base @border-color-split;
    .clearfix();

    & > li {
      margin: @card-actions-li-margin;
      color: @text-color-secondary;
      text-align: center;

      > span {
        position: relative;
        display: block;
        min-width: 32px;
        font-size: @font-size-base;
        line-height: @line-height-base;
        cursor: pointer;

        &:hover {
          color: @primary-color;
          transition: color 0.3s;
        }

        a:not(.@{ant-prefix}-btn),
        > .@{iconfont-css-prefix} {
          display: inline-block;
          width: 100%;
          color: @text-color-secondary;
          line-height: 22px;
          transition: color 0.3s;

          &:hover {
            color: @primary-color;
          }
        }

        > .@{iconfont-css-prefix} {
          font-size: @card-action-icon-size;
          line-height: 22px;
        }
      }

      &:not(:last-child) {
        border-right: @border-width-base @border-style-base @border-color-split;

        .@{card-prefix-cls}-rtl & {
          border-right: none;
          border-left: @border-width-base @border-style-base @border-color-split;
        }
      }
    }
  }

  &-type-inner &-head {
    padding: 0 @card-padding-base;
    background: @background-color-light;

    &-title {
      padding: @card-inner-head-padding 0;
      font-size: @font-size-base;
    }
  }

  &-type-inner &-body {
    padding: 16px @card-padding-base;
  }

  &-type-inner &-extra {
    padding: @card-inner-head-padding + 1.5px 0;
  }

  &-meta {
    display: flex;
    margin: -4px 0;
    .clearfix();

    &-avatar {
      padding-right: 16px;

      .@{card-prefix-cls}-rtl & {
        padding-right: 0;
        padding-left: 16px;
      }
    }

    &-detail {
      flex: 1;
      overflow: hidden;

      > div:not(:last-child) {
        margin-bottom: @margin-xs;
      }
    }

    &-title {
      overflow: hidden;
      color: @card-head-color;
      font-weight: 500;
      font-size: @font-size-lg;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &-description {
      color: @text-color-secondary;
    }
  }

  &-loading {
    overflow: hidden;
  }

  &-loading &-body {
    user-select: none;
  }
}

@import './size';
