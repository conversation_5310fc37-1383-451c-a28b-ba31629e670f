/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */
declare const _default: {
    locale: string;
    Pagination: {
        items_per_page: string;
        jump_to: string;
        jump_to_confirm: string;
        page: string;
        prev_page: string;
        next_page: string;
        prev_5: string;
        next_5: string;
        prev_3: string;
        next_3: string;
        page_size: string;
    };
    DatePicker: {
        lang: {
            placeholder: string;
            yearPlaceholder: string;
            quarterPlaceholder: string;
            monthPlaceholder: string;
            weekPlaceholder: string;
            rangePlaceholder: string[];
            rangeYearPlaceholder: string[];
            rangeMonthPlaceholder: string[];
            rangeWeekPlaceholder: string[];
            locale: string;
            today: string;
            now: string;
            backToToday: string;
            ok: string;
            clear: string;
            month: string;
            year: string;
            timeSelect: string;
            dateSelect: string;
            weekSelect: string;
            monthSelect: string;
            yearSelect: string;
            decadeSelect: string;
            yearFormat: string;
            dateFormat: string;
            dayFormat: string;
            dateTimeFormat: string;
            monthBeforeYear: boolean;
            previousMonth: string;
            nextMonth: string;
            previousYear: string;
            nextYear: string;
            previousDecade: string;
            nextDecade: string;
            previousCentury: string;
            nextCentury: string;
            shortWeekDays: string[];
            shortMonths: string[];
        };
        timePickerLocale: {
            placeholder: string;
            rangePlaceholder: string[];
        };
    };
    TimePicker: {
        placeholder: string;
        rangePlaceholder: string[];
    };
    Calendar: {
        lang: {
            placeholder: string;
            yearPlaceholder: string;
            quarterPlaceholder: string;
            monthPlaceholder: string;
            weekPlaceholder: string;
            rangePlaceholder: string[];
            rangeYearPlaceholder: string[];
            rangeMonthPlaceholder: string[];
            rangeWeekPlaceholder: string[];
            locale: string;
            today: string;
            now: string;
            backToToday: string;
            ok: string;
            clear: string;
            month: string;
            year: string;
            timeSelect: string;
            dateSelect: string;
            weekSelect: string;
            monthSelect: string;
            yearSelect: string;
            decadeSelect: string;
            yearFormat: string;
            dateFormat: string;
            dayFormat: string;
            dateTimeFormat: string;
            monthBeforeYear: boolean;
            previousMonth: string;
            nextMonth: string;
            previousYear: string;
            nextYear: string;
            previousDecade: string;
            nextDecade: string;
            previousCentury: string;
            nextCentury: string;
            shortWeekDays: string[];
            shortMonths: string[];
        };
        timePickerLocale: {
            placeholder: string;
            rangePlaceholder: string[];
        };
    };
    global: {
        placeholder: string;
    };
    Table: {
        filterTitle: string;
        filterConfirm: string;
        filterReset: string;
        filterEmptyText: string;
        emptyText: string;
        selectAll: string;
        selectInvert: string;
        selectionAll: string;
        sortTitle: string;
        expand: string;
        collapse: string;
        triggerDesc: string;
        triggerAsc: string;
        cancelSort: string;
        selectNone: string;
    };
    Modal: {
        okText: string;
        cancelText: string;
        justOkText: string;
    };
    Popconfirm: {
        okText: string;
        cancelText: string;
    };
    Transfer: {
        titles: string[];
        searchPlaceholder: string;
        itemUnit: string;
        itemsUnit: string;
        remove: string;
        selectCurrent: string;
        removeCurrent: string;
        selectAll: string;
        removeAll: string;
        selectInvert: string;
    };
    Upload: {
        uploading: string;
        removeFile: string;
        uploadError: string;
        previewFile: string;
        downloadFile: string;
    };
    Empty: {
        description: string;
    };
    Icon: {
        icon: string;
    };
    Text: {
        edit: string;
        copy: string;
        copied: string;
        expand: string;
    };
    PageHeader: {
        back: string;
    };
    Image: {
        preview: string;
    };
    CronExpression: {
        cronError: string;
        second: string;
        minute: string;
        hour: string;
        day: string;
        month: string;
        week: string;
    };
};
export default _default;
