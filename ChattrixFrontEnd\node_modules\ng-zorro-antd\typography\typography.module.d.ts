import * as i0 from "@angular/core";
import * as i1 from "./typography.component";
import * as i2 from "./text-copy.component";
import * as i3 from "./text-edit.component";
export declare class NzTypographyModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTypographyModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<NzTypographyModule, never, [typeof i1.NzTypographyComponent, typeof i2.NzTextCopyComponent, typeof i3.NzTextEditComponent], [typeof i1.NzTypographyComponent, typeof i2.NzTextCopyComponent, typeof i3.NzTextEditComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<NzTypographyModule>;
}
