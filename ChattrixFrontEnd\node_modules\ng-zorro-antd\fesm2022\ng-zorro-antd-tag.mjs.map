{"version": 3, "file": "ng-zorro-antd-tag.mjs", "sources": ["../../components/tag/tag.component.ts", "../../components/tag/tag.module.ts", "../../components/tag/public-api.ts", "../../components/tag/ng-zorro-antd-tag.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport {\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  Renderer2,\n  SimpleChanges,\n  ViewEncapsulation\n} from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport {\n  isPresetColor,\n  isStatusColor,\n  NzPresetColor,\n  NzStatusColor,\n  presetColors,\n  statusColors\n} from 'ng-zorro-antd/core/color';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\n@Component({\n  selector: 'nz-tag',\n  exportAs: 'nzTag',\n  preserveWhitespaces: false,\n  template: `\n    <ng-content></ng-content>\n    @if (nzMode === 'closeable') {\n      <nz-icon nzType=\"close\" class=\"ant-tag-close-icon\" tabindex=\"-1\" (click)=\"closeTag($event)\" />\n    }\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  host: {\n    class: 'ant-tag',\n    '[style.background-color]': `isPresetColor ? '' : nzColor`,\n    '[class.ant-tag-has-color]': `nzColor && !isPresetColor`,\n    '[class.ant-tag-checkable]': `nzMode === 'checkable'`,\n    '[class.ant-tag-checkable-checked]': `nzChecked`,\n    '[class.ant-tag-rtl]': `dir === 'rtl'`,\n    '[class.ant-tag-borderless]': `!nzBordered`,\n    '(click)': 'updateCheckedStatus()'\n  },\n  imports: [NzIconModule]\n})\nexport class NzTagComponent implements OnChanges, OnDestroy, OnInit {\n  isPresetColor = false;\n  @Input() nzMode: 'default' | 'closeable' | 'checkable' = 'default';\n  @Input() nzColor?: string | NzStatusColor | NzPresetColor;\n  @Input({ transform: booleanAttribute }) nzChecked = false;\n  @Input({ transform: booleanAttribute }) nzBordered = true;\n  @Output() readonly nzOnClose = new EventEmitter<MouseEvent>();\n  @Output() readonly nzCheckedChange = new EventEmitter<boolean>();\n  dir: Direction = 'ltr';\n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private cdr: ChangeDetectorRef,\n    private renderer: Renderer2,\n    private elementRef: ElementRef,\n    private directionality: Directionality\n  ) {}\n\n  updateCheckedStatus(): void {\n    if (this.nzMode === 'checkable') {\n      this.nzChecked = !this.nzChecked;\n      this.nzCheckedChange.emit(this.nzChecked);\n    }\n  }\n\n  closeTag(e: MouseEvent): void {\n    this.nzOnClose.emit(e);\n    if (!e.defaultPrevented) {\n      this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);\n    }\n  }\n\n  private clearPresetColor(): void {\n    const hostElement = this.elementRef.nativeElement as HTMLElement;\n    // /(ant-tag-(?:pink|red|...))/g\n    const regexp = new RegExp(`(ant-tag-(?:${[...presetColors, ...statusColors].join('|')}))`, 'g');\n    const classname = hostElement.classList.toString();\n    const matches: string[] = [];\n    let match: RegExpExecArray | null = regexp.exec(classname);\n    while (match !== null) {\n      matches.push(match[1]);\n      match = regexp.exec(classname);\n    }\n    hostElement.classList.remove(...matches);\n  }\n\n  private setPresetColor(): void {\n    const hostElement = this.elementRef.nativeElement as HTMLElement;\n    this.clearPresetColor();\n    if (!this.nzColor) {\n      this.isPresetColor = false;\n    } else {\n      this.isPresetColor = isPresetColor(this.nzColor) || isStatusColor(this.nzColor);\n    }\n    if (this.isPresetColor) {\n      hostElement.classList.add(`ant-tag-${this.nzColor}`);\n    }\n  }\n\n  ngOnInit(): void {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n\n    this.dir = this.directionality.value;\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzColor } = changes;\n    if (nzColor) {\n      this.setPresetColor();\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzTagComponent } from './tag.component';\n\n@NgModule({\n  imports: [NzTagComponent],\n  exports: [NzTagComponent]\n})\nexport class NzTagModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './tag.component';\nexport * from './tag.module';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": [], "mappings": ";;;;;;;;;MA2Da,cAAc,CAAA;AAYf,IAAA,GAAA;AACA,IAAA,QAAA;AACA,IAAA,UAAA;AACA,IAAA,cAAA;IAdV,aAAa,GAAG,KAAK;IACZ,MAAM,GAA0C,SAAS;AACzD,IAAA,OAAO;IACwB,SAAS,GAAG,KAAK;IACjB,UAAU,GAAG,IAAI;AACtC,IAAA,SAAS,GAAG,IAAI,YAAY,EAAc;AAC1C,IAAA,eAAe,GAAG,IAAI,YAAY,EAAW;IAChE,GAAG,GAAc,KAAK;AACd,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;AAEtC,IAAA,WAAA,CACU,GAAsB,EACtB,QAAmB,EACnB,UAAsB,EACtB,cAA8B,EAAA;QAH9B,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAU,CAAA,UAAA,GAAV,UAAU;QACV,IAAc,CAAA,cAAA,GAAd,cAAc;;IAGxB,mBAAmB,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS;YAChC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;;;AAI7C,IAAA,QAAQ,CAAC,CAAa,EAAA;AACpB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AACtB,QAAA,IAAI,CAAC,CAAC,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;;;IAI7G,gBAAgB,GAAA;AACtB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,aAA4B;;QAEhE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,CAAe,YAAA,EAAA,CAAC,GAAG,YAAY,EAAE,GAAG,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAI,EAAA,CAAA,EAAE,GAAG,CAAC;QAC/F,MAAM,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,QAAQ,EAAE;QAClD,MAAM,OAAO,GAAa,EAAE;QAC5B,IAAI,KAAK,GAA2B,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;AAC1D,QAAA,OAAO,KAAK,KAAK,IAAI,EAAE;YACrB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB,YAAA,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;;QAEhC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC;;IAGlC,cAAc,GAAA;AACpB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,aAA4B;QAChE,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;;aACrB;AACL,YAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;;AAEjF,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAW,QAAA,EAAA,IAAI,CAAC,OAAO,CAAE,CAAA,CAAC;;;IAIxD,QAAQ,GAAA;QACN,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,YAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,SAAC,CAAC;QAEF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;;AAGtC,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO;QAC3B,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,cAAc,EAAE;;;IAIzB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;uGA7Ef,cAAc,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,EAIL,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAzB1B,EAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,uBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,wBAAA,EAAA,8BAAA,EAAA,yBAAA,EAAA,2BAAA,EAAA,yBAAA,EAAA,wBAAA,EAAA,iCAAA,EAAA,WAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,0BAAA,EAAA,aAAA,EAAA,EAAA,cAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,CAAA,OAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;AAKT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAaS,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEX,cAAc,EAAA,UAAA,EAAA,CAAA;kBAxB1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,QAAQ;AAClB,oBAAA,QAAQ,EAAE,OAAO;AACjB,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE;;;;;AAKT,EAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,SAAS;AAChB,wBAAA,0BAA0B,EAAE,CAA8B,4BAAA,CAAA;AAC1D,wBAAA,2BAA2B,EAAE,CAA2B,yBAAA,CAAA;AACxD,wBAAA,2BAA2B,EAAE,CAAwB,sBAAA,CAAA;AACrD,wBAAA,mCAAmC,EAAE,CAAW,SAAA,CAAA;AAChD,wBAAA,qBAAqB,EAAE,CAAe,aAAA,CAAA;AACtC,wBAAA,4BAA4B,EAAE,CAAa,WAAA,CAAA;AAC3C,wBAAA,SAAS,EAAE;AACZ,qBAAA;oBACD,OAAO,EAAE,CAAC,YAAY;AACvB,iBAAA;oKAGU,MAAM,EAAA,CAAA;sBAAd;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACuC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACnB,SAAS,EAAA,CAAA;sBAA3B;gBACkB,eAAe,EAAA,CAAA;sBAAjC;;;AClEH;;;AAGG;MAUU,WAAW,CAAA;uGAAX,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAX,WAAW,EAAA,OAAA,EAAA,CAHZ,cAAc,CAAA,EAAA,OAAA,EAAA,CACd,cAAc,CAAA,EAAA,CAAA;AAEb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,YAHZ,cAAc,CAAA,EAAA,CAAA;;2FAGb,WAAW,EAAA,UAAA,EAAA,CAAA;kBAJvB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,cAAc,CAAC;oBACzB,OAAO,EAAE,CAAC,cAAc;AACzB,iBAAA;;;ACZD;;;AAGG;;ACHH;;AAEG;;;;"}