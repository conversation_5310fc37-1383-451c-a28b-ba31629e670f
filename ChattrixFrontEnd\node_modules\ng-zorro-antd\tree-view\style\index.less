/*
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */

nz-tree-virtual-scroll-view {
  position: relative;
  display: block;
  overflow: auto;
  transform: translateZ(0);
  will-change: scroll-position;
  contain: strict;
  -webkit-overflow-scrolling: touch;

  .ant-tree-list, .ant-tree-list-holder {
    height: 100%;
  }
}

nz-tree-virtual-scroll-view, nz-tree-view {
  .ant-tree-switcher + .ant-tree-switcher.nz-tree-leaf-line-icon {
    display: none;
  }
}

nz-tree-view .ant-tree-list-holder-inner {
  display: flex;
  flex-direction: column;
}
