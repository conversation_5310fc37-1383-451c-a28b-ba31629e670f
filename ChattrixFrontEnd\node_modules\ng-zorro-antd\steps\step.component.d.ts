import { ChangeDetectorRef, ElementRef, OnInit, TemplateRef } from '@angular/core';
import { Subject } from 'rxjs';
import { NzDestroyService } from 'ng-zorro-antd/core/services';
import { NgClassType, NzSizeDSType } from 'ng-zorro-antd/core/types';
import { NzProgressFormatter } from 'ng-zorro-antd/progress';
import * as i0 from "@angular/core";
export declare class NzStepComponent implements OnInit {
    private cdr;
    private destroy$;
    processDotTemplate?: TemplateRef<void>;
    itemContainer: ElementRef<HTMLElement>;
    nzTitle?: string | TemplateRef<void>;
    nzSubtitle?: string | TemplateRef<void>;
    nzDescription?: string | TemplateRef<void>;
    nzDisabled: boolean;
    nzPercentage: number | null;
    nzSize: NzSizeDSType;
    get nzStatus(): string;
    set nzStatus(status: string);
    isCustomStatus: boolean;
    private _status;
    get nzIcon(): NgClassType | TemplateRef<void> | undefined;
    set nzIcon(value: NgClassType | TemplateRef<void> | undefined);
    oldAPIIcon: boolean;
    private _icon?;
    customProcessTemplate?: TemplateRef<{
        $implicit: TemplateRef<void>;
        status: string;
        index: number;
    }>;
    direction: string;
    index: number;
    last: boolean;
    outStatus: string;
    showProcessDot: boolean;
    clickable: boolean;
    clickOutsideAngular$: Subject<number>;
    readonly nullProcessFormat: NzProgressFormatter;
    get showProgress(): boolean;
    get currentIndex(): number;
    set currentIndex(current: number);
    private _currentIndex;
    constructor(cdr: ChangeDetectorRef, destroy$: NzDestroyService);
    ngOnInit(): void;
    enable(): void;
    disable(): void;
    markForCheck(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzStepComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzStepComponent, "nz-step", ["nzStep"], { "nzTitle": { "alias": "nzTitle"; "required": false; }; "nzSubtitle": { "alias": "nzSubtitle"; "required": false; }; "nzDescription": { "alias": "nzDescription"; "required": false; }; "nzDisabled": { "alias": "nzDisabled"; "required": false; }; "nzPercentage": { "alias": "nzPercentage"; "required": false; }; "nzSize": { "alias": "nzSize"; "required": false; }; "nzStatus": { "alias": "nzStatus"; "required": false; }; "nzIcon": { "alias": "nzIcon"; "required": false; }; }, {}, never, never, true, never>;
    static ngAcceptInputType_nzDisabled: unknown;
}
