{"version": 3, "file": "ng-zorro-antd-timeline.mjs", "sources": ["../../components/timeline/typings.ts", "../../components/timeline/timeline.service.ts", "../../components/timeline/timeline-item.component.ts", "../../components/timeline/timeline.component.ts", "../../components/timeline/timeline.module.ts", "../../components/timeline/public-api.ts", "../../components/timeline/ng-zorro-antd-timeline.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport type NzTimelineMode = 'left' | 'alternate' | 'right' | 'custom';\n\nexport type NzTimelinePosition = 'left' | 'right';\n\nexport const TimelineTimeDefaultColors = ['red', 'blue', 'green', 'grey', 'gray'] as const;\nexport type NzTimelineItemColor = (typeof TimelineTimeDefaultColors)[number] | string;\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Injectable } from '@angular/core';\nimport { ReplaySubject } from 'rxjs';\n\n@Injectable()\nexport class TimelineService {\n  check$ = new ReplaySubject<void>(1);\n  markForCheck(): void {\n    this.check$.next();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Input,\n  OnChanges,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation\n} from '@angular/core';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\n\nimport { TimelineService } from './timeline.service';\nimport { NzTimelineItemColor, NzTimelinePosition, TimelineTimeDefaultColors } from './typings';\n\nfunction isDefaultColor(color?: string): boolean {\n  return TimelineTimeDefaultColors.findIndex(i => i === color) !== -1;\n}\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  selector: 'nz-timeline-item, [nz-timeline-item]',\n  exportAs: 'nzTimelineItem',\n  template: `\n    <ng-template #template>\n      <li\n        class=\"ant-timeline-item\"\n        [class.ant-timeline-item-right]=\"(nzPosition || position) === 'right'\"\n        [class.ant-timeline-item-left]=\"(nzPosition || position) === 'left'\"\n        [class.ant-timeline-item-last]=\"isLast\"\n      >\n        @if (nzLabel) {\n          <div class=\"ant-timeline-item-label\">\n            <ng-container *nzStringTemplateOutlet=\"nzLabel\">{{ nzLabel }}</ng-container>\n          </div>\n        }\n        <div class=\"ant-timeline-item-tail\"></div>\n        <div\n          class=\"ant-timeline-item-head\"\n          [class.ant-timeline-item-head-red]=\"nzColor === 'red'\"\n          [class.ant-timeline-item-head-blue]=\"nzColor === 'blue'\"\n          [class.ant-timeline-item-head-green]=\"nzColor === 'green'\"\n          [class.ant-timeline-item-head-gray]=\"nzColor === 'gray'\"\n          [class.ant-timeline-item-head-custom]=\"!!nzDot\"\n          [style.border-color]=\"borderColor\"\n        >\n          <ng-container *nzStringTemplateOutlet=\"nzDot\">{{ nzDot }}</ng-container>\n        </div>\n        <div class=\"ant-timeline-item-content\">\n          <ng-content></ng-content>\n        </div>\n      </li>\n    </ng-template>\n  `,\n  imports: [NzOutletModule]\n})\nexport class NzTimelineItemComponent implements OnChanges {\n  @ViewChild('template', { static: false }) template!: TemplateRef<void>;\n\n  @Input() nzPosition?: NzTimelinePosition;\n  @Input() nzColor: NzTimelineItemColor = 'blue';\n  @Input() nzDot?: string | TemplateRef<void>;\n  @Input() nzLabel?: string | TemplateRef<void>;\n\n  isLast = false;\n  borderColor: string | null = null;\n  position?: NzTimelinePosition;\n\n  constructor(\n    private cdr: ChangeDetectorRef,\n    private timelineService: TimelineService\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    this.timelineService.markForCheck();\n    if (changes.nzColor) {\n      this.updateCustomColor();\n    }\n  }\n\n  detectChanges(): void {\n    this.cdr.detectChanges();\n  }\n\n  private updateCustomColor(): void {\n    this.borderColor = isDefaultColor(this.nzColor) ? null : this.nzColor;\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  QueryList,\n  SimpleChange,\n  SimpleChanges,\n  TemplateRef,\n  ViewEncapsulation,\n  booleanAttribute\n} from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\nimport { NzTimelineItemComponent } from './timeline-item.component';\nimport { TimelineService } from './timeline.service';\nimport { NzTimelineMode, NzTimelinePosition } from './typings';\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  selector: 'nz-timeline',\n  providers: [TimelineService],\n  exportAs: 'nzTimeline',\n  template: `\n    <ul\n      class=\"ant-timeline\"\n      [class.ant-timeline-label]=\"hasLabelItem\"\n      [class.ant-timeline-right]=\"!hasLabelItem && nzMode === 'right'\"\n      [class.ant-timeline-alternate]=\"nzMode === 'alternate' || nzMode === 'custom'\"\n      [class.ant-timeline-pending]=\"!!nzPending\"\n      [class.ant-timeline-reverse]=\"nzReverse\"\n      [class.ant-timeline-rtl]=\"dir === 'rtl'\"\n    >\n      <!-- pending dot (reversed) -->\n      @if (nzReverse) {\n        <ng-container [ngTemplateOutlet]=\"pendingTemplate\"></ng-container>\n      }\n      <!-- timeline items -->\n      @for (item of timelineItems; track item) {\n        <ng-template [ngTemplateOutlet]=\"item.template\"></ng-template>\n      }\n      @if (!nzReverse) {\n        <ng-container [ngTemplateOutlet]=\"pendingTemplate\"></ng-container>\n      }\n      <!-- pending dot -->\n    </ul>\n    <ng-template #pendingTemplate>\n      @if (nzPending) {\n        <li class=\"ant-timeline-item ant-timeline-item-pending\">\n          <div class=\"ant-timeline-item-tail\"></div>\n          <div class=\"ant-timeline-item-head ant-timeline-item-head-custom ant-timeline-item-head-blue\">\n            <ng-container *nzStringTemplateOutlet=\"nzPendingDot\">\n              {{ nzPendingDot }}\n              @if (!nzPendingDot) {\n                <nz-icon nzType=\"loading\" />\n              }\n            </ng-container>\n          </div>\n          <div class=\"ant-timeline-item-content\">\n            <ng-container *nzStringTemplateOutlet=\"nzPending\">\n              {{ isPendingBoolean ? '' : nzPending }}\n            </ng-container>\n          </div>\n        </li>\n      }\n    </ng-template>\n    <!-- Grasp items -->\n    <ng-content></ng-content>\n  `,\n  imports: [NgTemplateOutlet, NzOutletModule, NzIconModule]\n})\nexport class NzTimelineComponent implements AfterContentInit, OnChanges, OnDestroy, OnInit {\n  @ContentChildren(NzTimelineItemComponent) listOfItems!: QueryList<NzTimelineItemComponent>;\n\n  @Input() nzMode: NzTimelineMode = 'left';\n  @Input() nzPending?: string | boolean | TemplateRef<void>;\n  @Input() nzPendingDot?: string | TemplateRef<void>;\n  @Input({ transform: booleanAttribute }) nzReverse: boolean = false;\n\n  isPendingBoolean: boolean = false;\n  timelineItems: NzTimelineItemComponent[] = [];\n  dir: Direction = 'ltr';\n  hasLabelItem = false;\n\n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private cdr: ChangeDetectorRef,\n    private timelineService: TimelineService,\n    private directionality: Directionality\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzMode, nzReverse, nzPending } = changes;\n\n    if (simpleChangeActivated(nzMode) || simpleChangeActivated(nzReverse)) {\n      this.updateChildren();\n    }\n\n    if (nzPending) {\n      this.isPendingBoolean = nzPending.currentValue === true;\n    }\n  }\n\n  ngOnInit(): void {\n    this.timelineService.check$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.cdr.markForCheck();\n    });\n\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n\n    this.dir = this.directionality.value;\n  }\n\n  ngAfterContentInit(): void {\n    this.updateChildren();\n\n    this.listOfItems.changes.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateChildren();\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private updateChildren(): void {\n    if (this.listOfItems && this.listOfItems.length) {\n      const length = this.listOfItems.length;\n      let hasLabelItem = false;\n\n      this.listOfItems.forEach((item: NzTimelineItemComponent, index: number) => {\n        item.isLast = !this.nzReverse ? index === length - 1 : index === 0;\n        item.position = getInferredTimelineItemPosition(index, this.nzMode);\n\n        if (!hasLabelItem && item.nzLabel) {\n          hasLabelItem = true;\n        }\n\n        item.detectChanges();\n      });\n\n      this.timelineItems = this.nzReverse ? this.listOfItems.toArray().reverse() : this.listOfItems.toArray();\n      this.hasLabelItem = hasLabelItem;\n    } else {\n      this.timelineItems = [];\n      this.hasLabelItem = false;\n    }\n\n    this.cdr.markForCheck();\n  }\n}\n\nfunction simpleChangeActivated(simpleChange?: SimpleChange): boolean {\n  return !!(simpleChange && (simpleChange.previousValue !== simpleChange.currentValue || simpleChange.isFirstChange()));\n}\n\nfunction getInferredTimelineItemPosition(index: number, mode: NzTimelineMode): NzTimelinePosition | undefined {\n  return mode === 'custom'\n    ? undefined\n    : mode === 'left'\n      ? 'left'\n      : mode === 'right'\n        ? 'right'\n        : mode === 'alternate' && index % 2 === 0\n          ? 'left'\n          : 'right';\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzTimelineItemComponent } from './timeline-item.component';\nimport { NzTimelineComponent } from './timeline.component';\n\n@NgModule({\n  imports: [NzTimelineItemComponent, NzTimelineComponent],\n  exports: [NzTimelineItemComponent, NzTimelineComponent]\n})\nexport class NzTimelineModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './timeline-item.component';\nexport * from './timeline.component';\nexport * from './timeline.module';\nexport * from './timeline.service';\nexport type { NzTimelineMode, NzTimelinePosition, NzTimelineItemColor } from './typings';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1.TimelineService", "i2", "i3"], "mappings": ";;;;;;;;;;;AAAA;;;AAGG;AAMI,MAAM,yBAAyB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAU;;ACT1F;;;AAGG;MAMU,eAAe,CAAA;AAC1B,IAAA,MAAM,GAAG,IAAI,aAAa,CAAO,CAAC,CAAC;IACnC,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;;uGAHT,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAAf,eAAe,EAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B;;;ACRD;;;AAGG;AAmBH,SAAS,cAAc,CAAC,KAAc,EAAA;AACpC,IAAA,OAAO,yBAAyB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC;AACrE;MAyCa,uBAAuB,CAAA;AAaxB,IAAA,GAAA;AACA,IAAA,eAAA;AAbgC,IAAA,QAAQ;AAEzC,IAAA,UAAU;IACV,OAAO,GAAwB,MAAM;AACrC,IAAA,KAAK;AACL,IAAA,OAAO;IAEhB,MAAM,GAAG,KAAK;IACd,WAAW,GAAkB,IAAI;AACjC,IAAA,QAAQ;IAER,WACU,CAAA,GAAsB,EACtB,eAAgC,EAAA;QADhC,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAe,CAAA,eAAA,GAAf,eAAe;;AAGzB,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE;AACnC,QAAA,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,IAAI,CAAC,iBAAiB,EAAE;;;IAI5B,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;;IAGlB,iBAAiB,GAAA;AACvB,QAAA,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO;;uGA7B5D,uBAAuB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,uBAAuB,EAjCxB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sCAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EACS,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEb,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAvCnC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE,sCAAsC;AAChD,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BT,EAAA,CAAA;oBACD,OAAO,EAAE,CAAC,cAAc;AACzB,iBAAA;iHAE2C,QAAQ,EAAA,CAAA;sBAAjD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBAE/B,UAAU,EAAA,CAAA;sBAAlB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACQ,OAAO,EAAA,CAAA;sBAAf;;;MCkBU,mBAAmB,CAAA;AAgBpB,IAAA,GAAA;AACA,IAAA,eAAA;AACA,IAAA,cAAA;AAjBgC,IAAA,WAAW;IAE5C,MAAM,GAAmB,MAAM;AAC/B,IAAA,SAAS;AACT,IAAA,YAAY;IACmB,SAAS,GAAY,KAAK;IAElE,gBAAgB,GAAY,KAAK;IACjC,aAAa,GAA8B,EAAE;IAC7C,GAAG,GAAc,KAAK;IACtB,YAAY,GAAG,KAAK;AAEZ,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;AAEtC,IAAA,WAAA,CACU,GAAsB,EACtB,eAAgC,EAChC,cAA8B,EAAA;QAF9B,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAe,CAAA,eAAA,GAAf,eAAe;QACf,IAAc,CAAA,cAAA,GAAd,cAAc;;AAGxB,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,OAAO;QAEhD,IAAI,qBAAqB,CAAC,MAAM,CAAC,IAAI,qBAAqB,CAAC,SAAS,CAAC,EAAE;YACrE,IAAI,CAAC,cAAc,EAAE;;QAGvB,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC,YAAY,KAAK,IAAI;;;IAI3D,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AACxE,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,SAAC,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,YAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,SAAC,CAAC;QAEF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;;IAGtC,kBAAkB,GAAA;QAChB,IAAI,CAAC,cAAc,EAAE;AAErB,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YACrE,IAAI,CAAC,cAAc,EAAE;AACvB,SAAC,CAAC;;IAGJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;IAGlB,cAAc,GAAA;QACpB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;AAC/C,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM;YACtC,IAAI,YAAY,GAAG,KAAK;YAExB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAA6B,EAAE,KAAa,KAAI;gBACxE,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,KAAK,KAAK,MAAM,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC;gBAClE,IAAI,CAAC,QAAQ,GAAG,+BAA+B,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC;AAEnE,gBAAA,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,EAAE;oBACjC,YAAY,GAAG,IAAI;;gBAGrB,IAAI,CAAC,aAAa,EAAE;AACtB,aAAC,CAAC;YAEF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;AACvG,YAAA,IAAI,CAAC,YAAY,GAAG,YAAY;;aAC3B;AACL,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;AACvB,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK;;AAG3B,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;uGAlFd,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAnB,mBAAmB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,SAAA,EAAA,WAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAMV,gBAAgB,CAxDzB,EAAA,EAAA,SAAA,EAAA,CAAC,eAAe,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,SAAA,EAmDX,uBAAuB,EAjD9B,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACS,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAE,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAC,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAE,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE7C,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAvD/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE,aAAa;oBACvB,SAAS,EAAE,CAAC,eAAe,CAAC;AAC5B,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CT,EAAA,CAAA;AACD,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,cAAc,EAAE,YAAY;AACzD,iBAAA;gJAE2C,WAAW,EAAA,CAAA;sBAApD,eAAe;uBAAC,uBAAuB;gBAE/B,MAAM,EAAA,CAAA;sBAAd;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACuC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;;AAgFxC,SAAS,qBAAqB,CAAC,YAA2B,EAAA;IACxD,OAAO,CAAC,EAAE,YAAY,KAAK,YAAY,CAAC,aAAa,KAAK,YAAY,CAAC,YAAY,IAAI,YAAY,CAAC,aAAa,EAAE,CAAC,CAAC;AACvH;AAEA,SAAS,+BAA+B,CAAC,KAAa,EAAE,IAAoB,EAAA;IAC1E,OAAO,IAAI,KAAK;AACd,UAAE;UACA,IAAI,KAAK;AACT,cAAE;cACA,IAAI,KAAK;AACT,kBAAE;kBACA,IAAI,KAAK,WAAW,IAAI,KAAK,GAAG,CAAC,KAAK;AACtC,sBAAE;sBACA,OAAO;AACnB;;AC7LA;;;AAGG;MAWU,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAHjB,uBAAuB,EAAE,mBAAmB,CAC5C,EAAA,OAAA,EAAA,CAAA,uBAAuB,EAAE,mBAAmB,CAAA,EAAA,CAAA;wGAE3C,gBAAgB,EAAA,OAAA,EAAA,CAHjB,uBAAuB,EAAE,mBAAmB,CAAA,EAAA,CAAA;;2FAG3C,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,uBAAuB,EAAE,mBAAmB,CAAC;AACvD,oBAAA,OAAO,EAAE,CAAC,uBAAuB,EAAE,mBAAmB;AACvD,iBAAA;;;ACbD;;;AAGG;;ACHH;;AAEG;;;;"}