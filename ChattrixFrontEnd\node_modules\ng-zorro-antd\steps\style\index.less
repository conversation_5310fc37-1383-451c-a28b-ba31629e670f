@import '../../style/themes/index';
@import '../../style/mixins/index';

@steps-prefix-cls: ~'@{ant-prefix}-steps';
@process-icon-color: @primary-color;
@process-title-color: @heading-color;
@process-description-color: @text-color;
@process-icon-text-color: @text-color-inverse;
@wait-icon-color: @disabled-color;
@wait-title-color: @text-color-secondary;
@wait-description-color: @wait-title-color;
@wait-tail-color: @process-tail-color;
@finish-icon-color: @process-icon-color;
@finish-title-color: @text-color;
@finish-description-color: @text-color-secondary;
@finish-tail-color: @primary-color;
@error-icon-color: @error-color;
@error-title-color: @error-color;
@error-description-color: @error-color;
@error-tail-color: @wait-tail-color;
@steps-nav-active-color: @primary-color;

.@{steps-prefix-cls} {
  .reset-component();

  display: flex;
  width: 100%;
  font-size: 0;
  text-align: initial;
}

.@{steps-prefix-cls}-item {
  position: relative;
  display: inline-block;
  flex: 1;
  overflow: hidden;
  vertical-align: top;

  &-container {
    outline: none;
  }

  &:last-child {
    flex: none;
  }

  &:last-child > &-container > &-tail,
  &:last-child > &-container > &-content > &-title::after {
    display: none;
  }

  &-icon,
  &-content {
    display: inline-block;
    vertical-align: top;
  }

  &-icon {
    width: @steps-icon-size;
    height: @steps-icon-size;
    margin: @steps-icon-margin;
    font-size: @steps-icon-font-size;
    font-family: @font-family;
    line-height: @steps-icon-size;
    text-align: center;
    border: @border-width-base @border-style-base @wait-icon-color;
    border-radius: @steps-icon-size;
    transition: background-color 0.3s, border-color 0.3s;

    .@{steps-prefix-cls}-icon {
      position: relative;
      top: @steps-icon-top;
      color: @primary-color;
      line-height: 1;
    }
  }

  &-tail {
    position: absolute;
    top: 12px;
    left: 0;
    width: 100%;
    padding: 0 10px;

    &::after {
      display: inline-block;
      width: 100%;
      height: 1px;
      background: @border-color-split;
      border-radius: 1px;
      transition: background 0.3s;
      content: '';
    }
  }

  &-title {
    position: relative;
    display: inline-block;
    padding-right: 16px;
    color: @text-color;
    font-size: @font-size-lg;
    line-height: @steps-title-line-height;

    &::after {
      position: absolute;
      top: (@steps-title-line-height / 2);
      left: 100%;
      display: block;
      width: 9999px;
      height: 1px;
      background: @wait-tail-color;
      content: '';
    }
  }

  &-subtitle {
    display: inline;
    margin-left: 8px;
    color: @text-color-secondary;
    font-weight: normal;
    font-size: @font-size-base;
  }

  &-description {
    color: @text-color-secondary;
    font-size: @font-size-base;
  }
  .step-item-status(wait);
  .step-item-status(process);

  &-process > &-container > &-icon {
    background: @process-icon-color;
    .@{steps-prefix-cls}-icon {
      color: @process-icon-text-color;
    }
  }

  &-process > &-container > &-title {
    font-weight: 500;
  }
  .step-item-status(finish);
  .step-item-status(error);

  &.@{steps-prefix-cls}-next-error .@{steps-prefix-cls}-item-title::after {
    background: @error-icon-color;
  }

  &-disabled {
    cursor: not-allowed;
  }
}

// ===================== Clickable =====================
.@{steps-prefix-cls} .@{steps-prefix-cls}-item {
  &:not(.@{steps-prefix-cls}-item-active) {
    & > .@{steps-prefix-cls}-item-container[role='button'] {
      cursor: pointer;

      .@{steps-prefix-cls}-item {
        &-title,
        &-subtitle,
        &-description,
        &-icon .@{steps-prefix-cls}-icon {
          transition: color 0.3s;
        }
      }

      &:hover {
        .@{steps-prefix-cls}-item {
          &-title,
          &-subtitle,
          &-description {
            color: @primary-color;
          }
        }
      }
    }

    &:not(.@{steps-prefix-cls}-item-process) {
      & > .@{steps-prefix-cls}-item-container[role='button']:hover {
        .@{steps-prefix-cls}-item {
          &-icon {
            border-color: @primary-color;

            .@{steps-prefix-cls}-icon {
              color: @primary-color;
            }
          }
        }
      }
    }
  }
}

.@{steps-prefix-cls}-horizontal:not(.@{steps-prefix-cls}-label-vertical) {
  .@{steps-prefix-cls}-item {
    padding-left: 16px;
    white-space: nowrap;

    &:first-child {
      padding-left: 0;
    }
    &:last-child .@{steps-prefix-cls}-item-title {
      padding-right: 0;
    }

    &-tail {
      display: none;
    }

    &-description {
      max-width: @steps-description-max-width;
      white-space: normal;
    }
  }
}

.step-item-status(@status) {
  @icon-color: '@{status}-icon-color';
  @title-color: '@{status}-title-color';
  @description-color: '@{status}-description-color';
  @tail-color: '@{status}-tail-color';
  &-@{status} &-icon {
    background-color: @steps-background;
    border-color: @@icon-color;
    > .@{steps-prefix-cls}-icon {
      color: @@icon-color;
      .@{steps-prefix-cls}-icon-dot {
        background: @@icon-color;
      }
    }
  }
  &-@{status} > &-container > &-content > &-title {
    color: @@title-color;

    &::after {
      background-color: @@tail-color;
    }
  }
  &-@{status} > &-container > &-content > &-description {
    color: @@description-color;
  }
  &-@{status} > &-container > &-tail::after {
    background-color: @@tail-color;
  }
}

@import './custom-icon';
@import './small';
@import './vertical';
@import './label-placement';
@import './progress-dot';
@import './nav';
@import './rtl';
@import './progress.less';
