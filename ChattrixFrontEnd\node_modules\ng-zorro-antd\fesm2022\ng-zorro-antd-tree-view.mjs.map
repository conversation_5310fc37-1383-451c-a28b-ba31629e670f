{"version": 3, "file": "ng-zorro-antd-tree-view.mjs", "sources": ["../../components/tree-view/checkbox.ts", "../../components/tree-view/utils.ts", "../../components/tree-view/node-base.ts", "../../components/tree-view/tree.ts", "../../components/tree-view/indent.ts", "../../components/tree-view/toggle.ts", "../../components/tree-view/node.ts", "../../components/tree-view/option.ts", "../../components/tree-view/outlet.ts", "../../components/tree-view/padding.ts", "../../components/tree-view/tree-view.ts", "../../components/tree-view/tree-virtual-scroll-view.ts", "../../components/tree-view/tree-view.module.ts", "../../components/tree-view/data-source.ts", "../../components/tree-view/public-api.ts", "../../components/tree-view/ng-zorro-antd-tree-view.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnInit,\n  Output,\n  booleanAttribute\n} from '@angular/core';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\n\n@Component({\n  selector: 'nz-tree-node-checkbox:not([builtin])',\n  template: ` <span class=\"ant-tree-checkbox-inner\"></span> `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  preserveWhitespaces: false,\n  host: {\n    class: 'ant-tree-checkbox',\n    '[class.ant-tree-checkbox-checked]': `nzChecked`,\n    '[class.ant-tree-checkbox-indeterminate]': `nzIndeterminate`,\n    '[class.ant-tree-checkbox-disabled]': `nzDisabled`\n  },\n  providers: [NzDestroyService]\n})\nexport class NzTreeNodeCheckboxComponent implements OnInit {\n  @Input({ transform: booleanAttribute }) nzChecked?: boolean;\n  @Input({ transform: booleanAttribute }) nzIndeterminate?: boolean;\n  @Input({ transform: booleanAttribute }) nzDisabled?: boolean;\n  @Output() readonly nzClick = new EventEmitter<MouseEvent>();\n\n  constructor(\n    private ngZone: NgZone,\n    private ref: ChangeDetectorRef,\n    private host: ElementRef<HTMLElement>,\n    private destroy$: NzDestroyService\n  ) {}\n\n  ngOnInit(): void {\n    fromEventOutsideAngular<MouseEvent>(this.host.nativeElement, 'click')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe((event: MouseEvent) => {\n        if (!this.nzDisabled && this.nzClick.observers.length) {\n          this.ngZone.run(() => {\n            this.nzClick.emit(event);\n            this.ref.markForCheck();\n          });\n        }\n      });\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport const getParent = <T>(nodes: T[], node: T, getLevel: (dataNode: T) => number): T | null => {\n  let index = nodes.indexOf(node);\n  if (index < 0) {\n    return null;\n  }\n  const level = getLevel(node);\n  for (index--; index >= 0; index--) {\n    const preLevel = getLevel(nodes[index]);\n    if (preLevel + 1 === level) {\n      return nodes[index];\n    }\n    if (preLevel + 1 < level) {\n      return null;\n    }\n  }\n  return null;\n};\n\nexport const getNextSibling = <T>(\n  nodes: T[],\n  node: T,\n  getLevel: (dataNode: T) => number,\n  _index?: number\n): T | null => {\n  let index = typeof _index !== 'undefined' ? _index : nodes.indexOf(node);\n  if (index < 0) {\n    return null;\n  }\n  const level = getLevel(node);\n\n  for (index++; index < nodes.length; index++) {\n    const nextLevel = getLevel(nodes[index]);\n    if (nextLevel < level) {\n      return null;\n    }\n    if (nextLevel === level) {\n      return nodes[index];\n    }\n  }\n  return null;\n};\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { CdkTreeNode } from '@angular/cdk/tree';\n\nexport abstract class NzNodeBase<T> extends CdkTreeNode<T> {\n  abstract setIndents(indents: boolean[]): void;\n  abstract isLeaf: boolean;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { DataSource } from '@angular/cdk/collections';\nimport { CdkTree, TreeControl } from '@angular/cdk/tree';\nimport {\n  ChangeDetectorRef,\n  Component,\n  Input,\n  IterableDiffer,\n  IterableDiffers,\n  OnDestroy,\n  OnInit,\n  ViewContainerRef,\n  booleanAttribute,\n  inject\n} from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\n@Component({\n  template: ''\n})\n// eslint-disable-next-line @angular-eslint/component-class-suffix\nexport class NzTreeView<T> extends CdkTree<T> implements OnInit, OnDestroy {\n  private destroy$ = new Subject<boolean>();\n  dir: Direction = 'ltr';\n  _dataSourceChanged = new Subject<void>();\n  // eslint-disable-next-line @angular-eslint/no-input-rename\n  @Input('nzTreeControl') override treeControl?: TreeControl<T, NzSafeAny> = undefined;\n  @Input('nzDataSource')\n  override get dataSource(): DataSource<T> | Observable<T[]> | T[] {\n    return super.dataSource;\n  }\n  override set dataSource(dataSource: DataSource<T> | Observable<T[]> | T[]) {\n    super.dataSource = dataSource;\n  }\n  @Input({ transform: booleanAttribute }) nzDirectoryTree = false;\n  @Input({ transform: booleanAttribute }) nzBlockNode = false;\n\n  noAnimation = inject(NzNoAnimationDirective, { host: true, optional: true });\n\n  constructor(\n    protected differs: IterableDiffers,\n    protected changeDetectorRef: ChangeDetectorRef,\n    private directionality: Directionality\n  ) {\n    super(differs, changeDetectorRef, directionality);\n  }\n\n  override ngOnInit(): void {\n    super.ngOnInit();\n\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.changeDetectorRef.detectChanges();\n    });\n  }\n\n  override ngOnDestroy(): void {\n    super.ngOnDestroy();\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n\n  override renderNodeChanges(\n    data: T[] | readonly T[],\n    dataDiffer?: IterableDiffer<T>,\n    viewContainer?: ViewContainerRef,\n    parentData?: T\n  ): void {\n    super.renderNodeChanges(data, dataDiffer, viewContainer, parentData);\n    this._dataSourceChanged.next();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, Directive, Input, OnDestroy } from '@angular/core';\nimport { Subscription, animationFrameScheduler, asapScheduler, merge } from 'rxjs';\nimport { auditTime } from 'rxjs/operators';\n\nimport { NzNodeBase } from './node-base';\nimport { NzTreeView } from './tree';\nimport { getNextSibling, getParent } from './utils';\n\n/**\n * [true, false, false, true] => 1001\n */\nfunction booleanArrayToString(arr: boolean[]): string {\n  return arr.map(i => (i ? 1 : 0)).join('');\n}\n\nconst BUILD_INDENTS_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n\n@Component({\n  selector: 'nz-tree-node-indents',\n  template: `\n    @for (isEnd of indents; track isEnd) {\n      <span class=\"ant-tree-indent-unit\" [class.ant-tree-indent-unit-end]=\"!isEnd\"></span>\n    }\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    class: 'ant-tree-indent'\n  }\n})\nexport class NzTreeNodeIndentsComponent {\n  @Input() indents: boolean[] = [];\n}\n\n@Directive({\n  selector: 'nz-tree-node[nzTreeNodeIndentLine]',\n  host: {\n    class: 'ant-tree-show-line',\n    '[class.ant-tree-treenode-leaf-last]': 'isLast && isLeaf'\n  }\n})\nexport class NzTreeNodeIndentLineDirective<T> implements OnDestroy {\n  isLast: boolean | 'unset' = 'unset';\n  isLeaf = false;\n  private preNodeRef: T | null = null;\n  private nextNodeRef: T | null = null;\n  private currentIndents: string = '';\n  private changeSubscription: Subscription;\n\n  constructor(\n    private treeNode: NzNodeBase<T>,\n    private tree: NzTreeView<T>,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.buildIndents();\n    this.checkLast();\n\n    /**\n     * The dependent data (TreeControl.dataNodes) can be set after node instantiation,\n     * and setting the indents can cause frame rate loss if it is set too often.\n     */\n    this.changeSubscription = merge(this.treeNode._dataChanges, tree._dataSourceChanged)\n      .pipe(auditTime(0, BUILD_INDENTS_SCHEDULER))\n      .subscribe(() => {\n        this.buildIndents();\n        this.checkAdjacent();\n        this.cdr.markForCheck();\n      });\n  }\n\n  private getIndents(): boolean[] {\n    const indents: boolean[] = [];\n    if (!this.tree.treeControl) {\n      return indents;\n    }\n\n    const nodes = this.tree.treeControl.dataNodes;\n    const getLevel = this.tree.treeControl.getLevel;\n    let parent = getParent(nodes, this.treeNode.data, getLevel);\n    while (parent) {\n      const parentNextSibling = getNextSibling(nodes, parent, getLevel);\n      if (parentNextSibling) {\n        indents.unshift(true);\n      } else {\n        indents.unshift(false);\n      }\n      parent = getParent(nodes, parent, getLevel);\n    }\n    return indents;\n  }\n\n  private buildIndents(): void {\n    if (this.treeNode.data) {\n      const indents = this.getIndents();\n      const diffString = booleanArrayToString(indents);\n      if (diffString !== this.currentIndents) {\n        this.treeNode.setIndents(this.getIndents());\n        this.currentIndents = diffString;\n      }\n    }\n  }\n\n  /**\n   * We need to add an class name for the last child node,\n   * this result can also be affected when the adjacent nodes are changed.\n   */\n  private checkAdjacent(): void {\n    const nodes = this.tree.treeControl?.dataNodes || [];\n    const index = nodes.indexOf(this.treeNode.data);\n    const preNode = nodes[index - 1] || null;\n    const nextNode = nodes[index + 1] || null;\n    if (this.nextNodeRef !== nextNode || this.preNodeRef !== preNode) {\n      this.checkLast(index);\n    }\n    this.preNodeRef = preNode;\n    this.nextNodeRef = nextNode;\n  }\n\n  private checkLast(index?: number): void {\n    const nodes = this.tree.treeControl?.dataNodes || [];\n    this.isLeaf = this.treeNode.isLeaf;\n    this.isLast =\n      !!this.tree.treeControl && !getNextSibling(nodes, this.treeNode.data, this.tree.treeControl.getLevel, index);\n  }\n\n  ngOnDestroy(): void {\n    this.preNodeRef = null;\n    this.nextNodeRef = null;\n    this.changeSubscription.unsubscribe();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { CdkTreeNodeToggle } from '@angular/cdk/tree';\nimport { booleanAttribute, Directive, forwardRef, Input } from '@angular/core';\n\n@Directive({\n  selector: 'nz-tree-node-toggle[nzTreeNodeNoopToggle], [nzTreeNodeNoopToggle]',\n  host: {\n    class: 'ant-tree-switcher ant-tree-switcher-noop'\n  }\n})\nexport class NzTreeNodeNoopToggleDirective {}\n\n@Directive({\n  selector: 'nz-tree-node-toggle:not([nzTreeNodeNoopToggle]), [nzTreeNodeToggle]',\n  providers: [{ provide: CdkTreeNodeToggle, useExisting: forwardRef(() => NzTreeNodeToggleDirective) }],\n  host: {\n    class: 'ant-tree-switcher',\n    '[class.ant-tree-switcher_open]': 'isExpanded',\n    '[class.ant-tree-switcher_close]': '!isExpanded'\n  }\n})\nexport class NzTreeNodeToggleDirective<T> extends CdkTreeNodeToggle<T> {\n  @Input({ alias: 'nzTreeNodeToggleRecursive', transform: booleanAttribute }) override recursive = false;\n\n  get isExpanded(): boolean {\n    return this._treeNode.isExpanded;\n  }\n}\n\n@Directive({\n  selector: '[nzTreeNodeToggleRotateIcon]',\n  host: {\n    class: 'ant-tree-switcher-icon'\n  }\n})\nexport class NzTreeNodeToggleRotateIconDirective {}\n\n@Directive({\n  selector: '[nzTreeNodeToggleActiveIcon]',\n  host: {\n    class: 'ant-tree-switcher-loading-icon'\n  }\n})\nexport class NzTreeNodeToggleActiveIconDirective {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { CdkTreeNode, CdkTreeNodeDef, CdkTreeNodeOutletContext } from '@angular/cdk/tree';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Directive,\n  ElementRef,\n  EmbeddedViewRef,\n  forwardRef,\n  inject,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Renderer2,\n  SimpleChange,\n  SimpleChanges,\n  ViewContainerRef\n} from '@angular/core';\n\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\nimport { NzTreeNodeIndentsComponent } from './indent';\nimport { NzNodeBase } from './node-base';\nimport { NzTreeNodeNoopToggleDirective } from './toggle';\nimport { NzTreeView } from './tree';\n\nexport interface NzTreeVirtualNodeData<T> {\n  data: T;\n  context: CdkTreeNodeOutletContext<T>;\n  nodeDef: CdkTreeNodeDef<T>;\n}\n\n@Component({\n  selector: 'nz-tree-node:not([builtin])',\n  exportAs: 'nzTreeNode',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    { provide: CdkTreeNode, useExisting: forwardRef(() => NzTreeNodeComponent) },\n    { provide: NzNodeBase, useExisting: forwardRef(() => NzTreeNodeComponent) }\n  ],\n  template: `\n    @if (indents.length) {\n      <nz-tree-node-indents [indents]=\"indents\"></nz-tree-node-indents>\n    }\n    <ng-content select=\"nz-tree-node-toggle, [nz-tree-node-toggle]\"></ng-content>\n    @if (indents.length && isLeaf) {\n      <nz-tree-node-toggle class=\"nz-tree-leaf-line-icon\" nzTreeNodeNoopToggle>\n        <span class=\"ant-tree-switcher-leaf-line\"></span>\n      </nz-tree-node-toggle>\n    }\n    <ng-content select=\"nz-tree-node-checkbox\"></ng-content>\n    <ng-content select=\"nz-tree-node-option\"></ng-content>\n    <ng-content></ng-content>\n  `,\n  host: {\n    '[class.ant-tree-treenode-switcher-open]': 'isExpanded',\n    '[class.ant-tree-treenode-switcher-close]': '!isExpanded'\n  },\n  imports: [NzTreeNodeIndentsComponent, NzTreeNodeNoopToggleDirective]\n})\nexport class NzTreeNodeComponent<T> extends NzNodeBase<T> implements OnDestroy, OnInit {\n  indents: boolean[] = [];\n  disabled = false;\n  selected = false;\n  isLeaf = false;\n\n  private renderer = inject(Renderer2);\n  private cdr = inject(ChangeDetectorRef);\n\n  constructor(\n    protected elementRef: ElementRef<HTMLElement>,\n    protected tree: NzTreeView<T>\n  ) {\n    super(elementRef, tree);\n    this._elementRef.nativeElement.classList.add('ant-tree-treenode');\n  }\n\n  override ngOnInit(): void {\n    this.isLeaf = !this.tree.treeControl?.isExpandable(this.data);\n  }\n\n  disable(): void {\n    this.disabled = true;\n    this.updateDisabledClass();\n  }\n\n  enable(): void {\n    this.disabled = false;\n    this.updateDisabledClass();\n  }\n\n  select(): void {\n    this.selected = true;\n    this.updateSelectedClass();\n  }\n\n  deselect(): void {\n    this.selected = false;\n    this.updateSelectedClass();\n  }\n\n  setIndents(indents: boolean[]): void {\n    this.indents = indents;\n    this.cdr.markForCheck();\n  }\n\n  private updateSelectedClass(): void {\n    if (this.selected) {\n      this.renderer.addClass(this.elementRef.nativeElement, 'ant-tree-treenode-selected');\n    } else {\n      this.renderer.removeClass(this.elementRef.nativeElement, 'ant-tree-treenode-selected');\n    }\n  }\n\n  private updateDisabledClass(): void {\n    if (this.disabled) {\n      this.renderer.addClass(this.elementRef.nativeElement, 'ant-tree-treenode-disabled');\n    } else {\n      this.renderer.removeClass(this.elementRef.nativeElement, 'ant-tree-treenode-disabled');\n    }\n  }\n}\n\n@Directive({\n  selector: '[nzTreeNodeDef]',\n  providers: [\n    {\n      provide: CdkTreeNodeDef,\n      useExisting: forwardRef(() => NzTreeNodeDefDirective)\n    }\n  ]\n})\nexport class NzTreeNodeDefDirective<T> extends CdkTreeNodeDef<T> {\n  @Input('nzTreeNodeDefWhen') override when: (index: number, nodeData: T) => boolean = null!;\n}\n\n@Directive({\n  selector: '[nzTreeVirtualScrollNodeOutlet]'\n})\nexport class NzTreeVirtualScrollNodeOutletDirective<T> implements OnChanges {\n  private _viewRef: EmbeddedViewRef<NzSafeAny> | null = null;\n  @Input() data!: NzTreeVirtualNodeData<T>;\n  @Input() compareBy?: ((value: T) => T | string | number) | null;\n\n  constructor(private _viewContainerRef: ViewContainerRef) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const recreateView = this.shouldRecreateView(changes);\n    if (recreateView) {\n      const viewContainerRef = this._viewContainerRef;\n\n      if (this._viewRef) {\n        viewContainerRef.remove(viewContainerRef.indexOf(this._viewRef));\n      }\n\n      this._viewRef = this.data\n        ? viewContainerRef.createEmbeddedView(this.data.nodeDef.template, this.data.context)\n        : null;\n\n      if (CdkTreeNode.mostRecentTreeNode && this._viewRef) {\n        CdkTreeNode.mostRecentTreeNode.data = this.data.data;\n      }\n    } else if (this._viewRef && this.data.context) {\n      this.updateExistingContext(this.data.context);\n    }\n  }\n\n  private shouldRecreateView(changes: SimpleChanges): boolean {\n    const ctxChange = changes.data;\n    return ctxChange && this.hasContextShapeChanged(ctxChange);\n  }\n\n  private hasContextShapeChanged(ctxChange: SimpleChange): boolean {\n    const prevCtxKeys = Object.keys(ctxChange.previousValue || {});\n    const currCtxKeys = Object.keys(ctxChange.currentValue || {});\n\n    if (prevCtxKeys.length === currCtxKeys.length) {\n      for (const propName of currCtxKeys) {\n        if (prevCtxKeys.indexOf(propName) === -1) {\n          return true;\n        }\n      }\n      return (\n        this.innerCompareBy(ctxChange.previousValue?.data ?? null) !==\n        this.innerCompareBy(ctxChange.currentValue?.data ?? null)\n      );\n    }\n    return true;\n  }\n\n  get innerCompareBy(): (value: T | null) => T | string | number | null {\n    return value => {\n      if (value === null) return value;\n      if (this.compareBy) return this.compareBy(value as T);\n      return value;\n    };\n  }\n\n  private updateExistingContext(ctx: NzSafeAny): void {\n    for (const propName of Object.keys(ctx)) {\n      this._viewRef!.context[propName] = (this.data.context as NzSafeAny)[propName];\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnChanges,\n  OnInit,\n  Output,\n  SimpleChanges,\n  booleanAttribute\n} from '@angular/core';\nimport { filter, takeUntil } from 'rxjs/operators';\n\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\n\nimport { NzTreeNodeComponent } from './node';\n\n@Component({\n  selector: 'nz-tree-node-option',\n  template: ` <span class=\"ant-tree-title\"><ng-content></ng-content></span> `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    class: 'ant-tree-node-content-wrapper',\n    '[class.ant-tree-node-content-wrapper-open]': 'isExpanded',\n    '[class.ant-tree-node-selected]': 'nzSelected'\n  },\n  providers: [NzDestroyService]\n})\nexport class NzTreeNodeOptionComponent<T> implements OnChanges, OnInit {\n  @Input({ transform: booleanAttribute }) nzSelected = false;\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Output() readonly nzClick = new EventEmitter<MouseEvent>();\n\n  constructor(\n    private ngZone: NgZone,\n    private host: ElementRef<HTMLElement>,\n    private destroy$: NzDestroyService,\n    private treeNode: NzTreeNodeComponent<T>\n  ) {}\n\n  get isExpanded(): boolean {\n    return this.treeNode.isExpanded;\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzDisabled, nzSelected } = changes;\n    if (nzDisabled) {\n      if (nzDisabled.currentValue) {\n        this.treeNode.disable();\n      } else {\n        this.treeNode.enable();\n      }\n    }\n\n    if (nzSelected) {\n      if (nzSelected.currentValue) {\n        this.treeNode.select();\n      } else {\n        this.treeNode.deselect();\n      }\n    }\n  }\n\n  ngOnInit(): void {\n    fromEventOutsideAngular<MouseEvent>(this.host.nativeElement, 'click')\n      .pipe(\n        filter(() => !this.nzDisabled && this.nzClick.observers.length > 0),\n        takeUntil(this.destroy$)\n      )\n      .subscribe(event => {\n        this.ngZone.run(() => this.nzClick.emit(event));\n      });\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { CDK_TREE_NODE_OUTLET_NODE, CdkTreeNodeOutlet } from '@angular/cdk/tree';\nimport { Directive, ViewContainerRef, forwardRef, inject } from '@angular/core';\n\n@Directive({\n  selector: '[nzTreeNodeOutlet]',\n  providers: [\n    {\n      provide: CdkTreeNodeOutlet,\n      useExisting: forwardRef(() => NzTreeNodeOutletDirective)\n    }\n  ]\n})\nexport class NzTreeNodeOutletDirective implements CdkTreeNodeOutlet {\n  _node = inject(CDK_TREE_NODE_OUTLET_NODE, { optional: true });\n\n  constructor(public viewContainer: ViewContainerRef) {}\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { CdkTreeNodePadding } from '@angular/cdk/tree';\nimport { Directive, forwardRef, Input, numberAttribute } from '@angular/core';\n\n@Directive({\n  selector: '[nzTreeNodePadding]',\n  providers: [{ provide: CdkTreeNodePadding, useExisting: forwardRef(() => NzTreeNodePaddingDirective) }]\n})\nexport class NzTreeNodePaddingDirective<T> extends CdkTreeNodePadding<T> {\n  override _indent = 24;\n\n  @Input({ alias: 'nzTreeNodePadding', transform: numberAttribute })\n  override get level(): number {\n    return this._level;\n  }\n  override set level(value: number) {\n    this._setLevelInput(value);\n  }\n\n  @Input('nzTreeNodePaddingIndent')\n  override get indent(): number | string {\n    return this._indent;\n  }\n  override set indent(indent: number | string) {\n    this._setIndentInput(indent);\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { CdkTree } from '@angular/cdk/tree';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  Component,\n  forwardRef,\n  ViewChild,\n  ViewEncapsulation\n} from '@angular/core';\n\nimport { treeCollapseMotion } from 'ng-zorro-antd/core/animation';\n\nimport { NzTreeNodeOutletDirective } from './outlet';\nimport { NzTreeView } from './tree';\n\n@Component({\n  selector: 'nz-tree-view',\n  exportAs: 'nzTreeView',\n  template: `\n    <div class=\"ant-tree-list-holder\">\n      <div\n        [@.disabled]=\"!_afterViewInit || !!noAnimation?.nzNoAnimation\"\n        [@treeCollapseMotion]=\"_nodeOutlet.viewContainer.length\"\n        class=\"ant-tree-list-holder-inner\"\n      >\n        <ng-container nzTreeNodeOutlet></ng-container>\n      </div>\n    </div>\n  `,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    { provide: CdkTree, useExisting: forwardRef(() => NzTreeViewComponent) },\n    { provide: NzTreeView, useExisting: forwardRef(() => NzTreeViewComponent) }\n  ],\n  host: {\n    class: 'ant-tree',\n    '[class.ant-tree-block-node]': 'nzDirectoryTree || nzBlockNode',\n    '[class.ant-tree-directory]': 'nzDirectoryTree',\n    '[class.ant-tree-rtl]': `dir === 'rtl'`\n  },\n  animations: [treeCollapseMotion],\n  imports: [NzTreeNodeOutletDirective]\n})\nexport class NzTreeViewComponent<T> extends NzTreeView<T> implements AfterViewInit {\n  @ViewChild(NzTreeNodeOutletDirective, { static: true }) nodeOutlet!: NzTreeNodeOutletDirective;\n  _afterViewInit = false;\n  override ngAfterViewInit(): void {\n    Promise.resolve().then(() => {\n      this._afterViewInit = true;\n      this.changeDetectorRef.markForCheck();\n    });\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport { BaseTreeControl, CdkTree, CdkTreeNodeOutletContext } from '@angular/cdk/tree';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  forwardRef,\n  Input,\n  OnChanges,\n  SimpleChanges,\n  TrackByFunction,\n  ViewChild,\n  ViewEncapsulation\n} from '@angular/core';\n\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\nimport { NzTreeVirtualNodeData, NzTreeVirtualScrollNodeOutletDirective } from './node';\nimport { NzTreeNodeOutletDirective } from './outlet';\nimport { NzTreeView } from './tree';\n\nconst DEFAULT_SIZE = 28;\n\n@Component({\n  selector: 'nz-tree-virtual-scroll-view',\n  exportAs: 'nzTreeVirtualScrollView',\n  template: `\n    <div class=\"ant-tree-list\">\n      <cdk-virtual-scroll-viewport\n        class=\"ant-tree-list-holder\"\n        [itemSize]=\"nzItemSize\"\n        [minBufferPx]=\"nzMinBufferPx\"\n        [maxBufferPx]=\"nzMaxBufferPx\"\n      >\n        <ng-container *cdkVirtualFor=\"let item of nodes; let i = index; trackBy: innerTrackBy\">\n          <ng-template nzTreeVirtualScrollNodeOutlet [data]=\"item\" [compareBy]=\"compareBy\"></ng-template>\n        </ng-container>\n      </cdk-virtual-scroll-viewport>\n    </div>\n    <ng-container nzTreeNodeOutlet></ng-container>\n  `,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    { provide: NzTreeView, useExisting: forwardRef(() => NzTreeVirtualScrollViewComponent) },\n    { provide: CdkTree, useExisting: forwardRef(() => NzTreeVirtualScrollViewComponent) }\n  ],\n  host: {\n    class: 'ant-tree',\n    '[class.ant-tree-block-node]': 'nzDirectoryTree || nzBlockNode',\n    '[class.ant-tree-directory]': 'nzDirectoryTree',\n    '[class.ant-tree-rtl]': `dir === 'rtl'`\n  },\n  imports: [\n    NzTreeVirtualScrollNodeOutletDirective,\n    CdkVirtualForOf,\n    NzTreeNodeOutletDirective,\n    CdkVirtualScrollViewport,\n    CdkFixedSizeVirtualScroll\n  ]\n})\nexport class NzTreeVirtualScrollViewComponent<T> extends NzTreeView<T> implements OnChanges {\n  @ViewChild(NzTreeNodeOutletDirective, { static: true }) readonly nodeOutlet!: NzTreeNodeOutletDirective;\n  @ViewChild(CdkVirtualScrollViewport, { static: true }) readonly virtualScrollViewport!: CdkVirtualScrollViewport;\n\n  @Input() nzItemSize = DEFAULT_SIZE;\n  @Input() nzMinBufferPx = DEFAULT_SIZE * 5;\n  @Input() nzMaxBufferPx = DEFAULT_SIZE * 10;\n  @Input() override trackBy: TrackByFunction<T> = null!;\n  nodes: Array<NzTreeVirtualNodeData<T>> = [];\n  innerTrackBy: TrackByFunction<NzTreeVirtualNodeData<T>> = i => i;\n\n  ngOnChanges({ trackBy }: SimpleChanges): void {\n    if (trackBy) {\n      if (typeof trackBy.currentValue === 'function') {\n        this.innerTrackBy = (index: number, n) => this.trackBy(index, n.data);\n      } else {\n        this.innerTrackBy = i => i;\n      }\n    }\n  }\n\n  get compareBy(): ((value: T) => NzSafeAny) | null {\n    const baseTreeControl = this.treeControl as BaseTreeControl<T, NzSafeAny>;\n    if (baseTreeControl.trackBy) {\n      return baseTreeControl.trackBy;\n    }\n\n    return null;\n  }\n\n  override renderNodeChanges(data: T[] | readonly T[]): void {\n    this.nodes = new Array(...data).map((n, i) => this.createNode(n, i));\n    this._dataSourceChanged.next();\n    this.changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * @note\n   * angular/cdk v18.2.0 breaking changes: https://github.com/angular/components/pull/29062\n   * Temporary workaround: revert to old method of getting level\n   * TODO: refactor tree-view, remove #treeControl and adopt #levelAccessor and #childrenAccessor\n   * */\n  override _getLevel(nodeData: T): number | undefined {\n    if (this.treeControl?.getLevel) {\n      return this.treeControl.getLevel(nodeData);\n    }\n    return;\n  }\n\n  private createNode(nodeData: T, index: number): NzTreeVirtualNodeData<T> {\n    const node = this._getNodeDef(nodeData, index);\n    const context = new CdkTreeNodeOutletContext<T>(nodeData);\n    if (this.treeControl?.getLevel) {\n      context.level = this.treeControl.getLevel(nodeData);\n    } else {\n      context.level = 0;\n    }\n    return {\n      data: nodeData,\n      context,\n      nodeDef: node\n    };\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzTreeNodeCheckboxComponent } from './checkbox';\nimport { NzTreeNodeIndentLineDirective, NzTreeNodeIndentsComponent } from './indent';\nimport { NzTreeNodeComponent, NzTreeNodeDefDirective, NzTreeVirtualScrollNodeOutletDirective } from './node';\nimport { NzTreeNodeOptionComponent } from './option';\nimport { NzTreeNodeOutletDirective } from './outlet';\nimport { NzTreeNodePaddingDirective } from './padding';\nimport {\n  NzTreeNodeNoopToggleDirective,\n  NzTreeNodeToggleActiveIconDirective,\n  NzTreeNodeToggleDirective,\n  NzTreeNodeToggleRotateIconDirective\n} from './toggle';\nimport { NzTreeView } from './tree';\nimport { NzTreeViewComponent } from './tree-view';\nimport { NzTreeVirtualScrollViewComponent } from './tree-virtual-scroll-view';\n\nconst treeWithControlComponents = [\n  NzTreeView,\n  NzTreeNodeOutletDirective,\n  NzTreeViewComponent,\n  NzTreeNodeDefDirective,\n  NzTreeNodeComponent,\n  NzTreeNodeToggleDirective,\n  NzTreeNodePaddingDirective,\n  NzTreeNodeToggleRotateIconDirective,\n  NzTreeNodeToggleActiveIconDirective,\n  NzTreeNodeOptionComponent,\n  NzTreeNodeNoopToggleDirective,\n  NzTreeNodeCheckboxComponent,\n  NzTreeNodeIndentsComponent,\n  NzTreeVirtualScrollViewComponent,\n  NzTreeVirtualScrollNodeOutletDirective,\n  NzTreeNodeIndentLineDirective\n];\n\n@NgModule({\n  imports: [treeWithControlComponents],\n  exports: [treeWithControlComponents]\n})\nexport class NzTreeViewModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { CollectionViewer, DataSource } from '@angular/cdk/collections';\nimport { FlatTreeControl, TreeControl } from '@angular/cdk/tree';\nimport { BehaviorSubject, Observable, merge } from 'rxjs';\nimport { map, take } from 'rxjs/operators';\n\nexport class NzTreeFlattener<T, F, K = F> {\n  constructor(\n    public transformFunction: (node: T, level: number) => F,\n    public getLevel: (node: F) => number,\n    public isExpandable: (node: F) => boolean,\n    public getChildren: (node: T) => Observable<T[]> | T[] | undefined | null\n  ) {}\n\n  private flattenNode(node: T, level: number, resultNodes: F[], parentMap: boolean[]): F[] {\n    const flatNode = this.transformFunction(node, level);\n    resultNodes.push(flatNode);\n\n    if (this.isExpandable(flatNode)) {\n      const childrenNodes = this.getChildren(node);\n      if (childrenNodes) {\n        if (Array.isArray(childrenNodes)) {\n          this.flattenChildren(childrenNodes, level, resultNodes, parentMap);\n        } else {\n          childrenNodes.pipe(take(1)).subscribe(children => {\n            this.flattenChildren(children, level, resultNodes, parentMap);\n          });\n        }\n      }\n    }\n    return resultNodes;\n  }\n\n  private flattenChildren(children: T[], level: number, resultNodes: F[], parentMap: boolean[]): void {\n    children.forEach((child, index) => {\n      const childParentMap: boolean[] = parentMap.slice();\n      childParentMap.push(index !== children.length - 1);\n      this.flattenNode(child, level + 1, resultNodes, childParentMap);\n    });\n  }\n\n  /**\n   * Flatten a list of node type T to flattened version of node F.\n   * Please note that type T may be nested, and the length of `structuredData` may be different\n   * from that of returned list `F[]`.\n   */\n  flattenNodes(structuredData: T[]): F[] {\n    const resultNodes: F[] = [];\n    structuredData.forEach(node => this.flattenNode(node, 0, resultNodes, []));\n    return resultNodes;\n  }\n\n  /**\n   * Expand flattened node with current expansion status.\n   * The returned list may have different length.\n   */\n  expandFlattenedNodes(nodes: F[], treeControl: TreeControl<F, K>): F[] {\n    const results: F[] = [];\n    const currentExpand: boolean[] = [];\n    currentExpand[0] = true;\n\n    nodes.forEach(node => {\n      let expand = true;\n      for (let i = 0; i <= this.getLevel(node); i++) {\n        expand = expand && currentExpand[i];\n      }\n      if (expand) {\n        results.push(node);\n      }\n      if (this.isExpandable(node)) {\n        currentExpand[this.getLevel(node) + 1] = treeControl.isExpanded(node);\n      }\n    });\n    return results;\n  }\n}\n\nexport class NzTreeFlatDataSource<T, F, K = F> extends DataSource<F> {\n  _flattenedData = new BehaviorSubject<F[]>([]);\n\n  _expandedData = new BehaviorSubject<F[]>([]);\n\n  _data: BehaviorSubject<T[]>;\n\n  constructor(\n    private _treeControl: FlatTreeControl<F, K>,\n    private _treeFlattener: NzTreeFlattener<T, F, K>,\n    initialData: T[] = []\n  ) {\n    super();\n    this._data = new BehaviorSubject<T[]>(initialData);\n    this.flatNodes();\n  }\n\n  setData(value: T[]): void {\n    this._data.next(value);\n    this.flatNodes();\n  }\n\n  getData(): T[] {\n    return this._data.getValue();\n  }\n\n  connect(collectionViewer: CollectionViewer): Observable<F[]> {\n    const changes = [\n      collectionViewer.viewChange,\n      this._treeControl.expansionModel.changed.asObservable(),\n      this._flattenedData.asObservable()\n    ];\n    return merge(...changes).pipe(\n      map(() => {\n        this._expandedData.next(this._treeFlattener.expandFlattenedNodes(this._flattenedData.value, this._treeControl));\n        return this._expandedData.value;\n      })\n    );\n  }\n\n  disconnect(): void {\n    // no op\n  }\n\n  private flatNodes(): void {\n    this._flattenedData.next(this._treeFlattener.flattenNodes(this.getData()));\n    this._treeControl.dataNodes = this._flattenedData.value;\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './tree-view.module';\nexport * from './checkbox';\nexport * from './utils';\nexport * from './data-source';\nexport * from './indent';\nexport * from './node';\nexport * from './option';\nexport * from './outlet';\nexport * from './padding';\nexport * from './toggle';\nexport * from './tree-view';\nexport * from './tree';\nexport * from './tree-virtual-scroll-view';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1", "i1.NzNodeBase", "i2.NzTreeView", "i1.NzTreeView", "i2.NzTreeNodeComponent"], "mappings": ";;;;;;;;;;;;;;AAAA;;;AAGG;MAgCU,2BAA2B,CAAA;AAO5B,IAAA,MAAA;AACA,IAAA,GAAA;AACA,IAAA,IAAA;AACA,IAAA,QAAA;AAT8B,IAAA,SAAS;AACT,IAAA,eAAe;AACf,IAAA,UAAU;AAC/B,IAAA,OAAO,GAAG,IAAI,YAAY,EAAc;AAE3D,IAAA,WAAA,CACU,MAAc,EACd,GAAsB,EACtB,IAA6B,EAC7B,QAA0B,EAAA;QAH1B,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAQ,CAAA,QAAA,GAAR,QAAQ;;IAGlB,QAAQ,GAAA;QACN,uBAAuB,CAAa,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO;AACjE,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7B,aAAA,SAAS,CAAC,CAAC,KAAiB,KAAI;AAC/B,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE;AACrD,gBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAK;AACnB,oBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;AACxB,oBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,iBAAC,CAAC;;AAEN,SAAC,CAAC;;uGAvBK,2BAA2B,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA3B,2BAA2B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sCAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAClB,gBAAgB,CAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAChB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAChB,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iCAAA,EAAA,WAAA,EAAA,uCAAA,EAAA,iBAAA,EAAA,kCAAA,EAAA,YAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,SAAA,EALzB,CAAC,gBAAgB,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EATnB,CAAiD,+CAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAWhD,2BAA2B,EAAA,UAAA,EAAA,CAAA;kBAbvC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sCAAsC;AAChD,oBAAA,QAAQ,EAAE,CAAiD,+CAAA,CAAA;oBAC3D,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,mBAAmB;AAC1B,wBAAA,mCAAmC,EAAE,CAAW,SAAA,CAAA;AAChD,wBAAA,yCAAyC,EAAE,CAAiB,eAAA,CAAA;AAC5D,wBAAA,oCAAoC,EAAE,CAAY,UAAA;AACnD,qBAAA;oBACD,SAAS,EAAE,CAAC,gBAAgB;AAC7B,iBAAA;mKAEyC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACnB,OAAO,EAAA,CAAA;sBAAzB;;;ACvCH;;;AAGG;AAEU,MAAA,SAAS,GAAG,CAAI,KAAU,EAAE,IAAO,EAAE,QAAiC,KAAc;IAC/F,IAAI,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAC/B,IAAA,IAAI,KAAK,GAAG,CAAC,EAAE;AACb,QAAA,OAAO,IAAI;;AAEb,IAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC;IAC5B,KAAK,KAAK,EAAE,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;QACjC,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACvC,QAAA,IAAI,QAAQ,GAAG,CAAC,KAAK,KAAK,EAAE;AAC1B,YAAA,OAAO,KAAK,CAAC,KAAK,CAAC;;AAErB,QAAA,IAAI,QAAQ,GAAG,CAAC,GAAG,KAAK,EAAE;AACxB,YAAA,OAAO,IAAI;;;AAGf,IAAA,OAAO,IAAI;AACb;AAEO,MAAM,cAAc,GAAG,CAC5B,KAAU,EACV,IAAO,EACP,QAAiC,EACjC,MAAe,KACH;AACZ,IAAA,IAAI,KAAK,GAAG,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AACxE,IAAA,IAAI,KAAK,GAAG,CAAC,EAAE;AACb,QAAA,OAAO,IAAI;;AAEb,IAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC;AAE5B,IAAA,KAAK,KAAK,EAAE,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACxC,QAAA,IAAI,SAAS,GAAG,KAAK,EAAE;AACrB,YAAA,OAAO,IAAI;;AAEb,QAAA,IAAI,SAAS,KAAK,KAAK,EAAE;AACvB,YAAA,OAAO,KAAK,CAAC,KAAK,CAAC;;;AAGvB,IAAA,OAAO,IAAI;AACb;;AC7CA;;;AAGG;AAIG,MAAgB,UAAc,SAAQ,WAAc,CAAA;AAGzD;;ACmBD;AACM,MAAO,UAAc,SAAQ,OAAU,CAAA;AAmB/B,IAAA,OAAA;AACA,IAAA,iBAAA;AACF,IAAA,cAAA;AApBF,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAW;IACzC,GAAG,GAAc,KAAK;AACtB,IAAA,kBAAkB,GAAG,IAAI,OAAO,EAAQ;;IAEP,WAAW,GAA+B,SAAS;AACpF,IAAA,IACa,UAAU,GAAA;QACrB,OAAO,KAAK,CAAC,UAAU;;IAEzB,IAAa,UAAU,CAAC,UAAiD,EAAA;AACvE,QAAA,KAAK,CAAC,UAAU,GAAG,UAAU;;IAES,eAAe,GAAG,KAAK;IACvB,WAAW,GAAG,KAAK;AAE3D,IAAA,WAAW,GAAG,MAAM,CAAC,sBAAsB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAE5E,IAAA,WAAA,CACY,OAAwB,EACxB,iBAAoC,EACtC,cAA8B,EAAA;AAEtC,QAAA,KAAK,CAAC,OAAO,EAAE,iBAAiB,EAAE,cAAc,CAAC;QAJvC,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;QACnB,IAAc,CAAA,cAAA,GAAd,cAAc;;IAKf,QAAQ,GAAA;QACf,KAAK,CAAC,QAAQ,EAAE;QAEhB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;QACpC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,YAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,YAAA,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE;AACxC,SAAC,CAAC;;IAGK,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE;AACnB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;AAGjB,IAAA,iBAAiB,CACxB,IAAwB,EACxB,UAA8B,EAC9B,aAAgC,EAChC,UAAc,EAAA;QAEd,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,CAAC;AACpE,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE;;uGAjDrB,UAAU,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EAaD,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,CAAA,eAAA,EAAA,aAAA,CAAA,EAAA,UAAA,EAAA,CAAA,cAAA,EAAA,YAAA,CAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,gBAAgB,CAChB,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,oDAjB1B,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA;;2FAGD,UAAU,EAAA,UAAA,EAAA,CAAA;kBAJtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE;AACX,iBAAA;mJAOkC,WAAW,EAAA,CAAA;sBAA3C,KAAK;uBAAC,eAAe;gBAET,UAAU,EAAA,CAAA;sBADtB,KAAK;uBAAC,cAAc;gBAOmB,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;;;AC5CxC;;;AAGG;AAUH;;AAEG;AACH,SAAS,oBAAoB,CAAC,GAAc,EAAA;IAC1C,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;AAC3C;AAEA,MAAM,uBAAuB,GAAG,OAAO,qBAAqB,KAAK,WAAW,GAAG,uBAAuB,GAAG,aAAa;MAczG,0BAA0B,CAAA;IAC5B,OAAO,GAAc,EAAE;uGADrB,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAA1B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,0BAA0B,EAV3B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;AAIT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAMU,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBAZtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE;;;;AAIT,EAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE;AACR;AACF,iBAAA;8BAEU,OAAO,EAAA,CAAA;sBAAf;;MAUU,6BAA6B,CAAA;AAS9B,IAAA,QAAA;AACA,IAAA,IAAA;AACA,IAAA,GAAA;IAVV,MAAM,GAAsB,OAAO;IACnC,MAAM,GAAG,KAAK;IACN,UAAU,GAAa,IAAI;IAC3B,WAAW,GAAa,IAAI;IAC5B,cAAc,GAAW,EAAE;AAC3B,IAAA,kBAAkB;AAE1B,IAAA,WAAA,CACU,QAAuB,EACvB,IAAmB,EACnB,GAAsB,EAAA;QAFtB,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAG,CAAA,GAAA,GAAH,GAAG;QAEX,IAAI,CAAC,YAAY,EAAE;QACnB,IAAI,CAAC,SAAS,EAAE;AAEhB;;;AAGG;AACH,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,kBAAkB;AAChF,aAAA,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,uBAAuB,CAAC;aAC1C,SAAS,CAAC,MAAK;YACd,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,SAAC,CAAC;;IAGE,UAAU,GAAA;QAChB,MAAM,OAAO,GAAc,EAAE;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAA,OAAO,OAAO;;QAGhB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS;QAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ;AAC/C,QAAA,IAAI,MAAM,GAAG,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC;QAC3D,OAAO,MAAM,EAAE;YACb,MAAM,iBAAiB,GAAG,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC;YACjE,IAAI,iBAAiB,EAAE;AACrB,gBAAA,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;;iBAChB;AACL,gBAAA,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;;YAExB,MAAM,GAAG,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC;;AAE7C,QAAA,OAAO,OAAO;;IAGR,YAAY,GAAA;AAClB,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACtB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE;AACjC,YAAA,MAAM,UAAU,GAAG,oBAAoB,CAAC,OAAO,CAAC;AAChD,YAAA,IAAI,UAAU,KAAK,IAAI,CAAC,cAAc,EAAE;gBACtC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;AAC3C,gBAAA,IAAI,CAAC,cAAc,GAAG,UAAU;;;;AAKtC;;;AAGG;IACK,aAAa,GAAA;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,IAAI,EAAE;AACpD,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC/C,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI;QACxC,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI;AACzC,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO,EAAE;AAChE,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;AAEvB,QAAA,IAAI,CAAC,UAAU,GAAG,OAAO;AACzB,QAAA,IAAI,CAAC,WAAW,GAAG,QAAQ;;AAGrB,IAAA,SAAS,CAAC,KAAc,EAAA;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,IAAI,EAAE;QACpD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM;AAClC,QAAA,IAAI,CAAC,MAAM;AACT,YAAA,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC;;IAGhH,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;AACtB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;AACvB,QAAA,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE;;uGAvF5B,6BAA6B,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA7B,6BAA6B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oCAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,mCAAA,EAAA,kBAAA,EAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAA7B,6BAA6B,EAAA,UAAA,EAAA,CAAA;kBAPzC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oCAAoC;AAC9C,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,oBAAoB;AAC3B,wBAAA,qCAAqC,EAAE;AACxC;AACF,iBAAA;;;AC5CD;;;AAGG;MAWU,6BAA6B,CAAA;uGAA7B,6BAA6B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA7B,6BAA6B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mEAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,0CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAA7B,6BAA6B,EAAA,UAAA,EAAA,CAAA;kBANzC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mEAAmE;AAC7E,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE;AACR;AACF,iBAAA;;AAYK,MAAO,yBAA6B,SAAQ,iBAAoB,CAAA;IACiB,SAAS,GAAG,KAAK;AAEtG,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU;;uGAJvB,yBAAyB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAzB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,yBAAyB,uKACoB,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,8BAAA,EAAA,YAAA,EAAA,+BAAA,EAAA,aAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,SAAA,EAR7D,CAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,yBAAyB,CAAC,EAAE,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAO1F,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBATrC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qEAAqE;AAC/E,oBAAA,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,CAAC,MAA+B,yBAAA,CAAC,EAAE,CAAC;AACrG,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,mBAAmB;AAC1B,wBAAA,gCAAgC,EAAE,YAAY;AAC9C,wBAAA,iCAAiC,EAAE;AACpC;AACF,iBAAA;8BAEsF,SAAS,EAAA,CAAA;sBAA7F,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAE,KAAK,EAAE,2BAA2B,EAAE,SAAS,EAAE,gBAAgB,EAAE;;MAa/D,mCAAmC,CAAA;uGAAnC,mCAAmC,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAnC,mCAAmC,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,8BAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,wBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAnC,mCAAmC,EAAA,UAAA,EAAA,CAAA;kBAN/C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,8BAA8B;AACxC,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE;AACR;AACF,iBAAA;;MASY,mCAAmC,CAAA;uGAAnC,mCAAmC,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAnC,mCAAmC,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,8BAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,gCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAnC,mCAAmC,EAAA,UAAA,EAAA,CAAA;kBAN/C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,8BAA8B;AACxC,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE;AACR;AACF,iBAAA;;;AC9CD;;;AAGG;AA+DG,MAAO,mBAAuB,SAAQ,UAAa,CAAA;AAU3C,IAAA,UAAA;AACA,IAAA,IAAA;IAVZ,OAAO,GAAc,EAAE;IACvB,QAAQ,GAAG,KAAK;IAChB,QAAQ,GAAG,KAAK;IAChB,MAAM,GAAG,KAAK;AAEN,IAAA,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;AAC5B,IAAA,GAAG,GAAG,MAAM,CAAC,iBAAiB,CAAC;IAEvC,WACY,CAAA,UAAmC,EACnC,IAAmB,EAAA;AAE7B,QAAA,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC;QAHb,IAAU,CAAA,UAAA,GAAV,UAAU;QACV,IAAI,CAAA,IAAA,GAAJ,IAAI;QAGd,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC;;IAG1D,QAAQ,GAAA;AACf,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;IAG/D,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;QACpB,IAAI,CAAC,mBAAmB,EAAE;;IAG5B,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;QACrB,IAAI,CAAC,mBAAmB,EAAE;;IAG5B,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;QACpB,IAAI,CAAC,mBAAmB,EAAE;;IAG5B,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;QACrB,IAAI,CAAC,mBAAmB,EAAE;;AAG5B,IAAA,UAAU,CAAC,OAAkB,EAAA;AAC3B,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;AACtB,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;IAGjB,mBAAmB,GAAA;AACzB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,4BAA4B,CAAC;;aAC9E;AACL,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,4BAA4B,CAAC;;;IAIlF,mBAAmB,GAAA;AACzB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,4BAA4B,CAAC;;aAC9E;AACL,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,4BAA4B,CAAC;;;uGA1D/E,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,EAxBnB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,uCAAA,EAAA,YAAA,EAAA,wCAAA,EAAA,aAAA,EAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,mBAAmB,CAAC,EAAE;AAC5E,YAAA,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,mBAAmB,CAAC;SAC1E,EACS,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;GAaT,EAKS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,0BAA0B,sFAAE,6BAA6B,EAAA,QAAA,EAAA,mEAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAExD,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBA5B/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,6BAA6B;AACvC,oBAAA,QAAQ,EAAE,YAAY;oBACtB,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,MAAyB,mBAAA,CAAC,EAAE;AAC5E,wBAAA,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,MAAyB,mBAAA,CAAC;AAC1E,qBAAA;AACD,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;AAaT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,yCAAyC,EAAE,YAAY;AACvD,wBAAA,0CAA0C,EAAE;AAC7C,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,0BAA0B,EAAE,6BAA6B;AACpE,iBAAA;;AAyEK,MAAO,sBAA0B,SAAQ,cAAiB,CAAA;IACzB,IAAI,GAA4C,IAAK;uGAD/E,sBAAsB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAtB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,sBAAsB,EAPtB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,mBAAA,EAAA,MAAA,CAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,cAAc;AACvB,gBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,sBAAsB;AACrD;AACF,SAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEU,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBATlC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,cAAc;AACvB,4BAAA,WAAW,EAAE,UAAU,CAAC,4BAA4B;AACrD;AACF;AACF,iBAAA;8BAEsC,IAAI,EAAA,CAAA;sBAAxC,KAAK;uBAAC,mBAAmB;;MAMf,sCAAsC,CAAA;AAK7B,IAAA,iBAAA;IAJZ,QAAQ,GAAsC,IAAI;AACjD,IAAA,IAAI;AACJ,IAAA,SAAS;AAElB,IAAA,WAAA,CAAoB,iBAAmC,EAAA;QAAnC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;;AAErC,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;QACrD,IAAI,YAAY,EAAE;AAChB,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB;AAE/C,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,gBAAA,gBAAgB,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;AAGlE,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACnB,kBAAE,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO;kBACjF,IAAI;YAER,IAAI,WAAW,CAAC,kBAAkB,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACnD,WAAW,CAAC,kBAAkB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;;;aAEjD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YAC7C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;;;AAIzC,IAAA,kBAAkB,CAAC,OAAsB,EAAA;AAC/C,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI;QAC9B,OAAO,SAAS,IAAI,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC;;AAGpD,IAAA,sBAAsB,CAAC,SAAuB,EAAA;AACpD,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,IAAI,EAAE,CAAC;AAC9D,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,IAAI,EAAE,CAAC;QAE7D,IAAI,WAAW,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE;AAC7C,YAAA,KAAK,MAAM,QAAQ,IAAI,WAAW,EAAE;gBAClC,IAAI,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;AACxC,oBAAA,OAAO,IAAI;;;AAGf,YAAA,QACE,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,IAAI,IAAI,CAAC;AAC1D,gBAAA,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,IAAI,IAAI,CAAC;;AAG7D,QAAA,OAAO,IAAI;;AAGb,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,KAAK,IAAG;YACb,IAAI,KAAK,KAAK,IAAI;AAAE,gBAAA,OAAO,KAAK;YAChC,IAAI,IAAI,CAAC,SAAS;AAAE,gBAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAU,CAAC;AACrD,YAAA,OAAO,KAAK;AACd,SAAC;;AAGK,IAAA,qBAAqB,CAAC,GAAc,EAAA;QAC1C,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AACvC,YAAA,IAAI,CAAC,QAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAI,IAAI,CAAC,IAAI,CAAC,OAAqB,CAAC,QAAQ,CAAC;;;uGA7DtE,sCAAsC,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAtC,sCAAsC,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iCAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAtC,sCAAsC,EAAA,UAAA,EAAA,CAAA;kBAHlD,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE;AACX,iBAAA;qFAGU,IAAI,EAAA,CAAA;sBAAZ;gBACQ,SAAS,EAAA,CAAA;sBAAjB;;;ACpJH;;;AAGG;MAiCU,yBAAyB,CAAA;AAM1B,IAAA,MAAA;AACA,IAAA,IAAA;AACA,IAAA,QAAA;AACA,IAAA,QAAA;IAR8B,UAAU,GAAG,KAAK;IAClB,UAAU,GAAG,KAAK;AACvC,IAAA,OAAO,GAAG,IAAI,YAAY,EAAc;AAE3D,IAAA,WAAA,CACU,MAAc,EACd,IAA6B,EAC7B,QAA0B,EAC1B,QAAgC,EAAA;QAHhC,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAQ,CAAA,QAAA,GAAR,QAAQ;;AAGlB,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU;;AAGjC,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,OAAO;QAC1C,IAAI,UAAU,EAAE;AACd,YAAA,IAAI,UAAU,CAAC,YAAY,EAAE;AAC3B,gBAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;;iBAClB;AACL,gBAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;;;QAI1B,IAAI,UAAU,EAAE;AACd,YAAA,IAAI,UAAU,CAAC,YAAY,EAAE;AAC3B,gBAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;;iBACjB;AACL,gBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;;;IAK9B,QAAQ,GAAA;QACN,uBAAuB,CAAa,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO;AACjE,aAAA,IAAI,CACH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,EACnE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAEzB,SAAS,CAAC,KAAK,IAAG;AACjB,YAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjD,SAAC,CAAC;;uGA3CK,yBAAyB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,mBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAzB,yBAAyB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAChB,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,oOAJzB,CAAC,gBAAgB,CAAC,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAPnB,CAAiE,+DAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAShE,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBAXrC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,QAAQ,EAAE,CAAiE,+DAAA,CAAA;oBAC3E,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,+BAA+B;AACtC,wBAAA,4CAA4C,EAAE,YAAY;AAC1D,wBAAA,gCAAgC,EAAE;AACnC,qBAAA;oBACD,SAAS,EAAE,CAAC,gBAAgB;AAC7B,iBAAA;kKAEyC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACnB,OAAO,EAAA,CAAA;sBAAzB;;;ACvCH;;;AAGG;MAcU,yBAAyB,CAAA;AAGjB,IAAA,aAAA;IAFnB,KAAK,GAAG,MAAM,CAAC,yBAAyB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAE7D,IAAA,WAAA,CAAmB,aAA+B,EAAA;QAA/B,IAAa,CAAA,aAAA,GAAb,aAAa;;uGAHrB,yBAAyB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAzB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,yBAAyB,EAPzB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,yBAAyB;AACxD;AACF,SAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEU,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBATrC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,+BAA+B;AACxD;AACF;AACF,iBAAA;;;AChBD;;;AAGG;AASG,MAAO,0BAA8B,SAAQ,kBAAqB,CAAA;IAC7D,OAAO,GAAG,EAAE;AAErB,IAAA,IACa,KAAK,GAAA;QAChB,OAAO,IAAI,CAAC,MAAM;;IAEpB,IAAa,KAAK,CAAC,KAAa,EAAA;AAC9B,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;;AAG5B,IAAA,IACa,MAAM,GAAA;QACjB,OAAO,IAAI,CAAC,OAAO;;IAErB,IAAa,MAAM,CAAC,MAAuB,EAAA;AACzC,QAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;;uGAhBnB,0BAA0B,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAA1B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,0BAA0B,uGAGW,eAAe,CAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,QAAA,CAAA,EAAA,EAAA,SAAA,EALpD,CAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,0BAA0B,CAAC,EAAE,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAE5F,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBAJtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,UAAU,CAAC,MAAgC,0BAAA,CAAC,EAAE;AACvG,iBAAA;8BAKc,KAAK,EAAA,CAAA;sBADjB,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAE,KAAK,EAAE,mBAAmB,EAAE,SAAS,EAAE,eAAe,EAAE;gBASpD,MAAM,EAAA,CAAA;sBADlB,KAAK;uBAAC,yBAAyB;;;ACvBlC;;;AAGG;AA8CG,MAAO,mBAAuB,SAAQ,UAAa,CAAA;AACC,IAAA,UAAU;IAClE,cAAc,GAAG,KAAK;IACb,eAAe,GAAA;AACtB,QAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE;AACvC,SAAC,CAAC;;uGAPO,mBAAmB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,EAbnB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,2BAAA,EAAA,gCAAA,EAAA,0BAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,EAAA,cAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,mBAAmB,CAAC,EAAE;AACxE,YAAA,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,mBAAmB,CAAC;AAC1E,SAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAWU,yBAAyB,EA3B1B,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;AAUT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAcS,yBAAyB,EAAA,QAAA,EAAA,oBAAA,EAAA,CAAA,EAAA,UAAA,EADvB,CAAC,kBAAkB,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAGrB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBA7B/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,QAAQ,EAAE;;;;;;;;;;AAUT,EAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,MAAyB,mBAAA,CAAC,EAAE;AACxE,wBAAA,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,MAAyB,mBAAA,CAAC;AAC1E,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,UAAU;AACjB,wBAAA,6BAA6B,EAAE,gCAAgC;AAC/D,wBAAA,4BAA4B,EAAE,iBAAiB;AAC/C,wBAAA,sBAAsB,EAAE,CAAe,aAAA;AACxC,qBAAA;oBACD,UAAU,EAAE,CAAC,kBAAkB,CAAC;oBAChC,OAAO,EAAE,CAAC,yBAAyB;AACpC,iBAAA;8BAEyD,UAAU,EAAA,CAAA;sBAAjE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,yBAAyB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;;;AClDxD;;;AAGG;AAsBH,MAAM,YAAY,GAAG,EAAE;AAwCjB,MAAO,gCAAoC,SAAQ,UAAa,CAAA;AACH,IAAA,UAAU;AACX,IAAA,qBAAqB;IAE5E,UAAU,GAAG,YAAY;AACzB,IAAA,aAAa,GAAG,YAAY,GAAG,CAAC;AAChC,IAAA,aAAa,GAAG,YAAY,GAAG,EAAE;IACxB,OAAO,GAAuB,IAAK;IACrD,KAAK,GAAoC,EAAE;AAC3C,IAAA,YAAY,GAA8C,CAAC,IAAI,CAAC;IAEhE,WAAW,CAAC,EAAE,OAAO,EAAiB,EAAA;QACpC,IAAI,OAAO,EAAE;AACX,YAAA,IAAI,OAAO,OAAO,CAAC,YAAY,KAAK,UAAU,EAAE;gBAC9C,IAAI,CAAC,YAAY,GAAG,CAAC,KAAa,EAAE,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC;;iBAChE;gBACL,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC;;;;AAKhC,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,WAA4C;AACzE,QAAA,IAAI,eAAe,CAAC,OAAO,EAAE;YAC3B,OAAO,eAAe,CAAC,OAAO;;AAGhC,QAAA,OAAO,IAAI;;AAGJ,IAAA,iBAAiB,CAAC,IAAwB,EAAA;AACjD,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpE,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE;AAC9B,QAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE;;AAGvC;;;;;AAKK;AACI,IAAA,SAAS,CAAC,QAAW,EAAA;AAC5B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE;YAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC;;QAE5C;;IAGM,UAAU,CAAC,QAAW,EAAE,KAAa,EAAA;QAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC;AAC9C,QAAA,MAAM,OAAO,GAAG,IAAI,wBAAwB,CAAI,QAAQ,CAAC;AACzD,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE;YAC9B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC;;aAC9C;AACL,YAAA,OAAO,CAAC,KAAK,GAAG,CAAC;;QAEnB,OAAO;AACL,YAAA,IAAI,EAAE,QAAQ;YACd,OAAO;AACP,YAAA,OAAO,EAAE;SACV;;uGA7DQ,gCAAgC,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gCAAgC,EAlBhC,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,aAAA,EAAA,eAAA,EAAA,aAAA,EAAA,eAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,2BAAA,EAAA,gCAAA,EAAA,0BAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,EAAA,cAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,gCAAgC,CAAC,EAAE;AACxF,YAAA,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,gCAAgC,CAAC;SACpF,EAgBU,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,yBAAyB,EACzB,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,wBAAwB,EArCzB,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,yBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;GAcT,EAcC,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,sCAAsC,2GACtC,eAAe,EAAA,QAAA,EAAA,kCAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,sBAAA,EAAA,uBAAA,EAAA,gCAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACf,yBAAyB,EACzB,QAAA,EAAA,oBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,wBAAwB,iJACxB,yBAAyB,EAAA,QAAA,EAAA,uCAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,aAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAGhB,gCAAgC,EAAA,UAAA,EAAA,CAAA;kBAtC5C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,6BAA6B;AACvC,oBAAA,QAAQ,EAAE,yBAAyB;AACnC,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;AAcT,EAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,MAAsC,gCAAA,CAAC,EAAE;AACxF,wBAAA,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,MAAsC,gCAAA,CAAC;AACpF,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,UAAU;AACjB,wBAAA,6BAA6B,EAAE,gCAAgC;AAC/D,wBAAA,4BAA4B,EAAE,iBAAiB;AAC/C,wBAAA,sBAAsB,EAAE,CAAe,aAAA;AACxC,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,sCAAsC;wBACtC,eAAe;wBACf,yBAAyB;wBACzB,wBAAwB;wBACxB;AACD;AACF,iBAAA;8BAEkE,UAAU,EAAA,CAAA;sBAA1E,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,yBAAyB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACU,qBAAqB,EAAA,CAAA;sBAApF,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,wBAAwB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBAE5C,UAAU,EAAA,CAAA;sBAAlB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACiB,OAAO,EAAA,CAAA;sBAAxB;;;ACxEH;;;AAGG;AAoBH,MAAM,yBAAyB,GAAG;IAChC,UAAU;IACV,yBAAyB;IACzB,mBAAmB;IACnB,sBAAsB;IACtB,mBAAmB;IACnB,yBAAyB;IACzB,0BAA0B;IAC1B,mCAAmC;IACnC,mCAAmC;IACnC,yBAAyB;IACzB,6BAA6B;IAC7B,2BAA2B;IAC3B,0BAA0B;IAC1B,gCAAgC;IAChC,sCAAsC;IACtC;CACD;MAMY,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAtB3B,UAAU;YACV,yBAAyB;YACzB,mBAAmB;YACnB,sBAAsB;YACtB,mBAAmB;YACnB,yBAAyB;YACzB,0BAA0B;YAC1B,mCAAmC;YACnC,mCAAmC;YACnC,yBAAyB;YACzB,6BAA6B;YAC7B,2BAA2B;YAC3B,0BAA0B;YAC1B,gCAAgC;YAChC,sCAAsC;AACtC,YAAA,6BAA6B,aAf7B,UAAU;YACV,yBAAyB;YACzB,mBAAmB;YACnB,sBAAsB;YACtB,mBAAmB;YACnB,yBAAyB;YACzB,0BAA0B;YAC1B,mCAAmC;YACnC,mCAAmC;YACnC,yBAAyB;YACzB,6BAA6B;YAC7B,2BAA2B;YAC3B,0BAA0B;YAC1B,gCAAgC;YAChC,sCAAsC;YACtC,6BAA6B,CAAA,EAAA,CAAA;AAOlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAT3B,gCAAgC,CAAA,EAAA,CAAA;;2FASrB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,yBAAyB,CAAC;oBACpC,OAAO,EAAE,CAAC,yBAAyB;AACpC,iBAAA;;;AC7CD;;;AAGG;MAOU,eAAe,CAAA;AAEjB,IAAA,iBAAA;AACA,IAAA,QAAA;AACA,IAAA,YAAA;AACA,IAAA,WAAA;AAJT,IAAA,WAAA,CACS,iBAAgD,EAChD,QAA6B,EAC7B,YAAkC,EAClC,WAAkE,EAAA;QAHlE,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;QACjB,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAY,CAAA,YAAA,GAAZ,YAAY;QACZ,IAAW,CAAA,WAAA,GAAX,WAAW;;AAGZ,IAAA,WAAW,CAAC,IAAO,EAAE,KAAa,EAAE,WAAgB,EAAE,SAAoB,EAAA;QAChF,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC;AACpD,QAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;AAE1B,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;YAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YAC5C,IAAI,aAAa,EAAE;AACjB,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;oBAChC,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC;;qBAC7D;AACL,oBAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,IAAG;wBAC/C,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC;AAC/D,qBAAC,CAAC;;;;AAIR,QAAA,OAAO,WAAW;;AAGZ,IAAA,eAAe,CAAC,QAAa,EAAE,KAAa,EAAE,WAAgB,EAAE,SAAoB,EAAA;QAC1F,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;AAChC,YAAA,MAAM,cAAc,GAAc,SAAS,CAAC,KAAK,EAAE;YACnD,cAAc,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;AAClD,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,WAAW,EAAE,cAAc,CAAC;AACjE,SAAC,CAAC;;AAGJ;;;;AAIG;AACH,IAAA,YAAY,CAAC,cAAmB,EAAA;QAC9B,MAAM,WAAW,GAAQ,EAAE;QAC3B,cAAc,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC;AAC1E,QAAA,OAAO,WAAW;;AAGpB;;;AAGG;IACH,oBAAoB,CAAC,KAAU,EAAE,WAA8B,EAAA;QAC7D,MAAM,OAAO,GAAQ,EAAE;QACvB,MAAM,aAAa,GAAc,EAAE;AACnC,QAAA,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI;AAEvB,QAAA,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;YACnB,IAAI,MAAM,GAAG,IAAI;AACjB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7C,gBAAA,MAAM,GAAG,MAAM,IAAI,aAAa,CAAC,CAAC,CAAC;;YAErC,IAAI,MAAM,EAAE;AACV,gBAAA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;;AAEpB,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;AAC3B,gBAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC;;AAEzE,SAAC,CAAC;AACF,QAAA,OAAO,OAAO;;AAEjB;AAEK,MAAO,oBAAkC,SAAQ,UAAa,CAAA;AAQxD,IAAA,YAAA;AACA,IAAA,cAAA;AARV,IAAA,cAAc,GAAG,IAAI,eAAe,CAAM,EAAE,CAAC;AAE7C,IAAA,aAAa,GAAG,IAAI,eAAe,CAAM,EAAE,CAAC;AAE5C,IAAA,KAAK;AAEL,IAAA,WAAA,CACU,YAAmC,EACnC,cAAwC,EAChD,cAAmB,EAAE,EAAA;AAErB,QAAA,KAAK,EAAE;QAJC,IAAY,CAAA,YAAA,GAAZ,YAAY;QACZ,IAAc,CAAA,cAAA,GAAd,cAAc;QAItB,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,CAAM,WAAW,CAAC;QAClD,IAAI,CAAC,SAAS,EAAE;;AAGlB,IAAA,OAAO,CAAC,KAAU,EAAA;AAChB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QACtB,IAAI,CAAC,SAAS,EAAE;;IAGlB,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;;AAG9B,IAAA,OAAO,CAAC,gBAAkC,EAAA;AACxC,QAAA,MAAM,OAAO,GAAG;AACd,YAAA,gBAAgB,CAAC,UAAU;YAC3B,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,OAAO,CAAC,YAAY,EAAE;AACvD,YAAA,IAAI,CAAC,cAAc,CAAC,YAAY;SACjC;QACD,OAAO,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAC3B,GAAG,CAAC,MAAK;YACP,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAC/G,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK;SAChC,CAAC,CACH;;IAGH,UAAU,GAAA;;;IAIF,SAAS,GAAA;AACf,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;;AAE1D;;ACjID;;;AAGG;;ACHH;;AAEG;;;;"}