/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */
import { Direction, Directionality } from '@angular/cdk/bidi';
import { AfterViewInit, ChangeDetectorRef, EventEmitter, OnChanges, OnDestroy, OnInit, TemplateRef } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { BooleanInput } from 'ng-zorro-antd/core/types';
import { NzI18nService, NzUploadI18nInterface } from 'ng-zorro-antd/i18n';
import { NzIconRenderTemplate, NzShowUploadList, NzUploadChangeParam, NzUploadFile, NzUploadListType, NzUploadTransformFileType, NzUploadType, NzUploadXHRArgs, UploadFilter, ZipButtonOptions } from './interface';
import { NzUploadBtnComponent } from './upload-btn.component';
import { NzUploadListComponent } from './upload-list.component';
import * as i0 from "@angular/core";
export declare class NzUploadComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
    private cdr;
    private i18n;
    private directionality;
    static ngAcceptInputType_nzShowUploadList: BooleanInput | NzShowUploadList;
    private destroy$;
    uploadComp: NzUploadBtnComponent;
    listComp: NzUploadListComponent;
    locale: NzUploadI18nInterface;
    dir: Direction;
    nzType: NzUploadType;
    nzLimit: number;
    nzSize: number;
    nzFileType?: string;
    nzAccept?: string | string[];
    nzAction?: string | ((file: NzUploadFile) => string | Observable<string>);
    nzDirectory: boolean;
    nzOpenFileDialogOnClick: boolean;
    nzBeforeUpload?: (file: NzUploadFile, fileList: NzUploadFile[]) => boolean | Observable<boolean>;
    nzCustomRequest?: (item: NzUploadXHRArgs) => Subscription;
    nzData?: {} | ((file: NzUploadFile) => {} | Observable<{}>);
    nzFilter: UploadFilter[];
    nzFileList: NzUploadFile[];
    nzDisabled: boolean;
    nzHeaders?: {} | ((file: NzUploadFile) => {} | Observable<{}>);
    nzListType: NzUploadListType;
    nzMultiple: boolean;
    nzName: string;
    private _showUploadList;
    private document;
    set nzShowUploadList(value: boolean | NzShowUploadList);
    get nzShowUploadList(): boolean | NzShowUploadList;
    nzShowButton: boolean;
    nzWithCredentials: boolean;
    nzRemove?: (file: NzUploadFile) => boolean | Observable<boolean>;
    nzPreview?: (file: NzUploadFile) => void;
    nzPreviewFile?: (file: NzUploadFile) => Observable<string>;
    nzPreviewIsImage?: (file: NzUploadFile) => boolean;
    nzTransformFile?: (file: NzUploadFile) => NzUploadTransformFileType;
    nzDownload?: (file: NzUploadFile) => void;
    nzIconRender: NzIconRenderTemplate | null;
    nzFileListRender: TemplateRef<{
        $implicit: NzUploadFile[];
    }> | null;
    readonly nzChange: EventEmitter<NzUploadChangeParam>;
    readonly nzFileListChange: EventEmitter<NzUploadFile[]>;
    _btnOptions?: ZipButtonOptions;
    private zipOptions;
    private readonly platform;
    constructor(cdr: ChangeDetectorRef, i18n: NzI18nService, directionality: Directionality);
    private fileToObject;
    private getFileItem;
    private removeFileItem;
    private onStart;
    private onProgress;
    private onSuccess;
    private onError;
    private dragState?;
    fileDrop(e: DragEvent): void;
    private detectChangesList;
    onRemove: (file: NzUploadFile) => void;
    private prefixCls;
    classList: string[];
    private setClassMap;
    ngOnInit(): void;
    ngAfterViewInit(): void;
    ngOnChanges(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzUploadComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzUploadComponent, "nz-upload", ["nzUpload"], { "nzType": { "alias": "nzType"; "required": false; }; "nzLimit": { "alias": "nzLimit"; "required": false; }; "nzSize": { "alias": "nzSize"; "required": false; }; "nzFileType": { "alias": "nzFileType"; "required": false; }; "nzAccept": { "alias": "nzAccept"; "required": false; }; "nzAction": { "alias": "nzAction"; "required": false; }; "nzDirectory": { "alias": "nzDirectory"; "required": false; }; "nzOpenFileDialogOnClick": { "alias": "nzOpenFileDialogOnClick"; "required": false; }; "nzBeforeUpload": { "alias": "nzBeforeUpload"; "required": false; }; "nzCustomRequest": { "alias": "nzCustomRequest"; "required": false; }; "nzData": { "alias": "nzData"; "required": false; }; "nzFilter": { "alias": "nzFilter"; "required": false; }; "nzFileList": { "alias": "nzFileList"; "required": false; }; "nzDisabled": { "alias": "nzDisabled"; "required": false; }; "nzHeaders": { "alias": "nzHeaders"; "required": false; }; "nzListType": { "alias": "nzListType"; "required": false; }; "nzMultiple": { "alias": "nzMultiple"; "required": false; }; "nzName": { "alias": "nzName"; "required": false; }; "nzShowUploadList": { "alias": "nzShowUploadList"; "required": false; }; "nzShowButton": { "alias": "nzShowButton"; "required": false; }; "nzWithCredentials": { "alias": "nzWithCredentials"; "required": false; }; "nzRemove": { "alias": "nzRemove"; "required": false; }; "nzPreview": { "alias": "nzPreview"; "required": false; }; "nzPreviewFile": { "alias": "nzPreviewFile"; "required": false; }; "nzPreviewIsImage": { "alias": "nzPreviewIsImage"; "required": false; }; "nzTransformFile": { "alias": "nzTransformFile"; "required": false; }; "nzDownload": { "alias": "nzDownload"; "required": false; }; "nzIconRender": { "alias": "nzIconRender"; "required": false; }; "nzFileListRender": { "alias": "nzFileListRender"; "required": false; }; }, { "nzChange": "nzChange"; "nzFileListChange": "nzFileListChange"; }, never, ["*"], true, never>;
    static ngAcceptInputType_nzLimit: unknown;
    static ngAcceptInputType_nzSize: unknown;
    static ngAcceptInputType_nzDirectory: unknown;
    static ngAcceptInputType_nzOpenFileDialogOnClick: unknown;
    static ngAcceptInputType_nzDisabled: unknown;
    static ngAcceptInputType_nzMultiple: unknown;
    static ngAcceptInputType_nzShowButton: unknown;
    static ngAcceptInputType_nzWithCredentials: unknown;
}
