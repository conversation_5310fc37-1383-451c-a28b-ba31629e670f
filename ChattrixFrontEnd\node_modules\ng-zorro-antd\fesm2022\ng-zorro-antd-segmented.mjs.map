{"version": 3, "file": "ng-zorro-antd-segmented.mjs", "sources": ["../../components/segmented/segmented.service.ts", "../../components/segmented/segmented-item.component.ts", "../../components/segmented/types.ts", "../../components/segmented/segmented.component.ts", "../../components/segmented/segmented.module.ts", "../../components/segmented/public-api.ts", "../../components/segmented/ng-zorro-antd-segmented.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { AnimationEvent } from '@angular/animations';\nimport { Injectable, OnDestroy } from '@angular/core';\nimport { ReplaySubject, Subject } from 'rxjs';\n\n@Injectable()\nexport class NzSegmentedService implements OnDestroy {\n  readonly selected$ = new ReplaySubject<string | number>(1);\n  readonly activated$ = new ReplaySubject<HTMLElement>(1);\n  readonly change$ = new Subject<string | number>();\n  readonly disabled$ = new ReplaySubject<boolean>(1);\n  readonly animationDone$ = new Subject<AnimationEvent>();\n\n  ngOnDestroy(): void {\n    this.selected$.complete();\n    this.activated$.complete();\n    this.change$.complete();\n    this.disabled$.complete();\n    this.animationDone$.complete();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  DestroyRef,\n  ElementRef,\n  inject,\n  Input,\n  OnInit,\n  ViewEncapsulation\n} from '@angular/core';\nimport { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';\nimport { filter, map, switchMap, take, tap } from 'rxjs/operators';\n\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\nimport { NzSegmentedService } from './segmented.service';\n\n@Component({\n  selector: 'label[nz-segmented-item],label[nzSegmentedItem]',\n  exportAs: 'nzSegmentedItem',\n  imports: [NzIconModule, NgTemplateOutlet],\n  template: `\n    <input class=\"ant-segmented-item-input\" type=\"radio\" [checked]=\"isChecked\" (click)=\"$event.stopPropagation()\" />\n    <div class=\"ant-segmented-item-label\">\n      @if (nzIcon) {\n        <span class=\"ant-segmented-item-icon\"><nz-icon [nzType]=\"nzIcon\" /></span>\n        <span>\n          <ng-template [ngTemplateOutlet]=\"content\" />\n        </span>\n      } @else {\n        <ng-template [ngTemplateOutlet]=\"content\" />\n      }\n    </div>\n\n    <ng-template #content>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n  host: {\n    class: 'ant-segmented-item',\n    '[class.ant-segmented-item-selected]': 'isChecked',\n    '[class.ant-segmented-item-disabled]': 'nzDisabled || parentDisabled()',\n    '(click)': 'handleClick()'\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None\n})\nexport class NzSegmentedItemComponent implements OnInit {\n  @Input() nzIcon?: string;\n  @Input() nzValue!: string | number;\n  @Input() nzDisabled?: boolean;\n\n  protected isChecked = false;\n\n  private readonly service = inject(NzSegmentedService);\n\n  readonly parentDisabled = toSignal(this.service.disabled$, { initialValue: false });\n\n  constructor(\n    private cdr: ChangeDetectorRef,\n    private elementRef: ElementRef,\n    private destroyRef: DestroyRef\n  ) {}\n\n  ngOnInit(): void {\n    this.service.selected$\n      .pipe(\n        tap(value => {\n          this.isChecked = false;\n          this.cdr.markForCheck();\n          if (value === this.nzValue) {\n            this.service.activated$.next(this.elementRef.nativeElement);\n          }\n        }),\n        switchMap(value =>\n          this.service.animationDone$.pipe(\n            filter(event => event.toState === 'to'),\n            take(1),\n            map(() => value)\n          )\n        ),\n        filter(value => value === this.nzValue),\n        takeUntilDestroyed(this.destroyRef)\n      )\n      .subscribe(() => {\n        this.isChecked = true;\n        this.cdr.markForCheck();\n      });\n  }\n\n  handleClick(): void {\n    if (!this.nzDisabled && !this.parentDisabled()) {\n      this.service.selected$.next(this.nzValue);\n      this.service.change$.next(this.nzValue);\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport type NzSegmentedOption = {\n  value: string | number;\n  disabled?: boolean;\n} & (NzSegmentedWithLabel | NzSegmentedWithIcon);\n\nexport interface NzSegmentedWithLabel {\n  label: string;\n  icon?: string;\n}\n\nexport interface NzSegmentedWithIcon {\n  icon: string;\n  label?: string;\n}\n\nexport type NzSegmentedOptions = Array<NzSegmentedOption | string | number>;\n\nexport function normalizeOptions(unnormalized: NzSegmentedOptions): NzSegmentedOption[] {\n  return unnormalized.map(item => {\n    if (typeof item === 'string' || typeof item === 'number') {\n      return {\n        label: `${item}`,\n        value: item\n      };\n    }\n\n    return item;\n  });\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { AnimationEvent } from '@angular/animations';\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport {\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  contentChildren,\n  effect,\n  EventEmitter,\n  forwardRef,\n  inject,\n  Input,\n  OnChanges,\n  Output,\n  SimpleChanges,\n  viewChildren,\n  ViewEncapsulation\n} from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { bufferCount } from 'rxjs/operators';\n\nimport { ThumbAnimationProps, thumbMotion } from 'ng-zorro-antd/core/animation';\nimport { NzConfigKey, NzConfigService, WithConfig } from 'ng-zorro-antd/core/config';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzSizeLDSType, OnChangeType, OnTouchedType } from 'ng-zorro-antd/core/types';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\nimport { NzSegmentedItemComponent } from './segmented-item.component';\nimport { NzSegmentedService } from './segmented.service';\nimport { normalizeOptions, NzSegmentedOption, NzSegmentedOptions } from './types';\n\nconst NZ_CONFIG_MODULE_NAME: NzConfigKey = 'segmented';\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  selector: 'nz-segmented',\n  exportAs: 'nzSegmented',\n  template: `\n    <!-- thumb motion div -->\n    <div class=\"ant-segmented-group\">\n      @if (animationState) {\n        <div\n          class=\"ant-segmented-thumb ant-segmented-thumb-motion\"\n          [@thumbMotion]=\"animationState\"\n          (@thumbMotion.done)=\"handleThumbAnimationDone($event)\"\n        ></div>\n      }\n\n      <ng-content>\n        @for (item of normalizedOptions; track item.value) {\n          <label nz-segmented-item [nzIcon]=\"item.icon\" [nzValue]=\"item.value\" [nzDisabled]=\"item.disabled\">{{\n            item.label\n          }}</label>\n        }\n      </ng-content>\n    </div>\n  `,\n  host: {\n    class: 'ant-segmented',\n    '[class.ant-segmented-disabled]': 'nzDisabled',\n    '[class.ant-segmented-rtl]': `dir === 'rtl'`,\n    '[class.ant-segmented-lg]': `nzSize === 'large'`,\n    '[class.ant-segmented-sm]': `nzSize === 'small'`,\n    '[class.ant-segmented-block]': `nzBlock`\n  },\n  providers: [\n    NzSegmentedService,\n    { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => NzSegmentedComponent), multi: true }\n  ],\n  animations: [thumbMotion],\n  imports: [NzIconModule, NzOutletModule, NzSegmentedItemComponent]\n})\nexport class NzSegmentedComponent implements OnChanges, ControlValueAccessor {\n  readonly _nzModuleName: NzConfigKey = NZ_CONFIG_MODULE_NAME;\n\n  @Input({ transform: booleanAttribute }) nzBlock: boolean = false;\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Input() nzOptions: NzSegmentedOptions = [];\n  @Input() @WithConfig() nzSize: NzSizeLDSType = 'default';\n\n  @Output() readonly nzValueChange = new EventEmitter<number | string>();\n\n  private viewItemCmps = viewChildren(NzSegmentedItemComponent);\n  private contentItemCmps = contentChildren(NzSegmentedItemComponent);\n\n  protected dir: Direction = 'ltr';\n  protected value?: number | string;\n  protected animationState: null | { value: string; params: ThumbAnimationProps } = {\n    value: 'to',\n    params: thumbAnimationParamsOf()\n  };\n  protected normalizedOptions: NzSegmentedOption[] = [];\n  protected onChange: OnChangeType = () => {};\n  protected onTouched: OnTouchedType = () => {};\n\n  private readonly service = inject(NzSegmentedService);\n\n  constructor(\n    public readonly nzConfigService: NzConfigService,\n    private readonly cdr: ChangeDetectorRef,\n    private readonly directionality: Directionality\n  ) {\n    this.directionality.change.pipe(takeUntilDestroyed()).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.markForCheck();\n    });\n\n    this.service.selected$.pipe(takeUntilDestroyed()).subscribe(value => {\n      this.value = value;\n    });\n\n    this.service.change$.pipe(takeUntilDestroyed()).subscribe(value => {\n      this.nzValueChange.emit(value);\n      this.onChange(value);\n    });\n\n    this.service.activated$.pipe(bufferCount(2, 1), takeUntilDestroyed()).subscribe(elements => {\n      this.animationState = {\n        value: 'from',\n        params: thumbAnimationParamsOf(elements[0])\n      };\n      this.cdr.detectChanges();\n\n      this.animationState = {\n        value: 'to',\n        params: thumbAnimationParamsOf(elements[1])\n      };\n      this.cdr.detectChanges();\n    });\n\n    effect(() => {\n      const itemCmps = this.viewItemCmps().concat(this.contentItemCmps());\n\n      if (!itemCmps.length) {\n        return;\n      }\n\n      if (\n        this.value === undefined || // If no value is set, select the first item\n        !itemCmps.some(item => item.nzValue === this.value) // handle value not in options\n      ) {\n        this.service.selected$.next(itemCmps[0].nzValue);\n      }\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzOptions, nzDisabled } = changes;\n    if (nzOptions) {\n      this.normalizedOptions = normalizeOptions(nzOptions.currentValue);\n    }\n    if (nzDisabled) {\n      this.service.disabled$.next(nzDisabled.currentValue);\n    }\n  }\n\n  handleThumbAnimationDone(event: AnimationEvent): void {\n    if (event.toState === 'to') {\n      this.animationState = null;\n    }\n    this.service.animationDone$.next(event);\n  }\n\n  writeValue(value: number | string): void {\n    this.service.selected$.next(value);\n  }\n\n  registerOnChange(fn: OnChangeType): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: OnTouchedType): void {\n    this.onTouched = fn;\n  }\n}\n\nfunction thumbAnimationParamsOf(element?: HTMLElement): ThumbAnimationProps {\n  return {\n    transform: element?.offsetLeft ?? 0,\n    width: element?.clientWidth ?? 0\n  };\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzSegmentedItemComponent } from './segmented-item.component';\nimport { NzSegmentedComponent } from './segmented.component';\n\n@NgModule({\n  imports: [NzSegmentedComponent, NzSegmentedItemComponent],\n  exports: [NzSegmentedComponent, NzSegmentedItemComponent]\n})\nexport class NzSegmentedModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport { NzSegmentedItemComponent } from './segmented-item.component';\nexport { NzSegmentedComponent } from './segmented.component';\nexport { NzSegmentedModule } from './segmented.module';\nexport * from './types';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1"], "mappings": ";;;;;;;;;;;;;;;;MAUa,kBAAkB,CAAA;AACpB,IAAA,SAAS,GAAG,IAAI,aAAa,CAAkB,CAAC,CAAC;AACjD,IAAA,UAAU,GAAG,IAAI,aAAa,CAAc,CAAC,CAAC;AAC9C,IAAA,OAAO,GAAG,IAAI,OAAO,EAAmB;AACxC,IAAA,SAAS,GAAG,IAAI,aAAa,CAAU,CAAC,CAAC;AACzC,IAAA,cAAc,GAAG,IAAI,OAAO,EAAkB;IAEvD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;AACzB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAC1B,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACvB,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;AACzB,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;;uGAZrB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAAlB,kBAAkB,EAAA,CAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAD9B;;;ACTD;;;AAGG;MAmDU,wBAAwB,CAAA;AAYzB,IAAA,GAAA;AACA,IAAA,UAAA;AACA,IAAA,UAAA;AAbD,IAAA,MAAM;AACN,IAAA,OAAO;AACP,IAAA,UAAU;IAET,SAAS,GAAG,KAAK;AAEV,IAAA,OAAO,GAAG,MAAM,CAAC,kBAAkB,CAAC;AAE5C,IAAA,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;AAEnF,IAAA,WAAA,CACU,GAAsB,EACtB,UAAsB,EACtB,UAAsB,EAAA;QAFtB,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAU,CAAA,UAAA,GAAV,UAAU;QACV,IAAU,CAAA,UAAA,GAAV,UAAU;;IAGpB,QAAQ,GAAA;QACN,IAAI,CAAC,OAAO,CAAC;AACV,aAAA,IAAI,CACH,GAAG,CAAC,KAAK,IAAG;AACV,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACvB,YAAA,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,EAAE;AAC1B,gBAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;;AAE/D,SAAC,CAAC,EACF,SAAS,CAAC,KAAK,IACb,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAC9B,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC,EACvC,IAAI,CAAC,CAAC,CAAC,EACP,GAAG,CAAC,MAAM,KAAK,CAAC,CACjB,CACF,EACD,MAAM,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,EACvC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC;aAEpC,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,SAAC,CAAC;;IAGN,WAAW,GAAA;QACT,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;YAC9C,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;YACzC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;;;uGA9ChC,wBAAwB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAxB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,wBAAwB,EA1BzB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iDAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,eAAA,EAAA,EAAA,UAAA,EAAA,EAAA,mCAAA,EAAA,WAAA,EAAA,mCAAA,EAAA,gCAAA,EAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;GAgBT,EAjBS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,0NAAE,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FA2B7B,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBA9BpC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iDAAiD;AAC3D,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;AACzC,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;AAgBT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,oBAAoB;AAC3B,wBAAA,qCAAqC,EAAE,WAAW;AAClD,wBAAA,qCAAqC,EAAE,gCAAgC;AACvE,wBAAA,SAAS,EAAE;AACZ,qBAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC;AAClC,iBAAA;wIAEU,MAAM,EAAA,CAAA;sBAAd;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,UAAU,EAAA,CAAA;sBAAlB;;;ACzDH;;;AAGG;AAmBG,SAAU,gBAAgB,CAAC,YAAgC,EAAA;AAC/D,IAAA,OAAO,YAAY,CAAC,GAAG,CAAC,IAAI,IAAG;QAC7B,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YACxD,OAAO;gBACL,KAAK,EAAE,CAAG,EAAA,IAAI,CAAE,CAAA;AAChB,gBAAA,KAAK,EAAE;aACR;;AAGH,QAAA,OAAO,IAAI;AACb,KAAC,CAAC;AACJ;;ACKA,MAAM,qBAAqB,GAAgB,WAAW;IA0CzC,oBAAoB,GAAA,CAAA,MAAA;;;;iBAApB,oBAAoB,CAAA;;;AAMrB,YAAA,kBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;YAAC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,kBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,QAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,QAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,MAAM,EAAN,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,MAAM,GAA4B,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,oBAAA,EAAA,yBAAA,CAAA;;;QAoBvC,eAAe;QACd,GAAG;QACH,cAAc;QA3BxB,aAAa,GAAgB,qBAAqB;QAEnB,OAAO,GAAY,KAAK;QACxB,UAAU,GAAG,KAAK;QACjD,SAAS,GAAuB,EAAE;QACpB,MAAM,GAAA,iBAAA,CAAA,IAAA,EAAA,oBAAA,EAAkB,SAAS,CAAC;AAEtC,QAAA,aAAa,IAAG,iBAAA,CAAA,IAAA,EAAA,yBAAA,CAAA,EAAA,IAAI,YAAY,EAAmB;AAE9D,QAAA,YAAY,GAAG,YAAY,CAAC,wBAAwB,CAAC;AACrD,QAAA,eAAe,GAAG,eAAe,CAAC,wBAAwB,CAAC;QAEzD,GAAG,GAAc,KAAK;AACtB,QAAA,KAAK;AACL,QAAA,cAAc,GAA0D;AAChF,YAAA,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,sBAAsB;SAC/B;QACS,iBAAiB,GAAwB,EAAE;AAC3C,QAAA,QAAQ,GAAiB,MAAK,GAAG;AACjC,QAAA,SAAS,GAAkB,MAAK,GAAG;AAE5B,QAAA,OAAO,GAAG,MAAM,CAAC,kBAAkB,CAAC;AAErD,QAAA,WAAA,CACkB,eAAgC,EAC/B,GAAsB,EACtB,cAA8B,EAAA;YAF/B,IAAe,CAAA,eAAA,GAAf,eAAe;YACd,IAAG,CAAA,GAAA,GAAH,GAAG;YACH,IAAc,CAAA,cAAA,GAAd,cAAc;AAE/B,YAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,SAAS,CAAC,SAAS,IAAG;AAC1E,gBAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;AAEF,YAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AAClE,gBAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AACpB,aAAC,CAAC;AAEF,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AAChE,gBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9B,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;AACtB,aAAC,CAAC;YAEF,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,IAAG;gBACzF,IAAI,CAAC,cAAc,GAAG;AACpB,oBAAA,KAAK,EAAE,MAAM;AACb,oBAAA,MAAM,EAAE,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;iBAC3C;AACD,gBAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;gBAExB,IAAI,CAAC,cAAc,GAAG;AACpB,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,MAAM,EAAE,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;iBAC3C;AACD,gBAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,aAAC,CAAC;YAEF,MAAM,CAAC,MAAK;AACV,gBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;AAEnE,gBAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;oBACpB;;AAGF,gBAAA,IACE,IAAI,CAAC,KAAK,KAAK,SAAS;AACxB,oBAAA,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC;kBACnD;AACA,oBAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;;AAEpD,aAAC,CAAC;;AAGJ,QAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,YAAA,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,OAAO;YACzC,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC,SAAS,CAAC,YAAY,CAAC;;YAEnE,IAAI,UAAU,EAAE;gBACd,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;;;AAIxD,QAAA,wBAAwB,CAAC,KAAqB,EAAA;AAC5C,YAAA,IAAI,KAAK,CAAC,OAAO,KAAK,IAAI,EAAE;AAC1B,gBAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;YAE5B,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;;AAGzC,QAAA,UAAU,CAAC,KAAsB,EAAA;YAC/B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;;AAGpC,QAAA,gBAAgB,CAAC,EAAgB,EAAA;AAC/B,YAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAGpB,QAAA,iBAAiB,CAAC,EAAiB,EAAA;AACjC,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;2GApGV,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,IAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAApB,QAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,oBAAoB,EAGX,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAXzB,EAAA,SAAA,EAAA,WAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,OAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,8BAAA,EAAA,YAAA,EAAA,yBAAA,EAAA,eAAA,EAAA,wBAAA,EAAA,oBAAA,EAAA,wBAAA,EAAA,oBAAA,EAAA,2BAAA,EAAA,SAAA,EAAA,EAAA,cAAA,EAAA,eAAA,EAAA,EAAA,SAAA,EAAA;gBACT,kBAAkB;AAClB,gBAAA,EAAE,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,oBAAoB,CAAC,EAAE,KAAK,EAAE,IAAI;aAC/F,EAeyC,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,wBAAwB,EAD9B,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,wBAAwB,EA7ClD,WAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;GAmBT,EAcS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,8BAAE,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,wBAAwB,EADpD,QAAA,EAAA,iDAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,SAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA,CAAC,WAAW,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;;2FAGd,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAxChC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;AAmBT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,eAAe;AACtB,wBAAA,gCAAgC,EAAE,YAAY;AAC9C,wBAAA,2BAA2B,EAAE,CAAe,aAAA,CAAA;AAC5C,wBAAA,0BAA0B,EAAE,CAAoB,kBAAA,CAAA;AAChD,wBAAA,0BAA0B,EAAE,CAAoB,kBAAA,CAAA;AAChD,wBAAA,6BAA6B,EAAE,CAAS,OAAA;AACzC,qBAAA;AACD,oBAAA,SAAS,EAAE;wBACT,kBAAkB;AAClB,wBAAA,EAAE,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,CAAC,0BAA0B,CAAC,EAAE,KAAK,EAAE,IAAI;AAC/F,qBAAA;oBACD,UAAU,EAAE,CAAC,WAAW,CAAC;AACzB,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,wBAAwB;AACjE,iBAAA;mJAIyC,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,SAAS,EAAA,CAAA;sBAAjB;gBACsB,MAAM,EAAA,CAAA;sBAA5B;gBAEkB,aAAa,EAAA,CAAA;sBAA/B;;AAgGH,SAAS,sBAAsB,CAAC,OAAqB,EAAA;IACnD,OAAO;AACL,QAAA,SAAS,EAAE,OAAO,EAAE,UAAU,IAAI,CAAC;AACnC,QAAA,KAAK,EAAE,OAAO,EAAE,WAAW,IAAI;KAChC;AACH;;AC7LA;;;AAGG;MAWU,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,YAHlB,oBAAoB,EAAE,wBAAwB,CAC9C,EAAA,OAAA,EAAA,CAAA,oBAAoB,EAAE,wBAAwB,CAAA,EAAA,CAAA;wGAE7C,iBAAiB,EAAA,OAAA,EAAA,CAHlB,oBAAoB,EAAE,wBAAwB,CAAA,EAAA,CAAA;;2FAG7C,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAJ7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,oBAAoB,EAAE,wBAAwB,CAAC;AACzD,oBAAA,OAAO,EAAE,CAAC,oBAAoB,EAAE,wBAAwB;AACzD,iBAAA;;;ACbD;;;AAGG;;ACHH;;AAEG;;;;"}