import { ChangeDetectorRef, ElementRef, EventEmitter, NgZone, OnInit } from '@angular/core';
import { NzDestroyService } from 'ng-zorro-antd/core/services';
import { NzTSType } from 'ng-zorro-antd/core/types';
import { NzI18nService, NzTextI18nInterface } from 'ng-zorro-antd/i18n';
import { NzAutosizeDirective } from 'ng-zorro-antd/input';
import * as i0 from "@angular/core";
export declare class NzTextEditComponent implements OnInit {
    private ngZone;
    private cdr;
    private i18n;
    private destroy$;
    editing: boolean;
    locale: NzTextI18nInterface;
    text?: string;
    icon: NzTSType;
    tooltip?: null | NzTSType;
    readonly startEditing: EventEmitter<void>;
    readonly endEditing: EventEmitter<string>;
    set textarea(textarea: ElementRef<HTMLTextAreaElement> | undefined);
    autosizeDirective: NzAutosizeDirective;
    beforeText?: string;
    currentText?: string;
    nativeElement: HTMLElement;
    private textarea$;
    private injector;
    constructor(ngZone: NgZone, cdr: ChangeDetectorRef, i18n: NzI18nService, destroy$: NzDestroyService);
    ngOnInit(): void;
    onClick(): void;
    confirm(): void;
    onEnter(event: Event): void;
    onCancel(): void;
    focusAndSetValue(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTextEditComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzTextEditComponent, "nz-text-edit", ["nzTextEdit"], { "text": { "alias": "text"; "required": false; }; "icon": { "alias": "icon"; "required": false; }; "tooltip": { "alias": "tooltip"; "required": false; }; }, { "startEditing": "startEditing"; "endEditing": "endEditing"; }, never, never, true, never>;
}
