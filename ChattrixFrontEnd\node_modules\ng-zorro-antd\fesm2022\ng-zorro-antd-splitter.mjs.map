{"version": 3, "file": "ng-zorro-antd-splitter.mjs", "sources": ["../../components/splitter/splitter-bar.component.ts", "../../components/splitter/splitter-panel.component.ts", "../../components/splitter/utils.ts", "../../components/splitter/splitter.component.ts", "../../components/splitter/splitter.module.ts", "../../components/splitter/typings.ts", "../../components/splitter/public-api.ts", "../../components/splitter/ng-zorro-antd-splitter.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { coerceCssPixelValue } from '@angular/cdk/coercion';\nimport { ChangeDetectionStrategy, Component, computed, input, output, ViewEncapsulation } from '@angular/core';\n\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { getEventWithPoint } from 'ng-zorro-antd/resizable';\n\nimport { NzSplitterCollapseOption } from './typings';\n\n@Component({\n  selector: '[nz-splitter-bar]',\n  imports: [NzIconModule],\n  template: `\n    @if (lazy()) {\n      @let preview = active() && !!this.constrainedOffset();\n      <div\n        class=\"ant-splitter-bar-preview\"\n        [class.ant-splitter-bar-preview-active]=\"preview\"\n        [style.transform]=\"preview ? previewTransform() : null\"\n      ></div>\n    }\n\n    <div\n      class=\"ant-splitter-bar-dragger\"\n      [class.ant-splitter-bar-dragger-disabled]=\"!resizable()\"\n      [class.ant-splitter-bar-dragger-active]=\"active()\"\n      (mousedown)=\"resizeStartEvent($event)\"\n      (touchstart)=\"resizeStartEvent($event)\"\n    ></div>\n\n    @if (collapsible()?.start) {\n      <div class=\"ant-splitter-bar-collapse-bar ant-splitter-bar-collapse-bar-start\" (click)=\"collapseEvent('start')\">\n        <nz-icon\n          [nzType]=\"vertical() ? 'up' : 'left'\"\n          class=\"ant-splitter-bar-collapse-icon ant-splitter-bar-collapse-start\"\n        />\n      </div>\n    }\n\n    @if (collapsible()?.end) {\n      <div class=\"ant-splitter-bar-collapse-bar ant-splitter-bar-collapse-bar-end\" (click)=\"collapseEvent('end')\">\n        <nz-icon\n          [nzType]=\"vertical() ? 'down' : 'right'\"\n          class=\"ant-splitter-bar-collapse-icon ant-splitter-bar-collapse-end\"\n        />\n      </div>\n    }\n  `,\n  host: {\n    role: 'separator',\n    class: 'ant-splitter-bar',\n    '[attr.aria-valuenow]': 'getValidNumber(ariaNow())',\n    '[attr.aria-valuemin]': 'getValidNumber(ariaMin())',\n    '[attr.aria-valuemax]': 'getValidNumber(ariaMax())'\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class NzSplitterBarComponent {\n  readonly ariaNow = input.required<number>();\n  readonly ariaMin = input.required<number>();\n  readonly ariaMax = input.required<number>();\n  readonly active = input(false);\n  readonly resizable = input(true);\n  readonly vertical = input<boolean>();\n  readonly lazy = input(false);\n  readonly collapsible = input<NzSplitterCollapseOption>();\n  readonly constrainedOffset = input<number>();\n\n  readonly offsetStart = output<[x: number, y: number]>();\n  readonly collapse = output<'start' | 'end'>();\n\n  protected readonly previewTransform = computed(() => {\n    const offset = coerceCssPixelValue(this.constrainedOffset());\n    return this.vertical() ? `translateY(${offset})` : `translateX(${offset})`;\n  });\n\n  protected resizeStartEvent(event: MouseEvent | TouchEvent): void {\n    if (this.resizable()) {\n      const { pageX, pageY } = getEventWithPoint(event);\n      this.offsetStart.emit([pageX, pageY]);\n    }\n  }\n\n  protected collapseEvent(type: 'start' | 'end'): void {\n    this.collapse.emit(type);\n  }\n\n  protected getValidNumber(num: number | undefined): number {\n    return typeof num === 'number' && !isNaN(num) ? Math.round(num) : 0;\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  input,\n  TemplateRef,\n  Component,\n  ViewEncapsulation,\n  viewChild,\n  booleanAttribute,\n  ChangeDetectionStrategy\n} from '@angular/core';\n\nimport { NzSplitterCollapsible } from './typings';\n\n@Component({\n  selector: 'nz-splitter-panel',\n  exportAs: 'nzSplitterPanel',\n  template: `\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class NzSplitterPanelComponent {\n  readonly nzDefaultSize = input<number | string>();\n  readonly nzMin = input<number | string>();\n  readonly nzMax = input<number | string>();\n  readonly nzSize = input<number | string>();\n  readonly nzCollapsible = input<NzSplitterCollapsible>(false);\n  readonly nzResizable = input(true, { transform: booleanAttribute });\n  readonly contentTemplate = viewChild.required<TemplateRef<void>>('contentTemplate');\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NzSplitterCollapseOption, NzSplitterCollapsible } from './typings';\n\n/**\n * Get the percentage value of the string. (e.g. '50%' => 0.5)\n * @param str\n */\nexport function getPercentValue(str: string): number {\n  return Number(str.slice(0, -1)) / 100;\n}\n\n/**\n * Check if the size is percentage.\n * @param size\n */\nexport function isPercent(size: string | number | undefined): size is string {\n  return typeof size === 'string' && size.endsWith('%');\n}\n\n/**\n * Coerce the panel collapsible option to the NzSplitterCollapseOption type.\n */\nexport function coerceCollapsible(collapsible: NzSplitterCollapsible): NzSplitterCollapseOption {\n  if (collapsible && typeof collapsible === 'object') {\n    return collapsible;\n  }\n\n  const mergedCollapsible = !!collapsible;\n  return {\n    start: mergedCollapsible,\n    end: mergedCollapsible\n  };\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Directionality } from '@angular/cdk/bidi';\nimport { coerceCssPixelValue } from '@angular/cdk/coercion';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { DOCUMENT, NgTemplateOutlet } from '@angular/common';\nimport {\n  Component,\n  contentChildren,\n  ElementRef,\n  input,\n  output,\n  ViewEncapsulation,\n  inject,\n  computed,\n  linkedSignal,\n  signal,\n  booleanAttribute,\n  ChangeDetectionStrategy\n} from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { map, merge, Subject, takeUntil } from 'rxjs';\nimport { pairwise, startWith } from 'rxjs/operators';\n\nimport { NzResizeObserver } from 'ng-zorro-antd/cdk/resize-observer';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\nimport { getEventWithPoint } from 'ng-zorro-antd/resizable';\n\nimport { NzSplitterBarComponent } from './splitter-bar.component';\nimport { NzSplitterPanelComponent } from './splitter-panel.component';\nimport { NzSplitterCollapseOption, NzSplitterLayout } from './typings';\nimport { coerceCollapsible, getPercentValue, isPercent } from './utils';\n\ninterface PanelSize {\n  // Calculated size of the panel in pixels, constrained by the min and max size.\n  size: string | number | undefined;\n  // Size in pixels\n  postPxSize: number;\n  // The percentage size of the panel, calculated based on the container size.\n  percentage: number;\n  // Original min size of the panel set by the user.\n  min: string | number | undefined;\n  // Original max size of the panel set by the user.\n  max: string | number | undefined;\n  // Post processed min size of the panel in percentage.\n  postPercentMinSize: number;\n  // Post processed max size of the panel in percentage.\n  postPercentMaxSize: number;\n}\n\ninterface ResizableInfo {\n  resizable: boolean;\n  collapsible: Required<NzSplitterCollapseOption>;\n}\n\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({ passive: true });\n\n@Component({\n  selector: 'nz-splitter',\n  exportAs: 'nzSplitter',\n  template: `\n    @for (panel of panelProps(); let i = $index; track i; let last = $last) {\n      @let size = sizes()[i];\n      @let flexBasis = !!size.size ? size.size : 'auto';\n      @let flexGrow = !!size.size ? 0 : 1;\n      <div\n        class=\"ant-splitter-panel\"\n        [class.ant-splitter-panel-hidden]=\"size.postPxSize === 0\"\n        [style.flex-basis]=\"flexBasis\"\n        [style.flex-grow]=\"flexGrow\"\n      >\n        <ng-container *ngTemplateOutlet=\"panel.contentTemplate\"></ng-container>\n      </div>\n\n      @if (!last) {\n        @let resizableInfo = resizableInfos()[i];\n        @let ariaInfo = ariaInfos()[i];\n        <div\n          nz-splitter-bar\n          [ariaNow]=\"ariaInfo.ariaNow\"\n          [ariaMin]=\"ariaInfo.ariaMin\"\n          [ariaMax]=\"ariaInfo.ariaMax\"\n          [resizable]=\"resizableInfo.resizable\"\n          [collapsible]=\"resizableInfo.collapsible\"\n          [active]=\"movingIndex()?.index === i\"\n          [vertical]=\"nzLayout() === 'vertical'\"\n          [lazy]=\"nzLazy()\"\n          [constrainedOffset]=\"constrainedOffset()\"\n          (offsetStart)=\"startResize(i, $event)\"\n          (collapse)=\"collapse(i, $event)\"\n        ></div>\n      }\n    }\n\n    <!-- Fake mask for cursor -->\n    @if (movingIndex() !== null) {\n      <div\n        aria-hidden\n        class=\"ant-splitter-mask\"\n        [class.ant-splitter-mask-horizontal]=\"nzLayout() === 'horizontal'\"\n        [class.ant-splitter-mask-vertical]=\"nzLayout() === 'vertical'\"\n      ></div>\n    }\n  `,\n  imports: [NgTemplateOutlet, NzSplitterBarComponent],\n  providers: [NzDestroyService],\n  host: {\n    class: 'ant-splitter',\n    '[class.ant-splitter-horizontal]': 'nzLayout() === \"horizontal\"',\n    '[class.ant-splitter-vertical]': 'nzLayout() === \"vertical\"',\n    '[class.ant-splitter-rtl]': 'dir() === \"rtl\"'\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class NzSplitterComponent {\n  /** ------------------- Props ------------------- */\n  readonly nzLayout = input<NzSplitterLayout>('horizontal');\n  readonly nzLazy = input(false, { transform: booleanAttribute });\n  readonly nzResizeStart = output<number[]>();\n  readonly nzResize = output<number[]>();\n  readonly nzResizeEnd = output<number[]>();\n\n  protected readonly destroy$ = inject(NzDestroyService);\n  protected readonly elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  protected readonly directionality = inject(Directionality);\n  protected readonly resizeObserver = inject(NzResizeObserver);\n  protected readonly document = inject(DOCUMENT);\n\n  protected readonly dir = toSignal(this.directionality.change, { initialValue: this.directionality.value });\n\n  /** ------------------- Panels ------------------- */\n  // Get all panels from content children\n  protected readonly panels = contentChildren(NzSplitterPanelComponent);\n  // Subscribe to the change of properties\n  protected readonly panelProps = computed(() =>\n    this.panels().map(panel => ({\n      defaultSize: panel.nzDefaultSize(),\n      size: panel.nzSize(),\n      min: panel.nzMin(),\n      max: panel.nzMax(),\n      resizable: panel.nzResizable(),\n      collapsible: coerceCollapsible(panel.nzCollapsible()),\n      contentTemplate: panel.contentTemplate()\n    }))\n  );\n\n  /** ------------------- Sizes ------------------- */\n  /**\n   * Observe the size of the container.\n   */\n  private readonly containerBox = toSignal(\n    this.resizeObserver.observe(this.elementRef).pipe(\n      map(([item]) => item.target as HTMLElement),\n      map(el => ({ width: el.clientWidth, height: el.clientHeight }))\n    ),\n    {\n      initialValue: {\n        width: this.elementRef.nativeElement.clientWidth || 0,\n        height: this.elementRef.nativeElement.clientHeight || 0\n      }\n    }\n  );\n  /**\n   * The size of the container, used to calculate the percentage size and flex basis of each panel.\n   */\n  protected readonly containerSize = computed(() =>\n    this.nzLayout() === 'horizontal' ? this.containerBox().width : this.containerBox().height\n  );\n  /**\n   * Derived from defaultSize of each panel.\n   * After that it will be updated by the resize event with **real** size in pixels.\n   */\n  protected readonly innerSizes = linkedSignal({\n    source: this.panelProps,\n    computation: source => source.map(panel => panel.defaultSize)\n  });\n  /**\n   * Calculate the size of each panel based on the container size and the percentage size.\n   */\n  protected readonly sizes = computed(() => {\n    let emptyCount = 0;\n    const containerSize = this.containerSize();\n    const innerSizes = this.innerSizes();\n    /**\n     * Get the calculated size, min and max percentage of each panel\n     */\n    const sizes = this.panelProps().map((panel, index) => {\n      const size = panel.size ?? innerSizes[index];\n\n      // Calculate the percentage size of each panel.\n      let percentage: number | undefined;\n      if (isPercent(size)) {\n        percentage = getPercentValue(size);\n      } else if (size || size === 0) {\n        const num = Number(size);\n        if (!isNaN(num)) {\n          percentage = num / containerSize;\n        }\n      } else {\n        percentage = undefined;\n        emptyCount++;\n      }\n\n      // Calculate the min and max percentage size of each panel.\n      const minSize = panel.min;\n      const maxSize = panel.max;\n      const postPercentMinSize = isPercent(minSize) ? getPercentValue(minSize) : (minSize || 0) / containerSize;\n      const postPercentMaxSize = isPercent(maxSize)\n        ? getPercentValue(maxSize)\n        : (maxSize || containerSize) / containerSize;\n\n      return {\n        size,\n        percentage,\n        min: minSize,\n        max: maxSize,\n        postPercentMinSize,\n        postPercentMaxSize\n      } as PanelSize;\n    });\n\n    /**\n     * Normalize the percentage size of each panel if the total percentage is larger than 1 or smaller than 1.\n     */\n    const totalPercentage = sizes.reduce((acc, { percentage }) => acc + (percentage ?? 0), 0);\n\n    for (const size of sizes) {\n      if (totalPercentage > 1 && !emptyCount) {\n        // If total percentage is larger than 1, we will scale it down.\n        const scale = 1 / totalPercentage;\n        size.percentage = size.percentage === undefined ? 0 : size.percentage * scale;\n      } else {\n        // If total percentage is smaller than 1, we will fill the rest.\n        const averagePercentage = (1 - totalPercentage) / emptyCount;\n        size.percentage = size.percentage === undefined ? averagePercentage : size.percentage;\n      }\n\n      size.postPxSize = size.percentage * containerSize;\n      size.size = containerSize ? coerceCssPixelValue(size.postPxSize) : size.size;\n    }\n\n    return sizes;\n  });\n\n  protected readonly ariaInfos = computed(() => {\n    const stackSizes: number[] = [];\n    const ariaInfos: Array<{ ariaNow: number; ariaMin: number; ariaMax: number }> = [];\n    const sizes = this.sizes();\n\n    let stack = 0;\n    for (const size of sizes) {\n      stack += size.percentage;\n      stackSizes.push(stack);\n    }\n\n    for (let i = 0; i < sizes.length - 1; i += 1) {\n      const ariaMinStart = (stackSizes[i - 1] || 0) + sizes[i].postPercentMinSize;\n      const ariaMinEnd = (stackSizes[i + 1] || 100) - sizes[i + 1].postPercentMaxSize;\n\n      const ariaMaxStart = (stackSizes[i - 1] || 0) + sizes[i].postPercentMaxSize;\n      const ariaMaxEnd = (stackSizes[i + 1] || 100) - sizes[i + 1].postPercentMinSize;\n\n      ariaInfos.push({\n        ariaNow: stackSizes[i] * 100,\n        ariaMin: Math.max(ariaMinStart, ariaMinEnd) * 100,\n        ariaMax: Math.min(ariaMaxStart, ariaMaxEnd) * 100\n      });\n    }\n\n    return ariaInfos;\n  });\n\n  private getPxSizes(): number[] {\n    return this.sizes().map(s => s.postPxSize);\n  }\n\n  /** ------------------ Resize ------------------ */\n  /**\n   * The index of the panel that is being resized.\n   * @note Mark the moving splitter bar as activated to show the dragging effect even if the mouse is outside the\n   * splitter container.\n   */\n  protected readonly movingIndex = signal<{ index: number; confirmed: boolean } | null>(null);\n  /**\n   * The offset of preview position (lazy mode) when dragging the splitter bar.\n   * Constrained by the min and max size of the target panel.\n   */\n  protected readonly constrainedOffset = signal<number>(0);\n  /**\n   * The resizable information of each splitter bar.\n   */\n  protected readonly resizableInfos = computed(() => {\n    const items = this.panelProps();\n    const pxSizes = this.getPxSizes();\n\n    const resizeInfos: ResizableInfo[] = [];\n\n    for (let i = 0; i < items.length - 1; i += 1) {\n      const prevItem = items[i];\n      const nextItem = items[i + 1];\n      const prevSize = pxSizes[i];\n      const nextSize = pxSizes[i + 1];\n\n      const { resizable: prevResizable = true, min: prevMin, collapsible: prevCollapsible } = prevItem;\n      const { resizable: nextResizable = true, min: nextMin, collapsible: nextCollapsible } = nextItem;\n\n      const mergedResizable =\n        // Both need to be resizable\n        prevResizable &&\n        nextResizable &&\n        // Prev is not collapsed and limit min size\n        (prevSize !== 0 || !prevMin) &&\n        // Next is not collapsed and limit min size\n        (nextSize !== 0 || !nextMin);\n\n      const startCollapsible =\n        // Self is collapsible\n        (prevCollapsible.end && prevSize > 0) ||\n        // Collapsed and can be collapsed\n        (nextCollapsible.start && nextSize === 0 && prevSize > 0);\n\n      const endCollapsible =\n        // Self is collapsible\n        (nextCollapsible.start && nextSize > 0) ||\n        // Collapsed and can be collapsed\n        (prevCollapsible.end && prevSize === 0 && nextSize > 0);\n\n      resizeInfos[i] = {\n        resizable: mergedResizable,\n        collapsible: {\n          start: !!(this.dir() === 'rtl' ? endCollapsible : startCollapsible),\n          end: !!(this.dir() === 'rtl' ? startCollapsible : endCollapsible)\n        }\n      };\n    }\n\n    return resizeInfos;\n  });\n\n  /**\n   * Handle the resize start event for the specified panel.\n   * @param index The index of the panel.\n   * @param startPos The start position of the resize event.\n   */\n  protected startResize(index: number, startPos: [x: number, y: number]): void {\n    this.movingIndex.set({ index, confirmed: false });\n    this.nzResizeStart.emit(this.getPxSizes());\n    const end$ = new Subject<void>();\n\n    // Updated constraint calculation\n    const getConstrainedOffset = (rawOffset: number): number => {\n      const { percentage, postPercentMinSize, postPercentMaxSize } = this.sizes()[index];\n      const [ariaNow, ariaMin, ariaMax] = [percentage, postPercentMinSize, postPercentMaxSize].map(p => p * 100);\n\n      const containerSize = this.containerSize();\n      const currentPos = (containerSize * ariaNow) / 100;\n      const newPos = currentPos + rawOffset;\n\n      // Calculate available space\n      const minAllowed = Math.max(0, (containerSize * ariaMin) / 100);\n      const maxAllowed = Math.min(containerSize, (containerSize * ariaMax) / 100);\n\n      // Constrain new position within bounds\n      const clampedPos = Math.max(minAllowed, Math.min(maxAllowed, newPos));\n      return clampedPos - currentPos;\n    };\n\n    const handleLazyMove = (offset: number): void => {\n      this.constrainedOffset.set(getConstrainedOffset(offset));\n    };\n\n    const handleLazyEnd = (): void => {\n      this.updateOffset(index, this.constrainedOffset());\n      this.constrainedOffset.set(0);\n    };\n\n    // resizing\n    merge(\n      fromEventOutsideAngular<MouseEvent>(this.document, 'mousemove', passiveEventListenerOptions),\n      fromEventOutsideAngular<TouchEvent>(this.document, 'touchmove', passiveEventListenerOptions)\n    )\n      .pipe(\n        map(event => getEventWithPoint(event)),\n        map(({ pageX, pageY }) => (this.nzLayout() === 'horizontal' ? pageX - startPos[0] : pageY - startPos[1])),\n        // flip the offset if the direction is rtl\n        map(offset => (this.nzLayout() === 'horizontal' && this.dir() === 'rtl' ? -offset : offset)),\n        startWith(0),\n        pairwise(),\n        takeUntil(merge(end$, this.destroy$))\n      )\n      .subscribe(([prev, next]) => {\n        if (this.nzLazy() && next !== 0) {\n          handleLazyMove(next);\n        } else {\n          const deltaOffset = next - prev;\n          // filter out the 0 delta offset\n          if (deltaOffset !== 0) {\n            this.updateOffset(index, deltaOffset);\n          }\n        }\n      });\n\n    // resize end\n    merge(\n      fromEventOutsideAngular<MouseEvent>(this.document, 'mouseup'),\n      fromEventOutsideAngular<TouchEvent>(this.document, 'touchend')\n    )\n      .pipe(takeUntil(merge(end$, this.destroy$)))\n      .subscribe(() => {\n        if (this.nzLazy()) {\n          handleLazyEnd();\n        }\n        this.movingIndex.set(null);\n        this.nzResizeEnd.emit(this.getPxSizes());\n        end$.next();\n      });\n  }\n\n  /**\n   * Update the sizes of specified panels based on the move offset.\n   * @param index The index of the panel.\n   * @param offset The move offset in pixels.\n   */\n  private updateOffset(index: number, offset: number): void {\n    const containerSize = this.containerSize();\n    const limitSizes = this.sizes().map(p => [p.min, p.max]);\n    const pxSizes = this.sizes().map(p => p.percentage * containerSize);\n\n    const getLimitSize = (size: string | number | undefined, defaultLimit: number): number => {\n      if (typeof size === 'string') {\n        return getPercentValue(size) * containerSize;\n      }\n      return size ?? defaultLimit;\n    };\n\n    // First time trigger move index update is not sync in the state\n    let confirmedIndex: number | null = null;\n    const movingIndex = this.movingIndex();\n    // we need to know what the real index is\n    if ((!movingIndex || !movingIndex.confirmed) && offset !== 0) {\n      // search for the real index\n      if (offset > 0) {\n        confirmedIndex = index;\n        this.movingIndex.set({ index, confirmed: true });\n      } else {\n        for (let i = index; i >= 0; i -= 1) {\n          if (pxSizes[i] > 0 && this.resizableInfos()[i].resizable) {\n            confirmedIndex = i;\n            this.movingIndex.set({ index: i, confirmed: true });\n            break;\n          }\n        }\n      }\n    }\n\n    const mergedIndex = confirmedIndex ?? index;\n    const nextIndex = mergedIndex + 1;\n\n    // Get boundary\n    const startMinSize = getLimitSize(limitSizes[mergedIndex][0], 0);\n    const endMinSize = getLimitSize(limitSizes[nextIndex][0], 0);\n    const startMaxSize = getLimitSize(limitSizes[mergedIndex][1], containerSize);\n    const endMaxSize = getLimitSize(limitSizes[nextIndex][1], containerSize);\n\n    let mergedOffset = offset;\n\n    // Align with the boundary\n    if (pxSizes[mergedIndex] + mergedOffset < startMinSize) {\n      mergedOffset = startMinSize - pxSizes[mergedIndex];\n    }\n    if (pxSizes[nextIndex] - mergedOffset < endMinSize) {\n      mergedOffset = pxSizes[nextIndex] - endMinSize;\n    }\n    if (pxSizes[mergedIndex] + mergedOffset > startMaxSize) {\n      mergedOffset = startMaxSize - pxSizes[mergedIndex];\n    }\n    if (pxSizes[nextIndex] - mergedOffset > endMaxSize) {\n      mergedOffset = pxSizes[nextIndex] - endMaxSize;\n    }\n\n    // Do offset\n    pxSizes[mergedIndex] += mergedOffset;\n    pxSizes[nextIndex] -= mergedOffset;\n    this.innerSizes.set(pxSizes);\n    this.nzResize.emit(pxSizes);\n  }\n\n  /** ------------------ Resize ------------------ */\n  /**\n   * Record the original size of the collapsed panel.\n   * Used to restore the size when the panel is expanded back.\n   */\n  private readonly cacheCollapsedSize: number[] = [];\n\n  /**\n   * Collapse the specified panel.\n   * @param index The index of the panel to collapse.\n   * @param type The type of collapse, either `start` or `end`.\n   */\n  protected collapse(index: number, type: 'start' | 'end'): void {\n    const containerSize = this.containerSize();\n    const limitSizes = this.sizes().map(p => [p.min, p.max]);\n    const currentSizes = this.sizes().map(p => p.percentage * containerSize);\n    const adjustedType = this.dir() === 'rtl' ? (type === 'start' ? 'end' : 'start') : type;\n\n    const currentIndex = adjustedType === 'start' ? index : index + 1;\n    const targetIndex = adjustedType == 'start' ? index + 1 : index;\n    const currentSize = currentSizes[currentIndex];\n    const targetSize = currentSizes[targetIndex];\n\n    const getLimitSize = (size: string | number | undefined, defaultLimit: number): number => {\n      if (typeof size === 'string') {\n        return getPercentValue(size) * containerSize;\n      }\n      return size ?? defaultLimit;\n    };\n\n    if (currentSize !== 0 && targetSize !== 0) {\n      // Collapse directly\n      currentSizes[currentIndex] = 0;\n      currentSizes[targetIndex] += currentSize;\n      this.cacheCollapsedSize[index] = currentSize;\n    } else {\n      const totalSize = currentSize + targetSize;\n\n      const currentSizeMin = getLimitSize(limitSizes[currentIndex][0], 0);\n      const currentSizeMax = getLimitSize(limitSizes[currentIndex][1], containerSize);\n      const targetSizeMin = getLimitSize(limitSizes[targetIndex][0], 0);\n      const targetSizeMax = getLimitSize(limitSizes[targetIndex][1], containerSize);\n\n      const limitStart = Math.max(currentSizeMin, totalSize - targetSizeMax);\n      const limitEnd = Math.min(currentSizeMax, totalSize - targetSizeMin);\n      const halfOffset = (limitEnd - limitStart) / 2;\n\n      const targetCacheCollapsedSize = this.cacheCollapsedSize[index];\n      const currentCacheCollapsedSize = totalSize - targetCacheCollapsedSize;\n\n      const shouldUseCache =\n        targetCacheCollapsedSize &&\n        targetCacheCollapsedSize <= targetSizeMax &&\n        targetCacheCollapsedSize >= targetSizeMin &&\n        currentCacheCollapsedSize <= currentSizeMax &&\n        currentCacheCollapsedSize >= currentSizeMin;\n\n      if (shouldUseCache) {\n        currentSizes[targetIndex] = targetCacheCollapsedSize;\n        currentSizes[currentIndex] = currentCacheCollapsedSize;\n      } else {\n        currentSizes[currentIndex] -= halfOffset;\n        currentSizes[targetIndex] += halfOffset;\n      }\n    }\n\n    this.innerSizes.set(currentSizes);\n    this.nzResize.emit(currentSizes);\n    this.nzResizeEnd.emit(currentSizes);\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzSplitterPanelComponent } from './splitter-panel.component';\nimport { NzSplitterComponent } from './splitter.component';\n\n@NgModule({\n  imports: [NzSplitterComponent, NzSplitterPanelComponent],\n  exports: [NzSplitterComponent, NzSplitterPanelComponent]\n})\nexport class NzSplitterModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport type NzSplitterLayout = 'horizontal' | 'vertical';\nexport type NzSplitterCollapsible = boolean | NzSplitterCollapseOption;\n\nexport interface NzSplitterCollapseOption {\n  start?: boolean;\n  end?: boolean;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './splitter.component';\nexport * from './splitter-panel.component';\nexport * from './splitter.module';\nexport * from './typings';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;;AAGG;MA2DU,sBAAsB,CAAA;AACxB,IAAA,OAAO,GAAG,KAAK,CAAC,QAAQ,EAAU;AAClC,IAAA,OAAO,GAAG,KAAK,CAAC,QAAQ,EAAU;AAClC,IAAA,OAAO,GAAG,KAAK,CAAC,QAAQ,EAAU;AAClC,IAAA,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;AACrB,IAAA,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;IACvB,QAAQ,GAAG,KAAK,EAAW;AAC3B,IAAA,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC;IACnB,WAAW,GAAG,KAAK,EAA4B;IAC/C,iBAAiB,GAAG,KAAK,EAAU;IAEnC,WAAW,GAAG,MAAM,EAA0B;IAC9C,QAAQ,GAAG,MAAM,EAAmB;AAE1B,IAAA,gBAAgB,GAAG,QAAQ,CAAC,MAAK;QAClD,MAAM,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC5D,QAAA,OAAO,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAA,WAAA,EAAc,MAAM,CAAA,CAAA,CAAG,GAAG,CAAc,WAAA,EAAA,MAAM,GAAG;AAC5E,KAAC,CAAC;AAEQ,IAAA,gBAAgB,CAAC,KAA8B,EAAA;AACvD,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YACpB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,iBAAiB,CAAC,KAAK,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;;;AAI/B,IAAA,aAAa,CAAC,IAAqB,EAAA;AAC3C,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;AAGhB,IAAA,cAAc,CAAC,GAAuB,EAAA;QAC9C,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;;uGA/B1D,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAtB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,sBAAsB,EA9CvB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,OAAA,EAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,OAAA,EAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,MAAA,EAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,UAAA,EAAA,WAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,QAAA,EAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,IAAA,EAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,WAAA,EAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,UAAA,EAAA,aAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,iBAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,WAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,2BAAA,EAAA,oBAAA,EAAA,2BAAA,EAAA,oBAAA,EAAA,2BAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EApCS,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FA+CX,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAjDlC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;oBAC7B,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,IAAI,EAAE,WAAW;AACjB,wBAAA,KAAK,EAAE,kBAAkB;AACzB,wBAAA,sBAAsB,EAAE,2BAA2B;AACnD,wBAAA,sBAAsB,EAAE,2BAA2B;AACnD,wBAAA,sBAAsB,EAAE;AACzB,qBAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC;AAC1C,iBAAA;;;AC7DD;;;AAGG;MAyBU,wBAAwB,CAAA;IAC1B,aAAa,GAAG,KAAK,EAAmB;IACxC,KAAK,GAAG,KAAK,EAAmB;IAChC,KAAK,GAAG,KAAK,EAAmB;IAChC,MAAM,GAAG,KAAK,EAAmB;AACjC,IAAA,aAAa,GAAG,KAAK,CAAwB,KAAK,CAAC;IACnD,WAAW,GAAG,KAAK,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC;AAC1D,IAAA,eAAe,GAAG,SAAS,CAAC,QAAQ,CAAoB,iBAAiB,CAAC;uGAPxE,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAxB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,wBAAwB,EARzB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,aAAA,EAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,UAAA,EAAA,eAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,KAAA,EAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,UAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,KAAA,EAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,UAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,MAAA,EAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,aAAA,EAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,UAAA,EAAA,eAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,WAAA,EAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,UAAA,EAAA,aAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;AAIT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAIU,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBAXpC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,QAAQ,EAAE;;;;AAIT,EAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC;AAC1C,iBAAA;;;AC3BD;;;AAGG;AAIH;;;AAGG;AACG,SAAU,eAAe,CAAC,GAAW,EAAA;AACzC,IAAA,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;AACvC;AAEA;;;AAGG;AACG,SAAU,SAAS,CAAC,IAAiC,EAAA;IACzD,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;AACvD;AAEA;;AAEG;AACG,SAAU,iBAAiB,CAAC,WAAkC,EAAA;AAClE,IAAA,IAAI,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;AAClD,QAAA,OAAO,WAAW;;AAGpB,IAAA,MAAM,iBAAiB,GAAG,CAAC,CAAC,WAAW;IACvC,OAAO;AACL,QAAA,KAAK,EAAE,iBAAiB;AACxB,QAAA,GAAG,EAAE;KACN;AACH;;ACpCA;;;AAGG;AAwDH,MAAM,2BAA2B,GAAG,+BAA+B,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;MA4DzE,mBAAmB,CAAA;;AAErB,IAAA,QAAQ,GAAG,KAAK,CAAmB,YAAY,CAAC;IAChD,MAAM,GAAG,KAAK,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC;IACtD,aAAa,GAAG,MAAM,EAAY;IAClC,QAAQ,GAAG,MAAM,EAAY;IAC7B,WAAW,GAAG,MAAM,EAAY;AAEtB,IAAA,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC;AACnC,IAAA,UAAU,GAAG,MAAM,CAA0B,UAAU,CAAC;AACxD,IAAA,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;AACvC,IAAA,cAAc,GAAG,MAAM,CAAC,gBAAgB,CAAC;AACzC,IAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AAE3B,IAAA,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;;;AAIvF,IAAA,MAAM,GAAG,eAAe,CAAC,wBAAwB,CAAC;;AAElD,IAAA,UAAU,GAAG,QAAQ,CAAC,MACvC,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,KAAK,KAAK;AAC1B,QAAA,WAAW,EAAE,KAAK,CAAC,aAAa,EAAE;AAClC,QAAA,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE;AACpB,QAAA,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE;AAClB,QAAA,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE;AAClB,QAAA,SAAS,EAAE,KAAK,CAAC,WAAW,EAAE;AAC9B,QAAA,WAAW,EAAE,iBAAiB,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;AACrD,QAAA,eAAe,EAAE,KAAK,CAAC,eAAe;KACvC,CAAC,CAAC,CACJ;;AAGD;;AAEG;IACc,YAAY,GAAG,QAAQ,CACtC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAC/C,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,MAAqB,CAAC,EAC3C,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC,CAChE,EACD;AACE,QAAA,YAAY,EAAE;YACZ,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC;YACrD,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,IAAI;AACvD;AACF,KAAA,CACF;AACD;;AAEG;AACgB,IAAA,aAAa,GAAG,QAAQ,CAAC,MAC1C,IAAI,CAAC,QAAQ,EAAE,KAAK,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM,CAC1F;AACD;;;AAGG;IACgB,UAAU,GAAG,YAAY,CAAC;QAC3C,MAAM,EAAE,IAAI,CAAC,UAAU;AACvB,QAAA,WAAW,EAAE,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,WAAW;AAC7D,KAAA,CAAC;AACF;;AAEG;AACgB,IAAA,KAAK,GAAG,QAAQ,CAAC,MAAK;QACvC,IAAI,UAAU,GAAG,CAAC;AAClB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE;AAC1C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE;AACpC;;AAEG;AACH,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;YACnD,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,UAAU,CAAC,KAAK,CAAC;;AAG5C,YAAA,IAAI,UAA8B;AAClC,YAAA,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;AACnB,gBAAA,UAAU,GAAG,eAAe,CAAC,IAAI,CAAC;;AAC7B,iBAAA,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE;AAC7B,gBAAA,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC;AACxB,gBAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACf,oBAAA,UAAU,GAAG,GAAG,GAAG,aAAa;;;iBAE7B;gBACL,UAAU,GAAG,SAAS;AACtB,gBAAA,UAAU,EAAE;;;AAId,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG;AACzB,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG;YACzB,MAAM,kBAAkB,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,aAAa;AACzG,YAAA,MAAM,kBAAkB,GAAG,SAAS,CAAC,OAAO;AAC1C,kBAAE,eAAe,CAAC,OAAO;kBACvB,CAAC,OAAO,IAAI,aAAa,IAAI,aAAa;YAE9C,OAAO;gBACL,IAAI;gBACJ,UAAU;AACV,gBAAA,GAAG,EAAE,OAAO;AACZ,gBAAA,GAAG,EAAE,OAAO;gBACZ,kBAAkB;gBAClB;aACY;AAChB,SAAC,CAAC;AAEF;;AAEG;QACH,MAAM,eAAe,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,KAAK,GAAG,IAAI,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;AAEzF,QAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,YAAA,IAAI,eAAe,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE;;AAEtC,gBAAA,MAAM,KAAK,GAAG,CAAC,GAAG,eAAe;gBACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK;;iBACxE;;gBAEL,MAAM,iBAAiB,GAAG,CAAC,CAAC,GAAG,eAAe,IAAI,UAAU;AAC5D,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,GAAG,iBAAiB,GAAG,IAAI,CAAC,UAAU;;YAGvF,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,aAAa;AACjD,YAAA,IAAI,CAAC,IAAI,GAAG,aAAa,GAAG,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI;;AAG9E,QAAA,OAAO,KAAK;AACd,KAAC,CAAC;AAEiB,IAAA,SAAS,GAAG,QAAQ,CAAC,MAAK;QAC3C,MAAM,UAAU,GAAa,EAAE;QAC/B,MAAM,SAAS,GAAiE,EAAE;AAClF,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE;QAE1B,IAAI,KAAK,GAAG,CAAC;AACb,QAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,YAAA,KAAK,IAAI,IAAI,CAAC,UAAU;AACxB,YAAA,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;;AAGxB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AAC5C,YAAA,MAAM,YAAY,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,kBAAkB;YAC3E,MAAM,UAAU,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,kBAAkB;AAE/E,YAAA,MAAM,YAAY,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,kBAAkB;YAC3E,MAAM,UAAU,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,kBAAkB;YAE/E,SAAS,CAAC,IAAI,CAAC;AACb,gBAAA,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG;gBAC5B,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,GAAG,GAAG;gBACjD,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,GAAG;AAC/C,aAAA,CAAC;;AAGJ,QAAA,OAAO,SAAS;AAClB,KAAC,CAAC;IAEM,UAAU,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC;;;AAI5C;;;;AAIG;AACgB,IAAA,WAAW,GAAG,MAAM,CAA+C,IAAI,CAAC;AAC3F;;;AAGG;AACgB,IAAA,iBAAiB,GAAG,MAAM,CAAS,CAAC,CAAC;AACxD;;AAEG;AACgB,IAAA,cAAc,GAAG,QAAQ,CAAC,MAAK;AAChD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE;AAC/B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE;QAEjC,MAAM,WAAW,GAAoB,EAAE;AAEvC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AAC5C,YAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC;YACzB,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7B,YAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;AAE/B,YAAA,MAAM,EAAE,SAAS,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,GAAG,QAAQ;AAChG,YAAA,MAAM,EAAE,SAAS,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,GAAG,QAAQ;AAEhG,YAAA,MAAM,eAAe;;YAEnB,aAAa;gBACb,aAAa;;AAEb,iBAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;;AAE5B,iBAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;AAE9B,YAAA,MAAM,gBAAgB;;AAEpB,YAAA,CAAC,eAAe,CAAC,GAAG,IAAI,QAAQ,GAAG,CAAC;;AAEpC,iBAAC,eAAe,CAAC,KAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,QAAQ,GAAG,CAAC,CAAC;AAE3D,YAAA,MAAM,cAAc;;AAElB,YAAA,CAAC,eAAe,CAAC,KAAK,IAAI,QAAQ,GAAG,CAAC;;AAEtC,iBAAC,eAAe,CAAC,GAAG,IAAI,QAAQ,KAAK,CAAC,IAAI,QAAQ,GAAG,CAAC,CAAC;YAEzD,WAAW,CAAC,CAAC,CAAC,GAAG;AACf,gBAAA,SAAS,EAAE,eAAe;AAC1B,gBAAA,WAAW,EAAE;AACX,oBAAA,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,GAAG,cAAc,GAAG,gBAAgB,CAAC;AACnE,oBAAA,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,GAAG,gBAAgB,GAAG,cAAc;AACjE;aACF;;AAGH,QAAA,OAAO,WAAW;AACpB,KAAC,CAAC;AAEF;;;;AAIG;IACO,WAAW,CAAC,KAAa,EAAE,QAAgC,EAAA;AACnE,QAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;QACjD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1C,QAAA,MAAM,IAAI,GAAG,IAAI,OAAO,EAAQ;;AAGhC,QAAA,MAAM,oBAAoB,GAAG,CAAC,SAAiB,KAAY;AACzD,YAAA,MAAM,EAAE,UAAU,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC;YAClF,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AAE1G,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE;YAC1C,MAAM,UAAU,GAAG,CAAC,aAAa,GAAG,OAAO,IAAI,GAAG;AAClD,YAAA,MAAM,MAAM,GAAG,UAAU,GAAG,SAAS;;AAGrC,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,aAAa,GAAG,OAAO,IAAI,GAAG,CAAC;AAC/D,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,aAAa,GAAG,OAAO,IAAI,GAAG,CAAC;;AAG3E,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YACrE,OAAO,UAAU,GAAG,UAAU;AAChC,SAAC;AAED,QAAA,MAAM,cAAc,GAAG,CAAC,MAAc,KAAU;YAC9C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;AAC1D,SAAC;QAED,MAAM,aAAa,GAAG,MAAW;YAC/B,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAClD,YAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/B,SAAC;;QAGD,KAAK,CACH,uBAAuB,CAAa,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,2BAA2B,CAAC,EAC5F,uBAAuB,CAAa,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,2BAA2B,CAAC;aAE3F,IAAI,CACH,GAAG,CAAC,KAAK,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC,EACtC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,KAAK,YAAY,GAAG,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzG,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,YAAY,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,EAC5F,SAAS,CAAC,CAAC,CAAC,EACZ,QAAQ,EAAE,EACV,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aAEtC,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAI;YAC1B,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE;gBAC/B,cAAc,CAAC,IAAI,CAAC;;iBACf;AACL,gBAAA,MAAM,WAAW,GAAG,IAAI,GAAG,IAAI;;AAE/B,gBAAA,IAAI,WAAW,KAAK,CAAC,EAAE;AACrB,oBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC;;;AAG3C,SAAC,CAAC;;AAGJ,QAAA,KAAK,CACH,uBAAuB,CAAa,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,EAC7D,uBAAuB,CAAa,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC;AAE7D,aAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC1C,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;AACjB,gBAAA,aAAa,EAAE;;AAEjB,YAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC;YAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,IAAI,CAAC,IAAI,EAAE;AACb,SAAC,CAAC;;AAGN;;;;AAIG;IACK,YAAY,CAAC,KAAa,EAAE,MAAc,EAAA;AAChD,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE;QAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACxD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,GAAG,aAAa,CAAC;AAEnE,QAAA,MAAM,YAAY,GAAG,CAAC,IAAiC,EAAE,YAAoB,KAAY;AACvF,YAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,gBAAA,OAAO,eAAe,CAAC,IAAI,CAAC,GAAG,aAAa;;YAE9C,OAAO,IAAI,IAAI,YAAY;AAC7B,SAAC;;QAGD,IAAI,cAAc,GAAkB,IAAI;AACxC,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE;;AAEtC,QAAA,IAAI,CAAC,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,SAAS,KAAK,MAAM,KAAK,CAAC,EAAE;;AAE5D,YAAA,IAAI,MAAM,GAAG,CAAC,EAAE;gBACd,cAAc,GAAG,KAAK;AACtB,gBAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;;iBAC3C;AACL,gBAAA,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AAClC,oBAAA,IAAI,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;wBACxD,cAAc,GAAG,CAAC;AAClB,wBAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;wBACnD;;;;;AAMR,QAAA,MAAM,WAAW,GAAG,cAAc,IAAI,KAAK;AAC3C,QAAA,MAAM,SAAS,GAAG,WAAW,GAAG,CAAC;;AAGjC,QAAA,MAAM,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAChE,QAAA,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5D,QAAA,MAAM,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC;AAC5E,QAAA,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC;QAExE,IAAI,YAAY,GAAG,MAAM;;QAGzB,IAAI,OAAO,CAAC,WAAW,CAAC,GAAG,YAAY,GAAG,YAAY,EAAE;AACtD,YAAA,YAAY,GAAG,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC;;QAEpD,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,YAAY,GAAG,UAAU,EAAE;AAClD,YAAA,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,UAAU;;QAEhD,IAAI,OAAO,CAAC,WAAW,CAAC,GAAG,YAAY,GAAG,YAAY,EAAE;AACtD,YAAA,YAAY,GAAG,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC;;QAEpD,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,YAAY,GAAG,UAAU,EAAE;AAClD,YAAA,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,UAAU;;;AAIhD,QAAA,OAAO,CAAC,WAAW,CAAC,IAAI,YAAY;AACpC,QAAA,OAAO,CAAC,SAAS,CAAC,IAAI,YAAY;AAClC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC;AAC5B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;;;AAI7B;;;AAGG;IACc,kBAAkB,GAAa,EAAE;AAElD;;;;AAIG;IACO,QAAQ,CAAC,KAAa,EAAE,IAAqB,EAAA;AACrD,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE;QAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACxD,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,GAAG,aAAa,CAAC;AACxE,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,OAAO,IAAI,IAAI;AAEvF,QAAA,MAAM,YAAY,GAAG,YAAY,KAAK,OAAO,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC;AACjE,QAAA,MAAM,WAAW,GAAG,YAAY,IAAI,OAAO,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK;AAC/D,QAAA,MAAM,WAAW,GAAG,YAAY,CAAC,YAAY,CAAC;AAC9C,QAAA,MAAM,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC;AAE5C,QAAA,MAAM,YAAY,GAAG,CAAC,IAAiC,EAAE,YAAoB,KAAY;AACvF,YAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,gBAAA,OAAO,eAAe,CAAC,IAAI,CAAC,GAAG,aAAa;;YAE9C,OAAO,IAAI,IAAI,YAAY;AAC7B,SAAC;QAED,IAAI,WAAW,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE;;AAEzC,YAAA,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC;AAC9B,YAAA,YAAY,CAAC,WAAW,CAAC,IAAI,WAAW;AACxC,YAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,GAAG,WAAW;;aACvC;AACL,YAAA,MAAM,SAAS,GAAG,WAAW,GAAG,UAAU;AAE1C,YAAA,MAAM,cAAc,GAAG,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACnE,YAAA,MAAM,cAAc,GAAG,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC;AAC/E,YAAA,MAAM,aAAa,GAAG,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACjE,YAAA,MAAM,aAAa,GAAG,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC;AAE7E,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,GAAG,aAAa,CAAC;AACtE,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,GAAG,aAAa,CAAC;YACpE,MAAM,UAAU,GAAG,CAAC,QAAQ,GAAG,UAAU,IAAI,CAAC;YAE9C,MAAM,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;AAC/D,YAAA,MAAM,yBAAyB,GAAG,SAAS,GAAG,wBAAwB;YAEtE,MAAM,cAAc,GAClB,wBAAwB;AACxB,gBAAA,wBAAwB,IAAI,aAAa;AACzC,gBAAA,wBAAwB,IAAI,aAAa;AACzC,gBAAA,yBAAyB,IAAI,cAAc;gBAC3C,yBAAyB,IAAI,cAAc;YAE7C,IAAI,cAAc,EAAE;AAClB,gBAAA,YAAY,CAAC,WAAW,CAAC,GAAG,wBAAwB;AACpD,gBAAA,YAAY,CAAC,YAAY,CAAC,GAAG,yBAAyB;;iBACjD;AACL,gBAAA,YAAY,CAAC,YAAY,CAAC,IAAI,UAAU;AACxC,gBAAA,YAAY,CAAC,WAAW,CAAC,IAAI,UAAU;;;AAI3C,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC;AAChC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;;uGAzb1B,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,0oBAVnB,CAAC,gBAAgB,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EA4Be,wBAAwB,EAzE1D,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2CT,EACS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,oJAAE,sBAAsB,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,SAAA,EAAA,QAAA,EAAA,WAAA,EAAA,UAAA,EAAA,MAAA,EAAA,aAAA,EAAA,mBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,aAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAWvC,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBA1D/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CT,EAAA,CAAA;AACD,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,sBAAsB,CAAC;oBACnD,SAAS,EAAE,CAAC,gBAAgB,CAAC;AAC7B,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,cAAc;AACrB,wBAAA,iCAAiC,EAAE,6BAA6B;AAChE,wBAAA,+BAA+B,EAAE,2BAA2B;AAC5D,wBAAA,0BAA0B,EAAE;AAC7B,qBAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC;AAC1C,iBAAA;;;ACtHD;;;AAGG;MAWU,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAHjB,mBAAmB,EAAE,wBAAwB,CAC7C,EAAA,OAAA,EAAA,CAAA,mBAAmB,EAAE,wBAAwB,CAAA,EAAA,CAAA;AAE5C,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAHjB,mBAAmB,CAAA,EAAA,CAAA;;2FAGlB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,mBAAmB,EAAE,wBAAwB,CAAC;AACxD,oBAAA,OAAO,EAAE,CAAC,mBAAmB,EAAE,wBAAwB;AACxD,iBAAA;;;ACbD;;;AAGG;;ACHH;;;AAGG;;ACHH;;AAEG;;;;"}