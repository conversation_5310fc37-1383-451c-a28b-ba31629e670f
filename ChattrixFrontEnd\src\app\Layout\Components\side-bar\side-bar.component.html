<div
  class="sidebar-container"
  [class.collapsed]="isCollapsed"
  *ngIf="userProfile$ | async as userProfile"
>
  <!-- Header Section with Toggle Button -->
  <div class="header-section">
    <!-- Toggle Button (replaces logo when expanded) -->

    <!-- Logo and App Name (hidden when collapsed) -->
    <div *ngIf="!isCollapsed" class="app-branding">
      <div class="app-logo">
        <img
          src="logo/logo2.png"
          alt="Chattrix Logo"
          class="logo-image"
          #logoImg
          (error)="logoImg.style.display = 'none'"
        />
        <span
          class="logo-fallback"
          [style.display]="logoImg.style.display === 'none' ? 'flex' : 'none'"
          >C</span
        >
      </div>
      <h2 class="app-name">Chattrix</h2>
    </div>
    <button
      *ngIf="!isCollapsed"
      class="sidebar-toggle"
      (click)="toggleSidebar()"
      mat-icon-button
      matTooltip="Collapse sidebar"
      matTooltipPosition="right"
      aria-label="Collapse sidebar"
    >
      <mat-icon>chevron_left</mat-icon>
    </button>

    <!-- Expand <PERSON><PERSON> (visible when collapsed) -->
    <button
      *ngIf="isCollapsed"
      class="expand-toggle"
      (click)="toggleSidebar()"
      mat-icon-button
      matTooltip="Expand sidebar"
      matTooltipPosition="right"
      aria-label="Expand sidebar"
    >
      <mat-icon>chevron_right</mat-icon>
    </button>
  </div>

  <!-- Navigation Menu -->
  <div class="navigation-section">
    <mat-nav-list class="nav-list">
      <ng-container *ngFor="let item of navigationItems">
        <mat-list-item
          *ngIf="shouldShowNavItem(item, (hasAdminAccess$ | async) || false)"
          class="nav-item"
          [class.active]="isRouteActive(item.route)"
          (click)="onNavItemClick(item)"
          [matTooltip]="isCollapsed ? item.label : ''"
          matTooltipPosition="right"
          matRipple
        >
          <mat-icon matListItemIcon class="nav-icon">{{ item.icon }}</mat-icon>
          <span *ngIf="!isCollapsed" matListItemTitle class="nav-label">{{
            item.label
          }}</span>
          <span
            *ngIf="item.badge && item.badge > 0 && !isCollapsed"
            class="nav-badge"
            matListItemMeta
          >
            {{ item.badge }}
          </span>
        </mat-list-item>
      </ng-container>
    </mat-nav-list>
  </div>

  <!-- Settings Section -->
  <div class="settings-section">
    <mat-divider *ngIf="!isCollapsed"></mat-divider>

    <!-- Dark Mode Toggle -->
    <div class="setting-item">
      <mat-icon
        class="setting-icon"
        [matTooltip]="isCollapsed ? 'Toggle Dark Mode' : ''"
        matTooltipPosition="right"
        (click)="toggleTheme()"
        >{{ isDarkMode ? "light_mode" : "dark_mode" }}</mat-icon
      >
      <span *ngIf="!isCollapsed" class="setting-label" (click)="toggleTheme()"
        >Dark Mode</span
      >
      <mat-slide-toggle
        *ngIf="!isCollapsed"
        [checked]="isDarkMode"
        (change)="onThemeToggleChange($event)"
        class="theme-toggle"
        color="primary"
      ></mat-slide-toggle>
    </div>
  </div>

  <!-- Profile Section - Redesigned -->
  <div class="profile-section">
    <div class="profile-content">
      <!-- Profile Photo and Info -->
      <div class="profile-info">
        <div
          class="profile-avatar"
          [class.has-image]="userProfile.profilePictureUrl"
        >
          <img
            *ngIf="userProfile.profilePictureUrl"
            [src]="userProfile.profilePictureUrl"
            [alt]="getUserDisplayName(userProfile)"
            class="avatar-image"
            #avatarImg
            (error)="avatarImg.style.display = 'none'"
          />
          <span *ngIf="!userProfile.profilePictureUrl" class="avatar-initials">
            {{ getUserInitials(userProfile) }}
          </span>
        </div>

        <div *ngIf="!isCollapsed" class="profile-details">
          <h3 class="profile-name">{{ getUserDisplayName(userProfile) }}</h3>
          <p *ngIf="shouldShowAdminRole(userProfile)" class="profile-role">
            Admin
          </p>
        </div>
      </div>

      <!-- Logout Button -->
      <button
        mat-icon-button
        class="logout-button"
        (click)="onLogout()"
        [matTooltip]="isCollapsed ? 'Logout' : ''"
        matTooltipPosition="right"
        aria-label="Logout"
      >
        <mat-icon>logout</mat-icon>
      </button>
    </div>
  </div>
</div>
