{"version": 3, "file": "ng-zorro-antd-resizable.mjs", "sources": ["../../components/resizable/resizable-utils.ts", "../../components/resizable/resizable.service.ts", "../../components/resizable/resizable.directive.ts", "../../components/resizable/resize-handle.component.ts", "../../components/resizable/resize-handles.component.ts", "../../components/resizable/resizable.module.ts", "../../components/resizable/public-api.ts", "../../components/resizable/ng-zorro-antd-resizable.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { isTouchEvent } from 'ng-zorro-antd/core/util';\n\nexport function getEventWithPoint(event: MouseEvent | TouchEvent): MouseEvent | Touch {\n  return isTouchEvent(event) ? event.touches[0] || event.changedTouches[0] : (event as <PERSON>Event);\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { DOCUMENT } from '@angular/common';\nimport { Injectable, NgZone, OnDestroy, inject } from '@angular/core';\nimport { Subject } from 'rxjs';\n\nimport { isTouchEvent } from 'ng-zorro-antd/core/util';\n\nimport { NzResizeHandleMouseDownEvent } from './resize-handle.component';\n\n@Injectable()\nexport class NzResizableService implements OnDestroy {\n  private document: Document = inject(DOCUMENT);\n  private listeners = new Map<string, (event: MouseEvent | TouchEvent) => void>();\n\n  /**\n   * The `OutsideAngular` prefix means that the subject will emit events outside of the Angular zone,\n   * so that becomes a bit more descriptive for those who'll maintain the code in the future:\n   * ```ts\n   * nzResizableService.handleMouseDownOutsideAngular$.subscribe(event => {\n   *   console.log(Zone.current); // <root>\n   *   console.log(NgZone.isInAngularZone()); // false\n   * });\n   * ```\n   */\n  handleMouseDownOutsideAngular$ = new Subject<NzResizeHandleMouseDownEvent>();\n  documentMouseUpOutsideAngular$ = new Subject<MouseEvent | TouchEvent | null>();\n  documentMouseMoveOutsideAngular$ = new Subject<MouseEvent | TouchEvent>();\n  mouseEnteredOutsideAngular$ = new Subject<boolean>();\n\n  constructor(private ngZone: NgZone) {}\n\n  startResizing(event: MouseEvent | TouchEvent): void {\n    const _isTouchEvent = isTouchEvent(event);\n    this.clearListeners();\n    const moveEvent = _isTouchEvent ? 'touchmove' : 'mousemove';\n    const upEvent = _isTouchEvent ? 'touchend' : 'mouseup';\n    const moveEventHandler = (e: MouseEvent | TouchEvent): void => {\n      this.documentMouseMoveOutsideAngular$.next(e);\n    };\n    const upEventHandler = (e: MouseEvent | TouchEvent): void => {\n      this.documentMouseUpOutsideAngular$.next(e);\n      this.clearListeners();\n    };\n\n    this.listeners.set(moveEvent, moveEventHandler);\n    this.listeners.set(upEvent, upEventHandler);\n\n    this.ngZone.runOutsideAngular(() => {\n      this.listeners.forEach((handler, name) => {\n        this.document.addEventListener(name, handler as EventListener);\n      });\n    });\n  }\n\n  private clearListeners(): void {\n    this.listeners.forEach((handler, name) => {\n      this.document.removeEventListener(name, handler as EventListener);\n    });\n    this.listeners.clear();\n  }\n\n  ngOnDestroy(): void {\n    this.handleMouseDownOutsideAngular$.complete();\n    this.documentMouseUpOutsideAngular$.complete();\n    this.documentMouseMoveOutsideAngular$.complete();\n    this.mouseEnteredOutsideAngular$.complete();\n    this.clearListeners();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Platform } from '@angular/cdk/platform';\nimport {\n  AfterViewInit,\n  booleanAttribute,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Input,\n  NgZone,\n  numberAttribute,\n  OnDestroy,\n  Output,\n  Renderer2\n} from '@angular/core';\nimport { filter, takeUntil } from 'rxjs/operators';\n\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { ensureInBounds, fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\n\nimport { getEventWithPoint } from './resizable-utils';\nimport { NzResizableService } from './resizable.service';\nimport { NzResizeDirection, NzResizeHandleMouseDownEvent } from './resize-handle.component';\n\nexport interface NzResizeEvent {\n  width?: number;\n  height?: number;\n  col?: number;\n  mouseEvent?: MouseEvent | TouchEvent;\n  direction?: NzResizeDirection;\n}\n\n@Directive({\n  selector: '[nz-resizable]',\n  exportAs: 'nzResizable',\n  providers: [NzResizableService, NzDestroyService],\n  host: {\n    class: 'nz-resizable',\n    '[class.nz-resizable-resizing]': 'resizing',\n    '[class.nz-resizable-disabled]': 'nzDisabled'\n  }\n})\nexport class NzResizableDirective implements AfterViewInit, OnDestroy {\n  @Input() nzBounds: 'window' | 'parent' | ElementRef<HTMLElement> = 'parent';\n  @Input() nzMaxHeight?: number;\n  @Input() nzMaxWidth?: number;\n  @Input({ transform: numberAttribute }) nzMinHeight: number = 40;\n  @Input({ transform: numberAttribute }) nzMinWidth: number = 40;\n  @Input({ transform: numberAttribute }) nzGridColumnCount: number = -1;\n  @Input({ transform: numberAttribute }) nzMaxColumn: number = -1;\n  @Input({ transform: numberAttribute }) nzMinColumn: number = -1;\n  @Input({ transform: booleanAttribute }) nzLockAspectRatio: boolean = false;\n  @Input({ transform: booleanAttribute }) nzPreview: boolean = false;\n  @Input({ transform: booleanAttribute }) nzDisabled: boolean = false;\n  @Output() readonly nzResize = new EventEmitter<NzResizeEvent>();\n  @Output() readonly nzResizeEnd = new EventEmitter<NzResizeEvent>();\n  @Output() readonly nzResizeStart = new EventEmitter<NzResizeEvent>();\n\n  resizing = false;\n  private elRect!: ClientRect | DOMRect;\n  private currentHandleEvent: NzResizeHandleMouseDownEvent | null = null;\n  private ghostElement: HTMLDivElement | null = null;\n  private el!: HTMLElement;\n  private sizeCache: NzResizeEvent | null = null;\n\n  constructor(\n    private elementRef: ElementRef<HTMLElement>,\n    private renderer: Renderer2,\n    private nzResizableService: NzResizableService,\n    private platform: Platform,\n    private ngZone: NgZone,\n    private destroy$: NzDestroyService\n  ) {\n    this.nzResizableService.handleMouseDownOutsideAngular$.pipe(takeUntil(this.destroy$)).subscribe(event => {\n      if (this.nzDisabled) {\n        return;\n      }\n      this.resizing = true;\n      this.nzResizableService.startResizing(event.mouseEvent);\n      this.currentHandleEvent = event;\n      if (this.nzResizeStart.observers.length) {\n        this.ngZone.run(() => this.nzResizeStart.emit({ mouseEvent: event.mouseEvent, direction: event.direction }));\n      }\n      this.elRect = this.el.getBoundingClientRect();\n    });\n\n    this.nzResizableService.documentMouseUpOutsideAngular$\n      .pipe(takeUntil(this.destroy$), filter(Boolean))\n      .subscribe(event => {\n        if (this.resizing) {\n          this.resizing = false;\n          this.nzResizableService.documentMouseUpOutsideAngular$.next(null);\n          this.endResize(event);\n        }\n      });\n\n    this.nzResizableService.documentMouseMoveOutsideAngular$.pipe(takeUntil(this.destroy$)).subscribe(event => {\n      if (this.resizing) {\n        this.resize(event);\n      }\n    });\n  }\n\n  setPosition(): void {\n    const position = getComputedStyle(this.el).position;\n    if (position === 'static' || !position) {\n      this.renderer.setStyle(this.el, 'position', 'relative');\n    }\n  }\n\n  calcSize(width: number, height: number, ratio: number): NzResizeEvent {\n    let newWidth: number;\n    let newHeight: number;\n    let maxWidth: number;\n    let maxHeight: number;\n    let col = 0;\n    let spanWidth = 0;\n    let minWidth = this.nzMinWidth;\n    let boundWidth = Infinity;\n    let boundHeight = Infinity;\n    if (this.nzBounds === 'parent') {\n      const parent = this.renderer.parentNode(this.el);\n      if (parent instanceof HTMLElement) {\n        const parentRect = parent.getBoundingClientRect();\n        boundWidth = parentRect.width;\n        boundHeight = parentRect.height;\n      }\n    } else if (this.nzBounds === 'window') {\n      if (typeof window !== 'undefined') {\n        boundWidth = window.innerWidth;\n        boundHeight = window.innerHeight;\n      }\n    } else if (this.nzBounds && this.nzBounds.nativeElement && this.nzBounds.nativeElement instanceof HTMLElement) {\n      const boundsRect = this.nzBounds.nativeElement.getBoundingClientRect();\n      boundWidth = boundsRect.width;\n      boundHeight = boundsRect.height;\n    }\n\n    maxWidth = ensureInBounds(this.nzMaxWidth!, boundWidth);\n    // eslint-disable-next-line prefer-const\n    maxHeight = ensureInBounds(this.nzMaxHeight!, boundHeight);\n\n    if (this.nzGridColumnCount !== -1) {\n      spanWidth = maxWidth / this.nzGridColumnCount;\n      minWidth = this.nzMinColumn !== -1 ? spanWidth * this.nzMinColumn : minWidth;\n      maxWidth = this.nzMaxColumn !== -1 ? spanWidth * this.nzMaxColumn : maxWidth;\n    }\n\n    if (ratio !== -1) {\n      if (/(left|right)/i.test(this.currentHandleEvent!.direction)) {\n        newWidth = Math.min(Math.max(width, minWidth), maxWidth);\n        newHeight = Math.min(Math.max(newWidth / ratio, this.nzMinHeight), maxHeight);\n        if (newHeight >= maxHeight || newHeight <= this.nzMinHeight) {\n          newWidth = Math.min(Math.max(newHeight * ratio, minWidth), maxWidth);\n        }\n      } else {\n        newHeight = Math.min(Math.max(height, this.nzMinHeight), maxHeight);\n        newWidth = Math.min(Math.max(newHeight * ratio, minWidth), maxWidth);\n        if (newWidth >= maxWidth || newWidth <= minWidth) {\n          newHeight = Math.min(Math.max(newWidth / ratio, this.nzMinHeight), maxHeight);\n        }\n      }\n    } else {\n      newWidth = Math.min(Math.max(width, minWidth), maxWidth);\n      newHeight = Math.min(Math.max(height, this.nzMinHeight), maxHeight);\n    }\n\n    if (this.nzGridColumnCount !== -1) {\n      col = Math.round(newWidth / spanWidth);\n      newWidth = col * spanWidth;\n    }\n\n    return {\n      col,\n      width: newWidth,\n      height: newHeight\n    };\n  }\n\n  resize(event: MouseEvent | TouchEvent): void {\n    const elRect = this.elRect;\n    const resizeEvent = getEventWithPoint(event);\n    const handleEvent = getEventWithPoint(this.currentHandleEvent!.mouseEvent);\n    let width = elRect.width;\n    let height = elRect.height;\n    const ratio = this.nzLockAspectRatio ? width / height : -1;\n    switch (this.currentHandleEvent!.direction) {\n      case 'bottomRight':\n        width = resizeEvent.clientX - elRect.left;\n        height = resizeEvent.clientY - elRect.top;\n        break;\n      case 'bottomLeft':\n        width = elRect.width + handleEvent.clientX - resizeEvent.clientX;\n        height = resizeEvent.clientY - elRect.top;\n        break;\n      case 'topRight':\n        width = resizeEvent.clientX - elRect.left;\n        height = elRect.height + handleEvent.clientY - resizeEvent.clientY;\n        break;\n      case 'topLeft':\n        width = elRect.width + handleEvent.clientX - resizeEvent.clientX;\n        height = elRect.height + handleEvent.clientY - resizeEvent.clientY;\n        break;\n      case 'top':\n        height = elRect.height + handleEvent.clientY - resizeEvent.clientY;\n        break;\n      case 'right':\n        width = resizeEvent.clientX - elRect.left;\n        break;\n      case 'bottom':\n        height = resizeEvent.clientY - elRect.top;\n        break;\n      case 'left':\n        width = elRect.width + handleEvent.clientX - resizeEvent.clientX;\n    }\n    const size = this.calcSize(width, height, ratio);\n    this.sizeCache = { ...size };\n    // Re-enter the Angular zone and run the change detection only if there're any `nzResize` listeners,\n    // e.g.: `<div nz-resizable (nzResize)=\"...\"></div>`.\n    if (this.nzResize.observers.length) {\n      this.ngZone.run(() => {\n        this.nzResize.emit({\n          ...size,\n          mouseEvent: event,\n          direction: this.currentHandleEvent!.direction\n        });\n      });\n    }\n    if (this.nzPreview) {\n      this.previewResize(size);\n    }\n  }\n\n  endResize(event: MouseEvent | TouchEvent): void {\n    this.removeGhostElement();\n    const size = this.sizeCache\n      ? { ...this.sizeCache }\n      : {\n          width: this.elRect.width,\n          height: this.elRect.height\n        };\n    // Re-enter the Angular zone and run the change detection only if there're any `nzResizeEnd` listeners,\n    // e.g.: `<div nz-resizable (nzResizeEnd)=\"...\"></div>`.\n    if (this.nzResizeEnd.observers.length) {\n      this.ngZone.run(() => {\n        this.nzResizeEnd.emit({\n          ...size,\n          mouseEvent: event,\n          direction: this.currentHandleEvent!.direction\n        });\n      });\n    }\n    this.sizeCache = null;\n    this.currentHandleEvent = null;\n  }\n\n  previewResize({ width, height }: NzResizeEvent): void {\n    this.createGhostElement();\n    this.renderer.setStyle(this.ghostElement, 'width', `${width}px`);\n    this.renderer.setStyle(this.ghostElement, 'height', `${height}px`);\n  }\n\n  createGhostElement(): void {\n    if (!this.ghostElement) {\n      this.ghostElement = this.renderer.createElement('div');\n      this.renderer.setAttribute(this.ghostElement, 'class', 'nz-resizable-preview');\n    }\n    this.renderer.appendChild(this.el, this.ghostElement);\n  }\n\n  removeGhostElement(): void {\n    if (this.ghostElement) {\n      this.renderer.removeChild(this.el, this.ghostElement);\n    }\n  }\n\n  ngAfterViewInit(): void {\n    if (!this.platform.isBrowser) {\n      return;\n    }\n\n    this.el = this.elementRef.nativeElement;\n    this.setPosition();\n\n    fromEventOutsideAngular(this.el, 'mouseenter')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.nzResizableService.mouseEnteredOutsideAngular$.next(true);\n      });\n\n    fromEventOutsideAngular(this.el, 'mouseleave')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.nzResizableService.mouseEnteredOutsideAngular$.next(false);\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.ghostElement = null;\n    this.sizeCache = null;\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  EventEmitter,\n  HostListener,\n  Input,\n  OnInit,\n  Output,\n  Renderer2\n} from '@angular/core';\nimport { merge } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\n\nimport { NzResizableService } from './resizable.service';\n\nexport type NzCursorType = 'window' | 'grid';\n\nexport type NzResizeDirection =\n  | 'top'\n  | 'right'\n  | 'bottom'\n  | 'left'\n  | 'topRight'\n  | 'bottomRight'\n  | 'bottomLeft'\n  | 'topLeft';\n\nexport class NzResizeHandleMouseDownEvent {\n  constructor(\n    public direction: NzResizeDirection,\n    public mouseEvent: MouseEvent | TouchEvent\n  ) {}\n}\n\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({ passive: true }) as AddEventListenerOptions;\n\n@Component({\n  selector: 'nz-resize-handle, [nz-resize-handle]',\n  exportAs: 'nzResizeHandle',\n  template: ` <ng-content></ng-content> `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    class: 'nz-resizable-handle',\n    '[class.nz-resizable-handle-top]': `nzDirection === 'top'`,\n    '[class.nz-resizable-handle-right]': `nzDirection === 'right'`,\n    '[class.nz-resizable-handle-bottom]': `nzDirection === 'bottom'`,\n    '[class.nz-resizable-handle-left]': `nzDirection === 'left'`,\n    '[class.nz-resizable-handle-topRight]': `nzDirection === 'topRight'`,\n    '[class.nz-resizable-handle-bottomRight]': `nzDirection === 'bottomRight'`,\n    '[class.nz-resizable-handle-bottomLeft]': `nzDirection === 'bottomLeft'`,\n    '[class.nz-resizable-handle-topLeft]': `nzDirection === 'topLeft'`,\n    '[class.nz-resizable-handle-cursor-type-grid]': `nzCursorType === 'grid'`,\n    '[class.nz-resizable-handle-cursor-type-window]': `nzCursorType === 'window'`\n  },\n  providers: [NzDestroyService]\n})\nexport class NzResizeHandleComponent implements OnInit {\n  @Input() nzDirection: NzResizeDirection = 'bottomRight';\n  @Input() nzCursorType: NzCursorType = 'window';\n  @Output() readonly nzMouseDown = new EventEmitter<NzResizeHandleMouseDownEvent>();\n\n  constructor(\n    private nzResizableService: NzResizableService,\n    private renderer: Renderer2,\n    private host: ElementRef<HTMLElement>,\n    private destroy$: NzDestroyService\n  ) {}\n\n  ngOnInit(): void {\n    this.nzResizableService.mouseEnteredOutsideAngular$.pipe(takeUntil(this.destroy$)).subscribe(entered => {\n      if (entered) {\n        this.renderer.addClass(this.host.nativeElement, 'nz-resizable-handle-box-hover');\n      } else {\n        this.renderer.removeClass(this.host.nativeElement, 'nz-resizable-handle-box-hover');\n      }\n    });\n\n    // Note: since Chrome 56 defaults document level `touchstart` listener to passive.\n    // The element `touchstart` listener is not passive by default\n    // We never call `preventDefault()` on it, so we're safe making it passive too.\n    merge(\n      fromEventOutsideAngular<MouseEvent>(this.host.nativeElement, 'mousedown', passiveEventListenerOptions),\n      fromEventOutsideAngular<TouchEvent>(this.host.nativeElement, 'touchstart', passiveEventListenerOptions)\n    )\n      .pipe(takeUntil(this.destroy$))\n      .subscribe((event: MouseEvent | TouchEvent) => {\n        this.nzResizableService.handleMouseDownOutsideAngular$.next(\n          new NzResizeHandleMouseDownEvent(this.nzDirection, event)\n        );\n      });\n  }\n\n  @HostListener('pointerdown', ['$event'])\n  onPointerDown(event: PointerEvent): void {\n    (event.target as HTMLElement).setPointerCapture(event.pointerId);\n  }\n\n  @HostListener('pointerup', ['$event'])\n  onPointerUp(event: PointerEvent): void {\n    (event.target as HTMLElement).releasePointerCapture(event.pointerId);\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { ChangeDetectionStrategy, Component, Input, OnChanges, SimpleChanges } from '@angular/core';\n\nimport { NzCursorType, NzResizeDirection, NzResizeHandleComponent } from './resize-handle.component';\n\nexport const DEFAULT_RESIZE_DIRECTION: NzResizeDirection[] = [\n  'bottomRight',\n  'topRight',\n  'bottomLeft',\n  'topLeft',\n  'bottom',\n  'right',\n  'top',\n  'left'\n];\n\nexport interface NzResizeHandleOption {\n  direction: NzResizeDirection;\n  cursorType: NzCursorType;\n}\n\nfunction normalizeResizeHandleOptions(value: Array<NzResizeDirection | NzResizeHandleOption>): NzResizeHandleOption[] {\n  return value.map(val => {\n    if (typeof val === 'string') {\n      return {\n        direction: val,\n        cursorType: 'window'\n      };\n    }\n\n    return val;\n  });\n}\n\n@Component({\n  selector: 'nz-resize-handles',\n  exportAs: 'nzResizeHandles',\n  template: `\n    @for (option of resizeHandleOptions; track option) {\n      <nz-resize-handle [nzDirection]=\"option.direction\" [nzCursorType]=\"option.cursorType\" />\n    }\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [NzResizeHandleComponent]\n})\nexport class NzResizeHandlesComponent implements OnChanges {\n  @Input() nzDirections: Array<NzResizeDirection | NzResizeHandleOption> = DEFAULT_RESIZE_DIRECTION;\n\n  resizeHandleOptions = normalizeResizeHandleOptions(this.nzDirections);\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes.nzDirections) {\n      this.resizeHandleOptions = normalizeResizeHandleOptions(changes.nzDirections.currentValue);\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzResizableDirective } from './resizable.directive';\nimport { NzResizeHandleComponent } from './resize-handle.component';\nimport { NzResizeHandlesComponent } from './resize-handles.component';\n\n@NgModule({\n  imports: [NzResizableDirective, NzResizeHandleComponent, NzResizeHandlesComponent],\n  exports: [NzResizableDirective, NzResizeHandleComponent, NzResizeHandlesComponent]\n})\nexport class NzResizableModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './resizable.module';\nexport * from './resizable.directive';\nexport * from './resizable.service';\nexport * from './resize-handles.component';\nexport * from './resize-handle.component';\nexport * from './resizable-utils';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1.NzResizableService", "i2"], "mappings": ";;;;;;;;;;;AAAA;;;AAGG;AAIG,SAAU,iBAAiB,CAAC,KAA8B,EAAA;IAC9D,OAAO,YAAY,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,GAAI,KAAoB;AAClG;;ACTA;;;AAGG;MAWU,kBAAkB,CAAA;AAmBT,IAAA,MAAA;AAlBZ,IAAA,QAAQ,GAAa,MAAM,CAAC,QAAQ,CAAC;AACrC,IAAA,SAAS,GAAG,IAAI,GAAG,EAAoD;AAE/E;;;;;;;;;AASG;AACH,IAAA,8BAA8B,GAAG,IAAI,OAAO,EAAgC;AAC5E,IAAA,8BAA8B,GAAG,IAAI,OAAO,EAAkC;AAC9E,IAAA,gCAAgC,GAAG,IAAI,OAAO,EAA2B;AACzE,IAAA,2BAA2B,GAAG,IAAI,OAAO,EAAW;AAEpD,IAAA,WAAA,CAAoB,MAAc,EAAA;QAAd,IAAM,CAAA,MAAA,GAAN,MAAM;;AAE1B,IAAA,aAAa,CAAC,KAA8B,EAAA;AAC1C,QAAA,MAAM,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC;QACzC,IAAI,CAAC,cAAc,EAAE;QACrB,MAAM,SAAS,GAAG,aAAa,GAAG,WAAW,GAAG,WAAW;QAC3D,MAAM,OAAO,GAAG,aAAa,GAAG,UAAU,GAAG,SAAS;AACtD,QAAA,MAAM,gBAAgB,GAAG,CAAC,CAA0B,KAAU;AAC5D,YAAA,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/C,SAAC;AACD,QAAA,MAAM,cAAc,GAAG,CAAC,CAA0B,KAAU;AAC1D,YAAA,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,cAAc,EAAE;AACvB,SAAC;QAED,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,gBAAgB,CAAC;QAC/C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,cAAc,CAAC;AAE3C,QAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAK;YACjC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,IAAI,KAAI;gBACvC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAwB,CAAC;AAChE,aAAC,CAAC;AACJ,SAAC,CAAC;;IAGI,cAAc,GAAA;QACpB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,IAAI,KAAI;YACvC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAwB,CAAC;AACnE,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;;IAGxB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,8BAA8B,CAAC,QAAQ,EAAE;AAC9C,QAAA,IAAI,CAAC,8BAA8B,CAAC,QAAQ,EAAE;AAC9C,QAAA,IAAI,CAAC,gCAAgC,CAAC,QAAQ,EAAE;AAChD,QAAA,IAAI,CAAC,2BAA2B,CAAC,QAAQ,EAAE;QAC3C,IAAI,CAAC,cAAc,EAAE;;uGAxDZ,kBAAkB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAAlB,kBAAkB,EAAA,CAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAD9B;;;MCiCY,oBAAoB,CAAA;AAwBrB,IAAA,UAAA;AACA,IAAA,QAAA;AACA,IAAA,kBAAA;AACA,IAAA,QAAA;AACA,IAAA,MAAA;AACA,IAAA,QAAA;IA5BD,QAAQ,GAAkD,QAAQ;AAClE,IAAA,WAAW;AACX,IAAA,UAAU;IACoB,WAAW,GAAW,EAAE;IACxB,UAAU,GAAW,EAAE;IACvB,iBAAiB,GAAW,CAAC,CAAC;IAC9B,WAAW,GAAW,CAAC,CAAC;IACxB,WAAW,GAAW,CAAC,CAAC;IACvB,iBAAiB,GAAY,KAAK;IAClC,SAAS,GAAY,KAAK;IAC1B,UAAU,GAAY,KAAK;AAChD,IAAA,QAAQ,GAAG,IAAI,YAAY,EAAiB;AAC5C,IAAA,WAAW,GAAG,IAAI,YAAY,EAAiB;AAC/C,IAAA,aAAa,GAAG,IAAI,YAAY,EAAiB;IAEpE,QAAQ,GAAG,KAAK;AACR,IAAA,MAAM;IACN,kBAAkB,GAAwC,IAAI;IAC9D,YAAY,GAA0B,IAAI;AAC1C,IAAA,EAAE;IACF,SAAS,GAAyB,IAAI;IAE9C,WACU,CAAA,UAAmC,EACnC,QAAmB,EACnB,kBAAsC,EACtC,QAAkB,EAClB,MAAc,EACd,QAA0B,EAAA;QAL1B,IAAU,CAAA,UAAA,GAAV,UAAU;QACV,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB;QAClB,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAQ,CAAA,QAAA,GAAR,QAAQ;AAEhB,QAAA,IAAI,CAAC,kBAAkB,CAAC,8BAA8B,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AACtG,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB;;AAEF,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;YACpB,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC;AACvD,YAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK;YAC/B,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE;AACvC,gBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;;YAE9G,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,qBAAqB,EAAE;AAC/C,SAAC,CAAC;QAEF,IAAI,CAAC,kBAAkB,CAAC;AACrB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC;aAC9C,SAAS,CAAC,KAAK,IAAG;AACjB,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,gBAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;gBACrB,IAAI,CAAC,kBAAkB,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC;AACjE,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;AAEzB,SAAC,CAAC;AAEJ,QAAA,IAAI,CAAC,kBAAkB,CAAC,gCAAgC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AACxG,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;;AAEtB,SAAC,CAAC;;IAGJ,WAAW,GAAA;QACT,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ;AACnD,QAAA,IAAI,QAAQ,KAAK,QAAQ,IAAI,CAAC,QAAQ,EAAE;AACtC,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,UAAU,CAAC;;;AAI3D,IAAA,QAAQ,CAAC,KAAa,EAAE,MAAc,EAAE,KAAa,EAAA;AACnD,QAAA,IAAI,QAAgB;AACpB,QAAA,IAAI,SAAiB;AACrB,QAAA,IAAI,QAAgB;AACpB,QAAA,IAAI,SAAiB;QACrB,IAAI,GAAG,GAAG,CAAC;QACX,IAAI,SAAS,GAAG,CAAC;AACjB,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU;QAC9B,IAAI,UAAU,GAAG,QAAQ;QACzB,IAAI,WAAW,GAAG,QAAQ;AAC1B,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;AAC9B,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;AAChD,YAAA,IAAI,MAAM,YAAY,WAAW,EAAE;AACjC,gBAAA,MAAM,UAAU,GAAG,MAAM,CAAC,qBAAqB,EAAE;AACjD,gBAAA,UAAU,GAAG,UAAU,CAAC,KAAK;AAC7B,gBAAA,WAAW,GAAG,UAAU,CAAC,MAAM;;;AAE5B,aAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;AACrC,YAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACjC,gBAAA,UAAU,GAAG,MAAM,CAAC,UAAU;AAC9B,gBAAA,WAAW,GAAG,MAAM,CAAC,WAAW;;;AAE7B,aAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,YAAY,WAAW,EAAE;YAC7G,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,qBAAqB,EAAE;AACtE,YAAA,UAAU,GAAG,UAAU,CAAC,KAAK;AAC7B,YAAA,WAAW,GAAG,UAAU,CAAC,MAAM;;QAGjC,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC,UAAW,EAAE,UAAU,CAAC;;QAEvD,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,WAAY,EAAE,WAAW,CAAC;AAE1D,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,EAAE;AACjC,YAAA,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,iBAAiB;AAC7C,YAAA,QAAQ,GAAG,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ;AAC5E,YAAA,QAAQ,GAAG,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ;;AAG9E,QAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAmB,CAAC,SAAS,CAAC,EAAE;AAC5D,gBAAA,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC;gBACxD,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC;gBAC7E,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;AAC3D,oBAAA,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,KAAK,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC;;;iBAEjE;AACL,gBAAA,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC;AACnE,gBAAA,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,KAAK,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC;gBACpE,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,EAAE;oBAChD,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC;;;;aAG5E;AACL,YAAA,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC;AACxD,YAAA,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC;;AAGrE,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,EAAE;YACjC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC;AACtC,YAAA,QAAQ,GAAG,GAAG,GAAG,SAAS;;QAG5B,OAAO;YACL,GAAG;AACH,YAAA,KAAK,EAAE,QAAQ;AACf,YAAA,MAAM,EAAE;SACT;;AAGH,IAAA,MAAM,CAAC,KAA8B,EAAA;AACnC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM;AAC1B,QAAA,MAAM,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC;QAC5C,MAAM,WAAW,GAAG,iBAAiB,CAAC,IAAI,CAAC,kBAAmB,CAAC,UAAU,CAAC;AAC1E,QAAA,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK;AACxB,QAAA,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM;AAC1B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,GAAG,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;AAC1D,QAAA,QAAQ,IAAI,CAAC,kBAAmB,CAAC,SAAS;AACxC,YAAA,KAAK,aAAa;gBAChB,KAAK,GAAG,WAAW,CAAC,OAAO,GAAG,MAAM,CAAC,IAAI;gBACzC,MAAM,GAAG,WAAW,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG;gBACzC;AACF,YAAA,KAAK,YAAY;AACf,gBAAA,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO;gBAChE,MAAM,GAAG,WAAW,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG;gBACzC;AACF,YAAA,KAAK,UAAU;gBACb,KAAK,GAAG,WAAW,CAAC,OAAO,GAAG,MAAM,CAAC,IAAI;AACzC,gBAAA,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO;gBAClE;AACF,YAAA,KAAK,SAAS;AACZ,gBAAA,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO;AAChE,gBAAA,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO;gBAClE;AACF,YAAA,KAAK,KAAK;AACR,gBAAA,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO;gBAClE;AACF,YAAA,KAAK,OAAO;gBACV,KAAK,GAAG,WAAW,CAAC,OAAO,GAAG,MAAM,CAAC,IAAI;gBACzC;AACF,YAAA,KAAK,QAAQ;gBACX,MAAM,GAAG,WAAW,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG;gBACzC;AACF,YAAA,KAAK,MAAM;AACT,gBAAA,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO;;AAEpE,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;AAChD,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE;;;QAG5B,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE;AAClC,YAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAK;AACnB,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,oBAAA,GAAG,IAAI;AACP,oBAAA,UAAU,EAAE,KAAK;AACjB,oBAAA,SAAS,EAAE,IAAI,CAAC,kBAAmB,CAAC;AACrC,iBAAA,CAAC;AACJ,aAAC,CAAC;;AAEJ,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;;;AAI5B,IAAA,SAAS,CAAC,KAA8B,EAAA;QACtC,IAAI,CAAC,kBAAkB,EAAE;AACzB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC;AAChB,cAAE,EAAE,GAAG,IAAI,CAAC,SAAS;AACrB,cAAE;AACE,gBAAA,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;AACxB,gBAAA,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC;aACrB;;;QAGL,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE;AACrC,YAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAK;AACnB,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AACpB,oBAAA,GAAG,IAAI;AACP,oBAAA,UAAU,EAAE,KAAK;AACjB,oBAAA,SAAS,EAAE,IAAI,CAAC,kBAAmB,CAAC;AACrC,iBAAA,CAAC;AACJ,aAAC,CAAC;;AAEJ,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;;AAGhC,IAAA,aAAa,CAAC,EAAE,KAAK,EAAE,MAAM,EAAiB,EAAA;QAC5C,IAAI,CAAC,kBAAkB,EAAE;AACzB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,CAAA,EAAG,KAAK,CAAA,EAAA,CAAI,CAAC;AAChE,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,EAAE,CAAA,EAAG,MAAM,CAAA,EAAA,CAAI,CAAC;;IAGpE,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACtD,YAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,sBAAsB,CAAC;;AAEhF,QAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC;;IAGvD,kBAAkB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC;;;IAIzD,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YAC5B;;QAGF,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;QACvC,IAAI,CAAC,WAAW,EAAE;AAElB,QAAA,uBAAuB,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY;AAC1C,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC7B,SAAS,CAAC,MAAK;YACd,IAAI,CAAC,kBAAkB,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC;AAChE,SAAC,CAAC;AAEJ,QAAA,uBAAuB,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY;AAC1C,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC7B,SAAS,CAAC,MAAK;YACd,IAAI,CAAC,kBAAkB,CAAC,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC;AACjE,SAAC,CAAC;;IAGN,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;uGAjQZ,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,kBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAApB,oBAAoB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAIX,eAAe,CACf,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,eAAe,iEACf,eAAe,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EACf,eAAe,CACf,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,eAAe,iEACf,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAChB,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,iQAlBzB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAOtC,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAVhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,SAAS,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;AACjD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,cAAc;AACrB,wBAAA,+BAA+B,EAAE,UAAU;AAC3C,wBAAA,+BAA+B,EAAE;AAClC;AACF,iBAAA;gNAEU,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACsC,WAAW,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACE,iBAAiB,EAAA,CAAA;sBAAvD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACG,iBAAiB,EAAA,CAAA;sBAAxD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACnB,QAAQ,EAAA,CAAA;sBAA1B;gBACkB,WAAW,EAAA,CAAA;sBAA7B;gBACkB,aAAa,EAAA,CAAA;sBAA/B;;;AC5DH;;;AAGG;MAkCU,4BAA4B,CAAA;AAE9B,IAAA,SAAA;AACA,IAAA,UAAA;IAFT,WACS,CAAA,SAA4B,EAC5B,UAAmC,EAAA;QADnC,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAU,CAAA,UAAA,GAAV,UAAU;;AAEpB;AAED,MAAM,2BAA2B,GAAG,+BAA+B,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAA4B;MAsBpG,uBAAuB,CAAA;AAMxB,IAAA,kBAAA;AACA,IAAA,QAAA;AACA,IAAA,IAAA;AACA,IAAA,QAAA;IARD,WAAW,GAAsB,aAAa;IAC9C,YAAY,GAAiB,QAAQ;AAC3B,IAAA,WAAW,GAAG,IAAI,YAAY,EAAgC;AAEjF,IAAA,WAAA,CACU,kBAAsC,EACtC,QAAmB,EACnB,IAA6B,EAC7B,QAA0B,EAAA;QAH1B,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB;QAClB,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAQ,CAAA,QAAA,GAAR,QAAQ;;IAGlB,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,kBAAkB,CAAC,2BAA2B,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,IAAG;YACrG,IAAI,OAAO,EAAE;AACX,gBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,+BAA+B,CAAC;;iBAC3E;AACL,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,+BAA+B,CAAC;;AAEvF,SAAC,CAAC;;;;QAKF,KAAK,CACH,uBAAuB,CAAa,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,2BAA2B,CAAC,EACtG,uBAAuB,CAAa,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,EAAE,2BAA2B,CAAC;AAEtG,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7B,aAAA,SAAS,CAAC,CAAC,KAA8B,KAAI;AAC5C,YAAA,IAAI,CAAC,kBAAkB,CAAC,8BAA8B,CAAC,IAAI,CACzD,IAAI,4BAA4B,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAC1D;AACH,SAAC,CAAC;;AAIN,IAAA,aAAa,CAAC,KAAmB,EAAA;QAC9B,KAAK,CAAC,MAAsB,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC;;AAIlE,IAAA,WAAW,CAAC,KAAmB,EAAA;QAC5B,KAAK,CAAC,MAAsB,CAAC,qBAAqB,CAAC,KAAK,CAAC,SAAS,CAAC;;uGA3C3D,uBAAuB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,kBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,uBAAuB,EAFvB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sCAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,aAAA,EAAA,uBAAA,EAAA,WAAA,EAAA,qBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,+BAAA,EAAA,uBAAA,EAAA,iCAAA,EAAA,yBAAA,EAAA,kCAAA,EAAA,0BAAA,EAAA,gCAAA,EAAA,wBAAA,EAAA,oCAAA,EAAA,4BAAA,EAAA,uCAAA,EAAA,+BAAA,EAAA,sCAAA,EAAA,8BAAA,EAAA,mCAAA,EAAA,2BAAA,EAAA,4CAAA,EAAA,yBAAA,EAAA,8CAAA,EAAA,2BAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,SAAA,EAAA,CAAC,gBAAgB,CAAC,wDAfnB,CAA6B,2BAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAiB5B,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBApBnC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sCAAsC;AAChD,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,CAA6B,2BAAA,CAAA;oBACvC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,qBAAqB;AAC5B,wBAAA,iCAAiC,EAAE,CAAuB,qBAAA,CAAA;AAC1D,wBAAA,mCAAmC,EAAE,CAAyB,uBAAA,CAAA;AAC9D,wBAAA,oCAAoC,EAAE,CAA0B,wBAAA,CAAA;AAChE,wBAAA,kCAAkC,EAAE,CAAwB,sBAAA,CAAA;AAC5D,wBAAA,sCAAsC,EAAE,CAA4B,0BAAA,CAAA;AACpE,wBAAA,yCAAyC,EAAE,CAA+B,6BAAA,CAAA;AAC1E,wBAAA,wCAAwC,EAAE,CAA8B,4BAAA,CAAA;AACxE,wBAAA,qCAAqC,EAAE,CAA2B,yBAAA,CAAA;AAClE,wBAAA,8CAA8C,EAAE,CAAyB,uBAAA,CAAA;AACzE,wBAAA,gDAAgD,EAAE,CAA2B,yBAAA;AAC9E,qBAAA;oBACD,SAAS,EAAE,CAAC,gBAAgB;AAC7B,iBAAA;oKAEU,WAAW,EAAA,CAAA;sBAAnB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACkB,WAAW,EAAA,CAAA;sBAA7B;gBAkCD,aAAa,EAAA,CAAA;sBADZ,YAAY;uBAAC,aAAa,EAAE,CAAC,QAAQ,CAAC;gBAMvC,WAAW,EAAA,CAAA;sBADV,YAAY;uBAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;;;AC3GvC;;;AAGG;AAMU,MAAA,wBAAwB,GAAwB;IAC3D,aAAa;IACb,UAAU;IACV,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,OAAO;IACP,KAAK;IACL;;AAQF,SAAS,4BAA4B,CAAC,KAAsD,EAAA;AAC1F,IAAA,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG,IAAG;AACrB,QAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,OAAO;AACL,gBAAA,SAAS,EAAE,GAAG;AACd,gBAAA,UAAU,EAAE;aACb;;AAGH,QAAA,OAAO,GAAG;AACZ,KAAC,CAAC;AACJ;MAaa,wBAAwB,CAAA;IAC1B,YAAY,GAAoD,wBAAwB;AAEjG,IAAA,mBAAmB,GAAG,4BAA4B,CAAC,IAAI,CAAC,YAAY,CAAC;AAErE,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,OAAO,CAAC,YAAY,EAAE;YACxB,IAAI,CAAC,mBAAmB,GAAG,4BAA4B,CAAC,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC;;;uGAPnF,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAxB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,wBAAwB,EARzB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;AAIT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAES,uBAAuB,EAAA,QAAA,EAAA,sCAAA,EAAA,MAAA,EAAA,CAAA,aAAA,EAAA,cAAA,CAAA,EAAA,OAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAEtB,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBAXpC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,QAAQ,EAAE;;;;AAIT,EAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,OAAO,EAAE,CAAC,uBAAuB;AAClC,iBAAA;8BAEU,YAAY,EAAA,CAAA;sBAApB;;;AClDH;;;AAGG;MAYU,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAjB,iBAAiB,EAAA,OAAA,EAAA,CAHlB,oBAAoB,EAAE,uBAAuB,EAAE,wBAAwB,CAAA,EAAA,OAAA,EAAA,CACvE,oBAAoB,EAAE,uBAAuB,EAAE,wBAAwB,CAAA,EAAA,CAAA;wGAEtE,iBAAiB,EAAA,CAAA;;2FAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAJ7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,oBAAoB,EAAE,uBAAuB,EAAE,wBAAwB,CAAC;AAClF,oBAAA,OAAO,EAAE,CAAC,oBAAoB,EAAE,uBAAuB,EAAE,wBAAwB;AAClF,iBAAA;;;ACdD;;;AAGG;;ACHH;;AAEG;;;;"}