{"version": 3, "file": "ng-zorro-antd-tabs.mjs", "sources": ["../../components/tabs/interfaces.ts", "../../components/tabs/tab-add-button.component.ts", "../../components/tabs/tab-bar-extra-content.directive.ts", "../../components/tabs/tab-body.component.ts", "../../components/tabs/tab-close-button.component.ts", "../../components/tabs/tab-link.directive.ts", "../../components/tabs/tab-nav-item.directive.ts", "../../components/tabs/tab-nav-operation.component.ts", "../../components/tabs/tab-scroll-list.directive.ts", "../../components/tabs/tabs-ink-bar.directive.ts", "../../components/tabs/tab-nav-bar.component.ts", "../../components/tabs/tab.directive.ts", "../../components/tabs/tab.component.ts", "../../components/tabs/tabset.component.ts", "../../components/tabs/tabs.module.ts", "../../components/tabs/public-api.ts", "../../components/tabs/ng-zorro-antd-tabs.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Observable } from 'rxjs';\n\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\nexport type NzTabPosition = 'top' | 'bottom' | 'left' | 'right';\nexport type NzTabType = 'line' | 'card' | 'editable-card';\nexport type NzTabsCanDeactivateFn = (\n  fromIndex: number,\n  toIndex: number\n) => Observable<boolean> | Promise<boolean> | boolean;\nexport type NzTabPositionMode = 'horizontal' | 'vertical';\n\nexport interface NzAnimatedInterface {\n  inkBar: boolean;\n  tabPane: boolean;\n}\n\nexport class NzTabChangeEvent {\n  index?: number;\n  tab: NzSafeAny;\n}\n\nexport interface NzTabScrollListOffset {\n  x: number;\n  y: number;\n}\n\nexport type NzTabScrollListOffsetEvent = NzTabScrollListOffset & { event: Event };\n\ninterface NzTabWheelScrollEvent {\n  type: 'wheel';\n  event: WheelEvent;\n}\n\ninterface NzTabTouchScrollEvent {\n  type: 'touchstart' | 'touchmove' | 'touchend';\n  event: TouchEvent;\n}\n\nexport type NzTabScrollEvent = NzTabTouchScrollEvent | NzTabWheelScrollEvent;\nexport type NzTabScrollEventHandlerFun<T extends NzTabScrollEvent['event']> = (event: T) => void;\n\nexport interface TabTemplateContext {\n  visible: boolean;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Component, ElementRef, Input, TemplateRef } from '@angular/core';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\n@Component({\n  selector: 'nz-tab-add-button, button[nz-tab-add-button]',\n  template: `\n    <ng-container *nzStringTemplateOutlet=\"addIcon; let icon\">\n      <nz-icon [nzType]=\"icon\" nzTheme=\"outline\" />\n    </ng-container>\n  `,\n  host: {\n    class: 'ant-tabs-nav-add',\n    'aria-label': 'Add tab',\n    type: 'button'\n  },\n  imports: [NzOutletModule, NzIconModule]\n})\nexport class NzTabAddButtonComponent {\n  @Input() addIcon: string | TemplateRef<NzSafeAny> = 'plus';\n\n  private readonly element: HTMLElement;\n\n  constructor(private elementRef: ElementRef<HTMLElement>) {\n    this.element = this.elementRef.nativeElement;\n  }\n\n  getElementWidth(): number {\n    return this.element?.offsetWidth || 0;\n  }\n\n  getElementHeight(): number {\n    return this.element?.offsetHeight || 0;\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Directive, inject, input, TemplateRef } from '@angular/core';\n\n@Directive({\n  selector: '[nzTabBarExtraContent]:not(nz-tabset)'\n})\nexport class NzTabBarExtraContentDirective {\n  readonly position = input<'start' | 'end'>('end', { alias: 'nzTabBarExtraContent' });\n  readonly templateRef = inject(TemplateRef);\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgTemplateOutlet } from '@angular/common';\nimport { ChangeDetectionStrategy, Component, Input, TemplateRef, ViewEncapsulation } from '@angular/core';\n\nimport { tabSwitchMotion } from 'ng-zorro-antd/core/animation';\n\n@Component({\n  selector: '[nz-tab-body]',\n  exportAs: 'nzTabBody',\n  preserveWhitespaces: false,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  template: ` <ng-template [ngTemplateOutlet]=\"content\"></ng-template> `,\n  host: {\n    class: 'ant-tabs-tabpane',\n    '[class.ant-tabs-tabpane-active]': 'active',\n    '[class.ant-tabs-tabpane-hidden]': 'animated ? null : !active',\n    '[attr.tabindex]': 'active ? 0 : -1',\n    '[attr.aria-hidden]': '!active',\n    '[style.overflow-y]': 'animated ? active ? null : \"none\" : null',\n    '[@tabSwitchMotion]': `active ? 'enter' : 'leave'`,\n    '[@.disabled]': `!animated`\n  },\n  imports: [NgTemplateOutlet],\n  animations: [tabSwitchMotion]\n})\nexport class NzTabBodyComponent {\n  @Input() content: TemplateRef<void> | null = null;\n  @Input() active = false;\n  @Input() animated = true;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Component, Input, TemplateRef } from '@angular/core';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\n@Component({\n  selector: 'nz-tab-close-button, button[nz-tab-close-button]',\n  template: `\n    <ng-container *nzStringTemplateOutlet=\"closeIcon; let icon\">\n      <nz-icon [nzType]=\"icon\" nzTheme=\"outline\" />\n    </ng-container>\n  `,\n  host: {\n    class: 'ant-tabs-tab-remove',\n    'aria-label': 'Close tab',\n    type: 'button'\n  },\n  imports: [NzOutletModule, NzIconModule]\n})\nexport class NzTabCloseButtonComponent {\n  @Input() closeIcon: string | TemplateRef<NzSafeAny> = 'close';\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Directive, ElementRef, TemplateRef, inject } from '@angular/core';\nimport { RouterLink } from '@angular/router';\n\nimport { TabTemplateContext } from './interfaces';\n\n/**\n * Fix https://github.com/angular/angular/issues/8563\n */\n@Directive({\n  selector: 'ng-template[nzTabLink]',\n  exportAs: 'nzTabLinkTemplate'\n})\nexport class NzTabLinkTemplateDirective {\n  templateRef: TemplateRef<TabTemplateContext> = inject(TemplateRef, { host: true });\n}\n\n/**\n * This component is for catching `routerLink` directive.\n */\n@Directive({\n  selector: 'a[nz-tab-link]',\n  exportAs: 'nzTabLink'\n})\nexport class NzTabLinkDirective {\n  routerLink = inject(RouterLink, { self: true, optional: true });\n\n  constructor(public elementRef: ElementRef<HTMLAnchorElement>) {}\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { FocusableOption } from '@angular/cdk/a11y';\nimport { Directive, ElementRef, Input, booleanAttribute } from '@angular/core';\n\nimport { NzTabComponent } from './tab.component';\n\n@Directive({\n  selector: '[nzTabNavItem]'\n})\nexport class NzTabNavItemDirective implements FocusableOption {\n  @Input({ transform: booleanAttribute }) disabled: boolean = false;\n  @Input() tab!: NzTabComponent;\n  @Input({ transform: booleanAttribute }) active: boolean = false;\n  private el!: HTMLElement;\n  private parentElement!: HTMLElement;\n\n  constructor(public elementRef: ElementRef<HTMLElement>) {\n    this.el = elementRef.nativeElement;\n    this.parentElement = this.el.parentElement!;\n  }\n\n  focus(): void {\n    this.el.focus();\n  }\n\n  get width(): number {\n    return this.parentElement.offsetWidth;\n  }\n\n  get height(): number {\n    return this.parentElement.offsetHeight;\n  }\n\n  get left(): number {\n    return this.parentElement.offsetLeft;\n  }\n\n  get top(): number {\n    return this.parentElement.offsetTop;\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnDestroy,\n  Output,\n  TemplateRef,\n  ViewEncapsulation,\n  booleanAttribute\n} from '@angular/core';\n\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\nimport { NzDropDownDirective, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzMenuModule } from 'ng-zorro-antd/menu';\n\nimport { NzTabAddButtonComponent } from './tab-add-button.component';\nimport { NzTabNavItemDirective } from './tab-nav-item.directive';\n\n@Component({\n  selector: 'nz-tab-nav-operation',\n  exportAs: 'nzTabNavOperation',\n  preserveWhitespaces: false,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  template: `\n    <button\n      nz-dropdown\n      class=\"ant-tabs-nav-more\"\n      type=\"button\"\n      tabindex=\"-1\"\n      aria-hidden=\"true\"\n      nzOverlayClassName=\"nz-tabs-dropdown\"\n      #dropdownTrigger=\"nzDropdown\"\n      [nzDropdownMenu]=\"menu\"\n      [nzOverlayStyle]=\"{ minWidth: '46px' }\"\n      [nzMatchWidthElement]=\"null\"\n      (nzVisibleChange)=\"menuVisChange($event)\"\n      (mouseenter)=\"showItems()\"\n    >\n      <nz-icon nzType=\"ellipsis\" />\n    </button>\n    <nz-dropdown-menu #menu=\"nzDropdownMenu\">\n      @if (menuOpened) {\n        <ul nz-menu>\n          @for (item of items; track item) {\n            <li\n              nz-menu-item\n              class=\"ant-tabs-dropdown-menu-item\"\n              [class.ant-tabs-dropdown-menu-item-disabled]=\"item.disabled\"\n              [nzSelected]=\"item.active\"\n              [nzDisabled]=\"item.disabled\"\n              (click)=\"onSelect(item)\"\n              (contextmenu)=\"onContextmenu(item, $event)\"\n            >\n              <ng-container *nzStringTemplateOutlet=\"item.tab.label; context: { visible: false }\">\n                {{ item.tab.label }}\n              </ng-container>\n            </li>\n          }\n        </ul>\n      }\n    </nz-dropdown-menu>\n    @if (addable) {\n      <button nz-tab-add-button [addIcon]=\"addIcon\" (click)=\"addClicked.emit()\"></button>\n    }\n  `,\n  host: {\n    class: 'ant-tabs-nav-operations',\n    '[class.ant-tabs-nav-operations-hidden]': 'items.length === 0'\n  },\n  imports: [\n    NzIconModule,\n    NzOutletModule,\n    NzTabAddButtonComponent,\n    NzDropdownMenuComponent,\n    NzMenuModule,\n    NzDropDownDirective\n  ]\n})\nexport class NzTabNavOperationComponent implements OnDestroy {\n  @Input() items: NzTabNavItemDirective[] = [];\n  @Input({ transform: booleanAttribute }) addable: boolean = false;\n  @Input() addIcon: string | TemplateRef<NzSafeAny> = 'plus';\n\n  @Output() readonly addClicked = new EventEmitter<void>();\n  @Output() readonly selected = new EventEmitter<NzTabNavItemDirective>();\n  closeAnimationWaitTimeoutId?: ReturnType<typeof setTimeout>;\n  menuOpened = false;\n\n  private readonly element: HTMLElement;\n  constructor(\n    public cdr: ChangeDetectorRef,\n    private elementRef: ElementRef<HTMLElement>\n  ) {\n    this.element = this.elementRef.nativeElement;\n  }\n\n  onSelect(item: NzTabNavItemDirective): void {\n    if (!item.disabled) {\n      // ignore nzCanDeactivate\n      item.tab.nzClick.emit();\n      this.selected.emit(item);\n    }\n  }\n\n  onContextmenu(item: NzTabNavItemDirective, e: MouseEvent): void {\n    if (!item.disabled) {\n      item.tab.nzContextmenu.emit(e);\n    }\n  }\n  showItems(): void {\n    clearTimeout(this.closeAnimationWaitTimeoutId);\n    this.menuOpened = true;\n    this.cdr.markForCheck();\n  }\n\n  menuVisChange(visible: boolean): void {\n    if (!visible) {\n      this.closeAnimationWaitTimeoutId = setTimeout(() => {\n        this.menuOpened = false;\n        this.cdr.markForCheck();\n      }, 150);\n    }\n  }\n\n  getElementWidth(): number {\n    return this.element?.offsetWidth || 0;\n  }\n\n  getElementHeight(): number {\n    return this.element?.offsetHeight || 0;\n  }\n\n  ngOnDestroy(): void {\n    clearTimeout(this.closeAnimationWaitTimeoutId);\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Directive, ElementRef, EventEmitter, NgZone, OnDestroy, OnInit, Output } from '@angular/core';\nimport { fromEvent, Observable, Subscription } from 'rxjs';\n\nimport {\n  NzTabScrollEvent,\n  NzTabScrollEventHandlerFun,\n  NzTabScrollListOffset,\n  NzTabScrollListOffsetEvent\n} from './interfaces';\n\nconst MIN_SWIPE_DISTANCE = 0.1;\nconst STOP_SWIPE_DISTANCE = 0.01;\nconst REFRESH_INTERVAL = 20;\nconst SPEED_OFF_MULTIPLE = 0.995 ** REFRESH_INTERVAL;\n\n@Directive({\n  selector: '[nzTabScrollList]'\n})\nexport class NzTabScrollListDirective implements OnInit, On<PERSON><PERSON>roy {\n  lastWheelDirection: 'x' | 'y' | null = null;\n  lastWheelTimestamp = 0;\n  lastTimestamp = 0;\n  lastTimeDiff = 0;\n  lastMixedWheel = 0;\n  lastWheelPrevent = false;\n  touchPosition: NzTabScrollListOffset | null = null;\n  lastOffset: NzTabScrollListOffset | null = null;\n  motion = -1;\n\n  unsubscribe: () => void = () => void 0;\n\n  @Output() readonly offsetChange = new EventEmitter<NzTabScrollListOffsetEvent>();\n  @Output() readonly tabScroll = new EventEmitter<NzTabScrollEvent>();\n\n  constructor(\n    private ngZone: NgZone,\n    private elementRef: ElementRef<HTMLElement>\n  ) {}\n\n  ngOnInit(): void {\n    this.unsubscribe = this.ngZone.runOutsideAngular(() => {\n      const el = this.elementRef.nativeElement;\n\n      const wheel$ = fromEvent<WheelEvent>(el, 'wheel');\n      const touchstart$ = fromEvent<TouchEvent>(el, 'touchstart');\n      const touchmove$ = fromEvent<TouchEvent>(el, 'touchmove');\n      const touchend$ = fromEvent<TouchEvent>(el, 'touchend');\n\n      const subscription = new Subscription();\n      subscription.add(this.subscribeWrap('wheel', wheel$, this.onWheel));\n      subscription.add(this.subscribeWrap('touchstart', touchstart$, this.onTouchStart));\n      subscription.add(this.subscribeWrap('touchmove', touchmove$, this.onTouchMove));\n      subscription.add(this.subscribeWrap('touchend', touchend$, this.onTouchEnd));\n\n      return () => {\n        subscription.unsubscribe();\n      };\n    });\n  }\n\n  subscribeWrap<T extends NzTabScrollEvent['event']>(\n    type: NzTabScrollEvent['type'],\n    observable: Observable<T>,\n    handler: NzTabScrollEventHandlerFun<T>\n  ): Subscription {\n    return observable.subscribe(event => {\n      this.tabScroll.emit({\n        type,\n        event\n      } as NzTabScrollEvent);\n      if (!event.defaultPrevented) {\n        handler(event);\n      }\n    });\n  }\n\n  onTouchEnd = (e: TouchEvent): void => {\n    if (!this.touchPosition) {\n      return;\n    }\n    const lastOffset = this.lastOffset;\n    const lastTimeDiff = this.lastTimeDiff;\n\n    this.lastOffset = this.touchPosition = null;\n\n    if (lastOffset) {\n      const distanceX = lastOffset.x / lastTimeDiff;\n      const distanceY = lastOffset.y / lastTimeDiff;\n      const absX = Math.abs(distanceX);\n      const absY = Math.abs(distanceY);\n\n      // Skip swipe if low distance\n      if (Math.max(absX, absY) < MIN_SWIPE_DISTANCE) {\n        return;\n      }\n\n      let currentX = distanceX;\n      let currentY = distanceY;\n\n      this.motion = window.setInterval(() => {\n        if (Math.abs(currentX) < STOP_SWIPE_DISTANCE && Math.abs(currentY) < STOP_SWIPE_DISTANCE) {\n          window.clearInterval(this.motion);\n          return;\n        }\n\n        currentX *= SPEED_OFF_MULTIPLE;\n        currentY *= SPEED_OFF_MULTIPLE;\n        this.onOffset(currentX * REFRESH_INTERVAL, currentY * REFRESH_INTERVAL, e);\n      }, REFRESH_INTERVAL);\n    }\n  };\n\n  onTouchMove = (e: TouchEvent): void => {\n    if (!this.touchPosition) {\n      return;\n    }\n\n    e.preventDefault();\n    const { screenX, screenY } = e.touches[0];\n\n    const offsetX = screenX - this.touchPosition.x;\n    const offsetY = screenY - this.touchPosition.y;\n    this.onOffset(offsetX, offsetY, e);\n    const now = Date.now();\n\n    this.lastTimeDiff = now - this.lastTimestamp;\n    this.lastTimestamp = now;\n    this.lastOffset = { x: offsetX, y: offsetY };\n    this.touchPosition = { x: screenX, y: screenY };\n  };\n\n  onTouchStart = (e: TouchEvent): void => {\n    const { screenX, screenY } = e.touches[0];\n    this.touchPosition = { x: screenX, y: screenY };\n    window.clearInterval(this.motion);\n  };\n\n  onWheel = (e: WheelEvent): void => {\n    const { deltaX, deltaY } = e;\n    let mixed: number;\n    const absX = Math.abs(deltaX);\n    const absY = Math.abs(deltaY);\n\n    if (absX === absY) {\n      mixed = this.lastWheelDirection === 'x' ? deltaX : deltaY;\n    } else if (absX > absY) {\n      mixed = deltaX;\n      this.lastWheelDirection = 'x';\n    } else {\n      mixed = deltaY;\n      this.lastWheelDirection = 'y';\n    }\n\n    // Optimize mac touch scroll\n    const now = Date.now();\n    const absMixed = Math.abs(mixed);\n\n    if (now - this.lastWheelTimestamp > 100 || absMixed - this.lastMixedWheel > 10) {\n      this.lastWheelPrevent = false;\n    }\n    this.onOffset(-mixed, -mixed, e);\n    if (e.defaultPrevented || this.lastWheelPrevent) {\n      this.lastWheelPrevent = true;\n    }\n\n    this.lastWheelTimestamp = now;\n    this.lastMixedWheel = absMixed;\n  };\n\n  onOffset(x: number, y: number, event: Event): void {\n    this.ngZone.run(() => {\n      this.offsetChange.emit({\n        x,\n        y,\n        event\n      });\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.unsubscribe();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Directive, ElementRef, Input, NgZone, inject } from '@angular/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\nimport { reqAnimFrame } from 'ng-zorro-antd/core/polyfill';\n\nimport { NzTabPositionMode } from './interfaces';\n\n@Directive({\n  selector: 'nz-tabs-ink-bar, [nz-tabs-ink-bar]',\n  host: {\n    class: 'ant-tabs-ink-bar',\n    '[class.ant-tabs-ink-bar-animated]': '_animated'\n  }\n})\nexport class NzTabsInkBarDirective {\n  @Input() position: NzTabPositionMode = 'horizontal';\n  @Input() animated = true;\n\n  animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n  get _animated(): boolean {\n    return this.animationMode !== 'NoopAnimations' && this.animated;\n  }\n\n  constructor(\n    private elementRef: ElementRef<HTMLElement>,\n    private ngZone: NgZone\n  ) {}\n\n  alignToElement(element: HTMLElement): void {\n    this.ngZone.runOutsideAngular(() => {\n      reqAnimFrame(() => this.setStyles(element));\n    });\n  }\n\n  setStyles(element: HTMLElement): void {\n    const inkBar: HTMLElement = this.elementRef.nativeElement;\n\n    if (this.position === 'horizontal') {\n      inkBar.style.top = '';\n      inkBar.style.height = '';\n      inkBar.style.left = this.getLeftPosition(element);\n      inkBar.style.width = this.getElementWidth(element);\n    } else {\n      inkBar.style.left = '';\n      inkBar.style.width = '';\n      inkBar.style.top = this.getTopPosition(element);\n      inkBar.style.height = this.getElementHeight(element);\n    }\n  }\n\n  getLeftPosition(element: HTMLElement): string {\n    return element ? `${element.offsetLeft || 0}px` : '0';\n  }\n\n  getElementWidth(element: HTMLElement): string {\n    return element ? `${element.offsetWidth || 0}px` : '0';\n  }\n\n  getTopPosition(element: HTMLElement): string {\n    return element ? `${element.offsetTop || 0}px` : '0';\n  }\n\n  getElementHeight(element: HTMLElement): string {\n    return element ? `${element.offsetHeight || 0}px` : '0';\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { DOWN_ARROW, ENTER, LEFT_ARROW, RIGHT_ARROW, SPACE, UP_ARROW, hasModifierKey } from '@angular/cdk/keycodes';\nimport { ViewportRuler } from '@angular/cdk/overlay';\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  AfterContentChecked,\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  ElementRef,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  Output,\n  QueryList,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  computed,\n  input\n} from '@angular/core';\nimport { Subject, animationFrameScheduler, asapScheduler, merge, of } from 'rxjs';\nimport { auditTime, takeUntil } from 'rxjs/operators';\n\nimport { NzResizeObserver } from 'ng-zorro-antd/cdk/resize-observer';\nimport { reqAnimFrame } from 'ng-zorro-antd/core/polyfill';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\nimport { NzTabPositionMode, NzTabScrollEvent, NzTabScrollListOffsetEvent } from './interfaces';\nimport { NzTabAddButtonComponent } from './tab-add-button.component';\nimport { NzTabBarExtraContentDirective } from './tab-bar-extra-content.directive';\nimport { NzTabNavItemDirective } from './tab-nav-item.directive';\nimport { NzTabNavOperationComponent } from './tab-nav-operation.component';\nimport { NzTabScrollListDirective } from './tab-scroll-list.directive';\nimport { NzTabsInkBarDirective } from './tabs-ink-bar.directive';\n\nconst RESIZE_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\nconst CSS_TRANSFORM_TIME = 150;\n\n@Component({\n  selector: 'nz-tabs-nav',\n  exportAs: 'nzTabsNav',\n  preserveWhitespaces: false,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  template: `\n    @if (startExtraContent()) {\n      <div class=\"ant-tabs-extra-content\">\n        <ng-template [ngTemplateOutlet]=\"startExtraContent()!.templateRef\"></ng-template>\n      </div>\n    }\n    <div\n      class=\"ant-tabs-nav-wrap\"\n      [class.ant-tabs-nav-wrap-ping-left]=\"pingLeft\"\n      [class.ant-tabs-nav-wrap-ping-right]=\"pingRight\"\n      [class.ant-tabs-nav-wrap-ping-top]=\"pingTop\"\n      [class.ant-tabs-nav-wrap-ping-bottom]=\"pingBottom\"\n      #navWarp\n    >\n      <div\n        class=\"ant-tabs-nav-list\"\n        #navList\n        nzTabScrollList\n        (offsetChange)=\"onOffsetChange($event)\"\n        (tabScroll)=\"tabScroll.emit($event)\"\n        role=\"tablist\"\n      >\n        <ng-content></ng-content>\n        @if (showAddButton) {\n          <button\n            role=\"tab\"\n            [attr.tabindex]=\"-1\"\n            nz-tab-add-button\n            [addIcon]=\"addIcon\"\n            (click)=\"addClicked.emit()\"\n          ></button>\n        }\n        <div nz-tabs-ink-bar [hidden]=\"hideBar\" [position]=\"position\" [animated]=\"inkBarAnimated\"></div>\n      </div>\n    </div>\n    <nz-tab-nav-operation\n      (addClicked)=\"addClicked.emit()\"\n      (selected)=\"onSelectedFromMenu($event)\"\n      [addIcon]=\"addIcon\"\n      [addable]=\"addable\"\n      [items]=\"hiddenItems\"\n    ></nz-tab-nav-operation>\n    @if (endExtraContent()) {\n      <div class=\"ant-tabs-extra-content\">\n        <ng-template [ngTemplateOutlet]=\"endExtraContent()!.templateRef\"></ng-template>\n      </div>\n    } @else if (extraTemplate) {\n      <div class=\"ant-tabs-extra-content\">\n        <ng-template [ngTemplateOutlet]=\"extraTemplate\"></ng-template>\n      </div>\n    }\n  `,\n  host: {\n    class: 'ant-tabs-nav',\n    '(keydown)': 'handleKeydown($event)'\n  },\n  imports: [\n    NzTabScrollListDirective,\n    NzTabAddButtonComponent,\n    NzTabsInkBarDirective,\n    NzTabNavOperationComponent,\n    NgTemplateOutlet\n  ]\n})\nexport class NzTabNavBarComponent implements AfterViewInit, AfterContentChecked, OnDestroy, OnChanges {\n  @Output() readonly indexFocused: EventEmitter<number> = new EventEmitter<number>();\n  @Output() readonly selectFocusedIndex: EventEmitter<number> = new EventEmitter<number>();\n  @Output() readonly addClicked = new EventEmitter<void>();\n  @Output() readonly tabScroll = new EventEmitter<NzTabScrollEvent>();\n\n  @Input() position: NzTabPositionMode = 'horizontal';\n  @Input({ transform: booleanAttribute }) addable: boolean = false;\n  @Input({ transform: booleanAttribute }) hideBar: boolean = false;\n  @Input() addIcon: string | TemplateRef<NzSafeAny> = 'plus';\n  @Input() inkBarAnimated = true;\n  @Input() extraTemplate?: TemplateRef<void>;\n\n  readonly extraContents = input.required<readonly NzTabBarExtraContentDirective[]>();\n\n  readonly startExtraContent = computed(() => this.extraContents().find(item => item.position() === 'start'));\n  readonly endExtraContent = computed(() => this.extraContents().find(item => item.position() === 'end'));\n\n  @Input()\n  get selectedIndex(): number {\n    return this._selectedIndex;\n  }\n  set selectedIndex(value: number) {\n    const newValue = coerceNumberProperty(value);\n    if (this._selectedIndex !== newValue) {\n      this._selectedIndex = value;\n      this.selectedIndexChanged = true;\n      if (this.keyManager) {\n        this.keyManager.updateActiveItem(value);\n      }\n    }\n  }\n\n  @ViewChild('navWarp', { static: true }) navWarpRef!: ElementRef<HTMLElement>;\n  @ViewChild('navList', { static: true }) navListRef!: ElementRef<HTMLElement>;\n  @ViewChild(NzTabNavOperationComponent, { static: true }) operationRef!: NzTabNavOperationComponent;\n  @ViewChild(NzTabAddButtonComponent, { static: false }) addBtnRef!: NzTabAddButtonComponent;\n  @ViewChild(NzTabsInkBarDirective, { static: true }) inkBar!: NzTabsInkBarDirective;\n  @ContentChildren(NzTabNavItemDirective, { descendants: true }) items!: QueryList<NzTabNavItemDirective>;\n\n  /** Tracks which element has focus; used for keyboard navigation */\n  get focusIndex(): number {\n    return this.keyManager ? this.keyManager.activeItemIndex! : 0;\n  }\n\n  /** When the focus index is set, we must manually send focus to the correct label */\n  set focusIndex(value: number) {\n    if (!this.isValidIndex(value) || this.focusIndex === value || !this.keyManager) {\n      return;\n    }\n\n    this.keyManager.setActiveItem(value);\n  }\n\n  get showAddButton(): boolean {\n    return this.hiddenItems.length === 0 && this.addable;\n  }\n\n  translate: null | string = null;\n  transformX = 0;\n  transformY = 0;\n  pingLeft = false;\n  pingRight = false;\n  pingTop = false;\n  pingBottom = false;\n  hiddenItems: NzTabNavItemDirective[] = [];\n\n  private keyManager!: FocusKeyManager<NzTabNavItemDirective>;\n  private destroy$ = new Subject<void>();\n  private _selectedIndex = 0;\n  private wrapperWidth = 0;\n  private wrapperHeight = 0;\n  private scrollListWidth = 0;\n  private scrollListHeight = 0;\n  private operationWidth = 0;\n  private operationHeight = 0;\n  private addButtonWidth = 0;\n  private addButtonHeight = 0;\n  private selectedIndexChanged = false;\n  private lockAnimationTimeoutId?: ReturnType<typeof setTimeout>;\n  private cssTransformTimeWaitingId?: ReturnType<typeof setTimeout>;\n\n  constructor(\n    private cdr: ChangeDetectorRef,\n    private ngZone: NgZone,\n    private viewportRuler: ViewportRuler,\n    private nzResizeObserver: NzResizeObserver,\n    private dir: Directionality\n  ) {}\n\n  ngAfterViewInit(): void {\n    const dirChange = this.dir ? this.dir.change.asObservable() : of(null);\n    const resize = this.viewportRuler.change(150);\n\n    const realign = (): void => {\n      this.updateScrollListPosition();\n      this.alignInkBarToSelectedTab();\n    };\n    this.keyManager = new FocusKeyManager<NzTabNavItemDirective>(this.items)\n      .withHorizontalOrientation(this.getLayoutDirection())\n      .withWrap();\n    this.keyManager.updateActiveItem(this.selectedIndex);\n\n    reqAnimFrame(realign);\n\n    merge(this.nzResizeObserver.observe(this.navWarpRef), this.nzResizeObserver.observe(this.navListRef))\n      .pipe(takeUntil(this.destroy$), auditTime(16, RESIZE_SCHEDULER))\n      .subscribe(() => {\n        realign();\n      });\n    merge(dirChange, resize, this.items.changes)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        Promise.resolve().then(realign);\n        this.keyManager.withHorizontalOrientation(this.getLayoutDirection());\n      });\n\n    this.keyManager.change.pipe(takeUntil(this.destroy$)).subscribe(newFocusIndex => {\n      this.indexFocused.emit(newFocusIndex);\n      this.setTabFocus(newFocusIndex);\n      this.scrollToTab(this.keyManager.activeItem!);\n    });\n  }\n\n  ngAfterContentChecked(): void {\n    if (this.selectedIndexChanged) {\n      this.updateScrollListPosition();\n      this.alignInkBarToSelectedTab();\n      this.selectedIndexChanged = false;\n      this.cdr.markForCheck();\n    }\n  }\n\n  ngOnDestroy(): void {\n    clearTimeout(this.lockAnimationTimeoutId);\n    clearTimeout(this.cssTransformTimeWaitingId);\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  onSelectedFromMenu(tab: NzTabNavItemDirective): void {\n    const tabIndex = this.items.toArray().findIndex(e => e === tab);\n    if (tabIndex !== -1) {\n      this.keyManager.updateActiveItem(tabIndex);\n      if (this.focusIndex !== this.selectedIndex) {\n        this.selectFocusedIndex.emit(this.focusIndex);\n        this.scrollToTab(tab);\n      }\n    }\n  }\n\n  onOffsetChange(e: NzTabScrollListOffsetEvent): void {\n    if (this.position === 'horizontal') {\n      if (!this.lockAnimationTimeoutId) {\n        if (this.transformX >= 0 && e.x > 0) {\n          return;\n        }\n        if (this.transformX <= this.wrapperWidth - this.scrollListWidth && e.x < 0) {\n          return;\n        }\n      }\n      e.event.preventDefault();\n      this.transformX = this.clampTransformX(this.transformX + e.x);\n      this.setTransform(this.transformX, 0);\n    } else {\n      if (!this.lockAnimationTimeoutId) {\n        if (this.transformY >= 0 && e.y > 0) {\n          return;\n        }\n        if (this.transformY <= this.wrapperHeight - this.scrollListHeight && e.y < 0) {\n          return;\n        }\n      }\n      e.event.preventDefault();\n      this.transformY = this.clampTransformY(this.transformY + e.y);\n      this.setTransform(0, this.transformY);\n    }\n\n    this.lockAnimation();\n    this.setVisibleRange();\n    this.setPingStatus();\n  }\n\n  handleKeydown(event: KeyboardEvent): void {\n    const inNavigationList = this.navWarpRef.nativeElement.contains(event.target as HTMLElement);\n    if (hasModifierKey(event) || !inNavigationList) {\n      return;\n    }\n\n    switch (event.keyCode) {\n      case LEFT_ARROW:\n      case UP_ARROW:\n      case RIGHT_ARROW:\n      case DOWN_ARROW:\n        this.lockAnimation();\n        this.keyManager.onKeydown(event);\n        break;\n      case ENTER:\n      case SPACE:\n        if (this.focusIndex !== this.selectedIndex) {\n          this.selectFocusedIndex.emit(this.focusIndex);\n        }\n        break;\n      default:\n        this.keyManager.onKeydown(event);\n    }\n  }\n\n  private isValidIndex(index: number): boolean {\n    if (!this.items) {\n      return true;\n    }\n\n    const tab = this.items ? this.items.toArray()[index] : null;\n    return !!tab && !tab.disabled;\n  }\n\n  private scrollToTab(tab: NzTabNavItemDirective): void {\n    if (!this.items.find(e => e === tab)) {\n      return;\n    }\n    const tabs = this.items.toArray();\n\n    if (this.position === 'horizontal') {\n      let newTransform = this.transformX;\n      if (this.getLayoutDirection() === 'rtl') {\n        const right = tabs[0].left + tabs[0].width - tab.left - tab.width;\n\n        if (right < this.transformX) {\n          newTransform = right;\n        } else if (right + tab.width > this.transformX + this.wrapperWidth) {\n          newTransform = right + tab.width - this.wrapperWidth;\n        }\n      } else if (tab.left < -this.transformX) {\n        newTransform = -tab.left;\n      } else if (tab.left + tab.width > -this.transformX + this.wrapperWidth) {\n        newTransform = -(tab.left + tab.width - this.wrapperWidth);\n      }\n      this.transformX = newTransform;\n      this.transformY = 0;\n      this.setTransform(newTransform, 0);\n    } else {\n      let newTransform = this.transformY;\n\n      if (tab.top < -this.transformY) {\n        newTransform = -tab.top;\n      } else if (tab.top + tab.height > -this.transformY + this.wrapperHeight) {\n        newTransform = -(tab.top + tab.height - this.wrapperHeight);\n      }\n      this.transformY = newTransform;\n      this.transformX = 0;\n      this.setTransform(0, newTransform);\n    }\n\n    clearTimeout(this.cssTransformTimeWaitingId);\n    this.cssTransformTimeWaitingId = setTimeout(() => {\n      this.setVisibleRange();\n    }, CSS_TRANSFORM_TIME);\n  }\n\n  private lockAnimation(): void {\n    if (!this.lockAnimationTimeoutId) {\n      this.ngZone.runOutsideAngular(() => {\n        this.navListRef.nativeElement.style.transition = 'none';\n        this.lockAnimationTimeoutId = setTimeout(() => {\n          this.navListRef.nativeElement.style.transition = '';\n          this.lockAnimationTimeoutId = undefined;\n        }, CSS_TRANSFORM_TIME);\n      });\n    }\n  }\n\n  private setTransform(x: number, y: number): void {\n    this.navListRef.nativeElement.style.transform = `translate(${x}px, ${y}px)`;\n  }\n\n  private clampTransformX(transform: number): number {\n    const scrollWidth = this.wrapperWidth - this.scrollListWidth;\n    if (this.getLayoutDirection() === 'rtl') {\n      return Math.max(Math.min(scrollWidth, transform), 0);\n    } else {\n      return Math.min(Math.max(scrollWidth, transform), 0);\n    }\n  }\n\n  private clampTransformY(transform: number): number {\n    return Math.min(Math.max(this.wrapperHeight - this.scrollListHeight, transform), 0);\n  }\n\n  private updateScrollListPosition(): void {\n    this.resetSizes();\n    this.transformX = this.clampTransformX(this.transformX);\n    this.transformY = this.clampTransformY(this.transformY);\n    this.setVisibleRange();\n    this.setPingStatus();\n    if (this.keyManager) {\n      this.keyManager.updateActiveItem(this.keyManager.activeItemIndex!);\n      if (this.keyManager.activeItem) {\n        this.scrollToTab(this.keyManager.activeItem);\n      }\n    }\n  }\n\n  private resetSizes(): void {\n    this.addButtonWidth = this.addBtnRef ? this.addBtnRef.getElementWidth() : 0;\n    this.addButtonHeight = this.addBtnRef ? this.addBtnRef.getElementHeight() : 0;\n    this.operationWidth = this.operationRef.getElementWidth();\n    this.operationHeight = this.operationRef.getElementHeight();\n    this.wrapperWidth = this.navWarpRef.nativeElement.offsetWidth || 0;\n    this.wrapperHeight = this.navWarpRef.nativeElement.offsetHeight || 0;\n    this.scrollListHeight = this.navListRef.nativeElement.offsetHeight || 0;\n    this.scrollListWidth = this.navListRef.nativeElement.offsetWidth || 0;\n  }\n\n  private alignInkBarToSelectedTab(): void {\n    const selectedItem = this.items && this.items.length ? this.items.toArray()[this.selectedIndex] : null;\n    const selectedItemElement = selectedItem ? selectedItem.elementRef.nativeElement : null;\n\n    if (selectedItemElement) {\n      /**\n       * .ant-tabs-nav-list - Target offset parent element\n       *   └──.ant-tabs-tab\n       *        └──.ant-tabs-tab-btn - Currently focused element\n       */\n      this.inkBar.alignToElement(selectedItemElement.parentElement!);\n    }\n  }\n\n  private setPingStatus(): void {\n    const ping = {\n      top: false,\n      right: false,\n      bottom: false,\n      left: false\n    };\n    const navWarp = this.navWarpRef.nativeElement;\n    if (this.position === 'horizontal') {\n      if (this.getLayoutDirection() === 'rtl') {\n        ping.right = this.transformX > 0;\n        ping.left = this.transformX + this.wrapperWidth < this.scrollListWidth;\n      } else {\n        ping.left = this.transformX < 0;\n        ping.right = -this.transformX + this.wrapperWidth < this.scrollListWidth;\n      }\n    } else {\n      ping.top = this.transformY < 0;\n      ping.bottom = -this.transformY + this.wrapperHeight < this.scrollListHeight;\n    }\n\n    (Object.keys(ping) as Array<'top' | 'right' | 'bottom' | 'left'>).forEach(pos => {\n      const className = `ant-tabs-nav-wrap-ping-${pos}`;\n      if (ping[pos]) {\n        navWarp.classList.add(className);\n      } else {\n        navWarp.classList.remove(className);\n      }\n    });\n  }\n\n  private setVisibleRange(): void {\n    let unit: 'width' | 'height';\n    let position: 'left' | 'top' | 'right';\n    let transformSize: number;\n    let basicSize: number;\n    let tabContentSize: number;\n    let addSize: number;\n    const tabs = this.items.toArray();\n    const DEFAULT_SIZE = { width: 0, height: 0, left: 0, top: 0, right: 0 };\n\n    const getOffset = (index: number): number => {\n      let offset: number;\n      const size = tabs[index] || DEFAULT_SIZE;\n      if (position === 'right') {\n        offset = tabs[0].left + tabs[0].width - tabs[index].left - tabs[index].width;\n      } else {\n        offset = size[position];\n      }\n      return offset;\n    };\n\n    if (this.position === 'horizontal') {\n      unit = 'width';\n      basicSize = this.wrapperWidth;\n      tabContentSize = this.scrollListWidth - (this.hiddenItems.length ? this.operationWidth : 0);\n      addSize = this.addButtonWidth;\n      transformSize = Math.abs(this.transformX);\n      if (this.getLayoutDirection() === 'rtl') {\n        position = 'right';\n        this.pingRight = this.transformX > 0;\n        this.pingLeft = this.transformX + this.wrapperWidth < this.scrollListWidth;\n      } else {\n        this.pingLeft = this.transformX < 0;\n        this.pingRight = -this.transformX + this.wrapperWidth < this.scrollListWidth;\n        position = 'left';\n      }\n    } else {\n      unit = 'height';\n      basicSize = this.wrapperHeight;\n      tabContentSize = this.scrollListHeight - (this.hiddenItems.length ? this.operationHeight : 0);\n      addSize = this.addButtonHeight;\n      position = 'top';\n      transformSize = -this.transformY;\n      this.pingTop = this.transformY < 0;\n      this.pingBottom = -this.transformY + this.wrapperHeight < this.scrollListHeight;\n    }\n\n    let mergedBasicSize = basicSize;\n    if (tabContentSize + addSize > basicSize) {\n      mergedBasicSize = basicSize - addSize;\n    }\n\n    if (!tabs.length) {\n      this.hiddenItems = [];\n      this.cdr.markForCheck();\n      return;\n    }\n\n    const len = tabs.length;\n    let endIndex = len;\n    for (let i = 0; i < len; i += 1) {\n      const offset = getOffset(i);\n      const size = tabs[i] || DEFAULT_SIZE;\n      if (offset + size[unit] > transformSize + mergedBasicSize) {\n        endIndex = i - 1;\n        break;\n      }\n    }\n\n    let startIndex = 0;\n    for (let i = len - 1; i >= 0; i -= 1) {\n      const offset = getOffset(i);\n      if (offset < transformSize) {\n        startIndex = i + 1;\n        break;\n      }\n    }\n\n    const startHiddenTabs = tabs.slice(0, startIndex);\n    const endHiddenTabs = tabs.slice(endIndex + 1);\n    this.hiddenItems = [...startHiddenTabs, ...endHiddenTabs];\n    this.cdr.markForCheck();\n  }\n\n  private getLayoutDirection(): Direction {\n    return this.dir && this.dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n\n  private setTabFocus(_tabIndex: number): void {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { position } = changes;\n    // The first will be aligning in ngAfterViewInit\n    if (position && !position.isFirstChange()) {\n      this.alignInkBarToSelectedTab();\n      this.lockAnimation();\n      this.updateScrollListPosition();\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Directive } from '@angular/core';\n\n/** Decorates the `ng-template` tags and reads out the template from it. */\n@Directive({\n  selector: '[nz-tab]',\n  exportAs: 'nzTab'\n})\nexport class NzTabDirective {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ContentChild,\n  EventEmitter,\n  InjectionToken,\n  Input,\n  OnChanges,\n  OnDestroy,\n  Output,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject\n} from '@angular/core';\nimport { Subject } from 'rxjs';\n\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\nimport { TabTemplateContext } from './interfaces';\nimport { NzTabLinkDirective, NzTabLinkTemplateDirective } from './tab-link.directive';\nimport { NzTabDirective } from './tab.directive';\n\n/**\n * Used to provide a tab set to a tab without causing a circular dependency.\n */\nexport const NZ_TAB_SET = new InjectionToken<NzSafeAny>('NZ_TAB_SET');\n\n@Component({\n  selector: 'nz-tab',\n  exportAs: 'nzTab',\n  preserveWhitespaces: false,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  template: `\n    <ng-template #tabLinkTemplate>\n      <ng-content select=\"[nz-tab-link]\"></ng-content>\n    </ng-template>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `\n})\nexport class NzTabComponent implements OnChanges, OnDestroy {\n  @Input() nzTitle: string | TemplateRef<TabTemplateContext> = '';\n  @Input({ transform: booleanAttribute }) nzClosable = false;\n  @Input() nzCloseIcon: string | TemplateRef<NzSafeAny> = 'close';\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Input({ transform: booleanAttribute }) nzForceRender = false;\n  @Output() readonly nzSelect = new EventEmitter<void>();\n  @Output() readonly nzDeselect = new EventEmitter<void>();\n  @Output() readonly nzClick = new EventEmitter<void>();\n  @Output() readonly nzContextmenu = new EventEmitter<MouseEvent>();\n\n  @ContentChild(NzTabLinkTemplateDirective, { static: false }) nzTabLinkTemplateDirective!: NzTabLinkTemplateDirective;\n  @ContentChild(NzTabDirective, { static: false, read: TemplateRef }) template: TemplateRef<void> | null = null;\n  @ContentChild(NzTabLinkDirective, { static: false }) linkDirective!: NzTabLinkDirective;\n  @ViewChild('contentTemplate', { static: true }) contentTemplate!: TemplateRef<NzSafeAny>;\n\n  isActive: boolean = false;\n  hasBeenActive = false;\n  position: number | null = null;\n  origin: number | null = null;\n  closestTabSet = inject(NZ_TAB_SET);\n\n  readonly stateChanges = new Subject<void>();\n\n  get content(): TemplateRef<NzSafeAny> {\n    return this.template || this.contentTemplate;\n  }\n\n  get label(): string | TemplateRef<NzSafeAny> {\n    return this.nzTitle || this.nzTabLinkTemplateDirective?.templateRef;\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzTitle, nzDisabled, nzForceRender } = changes;\n    if (nzTitle || nzDisabled || nzForceRender) {\n      this.stateChanges.next();\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.stateChanges.complete();\n  }\n\n  setActive(active: boolean): void {\n    this.isActive = active;\n    if (active) {\n      this.hasBeenActive = true;\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/** get some code from https://github.com/angular/material2 */\n\nimport { A11yModule } from '@angular/cdk/a11y';\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  AfterContentChecked,\n  AfterContentInit,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  contentChildren,\n  ContentChildren,\n  EventEmitter,\n  forwardRef,\n  inject,\n  Input,\n  NgZone,\n  OnDestroy,\n  OnInit,\n  Output,\n  QueryList,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation\n} from '@angular/core';\nimport { NavigationEnd, Router, RouterLink } from '@angular/router';\nimport { merge, Observable, of, Subject, Subscription } from 'rxjs';\nimport { delay, filter, first, startWith, takeUntil } from 'rxjs/operators';\n\nimport { NzConfigKey, NzConfigService, WithConfig } from 'ng-zorro-antd/core/config';\nimport { PREFIX } from 'ng-zorro-antd/core/logger';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { NzSafeAny, NzSizeLDSType } from 'ng-zorro-antd/core/types';\nimport { wrapIntoObservable } from 'ng-zorro-antd/core/util';\n\nimport {\n  NzAnimatedInterface,\n  NzTabChangeEvent,\n  NzTabPosition,\n  NzTabPositionMode,\n  NzTabsCanDeactivateFn,\n  NzTabScrollEvent,\n  NzTabType\n} from './interfaces';\nimport { NzTabBarExtraContentDirective } from './tab-bar-extra-content.directive';\nimport { NzTabBodyComponent } from './tab-body.component';\nimport { NzTabCloseButtonComponent } from './tab-close-button.component';\nimport { NzTabLinkDirective } from './tab-link.directive';\nimport { NzTabNavBarComponent } from './tab-nav-bar.component';\nimport { NzTabNavItemDirective } from './tab-nav-item.directive';\nimport { NZ_TAB_SET, NzTabComponent } from './tab.component';\n\nconst NZ_CONFIG_MODULE_NAME: NzConfigKey = 'tabs';\n\nlet nextId = 0;\n\n@Component({\n  selector: 'nz-tabset',\n  exportAs: 'nzTabset',\n  preserveWhitespaces: false,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.Default,\n  providers: [\n    {\n      provide: NZ_TAB_SET,\n      useExisting: forwardRef(() => NzTabSetComponent)\n    }\n  ],\n  template: `\n    @if (tabs.length || addable) {\n      <nz-tabs-nav\n        [style]=\"nzTabBarStyle\"\n        [selectedIndex]=\"nzSelectedIndex || 0\"\n        [inkBarAnimated]=\"inkBarAnimated\"\n        [addable]=\"addable\"\n        [addIcon]=\"nzAddIcon\"\n        [hideBar]=\"nzHideAll\"\n        [position]=\"position\"\n        [extraTemplate]=\"nzTabBarExtraContent\"\n        [extraContents]=\"extraContents()\"\n        (tabScroll)=\"nzTabListScroll.emit($event)\"\n        (selectFocusedIndex)=\"setSelectedIndex($event)\"\n        (addClicked)=\"onAdd()\"\n      >\n        @for (tab of tabs; track tab; let i = $index) {\n          <div\n            class=\"ant-tabs-tab\"\n            [style.margin-right.px]=\"position === 'horizontal' ? nzTabBarGutter : null\"\n            [style.margin-bottom.px]=\"position === 'vertical' ? nzTabBarGutter : null\"\n            [class.ant-tabs-tab-active]=\"nzSelectedIndex === i\"\n            [class.ant-tabs-tab-disabled]=\"tab.nzDisabled\"\n            (click)=\"clickNavItem(tab, i, $event)\"\n            (contextmenu)=\"contextmenuNavItem(tab, $event)\"\n          >\n            <button\n              type=\"button\"\n              role=\"tab\"\n              [id]=\"getTabContentId(i)\"\n              [attr.tabIndex]=\"getTabIndex(tab, i)\"\n              [attr.aria-disabled]=\"tab.nzDisabled\"\n              [attr.aria-selected]=\"nzSelectedIndex === i && !nzHideAll\"\n              [attr.aria-controls]=\"getTabContentId(i)\"\n              [disabled]=\"tab.nzDisabled\"\n              [tab]=\"tab\"\n              [active]=\"nzSelectedIndex === i\"\n              class=\"ant-tabs-tab-btn\"\n              nzTabNavItem\n              cdkMonitorElementFocus\n            >\n              <ng-container *nzStringTemplateOutlet=\"tab.label; context: { visible: true }\">\n                {{ tab.label }}\n              </ng-container>\n              @if (tab.nzClosable && closable && !tab.nzDisabled) {\n                <button\n                  type=\"button\"\n                  nz-tab-close-button\n                  [closeIcon]=\"tab.nzCloseIcon\"\n                  (click)=\"onClose(i, $event)\"\n                ></button>\n              }\n            </button>\n          </div>\n        }\n      </nz-tabs-nav>\n    }\n    <div class=\"ant-tabs-content-holder\">\n      <div\n        class=\"ant-tabs-content\"\n        [class.ant-tabs-content-top]=\"nzTabPosition === 'top'\"\n        [class.ant-tabs-content-bottom]=\"nzTabPosition === 'bottom'\"\n        [class.ant-tabs-content-left]=\"nzTabPosition === 'left'\"\n        [class.ant-tabs-content-right]=\"nzTabPosition === 'right'\"\n        [class.ant-tabs-content-animated]=\"tabPaneAnimated\"\n      >\n        @if (!nzHideAll) {\n          @for (tab of tabs; track tab; let i = $index) {\n            @if (tab.nzForceRender) {\n              <ng-template [ngTemplateOutlet]=\"tabpaneTmpl\"></ng-template>\n            } @else if (nzDestroyInactiveTabPane) {\n              @if (nzSelectedIndex === i) {\n                <ng-template [ngTemplateOutlet]=\"tabpaneTmpl\"></ng-template>\n              }\n            } @else {\n              @if (nzSelectedIndex === i || tab.hasBeenActive) {\n                <ng-template [ngTemplateOutlet]=\"tabpaneTmpl\"></ng-template>\n              }\n            }\n\n            <ng-template #tabpaneTmpl>\n              <div\n                role=\"tabpanel\"\n                [id]=\"getTabContentId(i)\"\n                [attr.aria-labelledby]=\"getTabContentId(i)\"\n                nz-tab-body\n                [active]=\"nzSelectedIndex === i\"\n                [content]=\"tab.content\"\n                [animated]=\"tabPaneAnimated\"\n              ></div>\n            </ng-template>\n          }\n        }\n      </div>\n    </div>\n  `,\n  host: {\n    class: 'ant-tabs',\n    '[class.ant-tabs-card]': `nzType === 'card' || nzType === 'editable-card'`,\n    '[class.ant-tabs-editable]': `nzType === 'editable-card'`,\n    '[class.ant-tabs-editable-card]': `nzType === 'editable-card'`,\n    '[class.ant-tabs-centered]': `nzCentered`,\n    '[class.ant-tabs-rtl]': `dir === 'rtl'`,\n    '[class.ant-tabs-top]': `nzTabPosition === 'top'`,\n    '[class.ant-tabs-bottom]': `nzTabPosition === 'bottom'`,\n    '[class.ant-tabs-left]': `nzTabPosition === 'left'`,\n    '[class.ant-tabs-right]': `nzTabPosition === 'right'`,\n    '[class.ant-tabs-default]': `nzSize === 'default'`,\n    '[class.ant-tabs-small]': `nzSize === 'small'`,\n    '[class.ant-tabs-large]': `nzSize === 'large'`\n  },\n  imports: [\n    NzTabNavBarComponent,\n    NgTemplateOutlet,\n    NzTabNavItemDirective,\n    A11yModule,\n    NzOutletModule,\n    NzTabCloseButtonComponent,\n    NzTabBodyComponent\n  ]\n})\nexport class NzTabSetComponent implements OnInit, AfterContentChecked, OnDestroy, AfterContentInit {\n  readonly _nzModuleName: NzConfigKey = NZ_CONFIG_MODULE_NAME;\n\n  @Input()\n  get nzSelectedIndex(): number | null {\n    return this.selectedIndex;\n  }\n  set nzSelectedIndex(value: null | number) {\n    this.indexToSelect = coerceNumberProperty(value, null);\n  }\n  @Input() nzTabPosition: NzTabPosition = 'top';\n  @Input() nzTabBarExtraContent?: TemplateRef<void>;\n  @Input() nzCanDeactivate: NzTabsCanDeactivateFn | null = null;\n  @Input() nzAddIcon: string | TemplateRef<NzSafeAny> = 'plus';\n  @Input() nzTabBarStyle: Record<string, string> | null = null;\n  @Input() @WithConfig() nzType: NzTabType = 'line';\n  @Input() @WithConfig() nzSize: NzSizeLDSType = 'default';\n  @Input() @WithConfig() nzAnimated: NzAnimatedInterface | boolean = true;\n  @Input() @WithConfig() nzTabBarGutter?: number = undefined;\n  @Input({ transform: booleanAttribute }) nzHideAdd: boolean = false;\n  @Input({ transform: booleanAttribute }) nzCentered: boolean = false;\n  @Input({ transform: booleanAttribute }) nzHideAll = false;\n  @Input({ transform: booleanAttribute }) nzLinkRouter = false;\n  @Input({ transform: booleanAttribute }) nzLinkExact = true;\n  @Input({ transform: booleanAttribute }) nzDestroyInactiveTabPane = false;\n\n  @Output() readonly nzSelectChange: EventEmitter<NzTabChangeEvent> = new EventEmitter<NzTabChangeEvent>(true);\n  @Output() readonly nzSelectedIndexChange: EventEmitter<number> = new EventEmitter<number>();\n  @Output() readonly nzTabListScroll = new EventEmitter<NzTabScrollEvent>();\n  @Output() readonly nzClose = new EventEmitter<{ index: number }>();\n  @Output() readonly nzAdd = new EventEmitter<void>();\n\n  get position(): NzTabPositionMode {\n    return ['top', 'bottom'].indexOf(this.nzTabPosition) === -1 ? 'vertical' : 'horizontal';\n  }\n\n  get addable(): boolean {\n    return this.nzType === 'editable-card' && !this.nzHideAdd;\n  }\n\n  get closable(): boolean {\n    return this.nzType === 'editable-card';\n  }\n\n  get line(): boolean {\n    return this.nzType === 'line';\n  }\n\n  get inkBarAnimated(): boolean {\n    return this.line && (typeof this.nzAnimated === 'boolean' ? this.nzAnimated : this.nzAnimated.inkBar);\n  }\n\n  get tabPaneAnimated(): boolean {\n    return typeof this.nzAnimated === 'boolean' ? this.nzAnimated : this.nzAnimated.tabPane;\n  }\n\n  // Pick up only direct descendants under ivy rendering engine\n  // We filter out only the tabs that belong to this tab set in `tabs`.\n  @ContentChildren(NzTabComponent, { descendants: true })\n  allTabs: QueryList<NzTabComponent> = new QueryList<NzTabComponent>();\n\n  @ContentChildren(NzTabLinkDirective, { descendants: true })\n  tabLinks: QueryList<NzTabLinkDirective> = new QueryList<NzTabLinkDirective>();\n  @ViewChild(NzTabNavBarComponent, { static: false }) tabNavBarRef!: NzTabNavBarComponent;\n  // All the direct tabs for this tab set\n  tabs: QueryList<NzTabComponent> = new QueryList<NzTabComponent>();\n\n  readonly extraContents = contentChildren(NzTabBarExtraContentDirective);\n\n  dir: Direction = 'ltr';\n  private readonly tabSetId!: number;\n  private destroy$ = new Subject<void>();\n  private indexToSelect: number | null = 0;\n  private selectedIndex: number | null = null;\n  private tabLabelSubscription = Subscription.EMPTY;\n  private tabsSubscription = Subscription.EMPTY;\n  private canDeactivateSubscription = Subscription.EMPTY;\n  private router = inject(Router, { optional: true });\n\n  constructor(\n    public nzConfigService: NzConfigService,\n    private ngZone: NgZone,\n    private cdr: ChangeDetectorRef,\n    private directionality: Directionality\n  ) {\n    this.tabSetId = nextId++;\n  }\n\n  ngOnInit(): void {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.tabs.destroy();\n    this.tabLabelSubscription.unsubscribe();\n    this.tabsSubscription.unsubscribe();\n    this.canDeactivateSubscription.unsubscribe();\n  }\n\n  ngAfterContentInit(): void {\n    this.ngZone.runOutsideAngular(() => {\n      Promise.resolve().then(() => this.setUpRouter());\n    });\n\n    this.subscribeToTabLabels();\n    this.subscribeToAllTabChanges();\n\n    // Subscribe to changes of the number of tabs, to be\n    // able to re-render the content as new tabs are added or removed.\n    this.tabsSubscription = this.tabs.changes.subscribe(() => {\n      const indexToSelect = this.clampTabIndex(this.indexToSelect);\n\n      // Maintain the previously selected tab if a new tab is added or removed, and there is no\n      // explicit change that selects a different tab.\n      if (indexToSelect === this.selectedIndex) {\n        const tabs = this.tabs.toArray();\n\n        for (let i = 0; i < tabs.length; i++) {\n          if (tabs[i].isActive) {\n            // Assign both to the `indexToSelect` and `selectedIndex` so we don't fire a changed\n            // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n            // adding a tab within the `nzSelectedIndexChange` event.\n            this.indexToSelect = this.selectedIndex = i;\n            break;\n          }\n        }\n      }\n      this.subscribeToTabLabels();\n      this.cdr.markForCheck();\n    });\n  }\n\n  ngAfterContentChecked(): void {\n    // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n    // the amount of tabs changes before the actual change detection runs.\n    const indexToSelect = (this.indexToSelect = this.clampTabIndex(this.indexToSelect));\n\n    // If there is a change in the selected index, emit a change event. Should not trigger if\n    // the selected index has not yet been initialized.\n    if (this.selectedIndex !== indexToSelect) {\n      const isFirstRun = this.selectedIndex == null;\n\n      if (!isFirstRun) {\n        this.nzSelectChange.emit(this.createChangeEvent(indexToSelect));\n      }\n\n      // Changing these values after change detection has run\n      // since the checked content may contain references to them.\n      Promise.resolve().then(() => {\n        this.tabs.forEach((tab, index) => tab.setActive(index === indexToSelect));\n\n        if (!isFirstRun) {\n          this.nzSelectedIndexChange.emit(indexToSelect);\n        }\n      });\n    }\n\n    // Set up the position for each tab and optionally set up an origin on the next selected tab.\n    this.tabs.forEach((tab, index) => {\n      tab.position = index - indexToSelect;\n\n      // If there is already a selected tab, then set up an origin for the next selected tab\n      // if it doesn't have one already.\n      if (this.selectedIndex != null && tab.position === 0 && !tab.origin) {\n        tab.origin = indexToSelect - this.selectedIndex;\n      }\n    });\n\n    if (this.selectedIndex !== indexToSelect) {\n      this.selectedIndex = indexToSelect;\n      this.cdr.markForCheck();\n    }\n  }\n\n  onClose(index: number, e: MouseEvent): void {\n    e.preventDefault();\n    e.stopPropagation();\n    this.nzClose.emit({ index });\n  }\n\n  onAdd(): void {\n    this.nzAdd.emit();\n  }\n\n  private clampTabIndex(index: number | null): number {\n    return Math.min(this.tabs.length - 1, Math.max(index || 0, 0));\n  }\n\n  private createChangeEvent(index: number): NzTabChangeEvent {\n    const event = new NzTabChangeEvent();\n    event.index = index;\n    if (this.tabs && this.tabs.length) {\n      event.tab = this.tabs.toArray()[index];\n      this.tabs.forEach((tab, i) => {\n        if (i !== index) {\n          tab.nzDeselect.emit();\n        }\n      });\n      event.tab.nzSelect.emit();\n    }\n    return event;\n  }\n\n  private subscribeToTabLabels(): void {\n    if (this.tabLabelSubscription) {\n      this.tabLabelSubscription.unsubscribe();\n    }\n\n    this.tabLabelSubscription = merge(...this.tabs.map(tab => tab.stateChanges)).subscribe(() =>\n      this.cdr.markForCheck()\n    );\n  }\n\n  private subscribeToAllTabChanges(): void {\n    this.allTabs.changes.pipe(startWith(this.allTabs)).subscribe((tabs: QueryList<NzTabComponent>) => {\n      this.tabs.reset(tabs.filter(tab => tab.closestTabSet === this));\n      this.tabs.notifyOnChanges();\n    });\n  }\n\n  canDeactivateFun(pre: number, next: number): Observable<boolean> {\n    if (typeof this.nzCanDeactivate === 'function') {\n      const observable = wrapIntoObservable(this.nzCanDeactivate(pre, next));\n      return observable.pipe(first(), takeUntil(this.destroy$));\n    } else {\n      return of(true);\n    }\n  }\n\n  clickNavItem(tab: NzTabComponent, index: number, e: MouseEvent): void {\n    if (!tab.nzDisabled) {\n      // ignore nzCanDeactivate\n      tab.nzClick.emit();\n      if (!this.isRouterLinkClickEvent(index, e)) {\n        this.setSelectedIndex(index);\n      }\n    }\n  }\n\n  private isRouterLinkClickEvent(index: number, event: MouseEvent): boolean {\n    const target = event.target as HTMLElement;\n    if (this.nzLinkRouter) {\n      return !!this.tabs.toArray()[index]?.linkDirective?.elementRef.nativeElement.contains(target);\n    } else {\n      return false;\n    }\n  }\n\n  contextmenuNavItem(tab: NzTabComponent, e: MouseEvent): void {\n    if (!tab.nzDisabled) {\n      // ignore nzCanDeactivate\n      tab.nzContextmenu.emit(e);\n    }\n  }\n\n  setSelectedIndex(index: number): void {\n    this.canDeactivateSubscription.unsubscribe();\n    this.canDeactivateSubscription = this.canDeactivateFun(this.selectedIndex!, index).subscribe(can => {\n      if (can) {\n        this.nzSelectedIndex = index;\n        this.tabNavBarRef.focusIndex = index;\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  getTabIndex(tab: NzTabComponent, index: number): number | null {\n    if (tab.nzDisabled) {\n      return null;\n    }\n    return this.selectedIndex === index ? 0 : -1;\n  }\n\n  getTabContentId(i: number): string {\n    return `nz-tabs-${this.tabSetId}-tab-${i}`;\n  }\n\n  private setUpRouter(): void {\n    if (this.nzLinkRouter) {\n      if (!this.router) {\n        throw new Error(`${PREFIX} you should import 'RouterModule' if you want to use 'nzLinkRouter'!`);\n      }\n      merge(this.router.events.pipe(filter(e => e instanceof NavigationEnd)), this.tabLinks.changes)\n        .pipe(startWith(true), delay(0), takeUntil(this.destroy$))\n        .subscribe(() => {\n          this.updateRouterActive();\n          this.cdr.markForCheck();\n        });\n    }\n  }\n\n  private updateRouterActive(): void {\n    if (this.router?.navigated) {\n      const index = this.findShouldActiveTabIndex();\n      if (index !== this.selectedIndex) {\n        this.setSelectedIndex(index);\n      }\n      Promise.resolve().then(() => (this.nzHideAll = index === -1));\n    }\n  }\n\n  private findShouldActiveTabIndex(): number {\n    const tabs = this.tabs.toArray();\n    const isActive = this.isLinkActive(this.router);\n\n    return tabs.findIndex(tab => {\n      const c = tab.linkDirective;\n      return c ? isActive(c.routerLink) : false;\n    });\n  }\n\n  private isLinkActive(router: Router | null): (link?: RouterLink | null) => boolean {\n    return (link?: RouterLink | null) =>\n      link\n        ? !!router?.isActive(link.urlTree || '', {\n            paths: this.nzLinkExact ? 'exact' : 'subset',\n            queryParams: this.nzLinkExact ? 'exact' : 'subset',\n            fragment: 'ignored',\n            matrixParams: 'ignored'\n          })\n        : false;\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzTabAddButtonComponent } from './tab-add-button.component';\nimport { NzTabBarExtraContentDirective } from './tab-bar-extra-content.directive';\nimport { NzTabBodyComponent } from './tab-body.component';\nimport { NzTabCloseButtonComponent } from './tab-close-button.component';\nimport { NzTabLinkDirective, NzTabLinkTemplateDirective } from './tab-link.directive';\nimport { NzTabNavBarComponent } from './tab-nav-bar.component';\nimport { NzTabNavItemDirective } from './tab-nav-item.directive';\nimport { NzTabNavOperationComponent } from './tab-nav-operation.component';\nimport { NzTabScrollListDirective } from './tab-scroll-list.directive';\nimport { NzTabComponent } from './tab.component';\nimport { NzTabDirective } from './tab.directive';\nimport { NzTabsInkBarDirective } from './tabs-ink-bar.directive';\nimport { NzTabSetComponent } from './tabset.component';\n\nconst DIRECTIVES = [\n  NzTabSetComponent,\n  NzTabComponent,\n  NzTabNavBarComponent,\n  NzTabNavItemDirective,\n  NzTabsInkBarDirective,\n  NzTabScrollListDirective,\n  NzTabNavOperationComponent,\n  NzTabAddButtonComponent,\n  NzTabCloseButtonComponent,\n  NzTabDirective,\n  NzTabBodyComponent,\n  NzTabLinkDirective,\n  NzTabLinkTemplateDirective,\n  NzTabBarExtraContentDirective\n];\n\n@NgModule({\n  imports: [DIRECTIVES],\n  exports: [DIRECTIVES]\n})\nexport class NzTabsModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './interfaces';\nexport { NzTabAddButtonComponent as ɵNzTabAddButtonComponent } from './tab-add-button.component';\nexport * from './tab-bar-extra-content.directive';\nexport { NzTabBodyComponent as ɵNzTabBodyComponent } from './tab-body.component';\nexport { NzTabCloseButtonComponent as ɵNzTabCloseButtonComponent } from './tab-close-button.component';\nexport * from './tab-link.directive';\nexport { NzTabNavBarComponent as ɵNzTabNavBarComponent } from './tab-nav-bar.component';\nexport { NzTabNavItemDirective as ɵNzTabNavItemDirective } from './tab-nav-item.directive';\nexport { NzTabNavOperationComponent as ɵNzTabNavOperationComponent } from './tab-nav-operation.component';\nexport { NzTabScrollListDirective as ɵNzTabScrollListDirective } from './tab-scroll-list.directive';\nexport * from './tab.component';\nexport * from './tab.directive';\nexport { NzTabsInkBarDirective as ɵNzTabsInkBarDirective } from './tabs-ink-bar.directive';\nexport * from './tabs.module';\nexport * from './tabset.component';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1", "i2", "i3", "i4"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;MAmBU,gBAAgB,CAAA;AAC3B,IAAA,KAAK;AACL,IAAA,GAAG;AACJ;;ACzBD;;;AAGG;MAsBU,uBAAuB,CAAA;AAKd,IAAA,UAAA;IAJX,OAAO,GAAoC,MAAM;AAEzC,IAAA,OAAO;AAExB,IAAA,WAAA,CAAoB,UAAmC,EAAA;QAAnC,IAAU,CAAA,UAAA,GAAV,UAAU;QAC5B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;;IAG9C,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC;;IAGvC,gBAAgB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC;;uGAd7B,uBAAuB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,uBAAuB,EAZxB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,8CAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;GAIT,EAMS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,cAAc,gPAAE,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,CAAA;;2FAE3B,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAdnC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,8CAA8C;AACxD,oBAAA,QAAQ,EAAE;;;;AAIT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,kBAAkB;AACzB,wBAAA,YAAY,EAAE,SAAS;AACvB,wBAAA,IAAI,EAAE;AACP,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,cAAc,EAAE,YAAY;AACvC,iBAAA;+EAEU,OAAO,EAAA,CAAA;sBAAf;;;AC1BH;;;AAGG;MAOU,6BAA6B,CAAA;IAC/B,QAAQ,GAAG,KAAK,CAAkB,KAAK,EAAE,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC;AAC3E,IAAA,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;uGAF/B,6BAA6B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA7B,6BAA6B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uCAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,UAAA,EAAA,sBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAA7B,6BAA6B,EAAA,UAAA,EAAA,CAAA;kBAHzC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE;AACX,iBAAA;;;ACTD;;;AAGG;MA2BU,kBAAkB,CAAA;IACpB,OAAO,GAA6B,IAAI;IACxC,MAAM,GAAG,KAAK;IACd,QAAQ,GAAG,IAAI;uGAHb,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,miBAdnB,CAA4D,0DAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAW5D,gBAAgB,EACd,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA,CAAC,eAAe,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAElB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBApB9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,mBAAmB,EAAE,KAAK;oBAC1B,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE,CAA4D,0DAAA,CAAA;AACtE,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,kBAAkB;AACzB,wBAAA,iCAAiC,EAAE,QAAQ;AAC3C,wBAAA,iCAAiC,EAAE,2BAA2B;AAC9D,wBAAA,iBAAiB,EAAE,iBAAiB;AACpC,wBAAA,oBAAoB,EAAE,SAAS;AAC/B,wBAAA,oBAAoB,EAAE,0CAA0C;AAChE,wBAAA,oBAAoB,EAAE,CAA4B,0BAAA,CAAA;AAClD,wBAAA,cAAc,EAAE,CAAW,SAAA;AAC5B,qBAAA;oBACD,OAAO,EAAE,CAAC,gBAAgB,CAAC;oBAC3B,UAAU,EAAE,CAAC,eAAe;AAC7B,iBAAA;8BAEU,OAAO,EAAA,CAAA;sBAAf;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;;;ACjCH;;;AAGG;MAsBU,yBAAyB,CAAA;IAC3B,SAAS,GAAoC,OAAO;uGADlD,yBAAyB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAzB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,yBAAyB,EAZ1B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kDAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;GAIT,EAMS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,cAAc,gPAAE,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,CAAA;;2FAE3B,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBAdrC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kDAAkD;AAC5D,oBAAA,QAAQ,EAAE;;;;AAIT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,qBAAqB;AAC5B,wBAAA,YAAY,EAAE,WAAW;AACzB,wBAAA,IAAI,EAAE;AACP,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,cAAc,EAAE,YAAY;AACvC,iBAAA;8BAEU,SAAS,EAAA,CAAA;sBAAjB;;;AC1BH;;;AAGG;AAOH;;AAEG;MAKU,0BAA0B,CAAA;IACrC,WAAW,GAAoC,MAAM,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;uGADvE,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA1B,0BAA0B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAA1B,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBAJtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wBAAwB;AAClC,oBAAA,QAAQ,EAAE;AACX,iBAAA;;AAKD;;AAEG;MAKU,kBAAkB,CAAA;AAGV,IAAA,UAAA;AAFnB,IAAA,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAE/D,IAAA,WAAA,CAAmB,UAAyC,EAAA;QAAzC,IAAU,CAAA,UAAA,GAAV,UAAU;;uGAHlB,kBAAkB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAJ9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE;AACX,iBAAA;;;MCdY,qBAAqB,CAAA;AAOb,IAAA,UAAA;IANqB,QAAQ,GAAY,KAAK;AACxD,IAAA,GAAG;IAC4B,MAAM,GAAY,KAAK;AACvD,IAAA,EAAE;AACF,IAAA,aAAa;AAErB,IAAA,WAAA,CAAmB,UAAmC,EAAA;QAAnC,IAAU,CAAA,UAAA,GAAV,UAAU;AAC3B,QAAA,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,aAAa;QAClC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,EAAE,CAAC,aAAc;;IAG7C,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE;;AAGjB,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW;;AAGvC,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY;;AAGxC,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU;;AAGtC,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS;;uGA7B1B,qBAAqB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAArB,qBAAqB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EACZ,gBAAgB,CAAA,EAAA,GAAA,EAAA,KAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAEhB,gBAAgB,CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAHzB,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAHjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE;AACX,iBAAA;+EAEyC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,GAAG,EAAA,CAAA;sBAAX;gBACuC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;;;AChBxC;;;AAGG;MAsFU,0BAA0B,CAAA;AAY5B,IAAA,GAAA;AACC,IAAA,UAAA;IAZD,KAAK,GAA4B,EAAE;IACJ,OAAO,GAAY,KAAK;IACvD,OAAO,GAAoC,MAAM;AAEvC,IAAA,UAAU,GAAG,IAAI,YAAY,EAAQ;AACrC,IAAA,QAAQ,GAAG,IAAI,YAAY,EAAyB;AACvE,IAAA,2BAA2B;IAC3B,UAAU,GAAG,KAAK;AAED,IAAA,OAAO;IACxB,WACS,CAAA,GAAsB,EACrB,UAAmC,EAAA;QADpC,IAAG,CAAA,GAAA,GAAH,GAAG;QACF,IAAU,CAAA,UAAA,GAAV,UAAU;QAElB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;;AAG9C,IAAA,QAAQ,CAAC,IAA2B,EAAA;AAClC,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;AAElB,YAAA,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;AACvB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;;IAI5B,aAAa,CAAC,IAA2B,EAAE,CAAa,EAAA;AACtD,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;;;IAGlC,SAAS,GAAA;AACP,QAAA,YAAY,CAAC,IAAI,CAAC,2BAA2B,CAAC;AAC9C,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;AACtB,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB,IAAA,aAAa,CAAC,OAAgB,EAAA;QAC5B,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,IAAI,CAAC,2BAA2B,GAAG,UAAU,CAAC,MAAK;AACjD,gBAAA,IAAI,CAAC,UAAU,GAAG,KAAK;AACvB,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;aACxB,EAAE,GAAG,CAAC;;;IAIX,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC;;IAGvC,gBAAgB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC;;IAGxC,WAAW,GAAA;AACT,QAAA,YAAY,CAAC,IAAI,CAAC,2BAA2B,CAAC;;uGAvDrC,0BAA0B,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA1B,0BAA0B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAEjB,gBAAgB,CAzD1B,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,sCAAA,EAAA,oBAAA,EAAA,EAAA,cAAA,EAAA,yBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyCT,EAMC,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,YAAY,EACZ,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,cAAc,EACd,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAC,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,uBAAuB,8GACvB,uBAAuB,EAAA,QAAA,EAAA,kBAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EACvB,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,SAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,cAAA,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,mBAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,eAAA,EAAA,YAAA,EAAA,YAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,eAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACZ,mBAAmB,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,WAAA,EAAA,qBAAA,EAAA,YAAA,EAAA,aAAA,EAAA,YAAA,EAAA,WAAA,EAAA,oBAAA,EAAA,gBAAA,EAAA,aAAA,CAAA,EAAA,OAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAGV,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBA7DtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,mBAAmB,EAAE,KAAK;oBAC1B,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,yBAAyB;AAChC,wBAAA,wCAAwC,EAAE;AAC3C,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,YAAY;wBACZ,cAAc;wBACd,uBAAuB;wBACvB,uBAAuB;wBACvB,YAAY;wBACZ;AACD;AACF,iBAAA;+GAEU,KAAK,EAAA,CAAA;sBAAb;gBACuC,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,OAAO,EAAA,CAAA;sBAAf;gBAEkB,UAAU,EAAA,CAAA;sBAA5B;gBACkB,QAAQ,EAAA,CAAA;sBAA1B;;;AC/FH;;;AAGG;AAYH,MAAM,kBAAkB,GAAG,GAAG;AAC9B,MAAM,mBAAmB,GAAG,IAAI;AAChC,MAAM,gBAAgB,GAAG,EAAE;AAC3B,MAAM,kBAAkB,GAAG,KAAK,IAAI,gBAAgB;MAKvC,wBAAwB,CAAA;AAiBzB,IAAA,MAAA;AACA,IAAA,UAAA;IAjBV,kBAAkB,GAAqB,IAAI;IAC3C,kBAAkB,GAAG,CAAC;IACtB,aAAa,GAAG,CAAC;IACjB,YAAY,GAAG,CAAC;IAChB,cAAc,GAAG,CAAC;IAClB,gBAAgB,GAAG,KAAK;IACxB,aAAa,GAAiC,IAAI;IAClD,UAAU,GAAiC,IAAI;IAC/C,MAAM,GAAG,CAAC,CAAC;AAEX,IAAA,WAAW,GAAe,MAAM,KAAK,CAAC;AAEnB,IAAA,YAAY,GAAG,IAAI,YAAY,EAA8B;AAC7D,IAAA,SAAS,GAAG,IAAI,YAAY,EAAoB;IAEnE,WACU,CAAA,MAAc,EACd,UAAmC,EAAA;QADnC,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAU,CAAA,UAAA,GAAV,UAAU;;IAGpB,QAAQ,GAAA;QACN,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAK;AACpD,YAAA,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;YAExC,MAAM,MAAM,GAAG,SAAS,CAAa,EAAE,EAAE,OAAO,CAAC;YACjD,MAAM,WAAW,GAAG,SAAS,CAAa,EAAE,EAAE,YAAY,CAAC;YAC3D,MAAM,UAAU,GAAG,SAAS,CAAa,EAAE,EAAE,WAAW,CAAC;YACzD,MAAM,SAAS,GAAG,SAAS,CAAa,EAAE,EAAE,UAAU,CAAC;AAEvD,YAAA,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE;AACvC,YAAA,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AACnE,YAAA,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAClF,YAAA,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC/E,YAAA,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAE5E,YAAA,OAAO,MAAK;gBACV,YAAY,CAAC,WAAW,EAAE;AAC5B,aAAC;AACH,SAAC,CAAC;;AAGJ,IAAA,aAAa,CACX,IAA8B,EAC9B,UAAyB,EACzB,OAAsC,EAAA;AAEtC,QAAA,OAAO,UAAU,CAAC,SAAS,CAAC,KAAK,IAAG;AAClC,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAClB,IAAI;gBACJ;AACmB,aAAA,CAAC;AACtB,YAAA,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;gBAC3B,OAAO,CAAC,KAAK,CAAC;;AAElB,SAAC,CAAC;;AAGJ,IAAA,UAAU,GAAG,CAAC,CAAa,KAAU;AACnC,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB;;AAEF,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU;AAClC,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY;QAEtC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI;QAE3C,IAAI,UAAU,EAAE;AACd,YAAA,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,GAAG,YAAY;AAC7C,YAAA,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,GAAG,YAAY;YAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;YAChC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;;YAGhC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,kBAAkB,EAAE;gBAC7C;;YAGF,IAAI,QAAQ,GAAG,SAAS;YACxB,IAAI,QAAQ,GAAG,SAAS;YAExB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,MAAK;AACpC,gBAAA,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,mBAAmB,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,mBAAmB,EAAE;AACxF,oBAAA,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC;oBACjC;;gBAGF,QAAQ,IAAI,kBAAkB;gBAC9B,QAAQ,IAAI,kBAAkB;AAC9B,gBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,gBAAgB,EAAE,QAAQ,GAAG,gBAAgB,EAAE,CAAC,CAAC;aAC3E,EAAE,gBAAgB,CAAC;;AAExB,KAAC;AAED,IAAA,WAAW,GAAG,CAAC,CAAa,KAAU;AACpC,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB;;QAGF,CAAC,CAAC,cAAc,EAAE;AAClB,QAAA,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QAEzC,MAAM,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9C,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;AAClC,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;QAEtB,IAAI,CAAC,YAAY,GAAG,GAAG,GAAG,IAAI,CAAC,aAAa;AAC5C,QAAA,IAAI,CAAC,aAAa,GAAG,GAAG;AACxB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE;AAC5C,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE;AACjD,KAAC;AAED,IAAA,YAAY,GAAG,CAAC,CAAa,KAAU;AACrC,QAAA,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACzC,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE;AAC/C,QAAA,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC;AACnC,KAAC;AAED,IAAA,OAAO,GAAG,CAAC,CAAa,KAAU;AAChC,QAAA,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC;AAC5B,QAAA,IAAI,KAAa;QACjB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;AAE7B,QAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,YAAA,KAAK,GAAG,IAAI,CAAC,kBAAkB,KAAK,GAAG,GAAG,MAAM,GAAG,MAAM;;AACpD,aAAA,IAAI,IAAI,GAAG,IAAI,EAAE;YACtB,KAAK,GAAG,MAAM;AACd,YAAA,IAAI,CAAC,kBAAkB,GAAG,GAAG;;aACxB;YACL,KAAK,GAAG,MAAM;AACd,YAAA,IAAI,CAAC,kBAAkB,GAAG,GAAG;;;AAI/B,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;AAEhC,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,kBAAkB,GAAG,GAAG,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,GAAG,EAAE,EAAE;AAC9E,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;;QAE/B,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;QAChC,IAAI,CAAC,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC/C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;AAG9B,QAAA,IAAI,CAAC,kBAAkB,GAAG,GAAG;AAC7B,QAAA,IAAI,CAAC,cAAc,GAAG,QAAQ;AAChC,KAAC;AAED,IAAA,QAAQ,CAAC,CAAS,EAAE,CAAS,EAAE,KAAY,EAAA;AACzC,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAK;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACrB,CAAC;gBACD,CAAC;gBACD;AACD,aAAA,CAAC;AACJ,SAAC,CAAC;;IAGJ,WAAW,GAAA;QACT,IAAI,CAAC,WAAW,EAAE;;uGAlKT,wBAAwB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAxB,wBAAwB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAxB,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBAHpC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE;AACX,iBAAA;oGAcoB,YAAY,EAAA,CAAA;sBAA9B;gBACkB,SAAS,EAAA,CAAA;sBAA3B;;;ACrCH;;;AAGG;MAgBU,qBAAqB,CAAA;AAUtB,IAAA,UAAA;AACA,IAAA,MAAA;IAVD,QAAQ,GAAsB,YAAY;IAC1C,QAAQ,GAAG,IAAI;IAExB,aAAa,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AACjE,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,aAAa,KAAK,gBAAgB,IAAI,IAAI,CAAC,QAAQ;;IAGjE,WACU,CAAA,UAAmC,EACnC,MAAc,EAAA;QADd,IAAU,CAAA,UAAA,GAAV,UAAU;QACV,IAAM,CAAA,MAAA,GAAN,MAAM;;AAGhB,IAAA,cAAc,CAAC,OAAoB,EAAA;AACjC,QAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAK;YACjC,YAAY,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAC7C,SAAC,CAAC;;AAGJ,IAAA,SAAS,CAAC,OAAoB,EAAA;AAC5B,QAAA,MAAM,MAAM,GAAgB,IAAI,CAAC,UAAU,CAAC,aAAa;AAEzD,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,YAAY,EAAE;AAClC,YAAA,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE;AACrB,YAAA,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;YACxB,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;;aAC7C;AACL,YAAA,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE;AACtB,YAAA,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;YACvB,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;;;AAIxD,IAAA,eAAe,CAAC,OAAoB,EAAA;AAClC,QAAA,OAAO,OAAO,GAAG,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,CAAI,EAAA,CAAA,GAAG,GAAG;;AAGvD,IAAA,eAAe,CAAC,OAAoB,EAAA;AAClC,QAAA,OAAO,OAAO,GAAG,GAAG,OAAO,CAAC,WAAW,IAAI,CAAC,CAAI,EAAA,CAAA,GAAG,GAAG;;AAGxD,IAAA,cAAc,CAAC,OAAoB,EAAA;AACjC,QAAA,OAAO,OAAO,GAAG,GAAG,OAAO,CAAC,SAAS,IAAI,CAAC,CAAI,EAAA,CAAA,GAAG,GAAG;;AAGtD,IAAA,gBAAgB,CAAC,OAAoB,EAAA;AACnC,QAAA,OAAO,OAAO,GAAG,GAAG,OAAO,CAAC,YAAY,IAAI,CAAC,CAAI,EAAA,CAAA,GAAG,GAAG;;uGAjD9C,qBAAqB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAArB,qBAAqB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oCAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iCAAA,EAAA,WAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAArB,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAPjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oCAAoC;AAC9C,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,kBAAkB;AACzB,wBAAA,mCAAmC,EAAE;AACtC;AACF,iBAAA;oGAEU,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;;;ACrBH;;;AAGG;AA8CH,MAAM,gBAAgB,GAAG,OAAO,qBAAqB,KAAK,WAAW,GAAG,uBAAuB,GAAG,aAAa;AAC/G,MAAM,kBAAkB,GAAG,GAAG;MAwEjB,oBAAoB,CAAA;AAmFrB,IAAA,GAAA;AACA,IAAA,MAAA;AACA,IAAA,aAAA;AACA,IAAA,gBAAA;AACA,IAAA,GAAA;AAtFS,IAAA,YAAY,GAAyB,IAAI,YAAY,EAAU;AAC/D,IAAA,kBAAkB,GAAyB,IAAI,YAAY,EAAU;AACrE,IAAA,UAAU,GAAG,IAAI,YAAY,EAAQ;AACrC,IAAA,SAAS,GAAG,IAAI,YAAY,EAAoB;IAE1D,QAAQ,GAAsB,YAAY;IACX,OAAO,GAAY,KAAK;IACxB,OAAO,GAAY,KAAK;IACvD,OAAO,GAAoC,MAAM;IACjD,cAAc,GAAG,IAAI;AACrB,IAAA,aAAa;AAEb,IAAA,aAAa,GAAG,KAAK,CAAC,QAAQ,EAA4C;IAE1E,iBAAiB,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC;IAClG,eAAe,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,CAAC;AAEvG,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc;;IAE5B,IAAI,aAAa,CAAC,KAAa,EAAA;AAC7B,QAAA,MAAM,QAAQ,GAAG,oBAAoB,CAAC,KAAK,CAAC;AAC5C,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,QAAQ,EAAE;AACpC,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC3B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;AAChC,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,gBAAA,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC;;;;AAKL,IAAA,UAAU;AACV,IAAA,UAAU;AACO,IAAA,YAAY;AACd,IAAA,SAAS;AACZ,IAAA,MAAM;AACK,IAAA,KAAK;;AAGpE,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,eAAgB,GAAG,CAAC;;;IAI/D,IAAI,UAAU,CAAC,KAAa,EAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAC9E;;AAGF,QAAA,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC;;AAGtC,IAAA,IAAI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO;;IAGtD,SAAS,GAAkB,IAAI;IAC/B,UAAU,GAAG,CAAC;IACd,UAAU,GAAG,CAAC;IACd,QAAQ,GAAG,KAAK;IAChB,SAAS,GAAG,KAAK;IACjB,OAAO,GAAG,KAAK;IACf,UAAU,GAAG,KAAK;IAClB,WAAW,GAA4B,EAAE;AAEjC,IAAA,UAAU;AACV,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;IAC9B,cAAc,GAAG,CAAC;IAClB,YAAY,GAAG,CAAC;IAChB,aAAa,GAAG,CAAC;IACjB,eAAe,GAAG,CAAC;IACnB,gBAAgB,GAAG,CAAC;IACpB,cAAc,GAAG,CAAC;IAClB,eAAe,GAAG,CAAC;IACnB,cAAc,GAAG,CAAC;IAClB,eAAe,GAAG,CAAC;IACnB,oBAAoB,GAAG,KAAK;AAC5B,IAAA,sBAAsB;AACtB,IAAA,yBAAyB;IAEjC,WACU,CAAA,GAAsB,EACtB,MAAc,EACd,aAA4B,EAC5B,gBAAkC,EAClC,GAAmB,EAAA;QAJnB,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAa,CAAA,aAAA,GAAb,aAAa;QACb,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;QAChB,IAAG,CAAA,GAAA,GAAH,GAAG;;IAGb,eAAe,GAAA;QACb,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC;QACtE,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC;QAE7C,MAAM,OAAO,GAAG,MAAW;YACzB,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,wBAAwB,EAAE;AACjC,SAAC;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,eAAe,CAAwB,IAAI,CAAC,KAAK;AACpE,aAAA,yBAAyB,CAAC,IAAI,CAAC,kBAAkB,EAAE;AACnD,aAAA,QAAQ,EAAE;QACb,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC;QAEpD,YAAY,CAAC,OAAO,CAAC;QAErB,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;AACjG,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,gBAAgB,CAAC;aAC9D,SAAS,CAAC,MAAK;AACd,YAAA,OAAO,EAAE;AACX,SAAC,CAAC;QACJ,KAAK,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO;AACxC,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC7B,SAAS,CAAC,MAAK;YACd,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;YAC/B,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;AACtE,SAAC,CAAC;AAEJ,QAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,IAAG;AAC9E,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC;AACrC,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;YAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,UAAW,CAAC;AAC/C,SAAC,CAAC;;IAGJ,qBAAqB,GAAA;AACnB,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,wBAAwB,EAAE;AAC/B,YAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;AACjC,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;;IAI3B,WAAW,GAAA;AACT,QAAA,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC;AACzC,QAAA,YAAY,CAAC,IAAI,CAAC,yBAAyB,CAAC;AAC5C,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;AAG1B,IAAA,kBAAkB,CAAC,GAA0B,EAAA;AAC3C,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC;AAC/D,QAAA,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,CAAC;YAC1C,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,aAAa,EAAE;gBAC1C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;AAC7C,gBAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;;;;AAK3B,IAAA,cAAc,CAAC,CAA6B,EAAA;AAC1C,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,YAAY,EAAE;AAClC,YAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;AAChC,gBAAA,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;oBACnC;;AAEF,gBAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;oBAC1E;;;AAGJ,YAAA,CAAC,CAAC,KAAK,CAAC,cAAc,EAAE;AACxB,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;;aAChC;AACL,YAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;AAChC,gBAAA,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;oBACnC;;AAEF,gBAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;oBAC5E;;;AAGJ,YAAA,CAAC,CAAC,KAAK,CAAC,cAAc,EAAE;AACxB,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7D,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC;;QAGvC,IAAI,CAAC,aAAa,EAAE;QACpB,IAAI,CAAC,eAAe,EAAE;QACtB,IAAI,CAAC,aAAa,EAAE;;AAGtB,IAAA,aAAa,CAAC,KAAoB,EAAA;AAChC,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAqB,CAAC;QAC5F,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC9C;;AAGF,QAAA,QAAQ,KAAK,CAAC,OAAO;AACnB,YAAA,KAAK,UAAU;AACf,YAAA,KAAK,QAAQ;AACb,YAAA,KAAK,WAAW;AAChB,YAAA,KAAK,UAAU;gBACb,IAAI,CAAC,aAAa,EAAE;AACpB,gBAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC;gBAChC;AACF,YAAA,KAAK,KAAK;AACV,YAAA,KAAK,KAAK;gBACR,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,aAAa,EAAE;oBAC1C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;;gBAE/C;AACF,YAAA;AACE,gBAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC;;;AAI9B,IAAA,YAAY,CAAC,KAAa,EAAA;AAChC,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACf,YAAA,OAAO,IAAI;;QAGb,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI;QAC3D,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ;;AAGvB,IAAA,WAAW,CAAC,GAA0B,EAAA;AAC5C,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;YACpC;;QAEF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AAEjC,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,YAAY,EAAE;AAClC,YAAA,IAAI,YAAY,GAAG,IAAI,CAAC,UAAU;AAClC,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,KAAK,EAAE;gBACvC,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK;AAEjE,gBAAA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE;oBAC3B,YAAY,GAAG,KAAK;;AACf,qBAAA,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE;oBAClE,YAAY,GAAG,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY;;;iBAEjD,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE;AACtC,gBAAA,YAAY,GAAG,CAAC,GAAG,CAAC,IAAI;;AACnB,iBAAA,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE;AACtE,gBAAA,YAAY,GAAG,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;;AAE5D,YAAA,IAAI,CAAC,UAAU,GAAG,YAAY;AAC9B,YAAA,IAAI,CAAC,UAAU,GAAG,CAAC;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC;;aAC7B;AACL,YAAA,IAAI,YAAY,GAAG,IAAI,CAAC,UAAU;YAElC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE;AAC9B,gBAAA,YAAY,GAAG,CAAC,GAAG,CAAC,GAAG;;AAClB,iBAAA,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE;AACvE,gBAAA,YAAY,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;;AAE7D,YAAA,IAAI,CAAC,UAAU,GAAG,YAAY;AAC9B,YAAA,IAAI,CAAC,UAAU,GAAG,CAAC;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,YAAY,CAAC;;AAGpC,QAAA,YAAY,CAAC,IAAI,CAAC,yBAAyB,CAAC;AAC5C,QAAA,IAAI,CAAC,yBAAyB,GAAG,UAAU,CAAC,MAAK;YAC/C,IAAI,CAAC,eAAe,EAAE;SACvB,EAAE,kBAAkB,CAAC;;IAGhB,aAAa,GAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;AAChC,YAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAK;gBACjC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,MAAM;AACvD,gBAAA,IAAI,CAAC,sBAAsB,GAAG,UAAU,CAAC,MAAK;oBAC5C,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE;AACnD,oBAAA,IAAI,CAAC,sBAAsB,GAAG,SAAS;iBACxC,EAAE,kBAAkB,CAAC;AACxB,aAAC,CAAC;;;IAIE,YAAY,CAAC,CAAS,EAAE,CAAS,EAAA;AACvC,QAAA,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,CAAa,UAAA,EAAA,CAAC,CAAO,IAAA,EAAA,CAAC,KAAK;;AAGrE,IAAA,eAAe,CAAC,SAAiB,EAAA;QACvC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe;AAC5D,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,KAAK,EAAE;AACvC,YAAA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;;aAC/C;AACL,YAAA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;;;AAIhD,IAAA,eAAe,CAAC,SAAiB,EAAA;QACvC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;;IAG7E,wBAAwB,GAAA;QAC9B,IAAI,CAAC,UAAU,EAAE;QACjB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC;QACvD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC;QACvD,IAAI,CAAC,eAAe,EAAE;QACtB,IAAI,CAAC,aAAa,EAAE;AACpB,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,eAAgB,CAAC;AAClE,YAAA,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;gBAC9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC;;;;IAK1C,UAAU,GAAA;AAChB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,GAAG,CAAC;AAC3E,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,GAAG,CAAC;QAC7E,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE;QACzD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;AAC3D,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC;AAClE,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,IAAI,CAAC;AACpE,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,IAAI,CAAC;AACvE,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC;;IAG/D,wBAAwB,GAAA;AAC9B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI;AACtG,QAAA,MAAM,mBAAmB,GAAG,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,aAAa,GAAG,IAAI;QAEvF,IAAI,mBAAmB,EAAE;AACvB;;;;AAIG;YACH,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,mBAAmB,CAAC,aAAc,CAAC;;;IAI1D,aAAa,GAAA;AACnB,QAAA,MAAM,IAAI,GAAG;AACX,YAAA,GAAG,EAAE,KAAK;AACV,YAAA,KAAK,EAAE,KAAK;AACZ,YAAA,MAAM,EAAE,KAAK;AACb,YAAA,IAAI,EAAE;SACP;AACD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;AAC7C,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,YAAY,EAAE;AAClC,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,KAAK,EAAE;gBACvC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC;AAChC,gBAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe;;iBACjE;gBACL,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC;AAC/B,gBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe;;;aAErE;YACL,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC;AAC9B,YAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB;;QAG5E,MAAM,CAAC,IAAI,CAAC,IAAI,CAAgD,CAAC,OAAO,CAAC,GAAG,IAAG;AAC9E,YAAA,MAAM,SAAS,GAAG,CAA0B,uBAAA,EAAA,GAAG,EAAE;AACjD,YAAA,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE;AACb,gBAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC;;iBAC3B;AACL,gBAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC;;AAEvC,SAAC,CAAC;;IAGI,eAAe,GAAA;AACrB,QAAA,IAAI,IAAwB;AAC5B,QAAA,IAAI,QAAkC;AACtC,QAAA,IAAI,aAAqB;AACzB,QAAA,IAAI,SAAiB;AACrB,QAAA,IAAI,cAAsB;AAC1B,QAAA,IAAI,OAAe;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;QACjC,MAAM,YAAY,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;AAEvE,QAAA,MAAM,SAAS,GAAG,CAAC,KAAa,KAAY;AAC1C,YAAA,IAAI,MAAc;YAClB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,YAAY;AACxC,YAAA,IAAI,QAAQ,KAAK,OAAO,EAAE;AACxB,gBAAA,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK;;iBACvE;AACL,gBAAA,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;;AAEzB,YAAA,OAAO,MAAM;AACf,SAAC;AAED,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,YAAY,EAAE;YAClC,IAAI,GAAG,OAAO;AACd,YAAA,SAAS,GAAG,IAAI,CAAC,YAAY;YAC7B,cAAc,GAAG,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;AAC3F,YAAA,OAAO,GAAG,IAAI,CAAC,cAAc;YAC7B,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,KAAK,EAAE;gBACvC,QAAQ,GAAG,OAAO;gBAClB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC;AACpC,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe;;iBACrE;gBACL,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC;AACnC,gBAAA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe;gBAC5E,QAAQ,GAAG,MAAM;;;aAEd;YACL,IAAI,GAAG,QAAQ;AACf,YAAA,SAAS,GAAG,IAAI,CAAC,aAAa;YAC9B,cAAc,GAAG,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;AAC7F,YAAA,OAAO,GAAG,IAAI,CAAC,eAAe;YAC9B,QAAQ,GAAG,KAAK;AAChB,YAAA,aAAa,GAAG,CAAC,IAAI,CAAC,UAAU;YAChC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC;AAClC,YAAA,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB;;QAGjF,IAAI,eAAe,GAAG,SAAS;AAC/B,QAAA,IAAI,cAAc,GAAG,OAAO,GAAG,SAAS,EAAE;AACxC,YAAA,eAAe,GAAG,SAAS,GAAG,OAAO;;AAGvC,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;AACrB,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;YACvB;;AAGF,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM;QACvB,IAAI,QAAQ,GAAG,GAAG;AAClB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;AAC/B,YAAA,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC;YAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY;YACpC,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,aAAa,GAAG,eAAe,EAAE;AACzD,gBAAA,QAAQ,GAAG,CAAC,GAAG,CAAC;gBAChB;;;QAIJ,IAAI,UAAU,GAAG,CAAC;AAClB,QAAA,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AACpC,YAAA,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC;AAC3B,YAAA,IAAI,MAAM,GAAG,aAAa,EAAE;AAC1B,gBAAA,UAAU,GAAG,CAAC,GAAG,CAAC;gBAClB;;;QAIJ,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC;QACjD,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,eAAe,EAAE,GAAG,aAAa,CAAC;AACzD,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;IAGjB,kBAAkB,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;;IAGrD,WAAW,CAAC,SAAiB,EAAA;AAErC,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO;;QAE5B,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE;YACzC,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,wBAAwB,EAAE;;;uGAvcxB,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAAD,IAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAApB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,oBAAoB,EAOX,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,QAAA,EAAA,KAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,OAAA,EAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,QAAA,EAAA,KAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,gBAAgB,EAChB,EAAA,OAAA,EAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,QAAA,EAAA,KAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,gBAAgB,EA8BnB,EAAA,OAAA,EAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,QAAA,EAAA,KAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,EAAA,iBAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,KAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,aAAA,EAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,UAAA,EAAA,eAAA,EAAA,QAAA,EAAA,KAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,aAAA,EAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,UAAA,EAAA,eAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,aAAA,EAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,UAAA,EAAA,eAAA,EAAA,QAAA,EAAA,KAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,uBAAA,EAAA,EAAA,cAAA,EAAA,cAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,qBAAqB,EAH3B,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,0BAA0B,EAC1B,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,uBAAuB,EACvB,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,qBAAqB,EArGtB,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmDT,EAMC,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,wBAAwB,sGACxB,uBAAuB,EAAA,QAAA,EAAA,8CAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACvB,qBAAqB,EACrB,QAAA,EAAA,oCAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,0BAA0B,gLAC1B,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAGP,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAtEhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,mBAAmB,EAAE,KAAK;oBAC1B,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,cAAc;AACrB,wBAAA,WAAW,EAAE;AACd,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,wBAAwB;wBACxB,uBAAuB;wBACvB,qBAAqB;wBACrB,0BAA0B;wBAC1B;AACD;AACF,iBAAA;yMAEoB,YAAY,EAAA,CAAA;sBAA9B;gBACkB,kBAAkB,EAAA,CAAA;sBAApC;gBACkB,UAAU,EAAA,CAAA;sBAA5B;gBACkB,SAAS,EAAA,CAAA;sBAA3B;gBAEQ,QAAQ,EAAA,CAAA;sBAAhB;gBACuC,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,OAAO,EAAA,CAAA;sBAAf;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBAQG,aAAa,EAAA,CAAA;sBADhB;gBAeuC,UAAU,EAAA,CAAA;sBAAjD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACmB,YAAY,EAAA,CAAA;sBAApE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,0BAA0B,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACA,SAAS,EAAA,CAAA;sBAA/D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,uBAAuB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACD,MAAM,EAAA,CAAA;sBAAzD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,qBAAqB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACa,KAAK,EAAA,CAAA;sBAAnE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,qBAAqB,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;;;AChK/D;;;AAGG;AAIH;MAKa,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,QAAQ,EAAE;AACX,iBAAA;;;ACXD;;;AAGG;AA2BH;;AAEG;MACU,UAAU,GAAG,IAAI,cAAc,CAAY,YAAY;MAiBvD,cAAc,CAAA;IAChB,OAAO,GAA6C,EAAE;IACvB,UAAU,GAAG,KAAK;IACjD,WAAW,GAAoC,OAAO;IACvB,UAAU,GAAG,KAAK;IAClB,aAAa,GAAG,KAAK;AAC1C,IAAA,QAAQ,GAAG,IAAI,YAAY,EAAQ;AACnC,IAAA,UAAU,GAAG,IAAI,YAAY,EAAQ;AACrC,IAAA,OAAO,GAAG,IAAI,YAAY,EAAQ;AAClC,IAAA,aAAa,GAAG,IAAI,YAAY,EAAc;AAEJ,IAAA,0BAA0B;IACnB,QAAQ,GAA6B,IAAI;AACxD,IAAA,aAAa;AAClB,IAAA,eAAe;IAE/D,QAAQ,GAAY,KAAK;IACzB,aAAa,GAAG,KAAK;IACrB,QAAQ,GAAkB,IAAI;IAC9B,MAAM,GAAkB,IAAI;AAC5B,IAAA,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC;AAEzB,IAAA,YAAY,GAAG,IAAI,OAAO,EAAQ;AAE3C,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe;;AAG9C,IAAA,IAAI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,0BAA0B,EAAE,WAAW;;AAGrE,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,OAAO;AACtD,QAAA,IAAI,OAAO,IAAI,UAAU,IAAI,aAAa,EAAE;AAC1C,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;;IAI5B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;;AAG9B,IAAA,SAAS,CAAC,MAAe,EAAA;AACvB,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM;QACtB,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;;uGA9ClB,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,EAEL,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAEhB,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,qDAChB,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,4BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAMtB,0BAA0B,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAC1B,cAAc,EAAA,WAAA,EAAA,IAAA,EAAA,IAAA,EAAyB,WAAW,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAClD,kBAAkB,EAtBtB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,OAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;AAOT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEU,cAAc,EAAA,UAAA,EAAA,CAAA;kBAf1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,QAAQ;AAClB,oBAAA,QAAQ,EAAE,OAAO;AACjB,oBAAA,mBAAmB,EAAE,KAAK;oBAC1B,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE;;;;;;;AAOT,EAAA;AACF,iBAAA;8BAEU,OAAO,EAAA,CAAA;sBAAf;gBACuC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,WAAW,EAAA,CAAA;sBAAnB;gBACuC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACnB,QAAQ,EAAA,CAAA;sBAA1B;gBACkB,UAAU,EAAA,CAAA;sBAA5B;gBACkB,OAAO,EAAA,CAAA;sBAAzB;gBACkB,aAAa,EAAA,CAAA;sBAA/B;gBAE4D,0BAA0B,EAAA,CAAA;sBAAtF,YAAY;AAAC,gBAAA,IAAA,EAAA,CAAA,0BAA0B,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACS,QAAQ,EAAA,CAAA;sBAA3E,YAAY;uBAAC,cAAc,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE;gBACb,aAAa,EAAA,CAAA;sBAAjE,YAAY;AAAC,gBAAA,IAAA,EAAA,CAAA,kBAAkB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACH,eAAe,EAAA,CAAA;sBAA9D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;;;ACJhD,MAAM,qBAAqB,GAAgB,MAAM;AAEjD,IAAI,MAAM,GAAG,CAAC;IAuID,iBAAiB,GAAA,CAAA,MAAA;;;;;;;;;;;;;iBAAjB,iBAAiB,CAAA;;;AAelB,YAAA,kBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,kBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,sBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;AACZ,YAAA,0BAAA,GAAA,CAAA,UAAU,EAAE,CAAA;YAHC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,kBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,QAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,QAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,MAAM,EAAN,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,MAAM,GAAqB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,oBAAA,EAAA,yBAAA,CAAA;YAC3B,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,kBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,QAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,QAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,MAAM,EAAN,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,MAAM,GAA4B,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,oBAAA,EAAA,yBAAA,CAAA;YAClC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,YAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,UAAU,EAAV,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,UAAU,GAAuC,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,wBAAA,EAAA,6BAAA,CAAA;YACjD,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,0BAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,gBAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,cAAc,EAAd,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,cAAc,GAAsB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,4BAAA,EAAA,iCAAA,CAAA;;;QA8DlD,eAAe;QACd,MAAM;QACN,GAAG;QACH,cAAc;QAlFf,aAAa,GAAgB,qBAAqB;AAE3D,QAAA,IACI,eAAe,GAAA;YACjB,OAAO,IAAI,CAAC,aAAa;;QAE3B,IAAI,eAAe,CAAC,KAAoB,EAAA;YACtC,IAAI,CAAC,aAAa,GAAG,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC;;QAE/C,aAAa,GAAkB,KAAK;AACpC,QAAA,oBAAoB;QACpB,eAAe,GAAiC,IAAI;QACpD,SAAS,GAAoC,MAAM;QACnD,aAAa,GAAkC,IAAI;QACrC,MAAM,GAAA,iBAAA,CAAA,IAAA,EAAA,oBAAA,EAAc,MAAM,CAAC;QAC3B,MAAM,IAAA,iBAAA,CAAA,IAAA,EAAA,yBAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,oBAAA,EAAkB,SAAS,CAAC;QAClC,UAAU,IAAA,iBAAA,CAAA,IAAA,EAAA,yBAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,wBAAA,EAAkC,IAAI,CAAC;QACjD,cAAc,IAAA,iBAAA,CAAA,IAAA,EAAA,6BAAA,CAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,4BAAA,EAAY,SAAS,CAAC;QACnB,SAAS,IAAA,iBAAA,CAAA,IAAA,EAAA,iCAAA,CAAA,EAAY,KAAK;QAC1B,UAAU,GAAY,KAAK;QAC3B,SAAS,GAAG,KAAK;QACjB,YAAY,GAAG,KAAK;QACpB,WAAW,GAAG,IAAI;QAClB,wBAAwB,GAAG,KAAK;AAErD,QAAA,cAAc,GAAmC,IAAI,YAAY,CAAmB,IAAI,CAAC;AACzF,QAAA,qBAAqB,GAAyB,IAAI,YAAY,EAAU;AACxE,QAAA,eAAe,GAAG,IAAI,YAAY,EAAoB;AACtD,QAAA,OAAO,GAAG,IAAI,YAAY,EAAqB;AAC/C,QAAA,KAAK,GAAG,IAAI,YAAY,EAAQ;AAEnD,QAAA,IAAI,QAAQ,GAAA;YACV,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,GAAG,UAAU,GAAG,YAAY;;AAGzF,QAAA,IAAI,OAAO,GAAA;YACT,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,IAAI,CAAC,IAAI,CAAC,SAAS;;AAG3D,QAAA,IAAI,QAAQ,GAAA;AACV,YAAA,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe;;AAGxC,QAAA,IAAI,IAAI,GAAA;AACN,YAAA,OAAO,IAAI,CAAC,MAAM,KAAK,MAAM;;AAG/B,QAAA,IAAI,cAAc,GAAA;YAChB,OAAO,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;;AAGvG,QAAA,IAAI,eAAe,GAAA;YACjB,OAAO,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO;;;;AAMzF,QAAA,OAAO,GAA8B,IAAI,SAAS,EAAkB;AAGpE,QAAA,QAAQ,GAAkC,IAAI,SAAS,EAAsB;AACzB,QAAA,YAAY;;AAEhE,QAAA,IAAI,GAA8B,IAAI,SAAS,EAAkB;AAExD,QAAA,aAAa,GAAG,eAAe,CAAC,6BAA6B,CAAC;QAEvE,GAAG,GAAc,KAAK;AACL,QAAA,QAAQ;AACjB,QAAA,QAAQ,GAAG,IAAI,OAAO,EAAQ;QAC9B,aAAa,GAAkB,CAAC;QAChC,aAAa,GAAkB,IAAI;AACnC,QAAA,oBAAoB,GAAG,YAAY,CAAC,KAAK;AACzC,QAAA,gBAAgB,GAAG,YAAY,CAAC,KAAK;AACrC,QAAA,yBAAyB,GAAG,YAAY,CAAC,KAAK;QAC9C,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAEnD,QAAA,WAAA,CACS,eAAgC,EAC/B,MAAc,EACd,GAAsB,EACtB,cAA8B,EAAA;YAH/B,IAAe,CAAA,eAAA,GAAf,eAAe;YACd,IAAM,CAAA,MAAA,GAAN,MAAM;YACN,IAAG,CAAA,GAAA,GAAH,GAAG;YACH,IAAc,CAAA,cAAA,GAAd,cAAc;AAEtB,YAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,EAAE;;QAG1B,QAAQ,GAAA;YACN,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;YACpC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,gBAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,gBAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,aAAC,CAAC;;QAGJ,WAAW,GAAA;AACT,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;AACxB,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACnB,YAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE;AACvC,YAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE;AACnC,YAAA,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE;;QAG9C,kBAAkB,GAAA;AAChB,YAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAK;AACjC,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;AAClD,aAAC,CAAC;YAEF,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,wBAAwB,EAAE;;;AAI/B,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAK;gBACvD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC;;;AAI5D,gBAAA,IAAI,aAAa,KAAK,IAAI,CAAC,aAAa,EAAE;oBACxC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAEhC,oBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,wBAAA,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;;;;4BAIpB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC;4BAC3C;;;;gBAIN,IAAI,CAAC,oBAAoB,EAAE;AAC3B,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;;QAGJ,qBAAqB,GAAA;;;AAGnB,YAAA,MAAM,aAAa,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;;;AAInF,YAAA,IAAI,IAAI,CAAC,aAAa,KAAK,aAAa,EAAE;AACxC,gBAAA,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI;gBAE7C,IAAI,CAAC,UAAU,EAAE;AACf,oBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;;;;AAKjE,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;oBAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK,GAAG,CAAC,SAAS,CAAC,KAAK,KAAK,aAAa,CAAC,CAAC;oBAEzE,IAAI,CAAC,UAAU,EAAE;AACf,wBAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,aAAa,CAAC;;AAElD,iBAAC,CAAC;;;YAIJ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,KAAI;AAC/B,gBAAA,GAAG,CAAC,QAAQ,GAAG,KAAK,GAAG,aAAa;;;AAIpC,gBAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,IAAI,GAAG,CAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;oBACnE,GAAG,CAAC,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC,aAAa;;AAEnD,aAAC,CAAC;AAEF,YAAA,IAAI,IAAI,CAAC,aAAa,KAAK,aAAa,EAAE;AACxC,gBAAA,IAAI,CAAC,aAAa,GAAG,aAAa;AAClC,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;;QAI3B,OAAO,CAAC,KAAa,EAAE,CAAa,EAAA;YAClC,CAAC,CAAC,cAAc,EAAE;YAClB,CAAC,CAAC,eAAe,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC;;QAG9B,KAAK,GAAA;AACH,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;;AAGX,QAAA,aAAa,CAAC,KAAoB,EAAA;YACxC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;;AAGxD,QAAA,iBAAiB,CAAC,KAAa,EAAA;AACrC,YAAA,MAAM,KAAK,GAAG,IAAI,gBAAgB,EAAE;AACpC,YAAA,KAAK,CAAC,KAAK,GAAG,KAAK;YACnB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACjC,gBAAA,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;gBACtC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,KAAI;AAC3B,oBAAA,IAAI,CAAC,KAAK,KAAK,EAAE;AACf,wBAAA,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;;AAEzB,iBAAC,CAAC;AACF,gBAAA,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;;AAE3B,YAAA,OAAO,KAAK;;QAGN,oBAAoB,GAAA;AAC1B,YAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,gBAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE;;AAGzC,YAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,MACrF,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CACxB;;QAGK,wBAAwB,GAAA;YAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAA+B,KAAI;gBAC/F,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,aAAa,KAAK,IAAI,CAAC,CAAC;AAC/D,gBAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AAC7B,aAAC,CAAC;;QAGJ,gBAAgB,CAAC,GAAW,EAAE,IAAY,EAAA;AACxC,YAAA,IAAI,OAAO,IAAI,CAAC,eAAe,KAAK,UAAU,EAAE;AAC9C,gBAAA,MAAM,UAAU,GAAG,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACtE,gBAAA,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;iBACpD;AACL,gBAAA,OAAO,EAAE,CAAC,IAAI,CAAC;;;AAInB,QAAA,YAAY,CAAC,GAAmB,EAAE,KAAa,EAAE,CAAa,EAAA;AAC5D,YAAA,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;;AAEnB,gBAAA,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;gBAClB,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE;AAC1C,oBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;;;;QAK1B,sBAAsB,CAAC,KAAa,EAAE,KAAiB,EAAA;AAC7D,YAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAqB;AAC1C,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC;;iBACxF;AACL,gBAAA,OAAO,KAAK;;;QAIhB,kBAAkB,CAAC,GAAmB,EAAE,CAAa,EAAA;AACnD,YAAA,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;;AAEnB,gBAAA,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;;;AAI7B,QAAA,gBAAgB,CAAC,KAAa,EAAA;AAC5B,YAAA,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE;AAC5C,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAc,EAAE,KAAK,CAAC,CAAC,SAAS,CAAC,GAAG,IAAG;gBACjG,IAAI,GAAG,EAAE;AACP,oBAAA,IAAI,CAAC,eAAe,GAAG,KAAK;AAC5B,oBAAA,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,KAAK;AACpC,oBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAE3B,aAAC,CAAC;;QAGJ,WAAW,CAAC,GAAmB,EAAE,KAAa,EAAA;AAC5C,YAAA,IAAI,GAAG,CAAC,UAAU,EAAE;AAClB,gBAAA,OAAO,IAAI;;AAEb,YAAA,OAAO,IAAI,CAAC,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;;AAG9C,QAAA,eAAe,CAAC,CAAS,EAAA;AACvB,YAAA,OAAO,WAAW,IAAI,CAAC,QAAQ,CAAQ,KAAA,EAAA,CAAC,EAAE;;QAGpC,WAAW,GAAA;AACjB,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,oBAAA,MAAM,IAAI,KAAK,CAAC,GAAG,MAAM,CAAA,oEAAA,CAAsE,CAAC;;gBAElG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;AAC1F,qBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;qBACxD,SAAS,CAAC,MAAK;oBACd,IAAI,CAAC,kBAAkB,EAAE;AACzB,oBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,iBAAC,CAAC;;;QAIA,kBAAkB,GAAA;AACxB,YAAA,IAAI,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE;AAC1B,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,wBAAwB,EAAE;AAC7C,gBAAA,IAAI,KAAK,KAAK,IAAI,CAAC,aAAa,EAAE;AAChC,oBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;;gBAE9B,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,SAAS,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC;;;QAIzD,wBAAwB,GAAA;YAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;AAE/C,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,IAAG;AAC1B,gBAAA,MAAM,CAAC,GAAG,GAAG,CAAC,aAAa;AAC3B,gBAAA,OAAO,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,KAAK;AAC3C,aAAC,CAAC;;AAGI,QAAA,YAAY,CAAC,MAAqB,EAAA;AACxC,YAAA,OAAO,CAAC,IAAwB,KAC9B;AACE,kBAAE,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,EAAE;oBACrC,KAAK,EAAE,IAAI,CAAC,WAAW,GAAG,OAAO,GAAG,QAAQ;oBAC5C,WAAW,EAAE,IAAI,CAAC,WAAW,GAAG,OAAO,GAAG,QAAQ;AAClD,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,YAAY,EAAE;iBACf;kBACD,KAAK;;2GAtUF,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAF,IAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,QAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,EAmBR,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,EAAA,eAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAChB,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAChB,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAChB,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,CAChB,EAAA,wBAAA,EAAA,CAAA,0BAAA,EAAA,0BAAA,EAAA,gBAAgB,CAvJzB,EAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,qBAAA,EAAA,iDAAA,EAAA,yBAAA,EAAA,4BAAA,EAAA,8BAAA,EAAA,4BAAA,EAAA,yBAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,oBAAA,EAAA,yBAAA,EAAA,uBAAA,EAAA,4BAAA,EAAA,qBAAA,EAAA,0BAAA,EAAA,sBAAA,EAAA,2BAAA,EAAA,wBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,oBAAA,EAAA,EAAA,cAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA;AACT,gBAAA;AACE,oBAAA,OAAO,EAAE,UAAU;AACnB,oBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,iBAAiB;AAChD;AACF,aAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,SAAA,EA6LwC,6BAA6B,EATrD,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EAAA,cAAc,8DAGd,kBAAkB,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAExB,oBAAoB,EAxLrB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+FT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAiBC,oBAAoB,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,SAAA,EAAA,SAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,eAAA,EAAA,eAAA,CAAA,EAAA,OAAA,EAAA,CAAA,cAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,WAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACpB,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAChB,qBAAqB,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,KAAA,EAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EACrB,UAAU,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAC,IAAA,CAAA,eAAA,EAAA,QAAA,EAAA,oDAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EACV,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAC,EAAA,CAAA,+BAAA,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACd,yBAAyB,EAAA,QAAA,EAAA,kDAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACzB,kBAAkB,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;;2FAGT,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBArI7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,mBAAmB,EAAE,KAAK;oBAC1B,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,OAAO;AAChD,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,UAAU;AACnB,4BAAA,WAAW,EAAE,UAAU,CAAC,uBAAuB;AAChD;AACF,qBAAA;AACD,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+FT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,UAAU;AACjB,wBAAA,uBAAuB,EAAE,CAAiD,+CAAA,CAAA;AAC1E,wBAAA,2BAA2B,EAAE,CAA4B,0BAAA,CAAA;AACzD,wBAAA,gCAAgC,EAAE,CAA4B,0BAAA,CAAA;AAC9D,wBAAA,2BAA2B,EAAE,CAAY,UAAA,CAAA;AACzC,wBAAA,sBAAsB,EAAE,CAAe,aAAA,CAAA;AACvC,wBAAA,sBAAsB,EAAE,CAAyB,uBAAA,CAAA;AACjD,wBAAA,yBAAyB,EAAE,CAA4B,0BAAA,CAAA;AACvD,wBAAA,uBAAuB,EAAE,CAA0B,wBAAA,CAAA;AACnD,wBAAA,wBAAwB,EAAE,CAA2B,yBAAA,CAAA;AACrD,wBAAA,0BAA0B,EAAE,CAAsB,oBAAA,CAAA;AAClD,wBAAA,wBAAwB,EAAE,CAAoB,kBAAA,CAAA;AAC9C,wBAAA,wBAAwB,EAAE,CAAoB,kBAAA;AAC/C,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,oBAAoB;wBACpB,gBAAgB;wBAChB,qBAAqB;wBACrB,UAAU;wBACV,cAAc;wBACd,yBAAyB;wBACzB;AACD;AACF,iBAAA;0KAKK,eAAe,EAAA,CAAA;sBADlB;gBAOQ,aAAa,EAAA,CAAA;sBAArB;gBACQ,oBAAoB,EAAA,CAAA;sBAA5B;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBACsB,MAAM,EAAA,CAAA;sBAA5B;gBACsB,MAAM,EAAA,CAAA;sBAA5B;gBACsB,UAAU,EAAA,CAAA;sBAAhC;gBACsB,cAAc,EAAA,CAAA;sBAApC;gBACuC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,wBAAwB,EAAA,CAAA;sBAA/D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAEnB,cAAc,EAAA,CAAA;sBAAhC;gBACkB,qBAAqB,EAAA,CAAA;sBAAvC;gBACkB,eAAe,EAAA,CAAA;sBAAjC;gBACkB,OAAO,EAAA,CAAA;sBAAzB;gBACkB,KAAK,EAAA,CAAA;sBAAvB;gBA6BD,OAAO,EAAA,CAAA;sBADN,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;gBAItD,QAAQ,EAAA,CAAA;sBADP,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,kBAAkB,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;gBAEN,YAAY,EAAA,CAAA;sBAA/D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,oBAAoB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;;;ACpQpD;;;AAGG;AAkBH,MAAM,UAAU,GAAG;IACjB,iBAAiB;IACjB,cAAc;IACd,oBAAoB;IACpB,qBAAqB;IACrB,qBAAqB;IACrB,wBAAwB;IACxB,0BAA0B;IAC1B,uBAAuB;IACvB,yBAAyB;IACzB,cAAc;IACd,kBAAkB;IAClB,kBAAkB;IAClB,0BAA0B;IAC1B;CACD;MAMY,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,YApBvB,iBAAiB;YACjB,cAAc;YACd,oBAAoB;YACpB,qBAAqB;YACrB,qBAAqB;YACrB,wBAAwB;YACxB,0BAA0B;YAC1B,uBAAuB;YACvB,yBAAyB;YACzB,cAAc;YACd,kBAAkB;YAClB,kBAAkB;YAClB,0BAA0B;AAC1B,YAAA,6BAA6B,aAb7B,iBAAiB;YACjB,cAAc;YACd,oBAAoB;YACpB,qBAAqB;YACrB,qBAAqB;YACrB,wBAAwB;YACxB,0BAA0B;YAC1B,uBAAuB;YACvB,yBAAyB;YACzB,cAAc;YACd,kBAAkB;YAClB,kBAAkB;YAClB,0BAA0B;YAC1B,6BAA6B,CAAA,EAAA,CAAA;AAOlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,YApBvB,iBAAiB;YAEjB,oBAAoB;YAIpB,0BAA0B;YAC1B,uBAAuB;YACvB,yBAAyB,CAAA,EAAA,CAAA;;2FAYd,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,UAAU,CAAC;oBACrB,OAAO,EAAE,CAAC,UAAU;AACrB,iBAAA;;;ACzCD;;;AAGG;;ACHH;;AAEG;;;;"}