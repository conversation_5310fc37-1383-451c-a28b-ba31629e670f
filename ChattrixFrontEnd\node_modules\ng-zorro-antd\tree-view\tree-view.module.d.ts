import * as i0 from "@angular/core";
import * as i1 from "./tree";
import * as i2 from "./outlet";
import * as i3 from "./tree-view";
import * as i4 from "./node";
import * as i5 from "./toggle";
import * as i6 from "./padding";
import * as i7 from "./option";
import * as i8 from "./checkbox";
import * as i9 from "./indent";
import * as i10 from "./tree-virtual-scroll-view";
export declare class NzTreeViewModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTreeViewModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<NzTreeViewModule, never, [typeof i1.NzTreeView, typeof i2.NzTreeNodeOutletDirective, typeof i3.NzTreeViewComponent, typeof i4.NzTreeNodeDefDirective, typeof i4.NzTreeNodeComponent, typeof i5.NzTreeNodeToggleDirective, typeof i6.NzTreeNodePaddingDirective, typeof i5.NzTreeNodeToggleRotateIconDirective, typeof i5.NzTreeNodeToggleActiveIconDirective, typeof i7.NzTreeNodeOptionComponent, typeof i5.NzTreeNodeNoopToggleDirective, typeof i8.NzTreeNodeCheckboxComponent, typeof i9.NzTreeNodeIndentsComponent, typeof i10.NzTreeVirtualScrollViewComponent, typeof i4.NzTreeVirtualScrollNodeOutletDirective, typeof i9.NzTreeNodeIndentLineDirective], [typeof i1.NzTreeView, typeof i2.NzTreeNodeOutletDirective, typeof i3.NzTreeViewComponent, typeof i4.NzTreeNodeDefDirective, typeof i4.NzTreeNodeComponent, typeof i5.NzTreeNodeToggleDirective, typeof i6.NzTreeNodePaddingDirective, typeof i5.NzTreeNodeToggleRotateIconDirective, typeof i5.NzTreeNodeToggleActiveIconDirective, typeof i7.NzTreeNodeOptionComponent, typeof i5.NzTreeNodeNoopToggleDirective, typeof i8.NzTreeNodeCheckboxComponent, typeof i9.NzTreeNodeIndentsComponent, typeof i10.NzTreeVirtualScrollViewComponent, typeof i4.NzTreeVirtualScrollNodeOutletDirective, typeof i9.NzTreeNodeIndentLineDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<NzTreeViewModule>;
}
