import { AfterViewInit } from '@angular/core';
import { NzTreeNodeOutletDirective } from './outlet';
import { NzTreeView } from './tree';
import * as i0 from "@angular/core";
export declare class NzTreeViewComponent<T> extends NzTreeView<T> implements AfterViewInit {
    nodeOutlet: NzTreeNodeOutletDirective;
    _afterViewInit: boolean;
    ngAfterViewInit(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTreeViewComponent<any>, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<NzTreeViewComponent<any>, "nz-tree-view", ["nzTreeView"], {}, {}, never, never, true, never>;
}
