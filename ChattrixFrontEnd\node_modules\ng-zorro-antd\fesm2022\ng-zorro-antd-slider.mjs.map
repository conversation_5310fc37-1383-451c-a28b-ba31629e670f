{"version": 3, "file": "ng-zorro-antd-slider.mjs", "sources": ["../../components/slider/slider.service.ts", "../../components/slider/handle.component.ts", "../../components/slider/marks.component.ts", "../../components/slider/step.component.ts", "../../components/slider/track.component.ts", "../../components/slider/slider.component.ts", "../../components/slider/slider.module.ts", "../../components/slider/typings.ts", "../../components/slider/public-api.ts", "../../components/slider/ng-zorro-antd-slider.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Injectable } from '@angular/core';\n\n@Injectable()\nexport class NzSliderService {\n  isDragging = false;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction } from '@angular/cdk/bidi';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  Input,\n  OnChanges,\n  SimpleChanges,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute\n} from '@angular/core';\n\nimport { NgStyleInterface, NzTSType } from 'ng-zorro-antd/core/types';\nimport { numberAttributeWithZeroFallback } from 'ng-zorro-antd/core/util';\nimport { NzToolTipModule, NzTooltipDirective } from 'ng-zorro-antd/tooltip';\n\nimport { NzSliderService } from './slider.service';\nimport { NzSliderShowTooltip } from './typings';\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  selector: 'nz-slider-handle',\n  exportAs: 'nzSliderHandle',\n  preserveWhitespaces: false,\n  template: `\n    <div\n      #handle\n      class=\"ant-slider-handle\"\n      tabindex=\"0\"\n      nz-tooltip\n      [style]=\"style\"\n      [nzTooltipTitle]=\"tooltipFormatter === null || tooltipVisible === 'never' ? null : tooltipTitle\"\n      [nzTooltipTitleContext]=\"{ $implicit: value }\"\n      [nzTooltipTrigger]=\"null\"\n      [nzTooltipPlacement]=\"tooltipPlacement\"\n    ></div>\n  `,\n  host: {\n    '(mouseenter)': 'enterHandle()',\n    '(mouseleave)': 'leaveHandle()'\n  },\n  imports: [NzToolTipModule]\n})\nexport class NzSliderHandleComponent implements OnChanges {\n  @ViewChild('handle', { static: false }) handleEl?: ElementRef;\n  @ViewChild(NzTooltipDirective, { static: false }) tooltip?: NzTooltipDirective;\n\n  @Input({ transform: booleanAttribute }) vertical?: boolean;\n  @Input({ transform: booleanAttribute }) reverse?: boolean;\n  @Input({ transform: numberAttributeWithZeroFallback }) offset?: number;\n  @Input({ transform: numberAttributeWithZeroFallback }) value?: number;\n  @Input() tooltipVisible: NzSliderShowTooltip = 'default';\n  @Input() tooltipPlacement?: string;\n  @Input() tooltipFormatter?: null | ((value: number) => string) | TemplateRef<void>;\n  @Input({ transform: booleanAttribute }) active = false;\n  @Input() dir: Direction = 'ltr';\n\n  tooltipTitle?: NzTSType;\n  style: NgStyleInterface = {};\n\n  constructor(\n    private sliderService: NzSliderService,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { offset, value, active, tooltipVisible, reverse, dir } = changes;\n\n    if (offset || reverse || dir) {\n      this.updateStyle();\n    }\n\n    if (value) {\n      this.updateTooltipTitle();\n      this.updateTooltipPosition();\n    }\n\n    if (active) {\n      if (active.currentValue) {\n        this.toggleTooltip(true);\n      } else {\n        this.toggleTooltip(false);\n      }\n    }\n\n    if (tooltipVisible?.currentValue === 'always') {\n      Promise.resolve().then(() => this.toggleTooltip(true, true));\n    }\n  }\n\n  enterHandle = (): void => {\n    if (!this.sliderService.isDragging) {\n      this.toggleTooltip(true);\n      this.updateTooltipPosition();\n      this.cdr.detectChanges();\n    }\n  };\n\n  leaveHandle = (): void => {\n    if (!this.sliderService.isDragging) {\n      this.toggleTooltip(false);\n      this.cdr.detectChanges();\n    }\n  };\n\n  focus(): void {\n    this.handleEl?.nativeElement.focus();\n  }\n\n  private toggleTooltip(show: boolean, force: boolean = false): void {\n    if (!force && (this.tooltipVisible !== 'default' || !this.tooltip)) {\n      return;\n    }\n\n    if (show) {\n      this.tooltip?.show();\n    } else {\n      this.tooltip?.hide();\n    }\n  }\n\n  private updateTooltipTitle(): void {\n    if (this.tooltipFormatter) {\n      this.tooltipTitle =\n        typeof this.tooltipFormatter === 'function' ? this.tooltipFormatter(this.value!) : this.tooltipFormatter;\n    } else {\n      this.tooltipTitle = `${this.value}`;\n    }\n  }\n\n  private updateTooltipPosition(): void {\n    if (this.tooltip) {\n      Promise.resolve().then(() => this.tooltip?.updatePosition());\n    }\n  }\n\n  private updateStyle(): void {\n    const vertical = this.vertical;\n    const reverse = this.reverse;\n    const offset = this.offset;\n\n    const positionStyle = vertical\n      ? {\n          [reverse ? 'top' : 'bottom']: `${offset}%`,\n          [reverse ? 'bottom' : 'top']: 'auto',\n          transform: reverse ? null : `translateY(+50%)`\n        }\n      : {\n          ...this.getHorizontalStylePosition(),\n          transform: `translateX(${reverse ? (this.dir === 'rtl' ? '-' : '+') : this.dir === 'rtl' ? '+' : '-'}50%)`\n        };\n\n    this.style = positionStyle;\n    this.cdr.markForCheck();\n  }\n\n  private getHorizontalStylePosition(): { left: string; right: string } {\n    let left = this.reverse ? 'auto' : `${this.offset}%`;\n    let right = this.reverse ? `${this.offset}%` : 'auto';\n    if (this.dir === 'rtl') {\n      const tmp = left;\n      left = right;\n      right = tmp;\n    }\n    return { left, right };\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Input,\n  OnChanges,\n  SimpleChanges,\n  ViewEncapsulation,\n  booleanAttribute,\n  numberAttribute\n} from '@angular/core';\n\nimport { NgStyleInterface } from 'ng-zorro-antd/core/types';\n\nimport { NzDisplayedMark, NzExtendedMark, NzMark, NzMarkObj } from './typings';\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  selector: 'nz-slider-marks',\n  exportAs: 'nzSliderMarks',\n  template: `\n    @for (attr of marks; track attr.value) {\n      <span\n        class=\"ant-slider-mark-text\"\n        [class.ant-slider-mark-active]=\"attr.active\"\n        [style]=\"attr.style\"\n        [innerHTML]=\"attr.label\"\n      ></span>\n    }\n  `,\n  host: {\n    class: 'ant-slider-mark'\n  }\n})\nexport class NzSliderMarksComponent implements OnChanges {\n  @Input() lowerBound: number | null = null;\n  @Input() upperBound: number | null = null;\n  @Input() marksArray: NzExtendedMark[] = [];\n  @Input({ transform: numberAttribute }) min!: number;\n  @Input({ transform: numberAttribute }) max!: number;\n  @Input({ transform: booleanAttribute }) vertical = false;\n  @Input({ transform: booleanAttribute }) included = false;\n  @Input({ transform: booleanAttribute }) reverse!: boolean;\n\n  marks: NzDisplayedMark[] = [];\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { marksArray, lowerBound, upperBound, reverse } = changes;\n\n    if (marksArray || reverse) {\n      this.buildMarks();\n    }\n\n    if (marksArray || lowerBound || upperBound || reverse) {\n      this.togglePointActive();\n    }\n  }\n\n  private buildMarks(): void {\n    const range = this.max - this.min;\n\n    this.marks = this.marksArray.map(mark => {\n      const { value, offset, config } = mark;\n      const style = this.getMarkStyles(value, range, config);\n      const label = isConfigObject(config) ? config.label : config;\n\n      return {\n        label,\n        offset,\n        style,\n        value,\n        config,\n        active: false\n      };\n    });\n  }\n\n  private getMarkStyles(value: number, range: number, config: NzMark): NgStyleInterface {\n    let style;\n    const markValue = this.reverse ? this.max + this.min - value : value;\n\n    if (this.vertical) {\n      style = {\n        marginBottom: '-50%',\n        bottom: `${((markValue - this.min) / range) * 100}%`\n      };\n    } else {\n      style = {\n        transform: `translate3d(-50%, 0, 0)`,\n        left: `${((markValue - this.min) / range) * 100}%`\n      };\n    }\n\n    if (isConfigObject(config) && config.style) {\n      style = { ...style, ...config.style };\n    }\n\n    return style;\n  }\n\n  private togglePointActive(): void {\n    if (this.marks && this.lowerBound !== null && this.upperBound !== null) {\n      this.marks.forEach(mark => {\n        const value = mark.value;\n        const isActive =\n          (!this.included && value === this.upperBound) ||\n          (this.included && value <= this.upperBound! && value >= this.lowerBound!);\n\n        mark.active = isActive;\n      });\n    }\n  }\n}\n\nfunction isConfigObject(config: NzMark): config is NzMarkObj {\n  return typeof config !== 'string';\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Input,\n  OnChanges,\n  SimpleChanges,\n  ViewEncapsulation,\n  booleanAttribute,\n  numberAttribute\n} from '@angular/core';\n\nimport { NzDisplayedStep, NzExtendedMark } from './typings';\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  selector: 'nz-slider-step',\n  exportAs: 'nzSliderStep',\n  preserveWhitespaces: false,\n  template: `\n    @for (step of steps; track step.value) {\n      <span class=\"ant-slider-dot\" [class.ant-slider-dot-active]=\"step.active\" [style]=\"step.style!\"></span>\n    }\n  `,\n  host: {\n    class: 'ant-slider-step'\n  }\n})\nexport class NzSliderStepComponent implements OnChanges {\n  @Input() lowerBound: number | null = null;\n  @Input() upperBound: number | null = null;\n  @Input() marksArray: NzExtendedMark[] = [];\n  @Input({ transform: numberAttribute }) min!: number;\n  @Input({ transform: numberAttribute }) max!: number;\n  @Input({ transform: booleanAttribute }) vertical = false;\n  @Input({ transform: booleanAttribute }) included = false;\n  @Input({ transform: booleanAttribute }) reverse!: boolean;\n\n  steps: NzDisplayedStep[] = [];\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { marksArray, lowerBound, upperBound, reverse } = changes;\n\n    if (marksArray || reverse) {\n      this.buildSteps();\n    }\n    if (marksArray || lowerBound || upperBound || reverse) {\n      this.togglePointActive();\n    }\n  }\n\n  private buildSteps(): void {\n    const orient = this.vertical ? 'bottom' : 'left';\n\n    this.steps = this.marksArray.map(mark => {\n      const { value, config } = mark;\n      let offset = mark.offset;\n      const range = this.max - this.min;\n\n      if (this.reverse) {\n        offset = ((this.max - value) / range) * 100;\n      }\n\n      return {\n        value,\n        offset,\n        config,\n        active: false,\n        style: {\n          [orient]: `${offset}%`,\n          transform: this.vertical ? 'translateY(50%)' : 'translateX(-50%)'\n        }\n      };\n    });\n  }\n\n  private togglePointActive(): void {\n    if (this.steps && this.lowerBound !== null && this.upperBound !== null) {\n      this.steps.forEach(step => {\n        const value = step.value;\n        const isActive =\n          (!this.included && value === this.upperBound) ||\n          (this.included && value <= this.upperBound! && value >= this.lowerBound!);\n        step.active = isActive;\n      });\n    }\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction } from '@angular/cdk/bidi';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Input,\n  OnChanges,\n  ViewEncapsulation,\n  booleanAttribute,\n  numberAttribute\n} from '@angular/core';\n\nexport interface NzSliderTrackStyle {\n  bottom?: string | null;\n  height?: string | null;\n  left?: string | null;\n  right?: string | null;\n  width?: string | null;\n  visibility?: string;\n}\n\n@Component({\n  selector: 'nz-slider-track',\n  exportAs: 'nzSliderTrack',\n  template: `<div class=\"ant-slider-track\" [style]=\"style\"></div>`,\n  preserveWhitespaces: false,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class NzSliderTrackComponent implements OnChanges {\n  @Input({ transform: numberAttribute }) offset: number = 0;\n  @Input({ transform: booleanAttribute }) reverse: boolean = false;\n  @Input() dir: Direction = 'ltr';\n  @Input({ transform: numberAttribute }) length: number = 0;\n  @Input({ transform: booleanAttribute }) vertical = false;\n  @Input({ transform: booleanAttribute }) included = false;\n\n  style: NzSliderTrackStyle = {};\n\n  ngOnChanges(): void {\n    const vertical = this.vertical;\n    const reverse = this.reverse;\n    const visibility = this.included ? 'visible' : 'hidden';\n    const offset = this.offset;\n    const length = this.length;\n\n    const positonStyle: NzSliderTrackStyle = vertical\n      ? {\n          [reverse ? 'top' : 'bottom']: `${offset}%`,\n          [reverse ? 'bottom' : 'top']: 'auto',\n          height: `${length}%`,\n          visibility\n        }\n      : {\n          ...this.getHorizontalStylePosition(),\n          width: `${length}%`,\n          visibility\n        };\n\n    this.style = positonStyle;\n  }\n\n  private getHorizontalStylePosition(): { left: string; right: string } {\n    let left = this.reverse ? 'auto' : `${this.offset}%`;\n    let right = this.reverse ? `${this.offset}%` : 'auto';\n    if (this.dir === 'rtl') {\n      const tmp = left;\n      left = right;\n      right = tmp;\n    }\n    return { left, right };\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport { DOWN_ARROW, LEFT_ARROW, RIGHT_ARROW, UP_ARROW } from '@angular/cdk/keycodes';\nimport { Platform } from '@angular/cdk/platform';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  QueryList,\n  SimpleChanges,\n  TemplateRef,\n  ViewChildren,\n  ViewEncapsulation,\n  booleanAttribute,\n  forwardRef,\n  numberAttribute\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Observable, Subject, Subscription, fromEvent, merge } from 'rxjs';\nimport { distinctUntilChanged, filter, map, takeUntil, tap } from 'rxjs/operators';\n\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\nimport {\n  MouseTouchObserverConfig,\n  arraysEqual,\n  ensureNumberInRange,\n  getElementOffset,\n  getPercent,\n  getPrecision,\n  isNil,\n  numberAttributeWithZeroFallback,\n  silentEvent\n} from 'ng-zorro-antd/core/util';\n\nimport { NzSliderHandleComponent } from './handle.component';\nimport { NzSliderMarksComponent } from './marks.component';\nimport { NzSliderService } from './slider.service';\nimport { NzSliderStepComponent } from './step.component';\nimport { NzSliderTrackComponent } from './track.component';\nimport { NzExtendedMark, NzMarks, NzSliderHandler, NzSliderShowTooltip, NzSliderValue } from './typings';\n\n@Component({\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  selector: 'nz-slider',\n  exportAs: 'nzSlider',\n  preserveWhitespaces: false,\n  providers: [\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => NzSliderComponent),\n      multi: true\n    },\n    NzSliderService\n  ],\n  template: `\n    <div class=\"ant-slider-rail\"></div>\n    <nz-slider-track\n      [vertical]=\"nzVertical\"\n      [included]=\"nzIncluded\"\n      [offset]=\"track.offset!\"\n      [length]=\"track.length!\"\n      [reverse]=\"nzReverse\"\n      [dir]=\"dir\"\n    ></nz-slider-track>\n    @if (marksArray) {\n      <nz-slider-step\n        [vertical]=\"nzVertical\"\n        [min]=\"nzMin\"\n        [max]=\"nzMax\"\n        [lowerBound]=\"$any(bounds.lower)\"\n        [upperBound]=\"$any(bounds.upper)\"\n        [marksArray]=\"marksArray\"\n        [included]=\"nzIncluded\"\n        [reverse]=\"nzReverse\"\n      ></nz-slider-step>\n    }\n    @for (handle of handles; track handle.value; let handleIndex = $index) {\n      <nz-slider-handle\n        [vertical]=\"nzVertical\"\n        [reverse]=\"nzReverse\"\n        [offset]=\"handle.offset!\"\n        [value]=\"handle.value!\"\n        [active]=\"handle.active\"\n        [tooltipFormatter]=\"nzTipFormatter\"\n        [tooltipVisible]=\"nzTooltipVisible\"\n        [tooltipPlacement]=\"nzTooltipPlacement\"\n        [dir]=\"dir\"\n        (focusin)=\"onHandleFocusIn(handleIndex)\"\n      ></nz-slider-handle>\n    }\n    @if (marksArray) {\n      <nz-slider-marks\n        [vertical]=\"nzVertical\"\n        [min]=\"nzMin\"\n        [max]=\"nzMax\"\n        [lowerBound]=\"$any(bounds.lower)\"\n        [upperBound]=\"$any(bounds.upper)\"\n        [marksArray]=\"marksArray\"\n        [included]=\"nzIncluded\"\n        [reverse]=\"nzReverse\"\n      ></nz-slider-marks>\n    }\n  `,\n  imports: [NzSliderTrackComponent, NzSliderStepComponent, NzSliderHandleComponent, NzSliderMarksComponent],\n  host: {\n    class: 'ant-slider',\n    '[class.ant-slider-rtl]': `dir === 'rtl'`,\n    '[class.ant-slider-disabled]': 'nzDisabled',\n    '[class.ant-slider-vertical]': 'nzVertical',\n    '[class.ant-slider-with-marks]': 'marksArray',\n    '(keydown)': 'onKeyDown($event)'\n  }\n})\nexport class NzSliderComponent implements ControlValueAccessor, OnInit, OnChanges, OnDestroy {\n  @ViewChildren(NzSliderHandleComponent) handlerComponents!: QueryList<NzSliderHandleComponent>;\n\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Input({ transform: booleanAttribute }) nzDots: boolean = false;\n  @Input({ transform: booleanAttribute }) nzIncluded: boolean = true;\n  @Input({ transform: booleanAttribute }) nzRange: boolean = false;\n  @Input({ transform: booleanAttribute }) nzVertical: boolean = false;\n  @Input({ transform: booleanAttribute }) nzReverse: boolean = false;\n  @Input() nzDefaultValue?: NzSliderValue;\n  @Input() nzMarks: NzMarks | null = null;\n  @Input({ transform: numberAttribute }) nzMax = 100;\n  @Input({ transform: numberAttribute }) nzMin = 0;\n  @Input({ transform: numberAttributeWithZeroFallback }) nzStep: number = 1;\n  @Input() nzTooltipVisible: NzSliderShowTooltip = 'default';\n  @Input() nzTooltipPlacement: string = 'top';\n  @Input() nzTipFormatter?: null | ((value: number) => string) | TemplateRef<void>;\n\n  @Output() readonly nzOnAfterChange = new EventEmitter<NzSliderValue>();\n\n  value: NzSliderValue | null = null;\n  cacheSliderStart: number | null = null;\n  cacheSliderLength: number | null = null;\n  activeValueIndex: number | undefined = undefined; // Current activated handle's index ONLY for range=true\n  track: { offset: null | number; length: null | number } = { offset: null, length: null }; // Track's offset and length\n  handles: NzSliderHandler[] = []; // Handles' offset\n  marksArray: NzExtendedMark[] | null = null; // \"steps\" in array type with more data & FILTER out the invalid mark\n  bounds: { lower: NzSliderValue | null; upper: NzSliderValue | null } = { lower: null, upper: null }; // now for nz-slider-step\n  dir: Direction = 'ltr';\n\n  private dragStart$?: Observable<number>;\n  private dragMove$?: Observable<number>;\n  private dragEnd$?: Observable<Event>;\n  private dragStart_?: Subscription | null;\n  private dragMove_?: Subscription | null;\n  private dragEnd_?: Subscription | null;\n  private destroy$ = new Subject<boolean>();\n  private isNzDisableFirstChange = true;\n\n  constructor(\n    public slider: ElementRef<HTMLDivElement>,\n    private sliderService: NzSliderService,\n    private cdr: ChangeDetectorRef,\n    private platform: Platform,\n    private directionality: Directionality\n  ) {}\n\n  ngOnInit(): void {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n      this.updateTrackAndHandles();\n      this.onValueChange(this.getValue(true));\n    });\n\n    this.handles = generateHandlers(this.nzRange ? 2 : 1);\n    this.marksArray = this.nzMarks ? this.generateMarkItems(this.nzMarks) : null;\n    this.bindDraggingHandlers();\n    this.toggleDragDisabled(this.nzDisabled);\n\n    if (this.getValue() === null) {\n      this.setValue(this.formatValue(null));\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzDisabled, nzMarks, nzRange } = changes;\n\n    if (nzDisabled && !nzDisabled.firstChange) {\n      this.toggleDragDisabled(nzDisabled.currentValue);\n    } else if (nzMarks && !nzMarks.firstChange) {\n      this.marksArray = this.nzMarks ? this.generateMarkItems(this.nzMarks) : null;\n    } else if (nzRange && !nzRange.firstChange) {\n      this.handles = generateHandlers(nzRange.currentValue ? 2 : 1);\n      this.setValue(this.formatValue(null));\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.unsubscribeDrag();\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n\n  writeValue(val: NzSliderValue | null): void {\n    this.setValue(val, true);\n  }\n\n  onValueChange(_value: NzSliderValue): void {}\n\n  onTouched(): void {}\n\n  registerOnChange(fn: (value: NzSliderValue) => void): void {\n    this.onValueChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this.onTouched = fn;\n  }\n\n  setDisabledState(isDisabled: boolean): void {\n    this.nzDisabled = (this.isNzDisableFirstChange && this.nzDisabled) || isDisabled;\n    this.isNzDisableFirstChange = false;\n    this.toggleDragDisabled(this.nzDisabled);\n    this.cdr.markForCheck();\n  }\n\n  /**\n   * Event handler is only triggered when a slider handler is focused.\n   */\n  onKeyDown(e: KeyboardEvent): void {\n    if (this.nzDisabled) {\n      return;\n    }\n\n    const code = e.keyCode;\n    const isIncrease = code === RIGHT_ARROW || code === UP_ARROW;\n    const isDecrease = code === LEFT_ARROW || code === DOWN_ARROW;\n\n    if (!(isIncrease || isDecrease)) {\n      return;\n    }\n\n    e.preventDefault();\n\n    let step = (isDecrease ? -this.nzStep : this.nzStep) * (this.nzReverse ? -1 : 1);\n    step = this.dir === 'rtl' ? step * -1 : step;\n    const newVal = this.nzRange\n      ? (this.value as number[])[this.activeValueIndex!] + step\n      : (this.value as number) + step;\n    this.setActiveValue(ensureNumberInRange(newVal, this.nzMin, this.nzMax));\n    this.nzOnAfterChange.emit(this.getValue(true));\n  }\n\n  onHandleFocusIn(index: number): void {\n    this.activeValueIndex = index;\n  }\n\n  private setValue(value: NzSliderValue | null, isWriteValue: boolean = false): void {\n    if (isWriteValue) {\n      this.value = this.formatValue(value);\n      this.updateTrackAndHandles();\n    } else if (!valuesEqual(this.value!, value!)) {\n      this.value = value;\n      this.updateTrackAndHandles();\n      this.onValueChange(this.getValue(true));\n    }\n  }\n\n  private getValue(cloneAndSort: boolean = false): NzSliderValue {\n    if (cloneAndSort && this.value && isValueRange(this.value)) {\n      return [...this.value].sort((a, b) => a - b);\n    }\n    return this.value!;\n  }\n\n  /**\n   * Clone & sort current value and convert them to offsets, then return the new one.\n   */\n  private getValueToOffset(value?: NzSliderValue): NzSliderValue {\n    let normalizedValue = value;\n\n    if (typeof normalizedValue === 'undefined') {\n      normalizedValue = this.getValue(true);\n    }\n\n    return isValueRange(normalizedValue)\n      ? normalizedValue.map(val => this.valueToOffset(val))\n      : this.valueToOffset(normalizedValue);\n  }\n\n  /**\n   * Find the closest value to be activated.\n   */\n  private setActiveValueIndex(pointerValue: number): void {\n    const value = this.getValue();\n    if (isValueRange(value)) {\n      let minimal: number | null = null;\n      let gap: number;\n      let activeIndex = -1;\n      value.forEach((val, index) => {\n        gap = Math.abs(pointerValue - val);\n        if (minimal === null || gap < minimal!) {\n          minimal = gap;\n          activeIndex = index;\n        }\n      });\n      this.activeValueIndex = activeIndex;\n      this.handlerComponents.toArray()[activeIndex].focus();\n    } else {\n      this.handlerComponents.toArray()[0].focus();\n    }\n  }\n\n  private setActiveValue(pointerValue: number): void {\n    if (isValueRange(this.value!)) {\n      const newValue = [...(this.value as number[])];\n      newValue[this.activeValueIndex!] = pointerValue;\n      this.setValue(newValue);\n    } else {\n      this.setValue(pointerValue);\n    }\n  }\n\n  /**\n   * Update track and handles' position and length.\n   */\n  private updateTrackAndHandles(): void {\n    const value = this.getValue();\n    const offset = this.getValueToOffset(value);\n    const valueSorted = this.getValue(true);\n    const offsetSorted = this.getValueToOffset(valueSorted);\n    const boundParts = isValueRange(valueSorted) ? valueSorted : [0, valueSorted];\n    const trackParts = isValueRange(offsetSorted)\n      ? [offsetSorted[0], offsetSorted[1] - offsetSorted[0]]\n      : [0, offsetSorted];\n\n    this.handles.forEach((handle, index) => {\n      handle.offset = isValueRange(offset) ? offset[index] : offset;\n      handle.value = isValueRange(value) ? value[index] : value || 0;\n    });\n\n    [this.bounds.lower, this.bounds.upper] = boundParts;\n    [this.track.offset, this.track.length] = trackParts;\n\n    this.cdr.markForCheck();\n  }\n\n  private onDragStart(value: number): void {\n    this.toggleDragMoving(true);\n    this.cacheSliderProperty();\n    this.setActiveValueIndex(this.getLogicalValue(value));\n    this.setActiveValue(this.getLogicalValue(value));\n    this.showHandleTooltip(this.nzRange ? this.activeValueIndex : 0);\n  }\n\n  private onDragMove(value: number): void {\n    this.setActiveValue(this.getLogicalValue(value));\n    this.cdr.markForCheck();\n  }\n\n  private getLogicalValue(value: number): number {\n    if (this.nzReverse) {\n      if (!this.nzVertical && this.dir === 'rtl') {\n        return value;\n      }\n      return this.nzMax - value + this.nzMin;\n    }\n    if (!this.nzVertical && this.dir === 'rtl') {\n      return this.nzMax - value + this.nzMin;\n    }\n\n    return value;\n  }\n\n  private onDragEnd(): void {\n    this.nzOnAfterChange.emit(this.getValue(true));\n    this.toggleDragMoving(false);\n    this.cacheSliderProperty(true);\n    this.hideAllHandleTooltip();\n    this.cdr.markForCheck();\n  }\n\n  /**\n   * Create user interactions handles.\n   */\n  private bindDraggingHandlers(): void {\n    if (!this.platform.isBrowser) {\n      return;\n    }\n    const pluckFunc: (keys: string[]) => (event: Event) => number = keys => (event: Event) =>\n      keys.reduce((acc: NzSafeAny, key: string) => acc[key] || acc, event);\n    const sliderDOM = this.slider.nativeElement;\n    const orientField = this.nzVertical ? 'pageY' : 'pageX';\n    const mouse: MouseTouchObserverConfig = {\n      start: 'mousedown',\n      move: 'mousemove',\n      end: 'mouseup',\n      pluckKey: [orientField]\n    };\n    const touch: MouseTouchObserverConfig = {\n      start: 'touchstart',\n      move: 'touchmove',\n      end: 'touchend',\n      pluckKey: ['touches', '0', orientField],\n      filter: (e: MouseEvent | TouchEvent) => e instanceof TouchEvent\n    };\n\n    [mouse, touch].forEach(source => {\n      const { start, move, end, pluckKey, filter: filterFunc = () => true } = source;\n\n      source.startPlucked$ = fromEvent(sliderDOM, start).pipe(\n        filter(filterFunc),\n        tap(silentEvent),\n        map(pluckFunc(pluckKey)),\n        map((position: number) => this.findClosestValue(position))\n      );\n      source.end$ = fromEvent(document, end);\n      source.moveResolved$ = fromEvent(document, move).pipe(\n        filter(filterFunc),\n        tap(silentEvent),\n        map(pluckFunc(pluckKey)),\n        distinctUntilChanged(),\n        map((position: number) => this.findClosestValue(position)),\n        distinctUntilChanged(),\n        takeUntil(source.end$)\n      );\n    });\n\n    this.dragStart$ = merge(mouse.startPlucked$!, touch.startPlucked$!);\n    this.dragMove$ = merge(mouse.moveResolved$!, touch.moveResolved$!);\n    this.dragEnd$ = merge(mouse.end$!, touch.end$!);\n  }\n\n  private subscribeDrag(periods: string[] = ['start', 'move', 'end']): void {\n    if (periods.indexOf('start') !== -1 && this.dragStart$ && !this.dragStart_) {\n      this.dragStart_ = this.dragStart$.subscribe(this.onDragStart.bind(this));\n    }\n\n    if (periods.indexOf('move') !== -1 && this.dragMove$ && !this.dragMove_) {\n      this.dragMove_ = this.dragMove$.subscribe(this.onDragMove.bind(this));\n    }\n\n    if (periods.indexOf('end') !== -1 && this.dragEnd$ && !this.dragEnd_) {\n      this.dragEnd_ = this.dragEnd$.subscribe(this.onDragEnd.bind(this));\n    }\n  }\n\n  private unsubscribeDrag(periods: string[] = ['start', 'move', 'end']): void {\n    if (periods.indexOf('start') !== -1 && this.dragStart_) {\n      this.dragStart_.unsubscribe();\n      this.dragStart_ = null;\n    }\n\n    if (periods.indexOf('move') !== -1 && this.dragMove_) {\n      this.dragMove_.unsubscribe();\n      this.dragMove_ = null;\n    }\n\n    if (periods.indexOf('end') !== -1 && this.dragEnd_) {\n      this.dragEnd_.unsubscribe();\n      this.dragEnd_ = null;\n    }\n  }\n\n  private toggleDragMoving(movable: boolean): void {\n    const periods = ['move', 'end'];\n    if (movable) {\n      this.sliderService.isDragging = true;\n      this.subscribeDrag(periods);\n    } else {\n      this.sliderService.isDragging = false;\n      this.unsubscribeDrag(periods);\n    }\n  }\n\n  private toggleDragDisabled(disabled: boolean): void {\n    if (disabled) {\n      this.unsubscribeDrag();\n    } else {\n      this.subscribeDrag(['start']);\n    }\n  }\n\n  private findClosestValue(position: number): number {\n    const sliderStart = this.getSliderStartPosition();\n    const sliderLength = this.getSliderLength();\n    const ratio = ensureNumberInRange((position - sliderStart) / sliderLength, 0, 1);\n    const val = (this.nzMax - this.nzMin) * (this.nzVertical ? 1 - ratio : ratio) + this.nzMin;\n    const points =\n      this.nzMarks === null\n        ? []\n        : Object.keys(this.nzMarks)\n            .map(parseFloat)\n            .sort((a, b) => a - b);\n\n    if (this.nzStep !== 0 && !this.nzDots) {\n      const closestOne = Math.round(val / this.nzStep) * this.nzStep;\n      points.push(closestOne);\n    }\n\n    const gaps = points.map(point => Math.abs(val - point));\n    const closest = points[gaps.indexOf(Math.min(...gaps))];\n\n    // return parseFloat(closest.toFixed(getPrecision(this.nzStep)));\n    return this.nzStep === 0 ? closest : parseFloat(closest.toFixed(getPrecision(this.nzStep)));\n  }\n\n  private valueToOffset(value: number): number {\n    return getPercent(this.nzMin, this.nzMax, value);\n  }\n\n  private getSliderStartPosition(): number {\n    if (this.cacheSliderStart !== null) {\n      return this.cacheSliderStart;\n    }\n    const offset = getElementOffset(this.slider.nativeElement);\n    return this.nzVertical ? offset.top : offset.left;\n  }\n\n  private getSliderLength(): number {\n    if (this.cacheSliderLength !== null) {\n      return this.cacheSliderLength;\n    }\n    const sliderDOM = this.slider.nativeElement;\n    return this.nzVertical ? sliderDOM.clientHeight : sliderDOM.clientWidth;\n  }\n\n  /**\n   * Cache DOM layout/reflow operations for performance (may not necessary?)\n   */\n  private cacheSliderProperty(remove: boolean = false): void {\n    this.cacheSliderStart = remove ? null : this.getSliderStartPosition();\n    this.cacheSliderLength = remove ? null : this.getSliderLength();\n  }\n\n  private formatValue(value: NzSliderValue | null): NzSliderValue {\n    if (isNil(value)) {\n      return this.nzRange ? [this.nzMin, this.nzMax] : this.nzMin;\n    } else if (assertValueValid(value, this.nzRange)) {\n      return isValueRange(value)\n        ? value.map(val => ensureNumberInRange(val, this.nzMin, this.nzMax))\n        : ensureNumberInRange(value, this.nzMin, this.nzMax);\n    } else {\n      return this.nzDefaultValue ? this.nzDefaultValue : this.nzRange ? [this.nzMin, this.nzMax] : this.nzMin;\n    }\n  }\n\n  /**\n   * Show one handle's tooltip and hide others'.\n   */\n  private showHandleTooltip(handleIndex: number = 0): void {\n    this.handles.forEach((handle, index) => {\n      handle.active = index === handleIndex;\n    });\n  }\n\n  private hideAllHandleTooltip(): void {\n    this.handles.forEach(handle => (handle.active = false));\n  }\n\n  private generateMarkItems(marks: NzMarks): NzExtendedMark[] | null {\n    const marksArray: NzExtendedMark[] = [];\n    for (const key in marks) {\n      if (marks.hasOwnProperty(key)) {\n        const mark = marks[key];\n        const val = typeof key === 'number' ? key : parseFloat(key);\n        if (val >= this.nzMin && val <= this.nzMax) {\n          marksArray.push({ value: val, offset: this.valueToOffset(val), config: mark });\n        }\n      }\n    }\n    return marksArray.length ? marksArray : null;\n  }\n}\n\nfunction getValueTypeNotMatchError(): Error {\n  return new Error(\n    `The \"nzRange\" can't match the \"ngModel\"'s type, please check these properties: \"nzRange\", \"ngModel\", \"nzDefaultValue\".`\n  );\n}\n\nfunction isValueRange(value: NzSliderValue): value is number[] {\n  if (value instanceof Array) {\n    return value.length === 2;\n  } else {\n    return false;\n  }\n}\n\nfunction generateHandlers(amount: number): NzSliderHandler[] {\n  return Array(amount)\n    .fill(0)\n    .map(() => ({ offset: null, value: null, active: false }));\n}\n\n/**\n * Check if value is valid and throw error if value-type/range not match.\n */\nfunction assertValueValid(value: NzSliderValue, isRange?: boolean): boolean {\n  if ((!isValueRange(value) && isNaN(value)) || (isValueRange(value) && value.some(v => isNaN(v)))) {\n    return false;\n  }\n  return assertValueTypeMatch(value, isRange);\n}\n\n/**\n * Assert that if `this.nzRange` is `true`, value is also a range, vice versa.\n */\nfunction assertValueTypeMatch(value: NzSliderValue, isRange: boolean = false): boolean {\n  if (isValueRange(value) !== isRange) {\n    throw getValueTypeNotMatchError();\n  }\n  return true;\n}\n\nfunction valuesEqual(valA: NzSliderValue, valB: NzSliderValue): boolean {\n  if (typeof valA !== typeof valB) {\n    return false;\n  }\n  return isValueRange(valA) && isValueRange(valB) ? arraysEqual<number>(valA, valB) : valA === valB;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzSliderHandleComponent } from './handle.component';\nimport { NzSliderMarksComponent } from './marks.component';\nimport { NzSliderComponent } from './slider.component';\nimport { NzSliderStepComponent } from './step.component';\nimport { NzSliderTrackComponent } from './track.component';\n\n@NgModule({\n  imports: [\n    NzSliderComponent,\n    NzSliderTrackComponent,\n    NzSliderHandleComponent,\n    NzSliderStepComponent,\n    NzSliderMarksComponent\n  ],\n  exports: [\n    NzSliderComponent,\n    NzSliderTrackComponent,\n    NzSliderHandleComponent,\n    NzSliderStepComponent,\n    NzSliderMarksComponent\n  ]\n})\nexport class NzSliderModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport type NzMark = string | NzMarkObj;\n\nexport interface NzMarkObj {\n  style?: object;\n  label: string;\n}\n\nexport class NzMarks {\n  [key: string]: NzMark;\n}\n\n/**\n * Processed steps that would be passed to sub components.\n */\nexport interface NzExtendedMark {\n  value: number;\n  offset: number;\n  config: NzMark;\n}\n\n/**\n * Marks that would be rendered.\n */\nexport interface NzDisplayedMark extends NzExtendedMark {\n  active: boolean;\n  label: string;\n  style?: object;\n}\n\n/**\n * Steps that would be rendered.\n */\nexport interface NzDisplayedStep extends NzExtendedMark {\n  active: boolean;\n  style?: object;\n}\n\nexport type NzSliderShowTooltip = 'always' | 'never' | 'default';\n\nexport type NzSliderValue = number[] | number;\n\nexport interface NzSliderHandler {\n  offset: number | null;\n  value: number | null;\n  active: boolean;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport { NzSliderComponent } from './slider.component';\nexport { NzSliderService as ɵNzSliderService } from './slider.service';\nexport { NzSliderModule } from './slider.module';\nexport { NzSliderHandleComponent as ɵNzSliderHandleComponent } from './handle.component';\nexport { NzSliderMarksComponent as ɵNzSliderMarksComponent } from './marks.component';\nexport { NzSliderStepComponent as ɵNzSliderStepComponent } from './step.component';\nexport { NzSliderTrackComponent as ɵNzSliderTrackComponent } from './track.component';\nexport type { NzSliderTrackStyle } from './track.component';\nexport * from './typings';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1.NzSliderService", "i2"], "mappings": ";;;;;;;;;;;;AAAA;;;AAGG;MAKU,eAAe,CAAA;IAC1B,UAAU,GAAG,KAAK;uGADP,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAAf,eAAe,EAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B;;;MC6CY,uBAAuB,CAAA;AAkBxB,IAAA,aAAA;AACA,IAAA,GAAA;AAlB8B,IAAA,QAAQ;AACE,IAAA,OAAO;AAEjB,IAAA,QAAQ;AACR,IAAA,OAAO;AACQ,IAAA,MAAM;AACN,IAAA,KAAK;IACnD,cAAc,GAAwB,SAAS;AAC/C,IAAA,gBAAgB;AAChB,IAAA,gBAAgB;IACe,MAAM,GAAG,KAAK;IAC7C,GAAG,GAAc,KAAK;AAE/B,IAAA,YAAY;IACZ,KAAK,GAAqB,EAAE;IAE5B,WACU,CAAA,aAA8B,EAC9B,GAAsB,EAAA;QADtB,IAAa,CAAA,aAAA,GAAb,aAAa;QACb,IAAG,CAAA,GAAA,GAAH,GAAG;;AAGb,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,OAAO;AAEvE,QAAA,IAAI,MAAM,IAAI,OAAO,IAAI,GAAG,EAAE;YAC5B,IAAI,CAAC,WAAW,EAAE;;QAGpB,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,qBAAqB,EAAE;;QAG9B,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,MAAM,CAAC,YAAY,EAAE;AACvB,gBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;;iBACnB;AACL,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;;;AAI7B,QAAA,IAAI,cAAc,EAAE,YAAY,KAAK,QAAQ,EAAE;AAC7C,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;;;IAIhE,WAAW,GAAG,MAAW;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;AAClC,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,qBAAqB,EAAE;AAC5B,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;;AAE5B,KAAC;IAED,WAAW,GAAG,MAAW;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;AAClC,YAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;AACzB,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;;AAE5B,KAAC;IAED,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,KAAK,EAAE;;AAG9B,IAAA,aAAa,CAAC,IAAa,EAAE,KAAA,GAAiB,KAAK,EAAA;AACzD,QAAA,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,cAAc,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAClE;;QAGF,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE;;aACf;AACL,YAAA,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE;;;IAIhB,kBAAkB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,YAAY;gBACf,OAAO,IAAI,CAAC,gBAAgB,KAAK,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAM,CAAC,GAAG,IAAI,CAAC,gBAAgB;;aACrG;YACL,IAAI,CAAC,YAAY,GAAG,CAAA,EAAG,IAAI,CAAC,KAAK,EAAE;;;IAI/B,qBAAqB,GAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,cAAc,EAAE,CAAC;;;IAIxD,WAAW,GAAA;AACjB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC9B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO;AAC5B,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM;QAE1B,MAAM,aAAa,GAAG;AACpB,cAAE;AACE,gBAAA,CAAC,OAAO,GAAG,KAAK,GAAG,QAAQ,GAAG,CAAA,EAAG,MAAM,CAAG,CAAA,CAAA;gBAC1C,CAAC,OAAO,GAAG,QAAQ,GAAG,KAAK,GAAG,MAAM;gBACpC,SAAS,EAAE,OAAO,GAAG,IAAI,GAAG,CAAkB,gBAAA;AAC/C;AACH,cAAE;gBACE,GAAG,IAAI,CAAC,0BAA0B,EAAE;AACpC,gBAAA,SAAS,EAAE,CAAc,WAAA,EAAA,OAAO,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,CAAM,IAAA;aAC3G;AAEL,QAAA,IAAI,CAAC,KAAK,GAAG,aAAa;AAC1B,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;IAGjB,0BAA0B,GAAA;AAChC,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,MAAM,GAAG,CAAA,EAAG,IAAI,CAAC,MAAM,GAAG;AACpD,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,CAAG,EAAA,IAAI,CAAC,MAAM,CAAA,CAAA,CAAG,GAAG,MAAM;AACrD,QAAA,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,EAAE;YACtB,MAAM,GAAG,GAAG,IAAI;YAChB,IAAI,GAAG,KAAK;YACZ,KAAK,GAAG,GAAG;;AAEb,QAAA,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;;uGAzHb,uBAAuB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,uBAAuB,EAId,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAChB,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,CAChB,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,+BAA+B,CAC/B,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,+BAA+B,CAI/B,EAAA,cAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,gBAAgB,CATzB,EAAA,GAAA,EAAA,KAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,YAAA,EAAA,eAAA,EAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,kBAAkB,EArBnB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;AAYT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAKS,eAAe,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,kBAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,uBAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,iBAAA,EAAA,kBAAA,EAAA,0BAAA,EAAA,0BAAA,EAAA,2BAAA,EAAA,uBAAA,EAAA,6BAAA,EAAA,yBAAA,EAAA,gBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEd,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAzBnC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;AAYT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,cAAc,EAAE,eAAe;AAC/B,wBAAA,cAAc,EAAE;AACjB,qBAAA;oBACD,OAAO,EAAE,CAAC,eAAe;AAC1B,iBAAA;iHAEyC,QAAQ,EAAA,CAAA;sBAA/C,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,QAAQ,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBACY,OAAO,EAAA,CAAA;sBAAxD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,kBAAkB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;gBAER,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACiB,MAAM,EAAA,CAAA;sBAA5D,KAAK;uBAAC,EAAE,SAAS,EAAE,+BAA+B,EAAE;gBACE,KAAK,EAAA,CAAA;sBAA3D,KAAK;uBAAC,EAAE,SAAS,EAAE,+BAA+B,EAAE;gBAC5C,cAAc,EAAA,CAAA;sBAAtB;gBACQ,gBAAgB,EAAA,CAAA;sBAAxB;gBACQ,gBAAgB,EAAA,CAAA;sBAAxB;gBACuC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,GAAG,EAAA,CAAA;sBAAX;;;AChEH;;;AAGG;MAqCU,sBAAsB,CAAA;IACxB,UAAU,GAAkB,IAAI;IAChC,UAAU,GAAkB,IAAI;IAChC,UAAU,GAAqB,EAAE;AACH,IAAA,GAAG;AACH,IAAA,GAAG;IACF,QAAQ,GAAG,KAAK;IAChB,QAAQ,GAAG,KAAK;AAChB,IAAA,OAAO;IAE/C,KAAK,GAAsB,EAAE;AAE7B,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,OAAO;AAE/D,QAAA,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,IAAI,CAAC,UAAU,EAAE;;QAGnB,IAAI,UAAU,IAAI,UAAU,IAAI,UAAU,IAAI,OAAO,EAAE;YACrD,IAAI,CAAC,iBAAiB,EAAE;;;IAIpB,UAAU,GAAA;QAChB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG;QAEjC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAG;YACtC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;AACtC,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;AACtD,YAAA,MAAM,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM;YAE5D,OAAO;gBACL,KAAK;gBACL,MAAM;gBACN,KAAK;gBACL,KAAK;gBACL,MAAM;AACN,gBAAA,MAAM,EAAE;aACT;AACH,SAAC,CAAC;;AAGI,IAAA,aAAa,CAAC,KAAa,EAAE,KAAa,EAAE,MAAc,EAAA;AAChE,QAAA,IAAI,KAAK;QACT,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,KAAK;AAEpE,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,KAAK,GAAG;AACN,gBAAA,YAAY,EAAE,MAAM;AACpB,gBAAA,MAAM,EAAE,CAAA,EAAG,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,CAAG,CAAA;aACrD;;aACI;AACL,YAAA,KAAK,GAAG;AACN,gBAAA,SAAS,EAAE,CAAyB,uBAAA,CAAA;AACpC,gBAAA,IAAI,EAAE,CAAA,EAAG,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,CAAG,CAAA;aACnD;;QAGH,IAAI,cAAc,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE;YAC1C,KAAK,GAAG,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,CAAC,KAAK,EAAE;;AAGvC,QAAA,OAAO,KAAK;;IAGN,iBAAiB,GAAA;AACvB,QAAA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;AACtE,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;AACxB,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;AACxB,gBAAA,MAAM,QAAQ,GACZ,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU;AAC5C,qBAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,UAAW,IAAI,KAAK,IAAI,IAAI,CAAC,UAAW,CAAC;AAE3E,gBAAA,IAAI,CAAC,MAAM,GAAG,QAAQ;AACxB,aAAC,CAAC;;;uGA3EK,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAtB,sBAAsB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAIb,eAAe,CAAA,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EACf,eAAe,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EACf,gBAAgB,CAChB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAChB,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,CAtB1B,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;AAST,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAKU,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBApBlC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE;;;;;;;;;AAST,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE;AACR;AACF,iBAAA;8BAEU,UAAU,EAAA,CAAA;sBAAlB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACsC,GAAG,EAAA,CAAA;sBAAzC,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACE,GAAG,EAAA,CAAA;sBAAzC,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACG,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;;AAwExC,SAAS,cAAc,CAAC,MAAc,EAAA;AACpC,IAAA,OAAO,OAAO,MAAM,KAAK,QAAQ;AACnC;;AC1HA;;;AAGG;MA8BU,qBAAqB,CAAA;IACvB,UAAU,GAAkB,IAAI;IAChC,UAAU,GAAkB,IAAI;IAChC,UAAU,GAAqB,EAAE;AACH,IAAA,GAAG;AACH,IAAA,GAAG;IACF,QAAQ,GAAG,KAAK;IAChB,QAAQ,GAAG,KAAK;AAChB,IAAA,OAAO;IAE/C,KAAK,GAAsB,EAAE;AAE7B,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,OAAO;AAE/D,QAAA,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,IAAI,CAAC,UAAU,EAAE;;QAEnB,IAAI,UAAU,IAAI,UAAU,IAAI,UAAU,IAAI,OAAO,EAAE;YACrD,IAAI,CAAC,iBAAiB,EAAE;;;IAIpB,UAAU,GAAA;AAChB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,MAAM;QAEhD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAG;AACtC,YAAA,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI;AAC9B,YAAA,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM;YACxB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG;AAEjC,YAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,gBAAA,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,KAAK,IAAI,GAAG;;YAG7C,OAAO;gBACL,KAAK;gBACL,MAAM;gBACN,MAAM;AACN,gBAAA,MAAM,EAAE,KAAK;AACb,gBAAA,KAAK,EAAE;AACL,oBAAA,CAAC,MAAM,GAAG,CAAA,EAAG,MAAM,CAAG,CAAA,CAAA;oBACtB,SAAS,EAAE,IAAI,CAAC,QAAQ,GAAG,iBAAiB,GAAG;AAChD;aACF;AACH,SAAC,CAAC;;IAGI,iBAAiB,GAAA;AACvB,QAAA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;AACtE,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;AACxB,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;AACxB,gBAAA,MAAM,QAAQ,GACZ,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU;AAC5C,qBAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,UAAW,IAAI,KAAK,IAAI,IAAI,CAAC,UAAW,CAAC;AAC3E,gBAAA,IAAI,CAAC,MAAM,GAAG,QAAQ;AACxB,aAAC,CAAC;;;uGAxDK,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAArB,qBAAqB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAIZ,eAAe,CAAA,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EACf,eAAe,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EACf,gBAAgB,CAChB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAChB,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,CAjB1B,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;AAIT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAKU,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAfjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE;;;;AAIT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE;AACR;AACF,iBAAA;8BAEU,UAAU,EAAA,CAAA;sBAAlB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,UAAU,EAAA,CAAA;sBAAlB;gBACsC,GAAG,EAAA,CAAA;sBAAzC,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACE,GAAG,EAAA,CAAA;sBAAzC,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACG,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;;;MCR3B,sBAAsB,CAAA;IACM,MAAM,GAAW,CAAC;IACjB,OAAO,GAAY,KAAK;IACvD,GAAG,GAAc,KAAK;IACQ,MAAM,GAAW,CAAC;IACjB,QAAQ,GAAG,KAAK;IAChB,QAAQ,GAAG,KAAK;IAExD,KAAK,GAAuB,EAAE;IAE9B,WAAW,GAAA;AACT,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC9B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO;AAC5B,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAG,SAAS,GAAG,QAAQ;AACvD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM;AAC1B,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM;QAE1B,MAAM,YAAY,GAAuB;AACvC,cAAE;AACE,gBAAA,CAAC,OAAO,GAAG,KAAK,GAAG,QAAQ,GAAG,CAAA,EAAG,MAAM,CAAG,CAAA,CAAA;gBAC1C,CAAC,OAAO,GAAG,QAAQ,GAAG,KAAK,GAAG,MAAM;gBACpC,MAAM,EAAE,CAAG,EAAA,MAAM,CAAG,CAAA,CAAA;gBACpB;AACD;AACH,cAAE;gBACE,GAAG,IAAI,CAAC,0BAA0B,EAAE;gBACpC,KAAK,EAAE,CAAG,EAAA,MAAM,CAAG,CAAA,CAAA;gBACnB;aACD;AAEL,QAAA,IAAI,CAAC,KAAK,GAAG,YAAY;;IAGnB,0BAA0B,GAAA;AAChC,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,MAAM,GAAG,CAAA,EAAG,IAAI,CAAC,MAAM,GAAG;AACpD,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,CAAG,EAAA,IAAI,CAAC,MAAM,CAAA,CAAA,CAAG,GAAG,MAAM;AACrD,QAAA,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,EAAE;YACtB,MAAM,GAAG,GAAG,IAAI;YAChB,IAAI,GAAG,KAAK;YACZ,KAAK,GAAG,GAAG;;AAEb,QAAA,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;;uGAzCb,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAtB,sBAAsB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EACb,eAAe,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EACf,gBAAgB,CAAA,EAAA,GAAA,EAAA,KAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAEhB,eAAe,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EACf,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAChB,gBAAgB,CAAA,EAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAX1B,CAAsD,oDAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAKrD,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBARlC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,CAAsD,oDAAA,CAAA;AAChE,oBAAA,mBAAmB,EAAE,KAAK;oBAC1B,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC;AAC1C,iBAAA;8BAEwC,MAAM,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACG,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,GAAG,EAAA,CAAA;sBAAX;gBACsC,MAAM,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACG,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;;;MCsF3B,iBAAiB,CAAA;AAwCnB,IAAA,MAAA;AACC,IAAA,aAAA;AACA,IAAA,GAAA;AACA,IAAA,QAAA;AACA,IAAA,cAAA;AA3C6B,IAAA,iBAAiB;IAEhB,UAAU,GAAG,KAAK;IAClB,MAAM,GAAY,KAAK;IACvB,UAAU,GAAY,IAAI;IAC1B,OAAO,GAAY,KAAK;IACxB,UAAU,GAAY,KAAK;IAC3B,SAAS,GAAY,KAAK;AACzD,IAAA,cAAc;IACd,OAAO,GAAmB,IAAI;IACA,KAAK,GAAG,GAAG;IACX,KAAK,GAAG,CAAC;IACO,MAAM,GAAW,CAAC;IAChE,gBAAgB,GAAwB,SAAS;IACjD,kBAAkB,GAAW,KAAK;AAClC,IAAA,cAAc;AAEJ,IAAA,eAAe,GAAG,IAAI,YAAY,EAAiB;IAEtE,KAAK,GAAyB,IAAI;IAClC,gBAAgB,GAAkB,IAAI;IACtC,iBAAiB,GAAkB,IAAI;AACvC,IAAA,gBAAgB,GAAuB,SAAS,CAAC;AACjD,IAAA,KAAK,GAAqD,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACzF,IAAA,OAAO,GAAsB,EAAE,CAAC;AAChC,IAAA,UAAU,GAA4B,IAAI,CAAC;AAC3C,IAAA,MAAM,GAAiE,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACpG,GAAG,GAAc,KAAK;AAEd,IAAA,UAAU;AACV,IAAA,SAAS;AACT,IAAA,QAAQ;AACR,IAAA,UAAU;AACV,IAAA,SAAS;AACT,IAAA,QAAQ;AACR,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAW;IACjC,sBAAsB,GAAG,IAAI;IAErC,WACS,CAAA,MAAkC,EACjC,aAA8B,EAC9B,GAAsB,EACtB,QAAkB,EAClB,cAA8B,EAAA;QAJ/B,IAAM,CAAA,MAAA,GAAN,MAAM;QACL,IAAa,CAAA,aAAA,GAAb,aAAa;QACb,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAc,CAAA,cAAA,GAAd,cAAc;;IAGxB,QAAQ,GAAA;QACN,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;QACpC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAC5F,YAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;YACxB,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACzC,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;QACrD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI;QAC5E,IAAI,CAAC,oBAAoB,EAAE;AAC3B,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC;AAExC,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;YAC5B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;;AAIzC,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,OAAO;AAEhD,QAAA,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;AACzC,YAAA,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,YAAY,CAAC;;AAC3C,aAAA,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC1C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI;;AACvE,aAAA,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;AAC1C,YAAA,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;YAC7D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;;IAIzC,WAAW,GAAA;QACT,IAAI,CAAC,eAAe,EAAE;AACtB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;AAG1B,IAAA,UAAU,CAAC,GAAyB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC;;IAG1B,aAAa,CAAC,MAAqB,EAAA;AAEnC,IAAA,SAAS;AAET,IAAA,gBAAgB,CAAC,EAAkC,EAAA;AACjD,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE;;AAGzB,IAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU;AAChF,QAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;AACnC,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB;;AAEG;AACH,IAAA,SAAS,CAAC,CAAgB,EAAA;AACxB,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB;;AAGF,QAAA,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO;QACtB,MAAM,UAAU,GAAG,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,QAAQ;QAC5D,MAAM,UAAU,GAAG,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,UAAU;AAE7D,QAAA,IAAI,EAAE,UAAU,IAAI,UAAU,CAAC,EAAE;YAC/B;;QAGF,CAAC,CAAC,cAAc,EAAE;AAElB,QAAA,IAAI,IAAI,GAAG,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAChF,QAAA,IAAI,GAAG,IAAI,CAAC,GAAG,KAAK,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI;AAC5C,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC;cACf,IAAI,CAAC,KAAkB,CAAC,IAAI,CAAC,gBAAiB,CAAC,GAAG;AACrD,cAAG,IAAI,CAAC,KAAgB,GAAG,IAAI;AACjC,QAAA,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACxE,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;AAGhD,IAAA,eAAe,CAAC,KAAa,EAAA;AAC3B,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;;AAGvB,IAAA,QAAQ,CAAC,KAA2B,EAAE,YAAA,GAAwB,KAAK,EAAA;QACzE,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACpC,IAAI,CAAC,qBAAqB,EAAE;;aACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAM,EAAE,KAAM,CAAC,EAAE;AAC5C,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK;YAClB,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;;IAInC,QAAQ,CAAC,eAAwB,KAAK,EAAA;AAC5C,QAAA,IAAI,YAAY,IAAI,IAAI,CAAC,KAAK,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1D,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;QAE9C,OAAO,IAAI,CAAC,KAAM;;AAGpB;;AAEG;AACK,IAAA,gBAAgB,CAAC,KAAqB,EAAA;QAC5C,IAAI,eAAe,GAAG,KAAK;AAE3B,QAAA,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE;AAC1C,YAAA,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;;QAGvC,OAAO,YAAY,CAAC,eAAe;AACjC,cAAE,eAAe,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;AACpD,cAAE,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC;;AAGzC;;AAEG;AACK,IAAA,mBAAmB,CAAC,YAAoB,EAAA;AAC9C,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;AAC7B,QAAA,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;YACvB,IAAI,OAAO,GAAkB,IAAI;AACjC,YAAA,IAAI,GAAW;AACf,YAAA,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,KAAI;gBAC3B,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC;gBAClC,IAAI,OAAO,KAAK,IAAI,IAAI,GAAG,GAAG,OAAQ,EAAE;oBACtC,OAAO,GAAG,GAAG;oBACb,WAAW,GAAG,KAAK;;AAEvB,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW;YACnC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE;;aAChD;YACL,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;;;AAIvC,IAAA,cAAc,CAAC,YAAoB,EAAA;AACzC,QAAA,IAAI,YAAY,CAAC,IAAI,CAAC,KAAM,CAAC,EAAE;YAC7B,MAAM,QAAQ,GAAG,CAAC,GAAI,IAAI,CAAC,KAAkB,CAAC;AAC9C,YAAA,QAAQ,CAAC,IAAI,CAAC,gBAAiB,CAAC,GAAG,YAAY;AAC/C,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;;aAClB;AACL,YAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;;;AAI/B;;AAEG;IACK,qBAAqB,GAAA;AAC3B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACvC,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;AACvD,QAAA,MAAM,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC;AAC7E,QAAA,MAAM,UAAU,GAAG,YAAY,CAAC,YAAY;AAC1C,cAAE,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;AACrD,cAAE,CAAC,CAAC,EAAE,YAAY,CAAC;QAErB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,KAAI;AACrC,YAAA,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM;YAC7D,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC;AAChE,SAAC,CAAC;AAEF,QAAA,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,UAAU;AACnD,QAAA,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,UAAU;AAEnD,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGjB,IAAA,WAAW,CAAC,KAAa,EAAA;AAC/B,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;QAC3B,IAAI,CAAC,mBAAmB,EAAE;QAC1B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;;AAG1D,IAAA,UAAU,CAAC,KAAa,EAAA;QAC9B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGjB,IAAA,eAAe,CAAC,KAAa,EAAA;AACnC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,EAAE;AAC1C,gBAAA,OAAO,KAAK;;YAEd,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK;;QAExC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,EAAE;YAC1C,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK;;AAGxC,QAAA,OAAO,KAAK;;IAGN,SAAS,GAAA;AACf,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC9C,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;AAC5B,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;QAC9B,IAAI,CAAC,oBAAoB,EAAE;AAC3B,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAGzB;;AAEG;IACK,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YAC5B;;AAEF,QAAA,MAAM,SAAS,GAAiD,IAAI,IAAI,CAAC,KAAY,KACnF,IAAI,CAAC,MAAM,CAAC,CAAC,GAAc,EAAE,GAAW,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC;AACtE,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa;AAC3C,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,GAAG,OAAO,GAAG,OAAO;AACvD,QAAA,MAAM,KAAK,GAA6B;AACtC,YAAA,KAAK,EAAE,WAAW;AAClB,YAAA,IAAI,EAAE,WAAW;AACjB,YAAA,GAAG,EAAE,SAAS;YACd,QAAQ,EAAE,CAAC,WAAW;SACvB;AACD,QAAA,MAAM,KAAK,GAA6B;AACtC,YAAA,KAAK,EAAE,YAAY;AACnB,YAAA,IAAI,EAAE,WAAW;AACjB,YAAA,GAAG,EAAE,UAAU;AACf,YAAA,QAAQ,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,WAAW,CAAC;YACvC,MAAM,EAAE,CAAC,CAA0B,KAAK,CAAC,YAAY;SACtD;QAED,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,IAAG;YAC9B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,GAAG,MAAM,IAAI,EAAE,GAAG,MAAM;YAE9E,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,IAAI,CACrD,MAAM,CAAC,UAAU,CAAC,EAClB,GAAG,CAAC,WAAW,CAAC,EAChB,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EACxB,GAAG,CAAC,CAAC,QAAgB,KAAK,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAC3D;YACD,MAAM,CAAC,IAAI,GAAG,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;YACtC,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,IAAI,CACnD,MAAM,CAAC,UAAU,CAAC,EAClB,GAAG,CAAC,WAAW,CAAC,EAChB,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EACxB,oBAAoB,EAAE,EACtB,GAAG,CAAC,CAAC,QAAgB,KAAK,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,EAC1D,oBAAoB,EAAE,EACtB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CACvB;AACH,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,aAAc,EAAE,KAAK,CAAC,aAAc,CAAC;AACnE,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,aAAc,EAAE,KAAK,CAAC,aAAc,CAAC;AAClE,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,IAAK,EAAE,KAAK,CAAC,IAAK,CAAC;;IAGzC,aAAa,CAAC,UAAoB,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAA;AAChE,QAAA,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC1E,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;AAG1E,QAAA,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACvE,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;AAGvE,QAAA,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACpE,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;IAI9D,eAAe,CAAC,UAAoB,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAA;AAClE,QAAA,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;AACtD,YAAA,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;AAC7B,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;AAGxB,QAAA,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE;AACpD,YAAA,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;AAGvB,QAAA,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;AAClD,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;AAC3B,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;;AAIhB,IAAA,gBAAgB,CAAC,OAAgB,EAAA;AACvC,QAAA,MAAM,OAAO,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC;QAC/B,IAAI,OAAO,EAAE;AACX,YAAA,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI;AACpC,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;;aACtB;AACL,YAAA,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,KAAK;AACrC,YAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;;;AAIzB,IAAA,kBAAkB,CAAC,QAAiB,EAAA;QAC1C,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,eAAe,EAAE;;aACjB;AACL,YAAA,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC;;;AAIzB,IAAA,gBAAgB,CAAC,QAAgB,EAAA;AACvC,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,EAAE;AACjD,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE;AAC3C,QAAA,MAAM,KAAK,GAAG,mBAAmB,CAAC,CAAC,QAAQ,GAAG,WAAW,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC;AAChF,QAAA,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK;AAC1F,QAAA,MAAM,MAAM,GACV,IAAI,CAAC,OAAO,KAAK;AACf,cAAE;cACA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;iBACrB,GAAG,CAAC,UAAU;AACd,iBAAA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE9B,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACrC,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM;AAC9D,YAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;;AAGzB,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;AACvD,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;;QAGvD,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;AAGrF,IAAA,aAAa,CAAC,KAAa,EAAA;AACjC,QAAA,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC;;IAG1C,sBAAsB,GAAA;AAC5B,QAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;YAClC,OAAO,IAAI,CAAC,gBAAgB;;QAE9B,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;AAC1D,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI;;IAG3C,eAAe,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE;YACnC,OAAO,IAAI,CAAC,iBAAiB;;AAE/B,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa;AAC3C,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,YAAY,GAAG,SAAS,CAAC,WAAW;;AAGzE;;AAEG;IACK,mBAAmB,CAAC,SAAkB,KAAK,EAAA;AACjD,QAAA,IAAI,CAAC,gBAAgB,GAAG,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,sBAAsB,EAAE;AACrE,QAAA,IAAI,CAAC,iBAAiB,GAAG,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,eAAe,EAAE;;AAGzD,IAAA,WAAW,CAAC,KAA2B,EAAA;AAC7C,QAAA,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;YAChB,OAAO,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK;;aACtD,IAAI,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;YAChD,OAAO,YAAY,CAAC,KAAK;kBACrB,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,mBAAmB,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;AACnE,kBAAE,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;;aACjD;AACL,YAAA,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK;;;AAI3G;;AAEG;IACK,iBAAiB,CAAC,cAAsB,CAAC,EAAA;QAC/C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,KAAI;AACrC,YAAA,MAAM,CAAC,MAAM,GAAG,KAAK,KAAK,WAAW;AACvC,SAAC,CAAC;;IAGI,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;;AAGjD,IAAA,iBAAiB,CAAC,KAAc,EAAA;QACtC,MAAM,UAAU,GAAqB,EAAE;AACvC,QAAA,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;AACvB,YAAA,IAAI,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AAC7B,gBAAA,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;AACvB,gBAAA,MAAM,GAAG,GAAG,OAAO,GAAG,KAAK,QAAQ,GAAG,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;AAC3D,gBAAA,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE;oBAC1C,UAAU,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;;;QAIpF,OAAO,UAAU,CAAC,MAAM,GAAG,UAAU,GAAG,IAAI;;uGArcnC,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,IAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,gGAGR,gBAAgB,CAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAChB,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,mCAChB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAChB,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAChB,gBAAgB,CAGhB,EAAA,cAAA,EAAA,gBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,eAAe,6BACf,eAAe,CAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EACf,+BAA+B,CAhFxC,EAAA,gBAAA,EAAA,kBAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,mBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,sBAAA,EAAA,eAAA,EAAA,2BAAA,EAAA,YAAA,EAAA,2BAAA,EAAA,YAAA,EAAA,6BAAA,EAAA,YAAA,EAAA,EAAA,cAAA,EAAA,YAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,UAAU,CAAC,MAAM,iBAAiB,CAAC;AAChD,gBAAA,KAAK,EAAE;AACR,aAAA;YACD;AACD,SAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,SAAA,EA6Da,uBAAuB,EA5D3B,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACS,sBAAsB,EAAE,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,SAAA,EAAA,KAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,qBAAqB,EAAE,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,KAAA,EAAA,UAAA,EAAA,UAAA,EAAA,SAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,uBAAuB,0NAAE,sBAAsB,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,KAAA,EAAA,UAAA,EAAA,UAAA,EAAA,SAAA,CAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAU7F,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAzE7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;oBACT,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAE,UAAU,CAAC,uBAAuB,CAAC;AAChD,4BAAA,KAAK,EAAE;AACR,yBAAA;wBACD;AACD,qBAAA;AACD,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDT,EAAA,CAAA;oBACD,OAAO,EAAE,CAAC,sBAAsB,EAAE,qBAAqB,EAAE,uBAAuB,EAAE,sBAAsB,CAAC;AACzG,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,YAAY;AACnB,wBAAA,wBAAwB,EAAE,CAAe,aAAA,CAAA;AACzC,wBAAA,6BAA6B,EAAE,YAAY;AAC3C,wBAAA,6BAA6B,EAAE,YAAY;AAC3C,wBAAA,+BAA+B,EAAE,YAAY;AAC7C,wBAAA,WAAW,EAAE;AACd;AACF,iBAAA;gMAEwC,iBAAiB,EAAA,CAAA;sBAAvD,YAAY;uBAAC,uBAAuB;gBAEG,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,cAAc,EAAA,CAAA;sBAAtB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACsC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACE,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE;gBACkB,MAAM,EAAA,CAAA;sBAA5D,KAAK;uBAAC,EAAE,SAAS,EAAE,+BAA+B,EAAE;gBAC5C,gBAAgB,EAAA,CAAA;sBAAxB;gBACQ,kBAAkB,EAAA,CAAA;sBAA1B;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBAEkB,eAAe,EAAA,CAAA;sBAAjC;;AAubH,SAAS,yBAAyB,GAAA;AAChC,IAAA,OAAO,IAAI,KAAK,CACd,CAAA,sHAAA,CAAwH,CACzH;AACH;AAEA,SAAS,YAAY,CAAC,KAAoB,EAAA;AACxC,IAAA,IAAI,KAAK,YAAY,KAAK,EAAE;AAC1B,QAAA,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC;;SACpB;AACL,QAAA,OAAO,KAAK;;AAEhB;AAEA,SAAS,gBAAgB,CAAC,MAAc,EAAA;IACtC,OAAO,KAAK,CAAC,MAAM;SAChB,IAAI,CAAC,CAAC;SACN,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;AAC9D;AAEA;;AAEG;AACH,SAAS,gBAAgB,CAAC,KAAoB,EAAE,OAAiB,EAAA;AAC/D,IAAA,IAAI,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChG,QAAA,OAAO,KAAK;;AAEd,IAAA,OAAO,oBAAoB,CAAC,KAAK,EAAE,OAAO,CAAC;AAC7C;AAEA;;AAEG;AACH,SAAS,oBAAoB,CAAC,KAAoB,EAAE,UAAmB,KAAK,EAAA;AAC1E,IAAA,IAAI,YAAY,CAAC,KAAK,CAAC,KAAK,OAAO,EAAE;QACnC,MAAM,yBAAyB,EAAE;;AAEnC,IAAA,OAAO,IAAI;AACb;AAEA,SAAS,WAAW,CAAC,IAAmB,EAAE,IAAmB,EAAA;AAC3D,IAAA,IAAI,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE;AAC/B,QAAA,OAAO,KAAK;;IAEd,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,WAAW,CAAS,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI;AACnG;;ACnnBA;;;AAGG;MA0BU,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAdvB,iBAAiB;YACjB,sBAAsB;YACtB,uBAAuB;YACvB,qBAAqB;AACrB,YAAA,sBAAsB,aAGtB,iBAAiB;YACjB,sBAAsB;YACtB,uBAAuB;YACvB,qBAAqB;YACrB,sBAAsB,CAAA,EAAA,CAAA;AAGb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAdvB,iBAAiB;YAEjB,uBAAuB,CAAA,EAAA,CAAA;;2FAYd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAhB1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,iBAAiB;wBACjB,sBAAsB;wBACtB,uBAAuB;wBACvB,qBAAqB;wBACrB;AACD,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,iBAAiB;wBACjB,sBAAsB;wBACtB,uBAAuB;wBACvB,qBAAqB;wBACrB;AACD;AACF,iBAAA;;;AC5BD;;;AAGG;MASU,OAAO,CAAA;AAEnB;;ACdD;;;AAGG;;ACHH;;AAEG;;;;"}