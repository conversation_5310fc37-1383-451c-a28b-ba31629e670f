{"version": 3, "file": "elements.js", "sourceRoot": "", "sources": ["../../../../schematics/utils/ng-update/elements.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAqBH,sFAyBC;AAED,gDAqBC;AAED,4DAqBC;AA1FD,mCAA8D;AAa9D,MAAM,YAAY,GAAG,CAAC,IAAa,EAAE,SAAiB,EAAyB,EAAE;;IAC/E,OAAO,MAAA,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,mDAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACnG,CAAC,CAAC;AAEF,MAAM,sBAAsB,GAAG,CAAC,CAAS,EAAE,CAAS,EAAW,EAAE,CAAC,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,WAAW,EAAE,OAAK,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,WAAW,EAAE,CAAA,CAAC;AAExG,SAAgB,qCAAqC,CAAC,IAAY,EAAE,OAAe,EAAE,aAAqB,EAAE,IAAY;IACtH,MAAM,QAAQ,GAAG,IAAA,sBAAa,EAAC,IAAI,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,CAAC,CAAC;IACvE,MAAM,QAAQ,GAAc,EAAE,CAAC;IAE/B,MAAM,UAAU,GAAG,CAAC,KAAkB,EAAQ,EAAE;QAC9C,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,aAAa,IAAI,CAAC,CAAE,IAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAK,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtJ,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YACjC,CAAC;YAED,IAAI,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC;gBACrD,MAAM,OAAO,GAAG,IAAe,CAAC;gBAChC,MAAM,SAAS,GAAG,IAAI,aAAa,EAAE,CAAC;gBACtC,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAK,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAK,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;oBAC7I,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAEhC,OAAO,QAAQ;SACZ,MAAM,CAAC,CAAC,CAAC,EAAE,WAAC,OAAA,MAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,kBAAkB,0CAAE,QAAQ,CAAA,EAAA,CAAC;SAC5C,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AACrE,CAAC;AAED,SAAgB,kBAAkB,CAAC,IAAY,EAAE,OAAe;IAC9D,MAAM,QAAQ,GAAG,IAAA,sBAAa,EAAC,IAAI,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,CAAC,CAAC;IACvE,MAAM,QAAQ,GAAc,EAAE,CAAC;IAE/B,MAAM,UAAU,GAAG,CAAC,KAAkB,EAAQ,EAAE;QAC9C,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gBACvB,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YACjC,CAAC;YAED,IAAI,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC;gBACrD,QAAQ,CAAC,IAAI,CAAC,IAAe,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAEhC,OAAO,QAAQ;SACZ,MAAM,CAAC,CAAC,CAAC,EAAE,WAAC,OAAA,MAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,kBAAkB,0CAAE,QAAQ,CAAA,EAAA,CAAC;SAC5C,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AACrE,CAAC;AAED,SAAgB,wBAAwB,CAAC,IAAY,EAAE,SAAiB,EAAE,OAAe;IACvF,MAAM,QAAQ,GAAG,IAAA,sBAAa,EAAC,IAAI,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,CAAC,CAAC;IACvE,MAAM,QAAQ,GAAc,EAAE,CAAC;IAE/B,MAAM,UAAU,GAAG,CAAC,KAAkB,EAAQ,EAAE;QAC9C,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gBACvB,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YACjC,CAAC;YAED,IAAI,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,IAAI,YAAY,CAAC,IAAe,EAAE,SAAS,CAAC,EAAE,CAAC;gBACjG,QAAQ,CAAC,IAAI,CAAC,IAAe,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAEhC,OAAO,QAAQ;SACZ,MAAM,CAAC,CAAC,CAAC,EAAE,WAAC,OAAA,MAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,kBAAkB,0CAAE,QAAQ,CAAA,EAAA,CAAC;SAC5C,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AACxE,CAAC"}