{"version": 3, "file": "ng-zorro-antd-transfer.mjs", "sources": ["../../components/transfer/interface.ts", "../../components/transfer/transfer-search.component.ts", "../../components/transfer/transfer-list.component.ts", "../../components/transfer/transfer.component.ts", "../../components/transfer/transfer.module.ts", "../../components/transfer/public-api.ts", "../../components/transfer/ng-zorro-antd-transfer.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\nexport type TransferDirection = 'left' | 'right';\n\nexport interface TransferItem {\n  title: string;\n  direction?: TransferDirection;\n  disabled?: boolean;\n  checked?: boolean;\n  hide?: boolean;\n  [key: string]: NzSafeAny;\n}\n\nexport interface TransferCanMove {\n  direction: TransferDirection;\n  list: TransferItem[];\n}\n\nexport interface TransferChange {\n  from: TransferDirection;\n  to: TransferDirection;\n  list: TransferItem[];\n}\n\nexport interface TransferSearchChange {\n  direction: TransferDirection;\n  value: string;\n}\n\nexport interface TransferSelectChange {\n  direction: TransferDirection;\n  checked: boolean;\n  list: TransferItem[];\n  item?: TransferItem;\n}\n\nexport interface TransferStat {\n  checkAll: boolean;\n  checkHalf: boolean;\n  checkCount: number;\n  shownCount: number;\n  availableCount: number;\n}\n\nexport interface RenderListContext {\n  $implicit: TransferItem[];\n  direction: TransferDirection;\n  disabled: boolean;\n  onItemSelectAll: (x: boolean) => void;\n  onItemSelect: (x: TransferItem) => void;\n  stat: TransferStat;\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  EventEmitter,\n  Input,\n  OnChanges,\n  Output,\n  ViewEncapsulation,\n  booleanAttribute\n} from '@angular/core';\nimport { FormsModule } from '@angular/forms';\n\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\n@Component({\n  selector: '[nz-transfer-search]',\n  exportAs: 'nzTransferSearch',\n  preserveWhitespaces: false,\n  template: `\n    <span class=\"ant-input-prefix\">\n      <nz-icon nzType=\"search\" />\n    </span>\n    <input\n      [(ngModel)]=\"value\"\n      (ngModelChange)=\"_handle()\"\n      [disabled]=\"disabled\"\n      [placeholder]=\"placeholder\"\n      class=\"ant-input\"\n      [class]=\"{ 'ant-input-disabled': disabled }\"\n    />\n    @if (value && value.length > 0) {\n      <span class=\"ant-input-suffix\" (click)=\"_clear()\">\n        <nz-icon nzType=\"close-circle\" nzTheme=\"fill\" class=\"ant-input-clear-icon\" />\n      </span>\n    }\n  `,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [FormsModule, NzIconModule]\n})\nexport class NzTransferSearchComponent implements OnChanges {\n  // region: fields\n\n  @Input() placeholder?: string;\n  @Input() value?: string;\n  @Input({ transform: booleanAttribute }) disabled: boolean = false;\n\n  @Output() readonly valueChanged = new EventEmitter<string>();\n  @Output() readonly valueClear = new EventEmitter<void>();\n\n  // endregion\n\n  constructor(private cdr: ChangeDetectorRef) {}\n\n  _handle(): void {\n    this.valueChanged.emit(this.value);\n  }\n\n  _clear(): void {\n    if (this.disabled) {\n      return;\n    }\n    this.value = '';\n    this.valueClear.emit();\n  }\n\n  ngOnChanges(): void {\n    this.cdr.detectChanges();\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  Output,\n  QueryList,\n  TemplateRef,\n  ViewChild,\n  ViewChildren,\n  ViewEncapsulation,\n  booleanAttribute\n} from '@angular/core';\nimport { merge } from 'rxjs';\nimport { startWith, switchMap } from 'rxjs/operators';\n\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NzCheckboxComponent, NzCheckboxModule } from 'ng-zorro-antd/checkbox';\nimport { fromEventOutsideAngular } from 'ng-zorro-antd/core/util';\nimport { NzEmptyModule } from 'ng-zorro-antd/empty';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\nimport { RenderListContext, TransferDirection, TransferItem, TransferStat } from './interface';\nimport { NzTransferSearchComponent } from './transfer-search.component';\n\n@Component({\n  selector: 'nz-transfer-list',\n  exportAs: 'nzTransferList',\n  preserveWhitespaces: false,\n  template: `\n    <div class=\"ant-transfer-list-header\">\n      @if (showSelectAll && !oneWay) {\n        <label\n          class=\"ant-transfer-list-checkbox\"\n          nz-checkbox\n          #headerCheckbox\n          [nzChecked]=\"stat.checkAll\"\n          (nzCheckedChange)=\"onItemSelectAll($event)\"\n          [nzIndeterminate]=\"stat.checkHalf\"\n          [nzDisabled]=\"stat.availableCount === 0 || disabled\"\n        ></label>\n      }\n      <span class=\"ant-transfer-list-header-selected\">\n        <span>\n          @if (!oneWay) {\n            {{ (stat.checkCount > 0 ? stat.checkCount + '/' : '') + stat.shownCount }}\n          } @else {\n            {{ stat.shownCount }}\n          }\n          {{ validData.length > 1 ? itemsUnit : itemUnit }}\n        </span>\n      </span>\n      @if (titleText) {\n        <span class=\"ant-transfer-list-header-title\">{{ titleText }}</span>\n      }\n    </div>\n    <div class=\"ant-transfer-list-body\" [class.ant-transfer-list-body-with-search]=\"showSearch\">\n      @if (showSearch) {\n        <div class=\"ant-transfer-list-body-search-wrapper\">\n          <span\n            nz-transfer-search\n            class=\"ant-input-affix-wrapper ant-transfer-list-search\"\n            (valueChanged)=\"handleFilter($event)\"\n            (valueClear)=\"handleClear()\"\n            [placeholder]=\"searchPlaceholder\"\n            [disabled]=\"disabled\"\n            [value]=\"filter\"\n          ></span>\n        </div>\n      }\n      @if (renderList) {\n        <div class=\"ant-transfer-list-body-customize-wrapper\">\n          <ng-container\n            *ngTemplateOutlet=\"\n              renderList;\n              context: {\n                $implicit: validData,\n                direction: direction,\n                disabled: disabled,\n                onItemSelectAll: onItemSelectAll,\n                onItemSelect: onItemSelect,\n                stat: stat\n              }\n            \"\n          ></ng-container>\n        </div>\n      } @else {\n        @if (stat.shownCount > 0) {\n          <ul class=\"ant-transfer-list-content\">\n            @for (item of validData; track item.key) {\n              <li\n                (click)=\"!oneWay ? onItemSelect(item) : null\"\n                class=\"ant-transfer-list-content-item\"\n                [class]=\"{ 'ant-transfer-list-content-item-disabled': disabled || item.disabled }\"\n              >\n                @if (!oneWay) {\n                  <label\n                    #checkboxes\n                    nz-checkbox\n                    [nzChecked]=\"item.checked\"\n                    (nzCheckedChange)=\"onItemSelect(item)\"\n                    [nzDisabled]=\"disabled || item.disabled\"\n                  >\n                    @if (!render) {\n                      {{ item.title }}\n                    } @else {\n                      <ng-template\n                        [ngTemplateOutlet]=\"render\"\n                        [ngTemplateOutletContext]=\"{ $implicit: item }\"\n                      ></ng-template>\n                    }\n                  </label>\n                } @else {\n                  @if (!render) {\n                    <span class=\"ant-transfer-list-content-item-text\">\n                      {{ item.title }}\n                    </span>\n                    <div\n                      class=\"ant-transfer-list-content-item-remove\"\n                      [class]=\"{ 'ant-transfer-list-content-item-disabled': disabled || item.disabled }\"\n                      (click)=\"!(disabled || item.disabled) ? deleteItem(item) : null\"\n                    >\n                      <nz-icon nzType=\"delete\" nzTheme=\"outline\" />\n                    </div>\n                  } @else {\n                    <ng-template\n                      [ngTemplateOutlet]=\"render\"\n                      [ngTemplateOutletContext]=\"{ $implicit: item }\"\n                    ></ng-template>\n                  }\n                }\n              </li>\n            }\n          </ul>\n        } @else {\n          <div class=\"ant-transfer-list-body-not-found\">\n            <nz-embed-empty [nzComponentName]=\"'transfer'\" [specificContent]=\"notFoundContent\"></nz-embed-empty>\n          </div>\n        }\n      }\n    </div>\n    @if (footer) {\n      <div class=\"ant-transfer-list-footer\">\n        <ng-template [ngTemplateOutlet]=\"footer\" [ngTemplateOutletContext]=\"{ $implicit: direction }\"></ng-template>\n      </div>\n    }\n  `,\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    class: 'ant-transfer-list',\n    '[class.ant-transfer-list-with-footer]': '!!footer'\n  },\n  imports: [NzCheckboxModule, NgTemplateOutlet, NzEmptyModule, NzTransferSearchComponent, NzIconModule, NzButtonModule]\n})\nexport class NzTransferListComponent implements AfterViewInit {\n  // #region fields\n\n  @Input() direction: TransferDirection = 'left';\n  @Input() titleText = '';\n  @Input() showSelectAll = true;\n\n  @Input() dataSource: TransferItem[] = [];\n\n  @Input() itemUnit: string | undefined = '';\n  @Input() itemsUnit: string | undefined = '';\n  @Input() filter = '';\n  @Input() oneWay: boolean = false;\n  @Input({ transform: booleanAttribute }) disabled: boolean = false;\n  @Input({ transform: booleanAttribute }) showSearch?: boolean;\n  @Input() searchPlaceholder?: string;\n  @Input() notFoundContent?: string;\n  @Input() filterOption?: (inputValue: string, item: TransferItem) => boolean;\n\n  @Input() renderList: TemplateRef<RenderListContext> | null = null;\n  @Input() render: TemplateRef<{ $implicit: TransferItem }> | null = null;\n  @Input() footer: TemplateRef<{ $implicit: TransferDirection }> | null = null;\n\n  // events\n  @Output() readonly handleSelectAll: EventEmitter<boolean> = new EventEmitter<boolean>();\n  @Output() readonly handleSelect = new EventEmitter<TransferItem>();\n  @Output() readonly filterChange = new EventEmitter<{ direction: TransferDirection; value: string }>();\n  @Output() readonly moveToLeft = new EventEmitter<void>();\n\n  @ViewChild('headerCheckbox', { read: NzCheckboxComponent }) headerCheckbox?: NzCheckboxComponent;\n\n  @ViewChildren('checkboxes', { read: ElementRef }) checkboxes!: QueryList<ElementRef<HTMLLabelElement>>;\n\n  stat: TransferStat = {\n    checkAll: false,\n    checkHalf: false,\n    checkCount: 0,\n    shownCount: 0,\n    availableCount: 0\n  };\n\n  get validData(): TransferItem[] {\n    return this.dataSource.filter(w => !w.hide);\n  }\n\n  get availableData(): TransferItem[] {\n    // filter disabled data\n    return this.validData.filter(w => !w.disabled);\n  }\n\n  onItemSelect = (item: TransferItem): void => {\n    if (this.disabled || item.disabled) {\n      return;\n    }\n    item.checked = !item.checked;\n    this.updateCheckStatus();\n    this.handleSelect.emit(item);\n  };\n\n  onItemSelectAll = (status: boolean): void => {\n    this.dataSource.forEach(item => {\n      if (!item.disabled && !item.hide) {\n        item.checked = status;\n      }\n    });\n\n    this.updateCheckStatus();\n    this.handleSelectAll.emit(status);\n  };\n\n  private updateCheckStatus(): void {\n    const validCount = this.dataSource.filter(w => !w.disabled).length;\n    this.stat.checkCount = this.dataSource.filter(w => w.checked && !w.disabled).length;\n    this.stat.shownCount = this.validData.length;\n    this.stat.availableCount = this.availableData.length;\n    this.stat.checkAll = validCount > 0 && validCount === this.stat.checkCount;\n    this.stat.checkHalf = this.stat.checkCount > 0 && !this.stat.checkAll;\n    // Note: this is done explicitly since the internal `nzChecked` value may not be updated in edge cases.\n    // Consider the following flow:\n    // 1) the initial value of `stat.checkAll` is `false`\n    // 2) the user filters items\n    // 3) the user clicks \"Select All\" checkbox\n    // 4) the `NzCheckboxComponent` sets `nzChecked` to `true` internally\n    // 5) the user clicks \"Move to right\"\n    // 6) items are moved and the `updateCheckStatus` is invoked\n    // 7) the `stat.checkAll` value has never been updated in this flow, it's always been `false`\n    // 8) the `nzChecked` is still `true` and the checkbox is not unchecked\n    // This is because Angular checks bindings and it checked that `[nzChecked]=\"stat.checkAll\"` has\n    // never been updated, so Angular did not set new `nzChecked` value on the checkbox.\n    this.headerCheckbox && (this.headerCheckbox.nzChecked = this.stat.checkAll);\n  }\n\n  // #endregion\n\n  // #region search\n\n  handleFilter(value: string): void {\n    this.filter = value;\n    this.dataSource.forEach(item => {\n      item.hide = value.length > 0 && !this.matchFilter(value, item);\n    });\n    this.stat.shownCount = this.validData.length;\n    this.stat.availableCount = this.availableData.length;\n    this.filterChange.emit({ direction: this.direction, value });\n  }\n\n  handleClear(): void {\n    this.handleFilter('');\n  }\n\n  deleteItem(item: TransferItem): void {\n    item.checked = true;\n    this.handleSelect.emit(item);\n    this.moveToLeft.emit();\n  }\n\n  private matchFilter(text: string, item: TransferItem): boolean {\n    if (this.filterOption) {\n      return this.filterOption(text, item);\n    }\n    return item.title.includes(text);\n  }\n\n  // #endregion\n\n  constructor(private cdr: ChangeDetectorRef) {}\n\n  markForCheck(): void {\n    this.updateCheckStatus();\n    this.cdr.markForCheck();\n  }\n\n  ngAfterViewInit(): void {\n    this.checkboxes.changes\n      .pipe(\n        startWith(this.checkboxes),\n        switchMap(() => {\n          const checkboxes = this.checkboxes.toArray();\n          return merge(\n            ...checkboxes.map(checkbox => fromEventOutsideAngular<MouseEvent>(checkbox.nativeElement, 'click'))\n          );\n        })\n      )\n      .subscribe(event => {\n        event.stopPropagation();\n      });\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Direction, Directionality } from '@angular/cdk/bidi';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  HostListener,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Output,\n  QueryList,\n  Renderer2,\n  SimpleChanges,\n  TemplateRef,\n  ViewChildren,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject\n} from '@angular/core';\nimport { Observable, Subject, of as observableOf, of } from 'rxjs';\nimport { distinctUntilChanged, map, takeUntil, withLatestFrom } from 'rxjs/operators';\n\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NzFormNoStatusService, NzFormStatusService } from 'ng-zorro-antd/core/form';\nimport { NgClassInterface, NgStyleInterface, NzSafeAny, NzStatus, NzValidateStatus } from 'ng-zorro-antd/core/types';\nimport { getStatusClassNames, toArray } from 'ng-zorro-antd/core/util';\nimport { NzI18nService, NzTransferI18nInterface } from 'ng-zorro-antd/i18n';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\nimport {\n  TransferCanMove,\n  TransferChange,\n  TransferDirection,\n  TransferItem,\n  TransferSearchChange,\n  TransferSelectChange\n} from './interface';\nimport { NzTransferListComponent } from './transfer-list.component';\n\n@Component({\n  selector: 'nz-transfer',\n  exportAs: 'nzTransfer',\n  preserveWhitespaces: false,\n  template: `\n    <nz-transfer-list\n      class=\"ant-transfer-list\"\n      [style]=\"nzListStyle\"\n      data-direction=\"left\"\n      direction=\"left\"\n      [titleText]=\"nzTitles[0]\"\n      [showSelectAll]=\"nzShowSelectAll\"\n      [dataSource]=\"leftDataSource\"\n      [filter]=\"leftFilter\"\n      [filterOption]=\"nzFilterOption\"\n      (filterChange)=\"handleFilterChange($event)\"\n      [renderList]=\"nzRenderList && nzRenderList[0]\"\n      [render]=\"nzRender\"\n      [disabled]=\"nzDisabled\"\n      [showSearch]=\"nzShowSearch\"\n      [searchPlaceholder]=\"nzSearchPlaceholder || locale?.searchPlaceholder\"\n      [notFoundContent]=\"nzNotFoundContent\"\n      [itemUnit]=\"nzItemUnit || locale?.itemUnit\"\n      [itemsUnit]=\"nzItemsUnit || locale?.itemsUnit\"\n      [footer]=\"nzFooter\"\n      (handleSelect)=\"handleLeftSelect($event)\"\n      (handleSelectAll)=\"handleLeftSelectAll($event)\"\n    ></nz-transfer-list>\n    @if (dir !== 'rtl') {\n      <div class=\"ant-transfer-operation\">\n        @if (!nzOneWay) {\n          <button\n            nz-button\n            type=\"button\"\n            (click)=\"moveToLeft()\"\n            [disabled]=\"nzDisabled || !leftActive\"\n            [nzType]=\"'primary'\"\n            [nzSize]=\"'small'\"\n          >\n            <nz-icon nzType=\"left\" />\n            @if (nzOperations[1]) {\n              <span>{{ nzOperations[1] }}</span>\n            }\n          </button>\n        }\n        <button\n          nz-button\n          type=\"button\"\n          (click)=\"moveToRight()\"\n          [disabled]=\"nzDisabled || !rightActive\"\n          [nzType]=\"'primary'\"\n          [nzSize]=\"'small'\"\n        >\n          <nz-icon nzType=\"right\" />\n          @if (nzOperations[0]) {\n            <span>{{ nzOperations[0] }}</span>\n          }\n        </button>\n      </div>\n    } @else {\n      <div class=\"ant-transfer-operation\">\n        <button\n          nz-button\n          type=\"button\"\n          (click)=\"moveToRight()\"\n          [disabled]=\"nzDisabled || !rightActive\"\n          [nzType]=\"'primary'\"\n          [nzSize]=\"'small'\"\n        >\n          <nz-icon nzType=\"left\" />\n          @if (nzOperations[0]) {\n            <span>{{ nzOperations[0] }}</span>\n          }\n        </button>\n        @if (!nzOneWay) {\n          <button\n            nz-button\n            type=\"button\"\n            (click)=\"moveToLeft()\"\n            [disabled]=\"nzDisabled || !leftActive\"\n            [nzType]=\"'primary'\"\n            [nzSize]=\"'small'\"\n          >\n            <nz-icon nzType=\"right\" />\n            @if (nzOperations[1]) {\n              <span>{{ nzOperations[1] }}</span>\n            }\n          </button>\n        }\n      </div>\n    }\n    <nz-transfer-list\n      class=\"ant-transfer-list\"\n      [style]=\"nzListStyle\"\n      data-direction=\"right\"\n      direction=\"right\"\n      [titleText]=\"nzTitles[1]\"\n      [showSelectAll]=\"nzShowSelectAll\"\n      [dataSource]=\"rightDataSource\"\n      [filter]=\"rightFilter\"\n      [filterOption]=\"nzFilterOption\"\n      (filterChange)=\"handleFilterChange($event)\"\n      [renderList]=\"nzRenderList && nzRenderList[1]\"\n      [render]=\"nzRender\"\n      [disabled]=\"nzDisabled\"\n      [showSearch]=\"nzShowSearch\"\n      [searchPlaceholder]=\"nzSearchPlaceholder || locale?.searchPlaceholder\"\n      [notFoundContent]=\"nzNotFoundContent\"\n      [itemUnit]=\"nzItemUnit || locale?.itemUnit\"\n      [itemsUnit]=\"nzItemsUnit || locale?.itemsUnit\"\n      [footer]=\"nzFooter\"\n      [oneWay]=\"nzOneWay\"\n      (moveToLeft)=\"moveToLeft()\"\n      (handleSelect)=\"handleRightSelect($event)\"\n      (handleSelectAll)=\"handleRightSelectAll($event)\"\n    ></nz-transfer-list>\n  `,\n  host: {\n    class: 'ant-transfer',\n    '[class.ant-transfer-rtl]': `dir === 'rtl'`,\n    '[class.ant-transfer-disabled]': `nzDisabled`,\n    '[class.ant-transfer-customize-list]': `nzRenderList`\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [NzTransferListComponent, NzIconModule, NzButtonModule]\n})\nexport class NzTransferComponent implements OnInit, OnChanges, OnDestroy {\n  private unsubscribe$ = new Subject<void>();\n\n  @ViewChildren(NzTransferListComponent) lists!: QueryList<NzTransferListComponent>;\n  locale!: NzTransferI18nInterface;\n\n  leftFilter = '';\n  rightFilter = '';\n  dir: Direction = 'ltr';\n\n  // status\n  prefixCls: string = 'ant-transfer';\n  statusCls: NgClassInterface = {};\n  hasFeedback: boolean = false;\n\n  // #region fields\n\n  @Input({ transform: booleanAttribute }) nzDisabled = false;\n  @Input() nzDataSource: TransferItem[] = [];\n  @Input() nzTitles: string[] = ['', ''];\n  @Input() nzOperations: string[] = [];\n  @Input() nzListStyle: NgStyleInterface = {};\n  @Input({ transform: booleanAttribute }) nzShowSelectAll = true;\n  @Input() nzItemUnit?: string;\n  @Input() nzItemsUnit?: string;\n  @Input() nzCanMove: (arg: TransferCanMove) => Observable<TransferItem[]> = (arg: TransferCanMove) => of(arg.list);\n  @Input() nzRenderList: Array<TemplateRef<NzSafeAny> | null> | null = null;\n  @Input() nzRender: TemplateRef<NzSafeAny> | null = null;\n  @Input() nzFooter: TemplateRef<NzSafeAny> | null = null;\n  @Input({ transform: booleanAttribute }) nzShowSearch = false;\n  @Input() nzFilterOption?: (inputValue: string, item: TransferItem) => boolean;\n  @Input() nzSearchPlaceholder?: string;\n  @Input() nzNotFoundContent?: string;\n  @Input() nzTargetKeys: string[] = [];\n  @Input() nzSelectedKeys: string[] = [];\n  @Input() nzStatus: NzStatus = '';\n  @Input({ transform: booleanAttribute }) nzOneWay: boolean = false;\n\n  // events\n  @Output() readonly nzChange = new EventEmitter<TransferChange>();\n  @Output() readonly nzSearchChange = new EventEmitter<TransferSearchChange>();\n  @Output() readonly nzSelectChange = new EventEmitter<TransferSelectChange>();\n\n  // #endregion\n\n  // #region process data\n\n  // left\n  leftDataSource: TransferItem[] = [];\n  lastLeftCheckedIndex?: number;\n\n  // right\n  rightDataSource: TransferItem[] = [];\n  lastRightCheckedIndex?: number;\n\n  isShiftPressed = false;\n\n  @HostListener('window:keydown.shift')\n  onTriggerShiftDown(): void {\n    this.isShiftPressed = true;\n  }\n\n  @HostListener('window:keyup.shift')\n  onTriggerShiftUp(): void {\n    this.isShiftPressed = false;\n  }\n\n  @HostListener('mousedown', ['$event'])\n  onTriggerMouseDown(event: MouseEvent): void {\n    const isInsideTransfer = (event.target as HTMLElement).closest('.ant-transfer-list');\n    if (event.shiftKey && isInsideTransfer) {\n      event.preventDefault();\n    }\n  }\n\n  private splitDataSource(): void {\n    this.leftDataSource = [];\n    this.rightDataSource = [];\n    this.nzDataSource.forEach(record => {\n      if (record.direction === 'right') {\n        record.direction = 'right';\n        this.rightDataSource.push(record);\n      } else {\n        record.direction = 'left';\n        this.leftDataSource.push(record);\n      }\n    });\n  }\n\n  private getCheckedData(direction: TransferDirection): TransferItem[] {\n    return this[direction === 'left' ? 'leftDataSource' : 'rightDataSource'].filter(w => w.checked);\n  }\n\n  handleLeftSelectAll = (checked: boolean): void => this.handleSelect('left', checked);\n  handleRightSelectAll = (checked: boolean): void => this.handleSelect('right', checked);\n\n  handleLeftSelect = (item: TransferItem): void => this.handleSelect('left', !!item.checked, item);\n  handleRightSelect = (item: TransferItem): void => this.handleSelect('right', !!item.checked, item);\n\n  handleSelect(direction: TransferDirection, checked: boolean, item?: TransferItem): void {\n    if (item) {\n      const datasource = direction === 'left' ? this.leftDataSource : this.rightDataSource;\n      const currentIndex = datasource.findIndex(i => i.key === item.key);\n      const lastCheckedIndex = this[direction === 'left' ? 'lastLeftCheckedIndex' : 'lastRightCheckedIndex'] ?? -1;\n      if (this.isShiftPressed && lastCheckedIndex > -1) {\n        const start = Math.min(lastCheckedIndex, currentIndex);\n        const end = Math.max(lastCheckedIndex, currentIndex);\n        for (let i = start; i <= end; i++) {\n          const item = datasource[i];\n          if (!item.disabled) {\n            item.checked = checked;\n          }\n        }\n        this.markForCheckAllList();\n      }\n      this[direction === 'left' ? 'lastLeftCheckedIndex' : 'lastRightCheckedIndex'] = currentIndex;\n    }\n    const list = this.getCheckedData(direction);\n    const count = list.filter(i => !i.disabled).length;\n    this.updateOperationStatus(direction, count);\n    this.nzSelectChange.emit({ direction, checked, list, item });\n  }\n\n  handleFilterChange(ret: { direction: TransferDirection; value: string }): void {\n    this.nzSearchChange.emit(ret);\n  }\n\n  // #endregion\n\n  // #region operation\n\n  leftActive = false;\n  rightActive = false;\n\n  private updateOperationStatus(direction: TransferDirection, count?: number): void {\n    this[direction === 'right' ? 'leftActive' : 'rightActive'] =\n      (typeof count === 'undefined' ? this.getCheckedData(direction).filter(w => !w.disabled).length : count) > 0;\n  }\n\n  moveToLeft = (): void => this.moveTo('left');\n  moveToRight = (): void => this.moveTo('right');\n\n  moveTo(direction: TransferDirection): void {\n    const oppositeDirection = direction === 'left' ? 'right' : 'left';\n    this.updateOperationStatus(oppositeDirection, 0);\n    const datasource = direction === 'left' ? this.rightDataSource : this.leftDataSource;\n    const moveList = datasource.filter(item => item.checked === true && !item.disabled);\n    this.nzCanMove({ direction, list: moveList }).subscribe(\n      newMoveList =>\n        this.truthMoveTo(\n          direction,\n          newMoveList.filter(i => !!i)\n        ),\n      () => moveList.forEach(i => (i.checked = false))\n    );\n  }\n\n  private truthMoveTo(direction: TransferDirection, list: TransferItem[]): void {\n    const oppositeDirection = direction === 'left' ? 'right' : 'left';\n    const datasource = direction === 'left' ? this.rightDataSource : this.leftDataSource;\n    const targetDatasource = direction === 'left' ? this.leftDataSource : this.rightDataSource;\n    for (const item of list) {\n      item.checked = false;\n      item.hide = false;\n      item.direction = direction;\n      datasource.splice(datasource.indexOf(item), 1);\n    }\n    targetDatasource.splice(0, 0, ...list);\n    this.updateOperationStatus(oppositeDirection);\n    this.nzChange.emit({ from: oppositeDirection, to: direction, list });\n    this.markForCheckAllList();\n  }\n\n  private nzFormStatusService = inject(NzFormStatusService, { optional: true });\n  private nzFormNoStatusService = inject(NzFormNoStatusService, { optional: true });\n\n  // #endregion\n\n  constructor(\n    private cdr: ChangeDetectorRef,\n    private i18n: NzI18nService,\n    private elementRef: ElementRef<HTMLElement>,\n    private renderer: Renderer2,\n    private directionality: Directionality\n  ) {}\n\n  private markForCheckAllList(): void {\n    if (!this.lists) {\n      return;\n    }\n    this.lists.forEach(i => i.markForCheck());\n  }\n\n  private handleNzTargetKeys(): void {\n    const keys = toArray(this.nzTargetKeys);\n    const hasOwnKey = (e: TransferItem): boolean => e.hasOwnProperty('key');\n    this.leftDataSource.forEach(e => {\n      if (hasOwnKey(e) && keys.indexOf(e.key) !== -1 && !e.disabled) {\n        e.checked = true;\n      }\n    });\n    this.moveToRight();\n  }\n\n  private handleNzSelectedKeys(): void {\n    const keys = toArray(this.nzSelectedKeys);\n    this.nzDataSource.forEach(e => {\n      if (keys.indexOf(e.key) !== -1) {\n        e.checked = true;\n      }\n    });\n\n    const term = (ld: TransferItem): boolean => ld.disabled === false && ld.checked === true;\n    this.rightActive = this.leftDataSource.some(term);\n    this.leftActive = this.rightDataSource.some(term);\n  }\n\n  ngOnInit(): void {\n    this.nzFormStatusService?.formStatusChanges\n      .pipe(\n        distinctUntilChanged((pre, cur) => {\n          return pre.status === cur.status && pre.hasFeedback === cur.hasFeedback;\n        }),\n        withLatestFrom(this.nzFormNoStatusService ? this.nzFormNoStatusService.noFormStatus : observableOf(false)),\n        map(([{ status, hasFeedback }, noStatus]) => ({ status: noStatus ? '' : status, hasFeedback })),\n        takeUntil(this.unsubscribe$)\n      )\n      .subscribe(({ status, hasFeedback }) => {\n        this.setStatusStyles(status, hasFeedback);\n      });\n\n    this.i18n.localeChange.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Transfer');\n      this.markForCheckAllList();\n    });\n\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.unsubscribe$)).subscribe((direction: Direction) => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    const { nzStatus, nzDataSource, nzTargetKeys, nzSelectedKeys } = changes;\n    if (nzDataSource) {\n      this.splitDataSource();\n      this.updateOperationStatus('left');\n      this.updateOperationStatus('right');\n      this.cdr.detectChanges();\n      this.markForCheckAllList();\n    }\n    if (nzTargetKeys) {\n      this.handleNzTargetKeys();\n    }\n    if (nzSelectedKeys) {\n      this.handleNzSelectedKeys();\n    }\n    if (nzStatus) {\n      this.setStatusStyles(this.nzStatus, this.hasFeedback);\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n\n  private setStatusStyles(status: NzValidateStatus, hasFeedback: boolean): void {\n    // set inner status\n    this.hasFeedback = hasFeedback;\n    this.cdr.markForCheck();\n    // render status if nzStatus is set\n    this.statusCls = getStatusClassNames(this.prefixCls, status, hasFeedback);\n    Object.keys(this.statusCls).forEach(status => {\n      if (this.statusCls[status]) {\n        this.renderer.addClass(this.elementRef.nativeElement, status);\n      } else {\n        this.renderer.removeClass(this.elementRef.nativeElement, status);\n      }\n    });\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzTransferListComponent } from './transfer-list.component';\nimport { NzTransferSearchComponent } from './transfer-search.component';\nimport { NzTransferComponent } from './transfer.component';\n\n@NgModule({\n  imports: [NzTransferComponent, NzTransferListComponent, NzTransferSearchComponent],\n  exports: [NzTransferComponent]\n})\nexport class NzTransferModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './interface';\nexport { NzTransferListComponent } from './transfer-list.component';\nexport { NzTransferSearchComponent } from './transfer-search.component';\nexport { NzTransferComponent } from './transfer.component';\nexport { NzTransferModule } from './transfer.module';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i2", "i1", "observableOf", "i4", "i5"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;;ACHH;;;AAGG;MA2CU,yBAAyB,CAAA;AAYhB,IAAA,GAAA;;AATX,IAAA,WAAW;AACX,IAAA,KAAK;IAC0B,QAAQ,GAAY,KAAK;AAE9C,IAAA,YAAY,GAAG,IAAI,YAAY,EAAU;AACzC,IAAA,UAAU,GAAG,IAAI,YAAY,EAAQ;;AAIxD,IAAA,WAAA,CAAoB,GAAsB,EAAA;QAAtB,IAAG,CAAA,GAAA,GAAH,GAAG;;IAEvB,OAAO,GAAA;QACL,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;;IAGpC,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB;;AAEF,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE;AACf,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;;IAGxB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;;uGA3Bf,yBAAyB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAzB,yBAAyB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,gBAAgB,CA3B1B,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;GAiBT,EAGS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,WAAW,8mBAAE,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAExB,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBA1BrC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;AAiBT,EAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY;AACpC,iBAAA;sFAIU,WAAW,EAAA,CAAA;sBAAnB;gBACQ,KAAK,EAAA,CAAA;sBAAb;gBACuC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAEnB,YAAY,EAAA,CAAA;sBAA9B;gBACkB,UAAU,EAAA,CAAA;sBAA5B;;;ACtDH;;;AAGG;MAiKU,uBAAuB,CAAA;AA6Hd,IAAA,GAAA;;IA1HX,SAAS,GAAsB,MAAM;IACrC,SAAS,GAAG,EAAE;IACd,aAAa,GAAG,IAAI;IAEpB,UAAU,GAAmB,EAAE;IAE/B,QAAQ,GAAuB,EAAE;IACjC,SAAS,GAAuB,EAAE;IAClC,MAAM,GAAG,EAAE;IACX,MAAM,GAAY,KAAK;IACQ,QAAQ,GAAY,KAAK;AACzB,IAAA,UAAU;AACzC,IAAA,iBAAiB;AACjB,IAAA,eAAe;AACf,IAAA,YAAY;IAEZ,UAAU,GAA0C,IAAI;IACxD,MAAM,GAAoD,IAAI;IAC9D,MAAM,GAAyD,IAAI;;AAGzD,IAAA,eAAe,GAA0B,IAAI,YAAY,EAAW;AACpE,IAAA,YAAY,GAAG,IAAI,YAAY,EAAgB;AAC/C,IAAA,YAAY,GAAG,IAAI,YAAY,EAAmD;AAClF,IAAA,UAAU,GAAG,IAAI,YAAY,EAAQ;AAEI,IAAA,cAAc;AAExB,IAAA,UAAU;AAE5D,IAAA,IAAI,GAAiB;AACnB,QAAA,QAAQ,EAAE,KAAK;AACf,QAAA,SAAS,EAAE,KAAK;AAChB,QAAA,UAAU,EAAE,CAAC;AACb,QAAA,UAAU,EAAE,CAAC;AACb,QAAA,cAAc,EAAE;KACjB;AAED,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;;AAG7C,IAAA,IAAI,aAAa,GAAA;;AAEf,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC;;AAGhD,IAAA,YAAY,GAAG,CAAC,IAAkB,KAAU;QAC1C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClC;;AAEF,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;QAC5B,IAAI,CAAC,iBAAiB,EAAE;AACxB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9B,KAAC;AAED,IAAA,eAAe,GAAG,CAAC,MAAe,KAAU;AAC1C,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAG;YAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AAChC,gBAAA,IAAI,CAAC,OAAO,GAAG,MAAM;;AAEzB,SAAC,CAAC;QAEF,IAAI,CAAC,iBAAiB,EAAE;AACxB,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC;AACnC,KAAC;IAEO,iBAAiB,GAAA;AACvB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM;QAClE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM;QACnF,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM;QAC5C,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM;AACpD,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,UAAU,GAAG,CAAC,IAAI,UAAU,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU;AAC1E,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ;;;;;;;;;;;;;AAarE,QAAA,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;;;;AAO7E,IAAA,YAAY,CAAC,KAAa,EAAA;AACxB,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAG;AAC7B,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;AAChE,SAAC,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM;QAC5C,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM;AACpD,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;;IAG9D,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;;AAGvB,IAAA,UAAU,CAAC,IAAkB,EAAA;AAC3B,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI;AACnB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AAC5B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;;IAGhB,WAAW,CAAC,IAAY,EAAE,IAAkB,EAAA;AAClD,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;;QAEtC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;;;AAKlC,IAAA,WAAA,CAAoB,GAAsB,EAAA;QAAtB,IAAG,CAAA,GAAA,GAAH,GAAG;;IAEvB,YAAY,GAAA;QACV,IAAI,CAAC,iBAAiB,EAAE;AACxB,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;IAGzB,eAAe,GAAA;QACb,IAAI,CAAC,UAAU,CAAC;aACb,IAAI,CACH,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,EAC1B,SAAS,CAAC,MAAK;YACb,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YAC5C,OAAO,KAAK,CACV,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,IAAI,uBAAuB,CAAa,QAAQ,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CACpG;AACH,SAAC,CAAC;aAEH,SAAS,CAAC,KAAK,IAAG;YACjB,KAAK,CAAC,eAAe,EAAE;AACzB,SAAC,CAAC;;uGAjJK,uBAAuB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAvB,uBAAuB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,EAAA,eAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAad,gBAAgB,CAChB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,2hBAeC,mBAAmB,EAAA,EAAA,EAAA,YAAA,EAAA,YAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,IAAA,EAEpB,UAAU,EA7JpC,CAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqHT,EAOS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,gBAAgB,EAAE,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAC,IAAA,CAAA,mBAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,aAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,MAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,EAAE,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,aAAa,8LAAE,yBAAyB,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,CAAA,aAAA,EAAA,OAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,cAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAE,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAE,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,2BAAA,EAAA,QAAA,EAAA,8IAAA,EAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEzG,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAlInC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHT,EAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,mBAAmB;AAC1B,wBAAA,uCAAuC,EAAE;AAC1C,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EAAE,yBAAyB,EAAE,YAAY,EAAE,cAAc;AACrH,iBAAA;sFAIU,SAAS,EAAA,CAAA;sBAAjB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,aAAa,EAAA,CAAA;sBAArB;gBAEQ,UAAU,EAAA,CAAA;sBAAlB;gBAEQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACuC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,eAAe,EAAA,CAAA;sBAAvB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBAEQ,UAAU,EAAA,CAAA;sBAAlB;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBACQ,MAAM,EAAA,CAAA;sBAAd;gBAGkB,eAAe,EAAA,CAAA;sBAAjC;gBACkB,YAAY,EAAA,CAAA;sBAA9B;gBACkB,YAAY,EAAA,CAAA;sBAA9B;gBACkB,UAAU,EAAA,CAAA;sBAA5B;gBAE2D,cAAc,EAAA,CAAA;sBAAzE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,gBAAgB,EAAE,EAAE,IAAI,EAAE,mBAAmB,EAAE;gBAER,UAAU,EAAA,CAAA;sBAA3D,YAAY;AAAC,gBAAA,IAAA,EAAA,CAAA,YAAY,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;;;MCrBrC,mBAAmB,CAAA;AAmLpB,IAAA,GAAA;AACA,IAAA,IAAA;AACA,IAAA,UAAA;AACA,IAAA,QAAA;AACA,IAAA,cAAA;AAtLF,IAAA,YAAY,GAAG,IAAI,OAAO,EAAQ;AAEH,IAAA,KAAK;AAC5C,IAAA,MAAM;IAEN,UAAU,GAAG,EAAE;IACf,WAAW,GAAG,EAAE;IAChB,GAAG,GAAc,KAAK;;IAGtB,SAAS,GAAW,cAAc;IAClC,SAAS,GAAqB,EAAE;IAChC,WAAW,GAAY,KAAK;;IAIY,UAAU,GAAG,KAAK;IACjD,YAAY,GAAmB,EAAE;AACjC,IAAA,QAAQ,GAAa,CAAC,EAAE,EAAE,EAAE,CAAC;IAC7B,YAAY,GAAa,EAAE;IAC3B,WAAW,GAAqB,EAAE;IACH,eAAe,GAAG,IAAI;AACrD,IAAA,UAAU;AACV,IAAA,WAAW;AACX,IAAA,SAAS,GAAyD,CAAC,GAAoB,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC;IACxG,YAAY,GAAgD,IAAI;IAChE,QAAQ,GAAkC,IAAI;IAC9C,QAAQ,GAAkC,IAAI;IACf,YAAY,GAAG,KAAK;AACnD,IAAA,cAAc;AACd,IAAA,mBAAmB;AACnB,IAAA,iBAAiB;IACjB,YAAY,GAAa,EAAE;IAC3B,cAAc,GAAa,EAAE;IAC7B,QAAQ,GAAa,EAAE;IACQ,QAAQ,GAAY,KAAK;;AAG9C,IAAA,QAAQ,GAAG,IAAI,YAAY,EAAkB;AAC7C,IAAA,cAAc,GAAG,IAAI,YAAY,EAAwB;AACzD,IAAA,cAAc,GAAG,IAAI,YAAY,EAAwB;;;;IAO5E,cAAc,GAAmB,EAAE;AACnC,IAAA,oBAAoB;;IAGpB,eAAe,GAAmB,EAAE;AACpC,IAAA,qBAAqB;IAErB,cAAc,GAAG,KAAK;IAGtB,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;IAI5B,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK;;AAI7B,IAAA,kBAAkB,CAAC,KAAiB,EAAA;QAClC,MAAM,gBAAgB,GAAI,KAAK,CAAC,MAAsB,CAAC,OAAO,CAAC,oBAAoB,CAAC;AACpF,QAAA,IAAI,KAAK,CAAC,QAAQ,IAAI,gBAAgB,EAAE;YACtC,KAAK,CAAC,cAAc,EAAE;;;IAIlB,eAAe,GAAA;AACrB,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE;AACxB,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE;AACzB,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,IAAG;AACjC,YAAA,IAAI,MAAM,CAAC,SAAS,KAAK,OAAO,EAAE;AAChC,gBAAA,MAAM,CAAC,SAAS,GAAG,OAAO;AAC1B,gBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC;;iBAC5B;AACL,gBAAA,MAAM,CAAC,SAAS,GAAG,MAAM;AACzB,gBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;;AAEpC,SAAC,CAAC;;AAGI,IAAA,cAAc,CAAC,SAA4B,EAAA;QACjD,OAAO,IAAI,CAAC,SAAS,KAAK,MAAM,GAAG,gBAAgB,GAAG,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;;AAGjG,IAAA,mBAAmB,GAAG,CAAC,OAAgB,KAAW,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC;AACpF,IAAA,oBAAoB,GAAG,CAAC,OAAgB,KAAW,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC;IAEtF,gBAAgB,GAAG,CAAC,IAAkB,KAAW,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IAChG,iBAAiB,GAAG,CAAC,IAAkB,KAAW,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;AAElG,IAAA,YAAY,CAAC,SAA4B,EAAE,OAAgB,EAAE,IAAmB,EAAA;QAC9E,IAAI,IAAI,EAAE;AACR,YAAA,MAAM,UAAU,GAAG,SAAS,KAAK,MAAM,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe;AACpF,YAAA,MAAM,YAAY,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC;AAClE,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,KAAK,MAAM,GAAG,sBAAsB,GAAG,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAC5G,IAAI,IAAI,CAAC,cAAc,IAAI,gBAAgB,GAAG,CAAC,CAAC,EAAE;gBAChD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,YAAY,CAAC;gBACtD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,YAAY,CAAC;AACpD,gBAAA,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE;AACjC,oBAAA,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC;AAC1B,oBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,wBAAA,IAAI,CAAC,OAAO,GAAG,OAAO;;;gBAG1B,IAAI,CAAC,mBAAmB,EAAE;;AAE5B,YAAA,IAAI,CAAC,SAAS,KAAK,MAAM,GAAG,sBAAsB,GAAG,uBAAuB,CAAC,GAAG,YAAY;;QAE9F,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;AAC3C,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM;AAClD,QAAA,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,KAAK,CAAC;AAC5C,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;AAG9D,IAAA,kBAAkB,CAAC,GAAoD,EAAA;AACrE,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC;;;;IAO/B,UAAU,GAAG,KAAK;IAClB,WAAW,GAAG,KAAK;IAEX,qBAAqB,CAAC,SAA4B,EAAE,KAAc,EAAA;AACxE,QAAA,IAAI,CAAC,SAAS,KAAK,OAAO,GAAG,YAAY,GAAG,aAAa,CAAC;AACxD,YAAA,CAAC,OAAO,KAAK,KAAK,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC;;IAG/G,UAAU,GAAG,MAAY,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5C,WAAW,GAAG,MAAY,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAE9C,IAAA,MAAM,CAAC,SAA4B,EAAA;AACjC,QAAA,MAAM,iBAAiB,GAAG,SAAS,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM;AACjE,QAAA,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,EAAE,CAAC,CAAC;AAChD,QAAA,MAAM,UAAU,GAAG,SAAS,KAAK,MAAM,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc;QACpF,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACnF,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,SAAS,CACrD,WAAW,IACT,IAAI,CAAC,WAAW,CACd,SAAS,EACT,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAC7B,EACH,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CACjD;;IAGK,WAAW,CAAC,SAA4B,EAAE,IAAoB,EAAA;AACpE,QAAA,MAAM,iBAAiB,GAAG,SAAS,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM;AACjE,QAAA,MAAM,UAAU,GAAG,SAAS,KAAK,MAAM,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc;AACpF,QAAA,MAAM,gBAAgB,GAAG,SAAS,KAAK,MAAM,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe;AAC1F,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;AACvB,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACpB,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK;AACjB,YAAA,IAAI,CAAC,SAAS,GAAG,SAAS;AAC1B,YAAA,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;QAEhD,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACtC,QAAA,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC;AAC7C,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;QACpE,IAAI,CAAC,mBAAmB,EAAE;;IAGpB,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrE,qBAAqB,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;IAIjF,WACU,CAAA,GAAsB,EACtB,IAAmB,EACnB,UAAmC,EACnC,QAAmB,EACnB,cAA8B,EAAA;QAJ9B,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAU,CAAA,UAAA,GAAV,UAAU;QACV,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAc,CAAA,cAAA,GAAd,cAAc;;IAGhB,mBAAmB,GAAA;AACzB,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf;;AAEF,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,CAAC;;IAGnC,kBAAkB,GAAA;QACxB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;AACvC,QAAA,MAAM,SAAS,GAAG,CAAC,CAAe,KAAc,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC;AACvE,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAG;YAC9B,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE;AAC7D,gBAAA,CAAC,CAAC,OAAO,GAAG,IAAI;;AAEpB,SAAC,CAAC;QACF,IAAI,CAAC,WAAW,EAAE;;IAGZ,oBAAoB,GAAA;QAC1B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC;AACzC,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAG;AAC5B,YAAA,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;AAC9B,gBAAA,CAAC,CAAC,OAAO,GAAG,IAAI;;AAEpB,SAAC,CAAC;AAEF,QAAA,MAAM,IAAI,GAAG,CAAC,EAAgB,KAAc,EAAE,CAAC,QAAQ,KAAK,KAAK,IAAI,EAAE,CAAC,OAAO,KAAK,IAAI;QACxF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;QACjD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;;IAGnD,QAAQ,GAAA;QACN,IAAI,CAAC,mBAAmB,EAAE;aACvB,IAAI,CACH,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,KAAI;AAChC,YAAA,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,KAAK,GAAG,CAAC,WAAW;AACzE,SAAC,CAAC,EACF,cAAc,CAAC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,GAAGC,EAAY,CAAC,KAAK,CAAC,CAAC,EAC1G,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,GAAG,EAAE,GAAG,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC,EAC/F,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC;aAE7B,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,KAAI;AACrC,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,CAAC;AAC3C,SAAC,CAAC;AAEJ,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YACvE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;YACjD,IAAI,CAAC,mBAAmB,EAAE;AAC5B,SAAC,CAAC;QAEF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;QACpC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAoB,KAAI;AAChG,YAAA,IAAI,CAAC,GAAG,GAAG,SAAS;AACpB,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;AAC1B,SAAC,CAAC;;AAGJ,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,OAAO;QACxE,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,eAAe,EAAE;AACtB,YAAA,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;AAClC,YAAA,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;AACnC,YAAA,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;YACxB,IAAI,CAAC,mBAAmB,EAAE;;QAE5B,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,kBAAkB,EAAE;;QAE3B,IAAI,cAAc,EAAE;YAClB,IAAI,CAAC,oBAAoB,EAAE;;QAE7B,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC;;;IAIzD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AACxB,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;;IAGtB,eAAe,CAAC,MAAwB,EAAE,WAAoB,EAAA;;AAEpE,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW;AAC9B,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;AAEvB,QAAA,IAAI,CAAC,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,CAAC;AACzE,QAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,IAAG;AAC3C,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AAC1B,gBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,MAAM,CAAC;;iBACxD;AACL,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,MAAM,CAAC;;AAEpE,SAAC,CAAC;;uGAxRO,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAD,IAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAAD,IAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAnB,mBAAmB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAiBV,gBAAgB,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,WAAA,EAAA,aAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAKhB,gBAAgB,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,WAAA,EAAA,YAAA,EAAA,cAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAOhB,gBAAgB,CAOhB,EAAA,cAAA,EAAA,gBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAjCtB,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,WAAA,EAAA,4BAAA,EAAA,EAAA,UAAA,EAAA,EAAA,wBAAA,EAAA,eAAA,EAAA,6BAAA,EAAA,YAAA,EAAA,mCAAA,EAAA,cAAA,EAAA,EAAA,cAAA,EAAA,cAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,uBAAuB,EA9H3B,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgHT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EASS,uBAAuB,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,eAAA,EAAA,YAAA,EAAA,UAAA,EAAA,WAAA,EAAA,QAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,cAAA,EAAA,YAAA,EAAA,QAAA,EAAA,QAAA,CAAA,EAAA,OAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,cAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAE,YAAY,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAE,cAAc,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAG,IAAA,CAAA,iBAAA,EAAA,QAAA,EAAA,iCAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,QAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAAC,EAAA,CAAA,2BAAA,EAAA,QAAA,EAAA,8IAAA,EAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,2EAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEpD,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBA/H/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,mBAAmB,EAAE,KAAK;AAC1B,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgHT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,cAAc;AACrB,wBAAA,0BAA0B,EAAE,CAAe,aAAA,CAAA;AAC3C,wBAAA,+BAA+B,EAAE,CAAY,UAAA,CAAA;AAC7C,wBAAA,qCAAqC,EAAE,CAAc,YAAA;AACtD,qBAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,OAAO,EAAE,CAAC,uBAAuB,EAAE,YAAY,EAAE,cAAc;AAChE,iBAAA;oMAIwC,KAAK,EAAA,CAAA;sBAA3C,YAAY;uBAAC,uBAAuB;gBAcG,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,YAAY,EAAA,CAAA;sBAApB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACuC,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,UAAU,EAAA,CAAA;sBAAlB;gBACQ,WAAW,EAAA,CAAA;sBAAnB;gBACQ,SAAS,EAAA,CAAA;sBAAjB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACuC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAC7B,cAAc,EAAA,CAAA;sBAAtB;gBACQ,mBAAmB,EAAA,CAAA;sBAA3B;gBACQ,iBAAiB,EAAA,CAAA;sBAAzB;gBACQ,YAAY,EAAA,CAAA;sBAApB;gBACQ,cAAc,EAAA,CAAA;sBAAtB;gBACQ,QAAQ,EAAA,CAAA;sBAAhB;gBACuC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBAGnB,QAAQ,EAAA,CAAA;sBAA1B;gBACkB,cAAc,EAAA,CAAA;sBAAhC;gBACkB,cAAc,EAAA,CAAA;sBAAhC;gBAiBD,kBAAkB,EAAA,CAAA;sBADjB,YAAY;uBAAC,sBAAsB;gBAMpC,gBAAgB,EAAA,CAAA;sBADf,YAAY;uBAAC,oBAAoB;gBAMlC,kBAAkB,EAAA,CAAA;sBADjB,YAAY;uBAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;;;ACjPvC;;;AAGG;MAYU,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAHjB,mBAAmB,EAAE,uBAAuB,EAAE,yBAAyB,aACvE,mBAAmB,CAAA,EAAA,CAAA;AAElB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,EAHjB,OAAA,EAAA,CAAA,mBAAmB,EAAE,uBAAuB,EAAE,yBAAyB,CAAA,EAAA,CAAA;;2FAGtE,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,mBAAmB,EAAE,uBAAuB,EAAE,yBAAyB,CAAC;oBAClF,OAAO,EAAE,CAAC,mBAAmB;AAC9B,iBAAA;;;ACdD;;;AAGG;;ACHH;;AAEG;;;;"}