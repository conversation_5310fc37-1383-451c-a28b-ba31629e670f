"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _index = _interopRequireDefault(require("../../nextSaturday/index.js"));
var _index2 = _interopRequireDefault(require("../_lib/convertToFP/index.js"));
// This file is generated automatically by `scripts/build/fp.ts`. Please, don't change it.
var _default = (0, _index2.default)(_index.default, 1);
exports.default = _default;
module.exports = exports.default;