{"version": 3, "file": "ng-zorro-antd-space.mjs", "sources": ["../../components/space/space-compact.token.ts", "../../components/space/space-compact.component.ts", "../../components/space/space-compact-item.directive.ts", "../../components/space/space-item.directive.ts", "../../components/space/space.component.ts", "../../components/space/space.module.ts", "../../components/space/types.ts", "../../components/space/public-api.ts", "../../components/space/ng-zorro-antd-space.ts"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { InjectionToken, Signal, WritableSignal } from '@angular/core';\n\nimport { NzSizeLDSType } from 'ng-zorro-antd/core/types';\n\nimport type { NzSpaceCompactItemDirective } from './space-compact-item.directive';\n\nexport const NZ_SPACE_COMPACT_SIZE = new InjectionToken<Signal<NzSizeLDSType>>('NZ_SPACE_COMPACT_SIZE');\nexport const NZ_SPACE_COMPACT_ITEMS = new InjectionToken<WritableSignal<NzSpaceCompactItemDirective[]>>(\n  'NZ_SPACE_COMPACT_ITEMS'\n);\nexport const NZ_SPACE_COMPACT_ITEM_TYPE = new InjectionToken<string>('NZ_SPACE_COMPACT_ITEM_TYPE');\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { booleanAttribute, ChangeDetectionStrategy, Component, ElementRef, inject, input, signal } from '@angular/core';\n\nimport { NzSizeLDSType } from 'ng-zorro-antd/core/types';\n\nimport { NZ_SPACE_COMPACT_ITEMS, NZ_SPACE_COMPACT_SIZE } from './space-compact.token';\n\n@Component({\n  selector: 'nz-space-compact',\n  exportAs: 'nzSpaceCompact',\n  template: `<ng-content></ng-content>`,\n  host: {\n    class: 'ant-space-compact',\n    '[class.ant-space-compact-block]': `nzBlock()`,\n    '[class.ant-space-compact-vertical]': `nzDirection() === 'vertical'`\n  },\n  providers: [\n    { provide: NZ_SPACE_COMPACT_SIZE, useFactory: () => inject(NzSpaceCompactComponent).nzSize },\n    { provide: NZ_SPACE_COMPACT_ITEMS, useFactory: () => signal([]) }\n  ],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class NzSpaceCompactComponent {\n  readonly nzBlock = input(false, { transform: booleanAttribute });\n  readonly nzDirection = input<'vertical' | 'horizontal'>('horizontal');\n  readonly nzSize = input<NzSizeLDSType>('default');\n  readonly elementRef: ElementRef<HTMLElement> = inject(ElementRef);\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Directionality } from '@angular/cdk/bidi';\nimport { afterNextRender, computed, Directive, ElementRef, inject, OnDestroy } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\n\nimport { NzSpaceCompactComponent } from './space-compact.component';\nimport { NZ_SPACE_COMPACT_ITEM_TYPE, NZ_SPACE_COMPACT_ITEMS } from './space-compact.token';\n\n@Directive({\n  exportAs: 'nzSpaceCompactItem',\n  host: {\n    '[class]': 'class()'\n  }\n})\nexport class NzSpaceCompactItemDirective implements OnDestroy {\n  /**\n   * Ancestor component injected from the parent.\n   * Note that it is not necessarily the direct parent component.\n   */\n  private readonly spaceCompactCmp = inject(NzSpaceCompactComponent, { host: true, optional: true });\n  private readonly items = inject(NZ_SPACE_COMPACT_ITEMS, { host: true, optional: true });\n  private readonly type = inject(NZ_SPACE_COMPACT_ITEM_TYPE);\n  private readonly elementRef: ElementRef<HTMLElement> = inject(ElementRef);\n  private readonly directionality = inject(Directionality);\n  private readonly dir = toSignal(this.directionality.change, { initialValue: this.directionality.value });\n\n  private get parentElement(): HTMLElement | null {\n    return this.elementRef.nativeElement?.parentElement;\n  }\n\n  protected class = computed(() => {\n    // Only handle when the parent is space compact component\n    if (!this.spaceCompactCmp || !this.items) return null;\n    // Ensure that the injected ancestor component's elements are parent elements\n    if (this.parentElement !== this.spaceCompactCmp!.elementRef.nativeElement) return null;\n\n    const items = this.items();\n    const direction = this.spaceCompactCmp.nzDirection();\n    const classes = [compactItemClassOf(this.type, direction, this.dir() === 'rtl')];\n    const index = items.indexOf(this);\n    const firstIndex = items.findIndex(element => element);\n    // Array [empty, item]\n    // In this case, the index of the first valid element is not 0,\n    // so we need to use findIndex to find the index value of the first valid element.\n    if (index === firstIndex) {\n      classes.push(compactFirstItemClassOf(this.type, direction));\n    } else if (index === items.length - 1) {\n      classes.push(compactLastItemClassOf(this.type, direction));\n    }\n\n    return classes;\n  });\n\n  constructor() {\n    if (!this.spaceCompactCmp || !this.items) return;\n\n    afterNextRender(() => {\n      // Ensure that the injected ancestor component's elements are parent elements\n      if (this.parentElement === this.spaceCompactCmp!.elementRef.nativeElement) {\n        const index = Array.from(this.parentElement.children).indexOf(this.elementRef.nativeElement);\n        this.items!.update(value => {\n          const newValue = value.slice();\n          newValue.splice(index, 0, this);\n          return newValue;\n        });\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.items?.update(value => value.filter(o => o !== this));\n  }\n}\n\nfunction generateCompactClass(\n  type: string,\n  direction: 'vertical' | 'horizontal',\n  position: 'item' | 'first-item' | 'last-item'\n): string {\n  const directionPrefix = direction === 'vertical' ? 'vertical-' : '';\n  return `ant-${type}-compact-${directionPrefix}${position}`;\n}\n\nfunction compactItemClassOf(type: string, direction: 'vertical' | 'horizontal', rtl?: boolean): string {\n  const rtlSuffix = rtl ? '-rtl' : '';\n  return `${generateCompactClass(type, direction, 'item')}${rtlSuffix}`;\n}\n\nfunction compactFirstItemClassOf(type: string, direction: 'vertical' | 'horizontal'): string {\n  return generateCompactClass(type, direction, 'first-item');\n}\n\nfunction compactLastItemClassOf(type: string, direction: 'vertical' | 'horizontal'): string {\n  return generateCompactClass(type, direction, 'last-item');\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { Directive } from '@angular/core';\n\n@Directive({\n  selector: '[nzSpaceItem]'\n})\nexport class NzSpaceItemDirective {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgTemplateOutlet } from '@angular/common';\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  Input,\n  OnChanges,\n  OnDestroy,\n  QueryList,\n  TemplateRef,\n  booleanAttribute\n} from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { NzConfigKey, NzConfigService, WithConfig } from 'ng-zorro-antd/core/config';\nimport { NzStringTemplateOutletDirective } from 'ng-zorro-antd/core/outlet';\nimport { NzSafeAny } from 'ng-zorro-antd/core/types';\n\nimport { NzSpaceItemDirective } from './space-item.directive';\nimport { NzSpaceAlign, NzSpaceDirection, NzSpaceSize, NzSpaceType } from './types';\n\nconst NZ_CONFIG_MODULE_NAME: NzConfigKey = 'space';\nconst SPACE_SIZE: Record<NzSpaceType, number> = {\n  small: 8,\n  middle: 16,\n  large: 24\n};\n\n@Component({\n  selector: 'nz-space, [nz-space]',\n  exportAs: 'nzSpace',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  template: `\n    <ng-content></ng-content>\n    @for (item of items; track item; let last = $last; let index = $index) {\n      <div\n        class=\"ant-space-item\"\n        [style.margin-block-end.px]=\"nzDirection === 'vertical' ? (last ? null : spaceSize) : null\"\n        [style.margin-inline-end.px]=\"nzDirection === 'horizontal' ? (last ? null : spaceSize) : null\"\n      >\n        <ng-container [ngTemplateOutlet]=\"item\"></ng-container>\n      </div>\n      @if (nzSplit && !last) {\n        <span\n          class=\"ant-space-split\"\n          [style.margin-block-end.px]=\"nzDirection === 'vertical' ? (last ? null : spaceSize) : null\"\n          [style.margin-inline-end.px]=\"nzDirection === 'horizontal' ? (last ? null : spaceSize) : null\"\n        >\n          <ng-template [nzStringTemplateOutlet]=\"nzSplit\" [nzStringTemplateOutletContext]=\"{ $implicit: index }\">{{\n            nzSplit\n          }}</ng-template>\n        </span>\n      }\n    }\n  `,\n  host: {\n    class: 'ant-space',\n    '[class.ant-space-horizontal]': 'nzDirection === \"horizontal\"',\n    '[class.ant-space-vertical]': 'nzDirection === \"vertical\"',\n    '[class.ant-space-align-start]': 'mergedAlign === \"start\"',\n    '[class.ant-space-align-end]': 'mergedAlign === \"end\"',\n    '[class.ant-space-align-center]': 'mergedAlign === \"center\"',\n    '[class.ant-space-align-baseline]': 'mergedAlign === \"baseline\"',\n    '[style.flex-wrap]': 'nzWrap ? \"wrap\" : null'\n  },\n  imports: [NgTemplateOutlet, NzStringTemplateOutletDirective]\n})\nexport class NzSpaceComponent implements OnChanges, OnDestroy, AfterContentInit {\n  readonly _nzModuleName: NzConfigKey = NZ_CONFIG_MODULE_NAME;\n\n  @Input() nzDirection: NzSpaceDirection = 'horizontal';\n  @Input() nzAlign?: NzSpaceAlign;\n  @Input() nzSplit: TemplateRef<{ $implicit: number }> | string | null = null;\n  @Input({ transform: booleanAttribute }) nzWrap: boolean = false;\n  @Input() @WithConfig() nzSize: NzSpaceSize = 'small';\n\n  @ContentChildren(NzSpaceItemDirective, { read: TemplateRef }) items!: QueryList<TemplateRef<NzSafeAny>>;\n\n  mergedAlign?: NzSpaceAlign;\n  spaceSize: number = SPACE_SIZE.small;\n  private destroy$ = new Subject<boolean>();\n\n  constructor(\n    public nzConfigService: NzConfigService,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  private updateSpaceItems(): void {\n    const numberSize = typeof this.nzSize === 'string' ? SPACE_SIZE[this.nzSize] : this.nzSize;\n    this.spaceSize = numberSize / (this.nzSplit ? 2 : 1);\n    this.cdr.markForCheck();\n  }\n\n  ngOnChanges(): void {\n    this.updateSpaceItems();\n    this.mergedAlign = this.nzAlign === undefined && this.nzDirection === 'horizontal' ? 'center' : this.nzAlign;\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n\n  ngAfterContentInit(): void {\n    this.updateSpaceItems();\n    this.items.changes.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.cdr.markForCheck();\n    });\n  }\n}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nimport { NgModule } from '@angular/core';\n\nimport { NzSpaceCompactComponent } from './space-compact.component';\nimport { NzSpaceItemDirective } from './space-item.directive';\nimport { NzSpaceComponent } from './space.component';\n\n@NgModule({\n  imports: [NzSpaceComponent, NzSpaceItemDirective, NzSpaceCompactComponent],\n  exports: [NzSpaceComponent, NzSpaceItemDirective, NzSpaceCompactComponent]\n})\nexport class NzSpaceModule {}\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport type NzSpaceDirection = 'vertical' | 'horizontal';\nexport type NzSpaceAlign = 'start' | 'end' | 'center' | 'baseline';\nexport type NzSpaceType = 'small' | 'middle' | 'large';\nexport type NzSpaceSize = NzSpaceType | number;\n", "/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\nexport * from './space-compact-item.directive';\nexport * from './space-compact.component';\nexport * from './space-compact.token';\nexport * from './space-item.directive';\nexport * from './space.component';\nexport * from './space.module';\nexport * from './types';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;AAGG;MAQU,qBAAqB,GAAG,IAAI,cAAc,CAAwB,uBAAuB;MACzF,sBAAsB,GAAG,IAAI,cAAc,CACtD,wBAAwB;MAEb,0BAA0B,GAAG,IAAI,cAAc,CAAS,4BAA4B;;ACfjG;;;AAGG;MAuBU,uBAAuB,CAAA;IACzB,OAAO,GAAG,KAAK,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC;AACvD,IAAA,WAAW,GAAG,KAAK,CAA4B,YAAY,CAAC;AAC5D,IAAA,MAAM,GAAG,KAAK,CAAgB,SAAS,CAAC;AACxC,IAAA,UAAU,GAA4B,MAAM,CAAC,UAAU,CAAC;uGAJtD,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,uBAAuB,EANvB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,WAAA,EAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,UAAA,EAAA,aAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,MAAA,EAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,+BAAA,EAAA,WAAA,EAAA,kCAAA,EAAA,8BAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAE,OAAO,EAAE,qBAAqB,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC,MAAM,EAAE;AAC5F,YAAA,EAAE,OAAO,EAAE,sBAAsB,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC,EAAE,CAAC;AAChE,SAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EATS,CAA2B,yBAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;2FAY1B,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAfnC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,CAA2B,yBAAA,CAAA;AACrC,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,mBAAmB;AAC1B,wBAAA,iCAAiC,EAAE,CAAW,SAAA,CAAA;AAC9C,wBAAA,oCAAoC,EAAE,CAA8B,4BAAA;AACrE,qBAAA;AACD,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAE,OAAO,EAAE,qBAAqB,EAAE,UAAU,EAAE,MAAM,MAAM,CAAyB,uBAAA,CAAA,CAAC,MAAM,EAAE;AAC5F,wBAAA,EAAE,OAAO,EAAE,sBAAsB,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC,EAAE,CAAC;AAChE,qBAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC;AAC1C,iBAAA;;;ACzBD;;;AAGG;MAeU,2BAA2B,CAAA;AACtC;;;AAGG;AACc,IAAA,eAAe,GAAG,MAAM,CAAC,uBAAuB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AACjF,IAAA,KAAK,GAAG,MAAM,CAAC,sBAAsB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AACtE,IAAA,IAAI,GAAG,MAAM,CAAC,0BAA0B,CAAC;AACzC,IAAA,UAAU,GAA4B,MAAM,CAAC,UAAU,CAAC;AACxD,IAAA,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;AACvC,IAAA,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AAExG,IAAA,IAAY,aAAa,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,aAAa;;AAG3C,IAAA,KAAK,GAAG,QAAQ,CAAC,MAAK;;QAE9B,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,KAAK;AAAE,YAAA,OAAO,IAAI;;QAErD,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,eAAgB,CAAC,UAAU,CAAC,aAAa;AAAE,YAAA,OAAO,IAAI;AAEtF,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE;AACpD,QAAA,MAAM,OAAO,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,CAAC;QAChF,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AACjC,QAAA,MAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,IAAI,OAAO,CAAC;;;;AAItD,QAAA,IAAI,KAAK,KAAK,UAAU,EAAE;AACxB,YAAA,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;;aACtD,IAAI,KAAK,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACrC,YAAA,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;;AAG5D,QAAA,OAAO,OAAO;AAChB,KAAC,CAAC;AAEF,IAAA,WAAA,GAAA;QACE,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE;QAE1C,eAAe,CAAC,MAAK;;AAEnB,YAAA,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,eAAgB,CAAC,UAAU,CAAC,aAAa,EAAE;gBACzE,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;AAC5F,gBAAA,IAAI,CAAC,KAAM,CAAC,MAAM,CAAC,KAAK,IAAG;AACzB,oBAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,EAAE;oBAC9B,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC;AAC/B,oBAAA,OAAO,QAAQ;AACjB,iBAAC,CAAC;;AAEN,SAAC,CAAC;;IAGJ,WAAW,GAAA;QACT,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;;uGAxDjD,2BAA2B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA3B,2BAA2B,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAA3B,2BAA2B,EAAA,UAAA,EAAA,CAAA;kBANvC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,IAAI,EAAE;AACJ,wBAAA,SAAS,EAAE;AACZ;AACF,iBAAA;;AA6DD,SAAS,oBAAoB,CAC3B,IAAY,EACZ,SAAoC,EACpC,QAA6C,EAAA;AAE7C,IAAA,MAAM,eAAe,GAAG,SAAS,KAAK,UAAU,GAAG,WAAW,GAAG,EAAE;AACnE,IAAA,OAAO,OAAO,IAAI,CAAA,SAAA,EAAY,eAAe,CAAG,EAAA,QAAQ,EAAE;AAC5D;AAEA,SAAS,kBAAkB,CAAC,IAAY,EAAE,SAAoC,EAAE,GAAa,EAAA;IAC3F,MAAM,SAAS,GAAG,GAAG,GAAG,MAAM,GAAG,EAAE;AACnC,IAAA,OAAO,CAAG,EAAA,oBAAoB,CAAC,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,CAAA,EAAG,SAAS,CAAA,CAAE;AACvE;AAEA,SAAS,uBAAuB,CAAC,IAAY,EAAE,SAAoC,EAAA;IACjF,OAAO,oBAAoB,CAAC,IAAI,EAAE,SAAS,EAAE,YAAY,CAAC;AAC5D;AAEA,SAAS,sBAAsB,CAAC,IAAY,EAAE,SAAoC,EAAA;IAChF,OAAO,oBAAoB,CAAC,IAAI,EAAE,SAAS,EAAE,WAAW,CAAC;AAC3D;;AClGA;;;AAGG;MAOU,oBAAoB,CAAA;uGAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAApB,oBAAoB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAHhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE;AACX,iBAAA;;;ACoBD,MAAM,qBAAqB,GAAgB,OAAO;AAClD,MAAM,UAAU,GAAgC;AAC9C,IAAA,KAAK,EAAE,CAAC;AACR,IAAA,MAAM,EAAE,EAAE;AACV,IAAA,KAAK,EAAE;CACR;IAyCY,gBAAgB,GAAA,CAAA,MAAA;;;;iBAAhB,gBAAgB,CAAA;;;AAOjB,YAAA,kBAAA,GAAA,CAAA,UAAU,EAAE,CAAA;YAAC,YAAA,CAAA,IAAA,EAAA,IAAA,EAAA,kBAAA,EAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,QAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,QAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,GAAA,CAAA,MAAM,EAAN,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,KAAA,EAAA,GAAA,CAAA,MAAM,GAAwB,KAAA,CAAA,EAAA,EAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,oBAAA,EAAA,yBAAA,CAAA;;;QAS5C,eAAe;QACd,GAAG;QAhBJ,aAAa,GAAgB,qBAAqB;QAElD,WAAW,GAAqB,YAAY;AAC5C,QAAA,OAAO;QACP,OAAO,GAAuD,IAAI;QACnC,MAAM,GAAY,KAAK;QACxC,MAAM,GAAA,iBAAA,CAAA,IAAA,EAAA,oBAAA,EAAgB,OAAO,CAAC;AAES,QAAA,KAAK,GAAqC,iBAAA,CAAA,IAAA,EAAA,yBAAA,CAAA;AAExG,QAAA,WAAW;AACX,QAAA,SAAS,GAAW,UAAU,CAAC,KAAK;AAC5B,QAAA,QAAQ,GAAG,IAAI,OAAO,EAAW;QAEzC,WACS,CAAA,eAAgC,EAC/B,GAAsB,EAAA;YADvB,IAAe,CAAA,eAAA,GAAf,eAAe;YACd,IAAG,CAAA,GAAA,GAAH,GAAG;;QAGL,gBAAgB,GAAA;YACtB,MAAM,UAAU,GAAG,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM;AAC1F,YAAA,IAAI,CAAC,SAAS,GAAG,UAAU,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;AACpD,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;;QAGzB,WAAW,GAAA;YACT,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,KAAK,YAAY,GAAG,QAAQ,GAAG,IAAI,CAAC,OAAO;;QAG9G,WAAW,GAAA;AACT,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;QAG1B,kBAAkB,GAAA;YAChB,IAAI,CAAC,gBAAgB,EAAE;AACvB,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AAC/D,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;AACzB,aAAC,CAAC;;2GAxCO,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,QAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,mKAMP,gBAAgB,CAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,4BAAA,EAAA,gCAAA,EAAA,0BAAA,EAAA,8BAAA,EAAA,6BAAA,EAAA,2BAAA,EAAA,2BAAA,EAAA,yBAAA,EAAA,8BAAA,EAAA,4BAAA,EAAA,gCAAA,EAAA,8BAAA,EAAA,iBAAA,EAAA,0BAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAGnB,oBAAoB,EAAA,IAAA,EAAU,WAAW,EA5ChD,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;GAsBT,EAWS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,oJAAE,+BAA+B,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,+BAAA,EAAA,wBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA;;;2FAEhD,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAvC5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE,SAAS;oBACnB,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;AAsBT,EAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,KAAK,EAAE,WAAW;AAClB,wBAAA,8BAA8B,EAAE,8BAA8B;AAC9D,wBAAA,4BAA4B,EAAE,4BAA4B;AAC1D,wBAAA,+BAA+B,EAAE,yBAAyB;AAC1D,wBAAA,6BAA6B,EAAE,uBAAuB;AACtD,wBAAA,gCAAgC,EAAE,0BAA0B;AAC5D,wBAAA,kCAAkC,EAAE,4BAA4B;AAChE,wBAAA,mBAAmB,EAAE;AACtB,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,+BAA+B;AAC5D,iBAAA;oHAIU,WAAW,EAAA,CAAA;sBAAnB;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACQ,OAAO,EAAA,CAAA;sBAAf;gBACuC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE;gBACf,MAAM,EAAA,CAAA;sBAA5B;gBAE6D,KAAK,EAAA,CAAA;sBAAlE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,oBAAoB,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE;;;ACpF9D;;;AAGG;MAYU,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAb,aAAa,EAAA,OAAA,EAAA,CAHd,gBAAgB,EAAE,oBAAoB,EAAE,uBAAuB,CAAA,EAAA,OAAA,EAAA,CAC/D,gBAAgB,EAAE,oBAAoB,EAAE,uBAAuB,CAAA,EAAA,CAAA;wGAE9D,aAAa,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,oBAAoB,EAAE,uBAAuB,CAAC;AAC1E,oBAAA,OAAO,EAAE,CAAC,gBAAgB,EAAE,oBAAoB,EAAE,uBAAuB;AAC1E,iBAAA;;;ACdD;;;AAGG;;ACHH;;;AAGG;;ACHH;;AAEG;;;;"}