import * as i0 from "@angular/core";
import * as i1 from "./transfer.component";
import * as i2 from "./transfer-list.component";
import * as i3 from "./transfer-search.component";
export declare class NzTransferModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<NzTransferModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<NzTransferModule, never, [typeof i1.NzTransferComponent, typeof i2.NzTransferListComponent, typeof i3.NzTransferSearchComponent], [typeof i1.NzTransferComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<NzTransferModule>;
}
