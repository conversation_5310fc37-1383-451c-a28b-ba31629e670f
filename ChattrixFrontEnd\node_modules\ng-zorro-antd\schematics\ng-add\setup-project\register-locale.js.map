{"version": 3, "file": "register-locale.js", "sourceRoot": "", "sources": ["../../../../schematics/ng-add/setup-project/register-locale.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;AA2BH,wCAWC;AApCD,wDAWiC;AAEjC,2DAA+D;AAC/D,yDAA8D;AAC9D,+DAAsF;AACtF,kFAAkF;AAClF,sEAA2F;AAC3F,qEAAqE;AACrE,iCAA2C;AAC3C,iCAAiC;AAEjC,6DAA+D;AAG/D,SAAgB,cAAc,CAAC,OAAe;IAC5C,OAAO,CAAO,IAAU,EAAE,EAAE;QAC1B,MAAM,SAAS,GAAG,MAAM,IAAA,wBAAY,EAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,IAAA,oCAAuB,EAAC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACpE,MAAM,QAAQ,GAAG,IAAA,+BAAkB,EAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,IAAA,4BAAe,EAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC;YACpC,OAAO,6BAA6B,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,OAAO,yBAAyB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACtD,CAAC;IACH,CAAC,CAAA,CAAC;AACJ,CAAC;AAED,SAAS,yBAAyB,CAAC,QAAgB,EAAE,OAAe;IAClE,OAAO,CAAO,IAAU,EAAE,EAAE;QAC1B,MAAM,aAAa,GAAG,IAAA,6BAAgB,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACvD,MAAM,YAAY,GAAG,IAAA,4BAAe,EAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAE1D,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC;QACzC,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1C,IAAA,kCAAkB,EAAC,IAAI,EAAE,aAAa,EAAE;YACtC,IAAA,yBAAY,EAAC,YAAY,EAAE,aAAa,EAAE,eAAe,EACvD,oBAAoB,CAAC;YACvB,IAAA,yBAAY,EAAC,YAAY,EAAE,aAAa,EAAE,MAAM,EAC9C,oBAAoB,CAAC;YACvB,IAAA,yBAAY,EAAC,YAAY,EAAE,aAAa,EAAE,oBAAoB,EAC5D,iBAAiB,CAAC;YACpB,IAAA,yBAAY,EAAC,YAAY,EAAE,aAAa,EAAE,YAAY,EACpD,2BAA2B,YAAY,EAAE,EAAE,IAAI,CAAC;YAClD,kBAAkB,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;YAC7D,GAAG,sBAAsB,CAAC,YAAY,EAAE,aAAa,EAAE,MAAM,CAAC;SAC/D,CAAC,CAAC;IACL,CAAC,CAAA,CAAC;AACJ,CAAC;AAED,SAAS,6BAA6B,CAAC,QAAgB,EAAE,OAAe;IACtE,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC;IAEzC,OAAO,IAAA,kBAAK,EAAC;QACX,CAAO,IAAU,EAAE,EAAE;YACnB,MAAM,aAAa,GAAG,IAAA,mCAA4B,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,IAAA,0BAAa,EAAC,aAAa,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC/D,MAAM,aAAa,GAAG,SAAS,CAAC,QAAQ,CAAC;YACzC,MAAM,eAAe,GAAG,IAAA,4BAAe,EAAC,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;YAClE,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAE1C,IAAA,kCAAkB,EAAC,IAAI,EAAE,aAAa,EAAE;gBACtC,IAAA,yBAAY,EAAC,eAAe,EAAE,aAAa,EAAE,MAAM,EAAE,oBAAoB,CAAC;gBAC1E,IAAA,yBAAY,EAAC,eAAe,EAAE,aAAa,EAAE,oBAAoB,EAAE,iBAAiB,CAAC;gBACrF,IAAA,yBAAY,EAAC,eAAe,EAAE,aAAa,EAAE,YAAY,EAAE,2BAA2B,YAAY,EAAE,EAAE,IAAI,CAAC;gBAC3G,kBAAkB,CAAC,eAAe,EAAE,aAAa,EAAE,YAAY,CAAC;aACjE,CAAC,CAAC;QACL,CAAC,CAAA;QACD,IAAA,yBAAe,EAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;YACtD,OAAO,IAAI,CAAA,GAAG,QAAQ,CAAC,eAAe,EAAE,oBAAoB,CAAC,IAAI,MAAM,GAAG,CAAC;QAC7E,CAAC,CAAC;KACH,CAAC,CAAC;AACL,CAAC;AAED,SAAS,kBAAkB,CAAC,YAA2B,EAAE,UAAkB,EAAE,MAAc;IACzF,MAAM,UAAU,GAAG,IAAA,sBAAS,EAAC,YAAY,EAAE,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;IAC5E,MAAM,MAAM,GAAG,IAAA,sBAAS,EAAC,YAAY,EAAE,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;IAE1E,MAAM,qBAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;;QACjD,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC/B,OAAO,CAAA,MAAA,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,0CAAE,OAAO,EAAE,MAAK,oBAAoB,CAAC;IACrE,CAAC,CAAC,CAAC;IAEH,IAAI,qBAAqB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACvC,OAAO,IAAA,sCAAyB,EAAC,UAAU,EAAE,0BAA0B,MAAM,IAAI,EAC/E,UAAU,EAAE,CAAC,CAAiB,CAAC;IACnC,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,IAAA,cAAM,EAAC,iDAAiD,IAAA,YAAI,EAAC,UAAU,CAAC,IAAI;YACtF,yDAAyD,CAAC,CAAC,CAAC;QAC9D,OAAO,CAAC,GAAG,CAAC,IAAA,cAAM,EAAC,yCAAyC,CAAC,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,IAAA,YAAI,EAAC,sBAAsB,MAAM,IAAI,CAAC,CAAC,CAAC;QACpD,OAAO,IAAI,mBAAU,EAAE,CAAC;IAC1B,CAAC;AACH,CAAC;AAED,SAAS,sBAAsB,CAAC,YAA2B,EAAE,UAAkB,EAAE,MAAc;IAC7F,MAAM,aAAa,GAAG,WAAW,CAAC;IAClC,MAAM,KAAK,GAAG,IAAA,iCAAoB,EAAC,YAAY,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC;IAC9E,MAAM,UAAU,GAAG,IAAA,wCAA2B,EAC1C,YAAY,EACZ,UAAU,EACV,WAAW,EACX,iBAAiB,MAAM,GAAG,EAC1B,IAAI,CACL,CAAC;IACJ,8DAA8D;IAC9D,MAAM,IAAI,GAAQ,KAAK,CAAC,CAAC,CAAC,CAAC;IAE3B,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,kBAAkB,GACrB,IAAmC,CAAC,UAAU;SAC5C,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC;SAC9D,MAAM,CAAC,CAAC,IAA2B,EAAE,EAAE;QACtC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,OAAQ,IAAsB,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,aAAa,CAAC;YACzE,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gBAC9B,OAAQ,IAAyB,CAAC,IAAI,KAAK,aAAa,CAAC;QAC7D,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;IAEP,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,kBAAkB,CAAC,MAAM,EAAE,CAAC;QAC9B,MAAM,UAAU,GAAG,kBAAkB,CAAC,CAAC,CAA0B,CAAC;QAClE,IAAI,UAAU,CAAC,WAAW,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,sBAAsB,EAAE,CAAC;YACzE,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,MAAM,UAAU,GAAG,UAAU,CAAC,WAAwC,CAAC;QACvE,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,UAAU,CAAC;QACpB,CAAC;aAAM,CAAC;YACN,MAAM,gBAAgB,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAC,OAAA,MAAA,CAAC,CAAC,OAAO,kDAAK,QAAQ,CAAC,SAAS,CAAC,CAAA,EAAA,CAAC,CAAC;YAC1F,MAAM,eAAe,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAC,OAAA,MAAA,CAAC,CAAC,OAAO,kDAAK,QAAQ,CAAC,eAAe,CAAC,CAAA,EAAA,CAAC,CAAC;YAE/F,IAAI,CAAC,eAAe,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1C,OAAO,UAAU,CAAC;YACpB,CAAC;YAED,OAAO,CAAC,GAAG,EAAE,CAAC;YACd,OAAO,CAAC,GAAG,CAAC,IAAA,cAAM,EAAC,+CAA+C,IAAA,YAAI,EAAC,UAAU,CAAC,0DAA0D,CAAC,CAAC,CAAC;YAE/I,IAAI,gBAAgB,EAAE,CAAC;gBACrB,OAAO,CAAC,GAAG,CAAC,IAAA,cAAM,EAAC,2DAA2D,CAAC,CAAC,CAAC;gBACjF,OAAO,CAAC,GAAG,CAAC,IAAA,YAAI,EAAC,iBAAiB,MAAM,GAAG,CAAC,CAAC,CAAC;YAChD,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;SAAM,CAAC;QACN,OAAO,UAAU,CAAC;IACpB,CAAC;AACH,CAAC"}